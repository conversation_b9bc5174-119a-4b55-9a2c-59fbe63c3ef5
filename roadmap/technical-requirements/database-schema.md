# AI Hub Database Schema

## Overview

This document outlines the database schema for the AI Hub application. The schema is designed to support all the features of the application while maintaining data integrity, performance, and scalability.

## Database Technology

The recommended database technology is a combination of:

- **Primary Database**: Azure Cosmos DB (or MongoDB) for flexible schema evolution and scalability
- **Search Index**: Azure Cognitive Search for advanced search capabilities
- **Cache Layer**: Redis for high-performance caching
- **Analytics Store**: Data warehouse for reporting and analytics

## Core Entities

### Users

```
users {
  id: string (primary key)
  email: string (unique)
  passwordHash: string
  firstName: string
  lastName: string
  role: string (enum: admin, manager, user)
  department: string
  jobTitle: string
  profileImage: string (URL)
  createdAt: timestamp
  updatedAt: timestamp
  lastLoginAt: timestamp
  status: string (enum: active, inactive, pending)
  preferences: {
    theme: string
    language: string
    notifications: {
      email: boolean
      inApp: boolean
    }
  }
}
```

### AI Agents

```
agents {
  id: string (primary key)
  name: string
  description: string
  type: string (enum: chatbot, assistant, analyzer, etc.)
  provider: string (enum: openai, anthropic, google, etc.)
  model: string
  capabilities: string[]
  icon: string (URL)
  status: string (enum: active, inactive, maintenance)
  createdAt: timestamp
  updatedAt: timestamp
  configuration: {
    parameters: object
    apiKeys: string (encrypted)
    endpoints: string[]
  }
  usage: {
    totalCalls: number
    totalTokens: number
    averageResponseTime: number
  }
  permissions: {
    roles: string[]
    departments: string[]
  }
}
```

### Agent Usage

```
agentUsage {
  id: string (primary key)
  agentId: string (foreign key)
  userId: string (foreign key)
  sessionId: string
  startTime: timestamp
  endTime: timestamp
  inputTokens: number
  outputTokens: number
  totalCost: number
  query: string
  response: string
  feedback: {
    rating: number
    comments: string
  }
  metadata: {
    browser: string
    device: string
    location: string
  }
}
```

### Learning Content

```
learningContent {
  id: string (primary key)
  title: string
  description: string
  type: string (enum: video, article, blog, resource)
  content: string (HTML or markdown)
  author: string
  publishedAt: timestamp
  updatedAt: timestamp
  status: string (enum: published, draft, archived)
  featuredImage: string (URL)
  categories: string[]
  tags: string[]
  duration: number (minutes)
  difficulty: string (enum: beginner, intermediate, advanced)
  views: number
  likes: number
  relatedContent: string[] (IDs)
}
```

### News

```
news {
  id: string (primary key)
  title: string
  description: string
  content: string (HTML or markdown)
  author: string
  source: string
  sourceUrl: string
  publishedAt: timestamp
  updatedAt: timestamp
  status: string (enum: published, draft, archived)
  featuredImage: string (URL)
  category: string (enum: research, industry, policy, events)
  tags: string[]
  featured: boolean
  views: number
}
```

### Organization Structure

```
orgNodes {
  id: string (primary key)
  name: string
  title: string
  department: string
  description: string
  status: string (enum: active, in-development, planned, concept)
  icon: string
  parentId: string (foreign key, self-referential)
  order: number
  createdAt: timestamp
  updatedAt: timestamp
  metadata: {
    createdBy: string (user ID)
    updatedBy: string (user ID)
  }
}
```

### Tools

```
tools {
  id: string (primary key)
  name: string
  description: string
  icon: string
  productIcon: string (URL)
  url: string
  category: string
  status: string (enum: active, inactive)
  createdAt: timestamp
  updatedAt: timestamp
}
```

### Node Tools

```
nodeTools {
  id: string (primary key)
  nodeId: string (foreign key)
  toolId: string (foreign key)
  order: number
  createdAt: timestamp
  updatedAt: timestamp
}
```

### User Progress

```
userProgress {
  id: string (primary key)
  userId: string (foreign key)
  contentId: string (foreign key)
  progress: number (percentage)
  completed: boolean
  lastAccessedAt: timestamp
  timeSpent: number (seconds)
  notes: string
  bookmarked: boolean
}
```

### Comments

```
comments {
  id: string (primary key)
  userId: string (foreign key)
  contentId: string (foreign key)
  parentId: string (for replies)
  text: string
  createdAt: timestamp
  updatedAt: timestamp
  likes: number
  status: string (enum: active, hidden, flagged)
}
```

### Notifications

```
notifications {
  id: string (primary key)
  userId: string (foreign key)
  title: string
  message: string
  type: string (enum: info, warning, error, success)
  read: boolean
  createdAt: timestamp
  link: string (URL)
  source: string (enum: system, user, agent)
  expiresAt: timestamp
}
```

## Relationships

- **Users to AgentUsage**: One-to-many (a user can have multiple agent usage records)
- **Agents to AgentUsage**: One-to-many (an agent can have multiple usage records)
- **Users to UserProgress**: One-to-many (a user can have progress on multiple content items)
- **LearningContent to UserProgress**: One-to-many (a content item can have progress records for multiple users)
- **OrgNodes to OrgNodes**: Self-referential one-to-many (parent-child relationship)
- **OrgNodes to NodeTools**: One-to-many (a node can have multiple tools)
- **Tools to NodeTools**: One-to-many (a tool can be associated with multiple nodes)
- **Users to Comments**: One-to-many (a user can create multiple comments)
- **LearningContent/News to Comments**: One-to-many (content can have multiple comments)
- **Comments to Comments**: Self-referential one-to-many (parent-child for replies)
- **Users to Notifications**: One-to-many (a user can have multiple notifications)

## Indexes

### Primary Indexes
- All primary keys (id fields)
- Unique indexes on email in users collection

### Secondary Indexes
- users: role, department, status
- agents: type, provider, status
- agentUsage: agentId, userId, sessionId
- learningContent: type, status, categories, tags
- news: category, status, featured
- orgNodes: parentId, department, status
- userProgress: userId, contentId
- comments: userId, contentId, parentId
- notifications: userId, read

### Compound Indexes
- agentUsage: [userId, agentId]
- userProgress: [userId, contentId]
- learningContent: [type, status]
- news: [category, publishedAt]
- orgNodes: [parentId, order]
- nodeTools: [nodeId, order]

## Data Migration Strategy

1. **Initial Data Import**: Import existing mock data into the production database
2. **Schema Evolution**: Use a schema versioning approach for future changes
3. **Data Validation**: Validate data integrity during migration
4. **Rollback Plan**: Maintain ability to rollback migrations
5. **Data Transformation**: Transform data as needed during migration

## Backup and Recovery

1. **Automated Backups**: Schedule regular automated backups
2. **Point-in-Time Recovery**: Support for restoring to specific points in time
3. **Geo-Redundant Backups**: Store backups in multiple geographic locations
4. **Backup Testing**: Regularly test backup restoration
5. **Retention Policy**: Define how long backups are retained

## Performance Considerations

1. **Sharding Strategy**: Partition data for horizontal scaling
2. **Indexing Strategy**: Optimize indexes for common queries
3. **Caching Layer**: Implement Redis caching for frequently accessed data
4. **Query Optimization**: Design efficient queries
5. **Connection Pooling**: Manage database connections efficiently
