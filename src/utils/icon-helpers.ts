import {
  faBrain,
  faCode,
  faChartLine,
  faServer,
  faShieldAlt,
  faPalette,
  faRobot,
  faUserTie,
  faUsers,
  faDatabase,
  faLightbulb,
  faChartPie,
  faNetworkWired,
  faMoneyBillWave,
  faBullhorn,
  faFlask,
  faGraduationCap,
  faProjectDiagram,
  faUserCog,
  faClipboardCheck,
  faFileAlt,
  faComments,
  faGlobe,
  faBuilding,
  faLock,
  faTools,
  faWrench,
  faMicrochip,
  faCloud,
  faDesktop,
  faMobile,
  faTablet,
  faEnvelope,
  faVideo,
  faImage,
  faMusic,
  faVoicemail,
  faCalculator,
  faCalendar,
  faSearch as faSearchAlt,
  faChartBar,
  faPencilAlt,
  faEdit,
  faTerminal,
  faKeyboard,
  faRobot as faRobotAlt,
  faLanguage,
  faFileCode,
  faFileWord,
  faFilePdf,
  faFileExcel,
  faFilePowerpoint,
  faFileImage,
  faFileVideo,
  faFileAudio,
  faFileArchive,
  faFileAlt as faFileAltAlt,
  faFileCsv,
  faFileContract,
  faFileSignature,
  faFileInvoice,
  faFileInvoiceDollar,
  faFileMedical,
  faFileMedicalAlt,
  faFileUpload,
  faFileDownload,
  faFileExport,
  faFileImport,
  faFilePrescription
} from '@fortawesome/free-solid-svg-icons';
import { AgentStatus, OrgNode, Tool } from '@/types/ai-org';

// Get status color
export const getStatusColor = (status: AgentStatus) => {
  switch (status) {
    case 'active': return 'text-green-500';
    case 'in-development': return 'text-blue-500';
    case 'planned': return 'text-amber-500';
    case 'concept': return 'text-gray-500';
    default: return 'text-gray-500';
  }
};

// Get status label
export const getStatusLabel = (status: AgentStatus) => {
  switch (status) {
    case 'active': return 'Active';
    case 'in-development': return 'In Development';
    case 'planned': return 'Planned';
    case 'concept': return 'Concept';
    default: return 'Unknown';
  }
};

// Get icon based on department or role
export const getNodeIcon = (node: OrgNode) => {
  // If node has a specific icon defined, use that
  if (node.icon) {
    switch (node.icon) {
      case 'brain': return faBrain;
      case 'code': return faCode;
      case 'chart-line': return faChartLine;
      case 'server': return faServer;
      case 'shield': return faShieldAlt;
      case 'palette': return faPalette;
      case 'robot': return faRobot;
      case 'user-tie': return faUserTie;
      case 'users': return faUsers;
      case 'database': return faDatabase;
      case 'lightbulb': return faLightbulb;
      case 'chart-pie': return faChartPie;
      case 'network': return faNetworkWired;
      case 'money': return faMoneyBillWave;
      case 'bullhorn': return faBullhorn;
      case 'flask': return faFlask;
      case 'graduation-cap': return faGraduationCap;
      case 'project-diagram': return faProjectDiagram;
      case 'user-cog': return faUserCog;
      case 'clipboard-check': return faClipboardCheck;
      case 'file': return faFileAlt;
      case 'comments': return faComments;
      case 'globe': return faGlobe;
      case 'building': return faBuilding;
      case 'lock': return faLock;
      default: return null;
    }
  }
  
  // Otherwise, determine icon based on department
  switch (node.department.toLowerCase()) {
    case 'executive': return faUserTie;
    case 'technology': return faServer;
    case 'software development': return faCode;
    case 'data science': return faDatabase;
    case 'cybersecurity': return faShieldAlt;
    case 'operations': return faProjectDiagram;
    case 'project management': return faClipboardCheck;
    case 'human resources': return faUsers;
    case 'finance': return faMoneyBillWave;
    case 'marketing': return faBullhorn;
    case 'research': return faFlask;
    case 'economics': return faChartLine;
    case 'policy research': return faFileAlt;
    case 'information technology': return faNetworkWired;
    default: return faBrain; // Default icon for AI agents
  }
};

// Get icon for tool
export const getToolIcon = (tool: Tool) => {
  // If tool has a product icon URL, return null (we'll use the URL directly)
  if (tool.productIcon) {
    return null;
  }
  
  // If tool has a specific icon defined, use that
  if (tool.icon) {
    switch (tool.icon) {
      // General tools
      case 'tools': return faTools;
      case 'wrench': return faWrench;
      case 'microchip': return faMicrochip;
      case 'cloud': return faCloud;
      case 'desktop': return faDesktop;
      case 'mobile': return faMobile;
      case 'tablet': return faTablet;
      
      // Communication tools
      case 'envelope': return faEnvelope;
      case 'video': return faVideo;
      case 'comments': return faComments;
      case 'voicemail': return faVoicemail;
      
      // Media tools
      case 'image': return faImage;
      case 'music': return faMusic;
      
      // Productivity tools
      case 'calculator': return faCalculator;
      case 'calendar': return faCalendar;
      case 'search': return faSearchAlt;
      case 'chart-bar': return faChartBar;
      case 'pencil': return faPencilAlt;
      case 'edit': return faEdit;
      
      // Development tools
      case 'terminal': return faTerminal;
      case 'keyboard': return faKeyboard;
      case 'robot': return faRobotAlt;
      case 'language': return faLanguage;
      
      // File types
      case 'file-code': return faFileCode;
      case 'file-word': return faFileWord;
      case 'file-pdf': return faFilePdf;
      case 'file-excel': return faFileExcel;
      case 'file-powerpoint': return faFilePowerpoint;
      case 'file-image': return faFileImage;
      case 'file-video': return faFileVideo;
      case 'file-audio': return faFileAudio;
      case 'file-archive': return faFileArchive;
      case 'file-alt': return faFileAltAlt;
      case 'file-csv': return faFileCsv;
      case 'file-contract': return faFileContract;
      case 'file-signature': return faFileSignature;
      case 'file-invoice': return faFileInvoice;
      case 'file-invoice-dollar': return faFileInvoiceDollar;
      case 'file-medical': return faFileMedical;
      case 'file-medical-alt': return faFileMedicalAlt;
      case 'file-upload': return faFileUpload;
      case 'file-download': return faFileDownload;
      case 'file-export': return faFileExport;
      case 'file-import': return faFileImport;
      case 'file-prescription': return faFilePrescription;
      
      // Default to a generic file icon
      default: return faFileAlt;
    }
  }
  
  // Default to a generic tool icon
  return faTools;
};
