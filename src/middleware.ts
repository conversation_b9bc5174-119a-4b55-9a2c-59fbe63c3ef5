import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// This function can be marked `async` if using `await` inside
export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Check if the pathname is a protected route
  const isProtectedRoute =
    pathname.startsWith('/browse') ||
    pathname.startsWith('/agent') ||
    pathname.startsWith('/favorites') ||
    pathname.startsWith('/popular') ||
    pathname.startsWith('/recent') ||
    pathname.startsWith('/saved') ||
    pathname.startsWith('/analytics') ||
    pathname.startsWith('/users') ||
    pathname.startsWith('/manage-agents') ||
    pathname === '/';

  // Check if the pathname is an auth route
  const isAuthRoute =
    pathname.startsWith('/auth/login') ||
    pathname.startsWith('/auth/register') ||
    pathname.startsWith('/auth/forgot-password') ||
    pathname.startsWith('/auth/error');

  // Check if user is logged in by looking for the user in localStorage
  // Note: This is a simplified approach. In a real app, you'd use cookies or JWT tokens
  const hasUser = request.cookies.has('user');

  // If the user is not authenticated and the route is protected, redirect to login
  if (!hasUser && isProtectedRoute) {
    const url = new URL('/auth/login', request.url);
    url.searchParams.set('callbackUrl', encodeURI(pathname));
    return NextResponse.redirect(url);
  }

  // If the user is authenticated and trying to access an auth route, redirect to home
  if (hasUser && isAuthRoute) {
    return NextResponse.redirect(new URL('/', request.url));
  }

  // Otherwise, continue with the request
  return NextResponse.next();
}

// See "Matching Paths" below to learn more
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!api|_next/static|_next/image|favicon.ico|public).*)',
  ],
};
