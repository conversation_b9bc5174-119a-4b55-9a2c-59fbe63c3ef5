'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faHome,
  faTableCells,
  faChartLine,
  faHeart,
  faGear,
  faCircleQuestion,
  faHistory,
  faBookmark,
  faUsers,
  faBars,
  faGraduationCap,
  faVideo,
  faNewspaper,
  faBlog,
  faToolbox,
  faShieldAlt,
  faUserCog,
  faSitemap
} from '@fortawesome/free-solid-svg-icons';

type NavItem = {
  name: string;
  href: string;
  icon: React.ReactNode;
  group?: string; // Optional group for visual separation
};

export default function Sidebar() {
  const pathname = usePathname();
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  // Define all navigation items in a flat list
  const navigationItems: NavItem[] = [
    // Discover group
    {
      name: 'Home',
      href: '/',
      icon: <FontAwesomeIcon icon={faHome} className="h-5 w-5" />,
      group: 'Discover'
    },
    {
      name: 'Browse Agents',
      href: '/browse',
      icon: <FontAwesomeIcon icon={faTableCells} className="h-5 w-5" />,
      group: 'Discover'
    },
    {
      name: 'Popular Agents',
      href: '/popular',
      icon: <FontAwesomeIcon icon={faChartLine} className="h-5 w-5" />,
      group: 'Discover'
    },
    {
      name: 'New Releases',
      href: '/new-releases',
      icon: <FontAwesomeIcon icon={faCircleQuestion} className="h-5 w-5" />,
      group: 'Discover'
    },

    // My Agents group
    {
      name: 'Favorites',
      href: '/favorites',
      icon: <FontAwesomeIcon icon={faHeart} className="h-5 w-5" />,
      group: 'My Agents'
    },
    {
      name: 'Recent Sessions',
      href: '/recent',
      icon: <FontAwesomeIcon icon={faHistory} className="h-5 w-5" />,
      group: 'My Agents'
    },
    {
      name: 'Saved Sessions',
      href: '/saved',
      icon: <FontAwesomeIcon icon={faBookmark} className="h-5 w-5" />,
      group: 'My Agents'
    },

    // Learning group
    {
      name: 'Learning Hub',
      href: '/learning',
      icon: <FontAwesomeIcon icon={faGraduationCap} className="h-5 w-5" />,
      group: 'Learning'
    },
    {
      name: 'Videos',
      href: '/learning/videos',
      icon: <FontAwesomeIcon icon={faVideo} className="h-5 w-5" />,
      group: 'Learning'
    },
    {
      name: 'Articles',
      href: '/learning/articles',
      icon: <FontAwesomeIcon icon={faNewspaper} className="h-5 w-5" />,
      group: 'Learning'
    },
    {
      name: 'Blog',
      href: '/learning/blog',
      icon: <FontAwesomeIcon icon={faBlog} className="h-5 w-5" />,
      group: 'Learning'
    },
    {
      name: 'Resources',
      href: '/learning/resources',
      icon: <FontAwesomeIcon icon={faToolbox} className="h-5 w-5" />,
      group: 'Learning'
    },

    // Admin group
    {
      name: 'Admin Dashboard',
      href: '/admin',
      icon: <FontAwesomeIcon icon={faUserCog} className="h-5 w-5" />,
      group: 'Admin'
    },
    {
      name: 'AI Organization',
      href: '/admin/ai-org',
      icon: <FontAwesomeIcon icon={faSitemap} className="h-5 w-5" />,
      group: 'Admin'
    },

  ];

  // Use useEffect to handle client-side only code
  React.useEffect(() => {
    setIsMounted(true);
  }, []);

  // Load collapsed state from localStorage on mount and listen for changes
  React.useEffect(() => {
    if (typeof window !== 'undefined') {
      // Initial load of collapsed state
      const loadCollapsedState = () => {
        const savedState = localStorage.getItem('sidebarCollapsed');
        if (savedState !== null) {
          setIsCollapsed(savedState === 'true');
        }
      };

      // Load initial state
      loadCollapsedState();

      // Handle sidebar toggle event from header
      const handleSidebarToggle = () => {
        // Update collapsed state from localStorage
        loadCollapsedState();

        // Force a re-render when the sidebar-open class changes for mobile
        if (window.innerWidth < 768) {
          setIsMounted(prev => !prev);
          setIsMounted(prev => !prev);
        }
      };

      // Listen for the custom sidebar-toggle event
      window.addEventListener('sidebar-toggle', handleSidebarToggle);

      // Also listen for storage events (in case localStorage changes in another tab)
      window.addEventListener('storage', (e) => {
        if (e.key === 'sidebarCollapsed') {
          loadCollapsedState();
        }
      });

      return () => {
        window.removeEventListener('sidebar-toggle', handleSidebarToggle);
        window.removeEventListener('storage', loadCollapsedState);
      };
    }
  }, []);

  return (
    <aside
      className={`fixed left-0 z-50 flex flex-col h-[calc(100vh-64px)] transition-all duration-300 ${
        isCollapsed ? 'w-16' : 'w-64'
      } md:translate-x-0 ${
        isMounted && typeof window !== 'undefined' && document.documentElement.classList.contains('sidebar-open')
          ? 'translate-x-0'
          : '-translate-x-full'
      }`}
    >
      <div className="flex h-16 items-center justify-between border-b border-gray-200/30 px-4 dark:border-gray-800/30 mt-2">
        <span className={`text-container text-lg font-semibold text-gray-900 dark:text-white whitespace-nowrap overflow-hidden ${isCollapsed ? 'hidden' : ''}`}>
          AI Hub
        </span>
        {isCollapsed && <div className="w-full"></div>}

        {/* Hamburger toggle button (visible only on desktop) */}
        <button
          type="button"
          className={`p-2 text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300 ${isCollapsed ? 'mx-auto' : ''}`}
          onClick={() => {
            const newState = !isCollapsed;
            setIsCollapsed(newState);

            // Store preference in localStorage
            if (typeof window !== 'undefined') {
              localStorage.setItem('sidebarCollapsed', newState.toString());

              // Dispatch a custom event to notify other components
              window.dispatchEvent(new Event('sidebar-toggle'));
            }
          }}
          aria-label="Toggle sidebar"
        >
          <FontAwesomeIcon icon={faBars} className="h-5 w-5" />
          <span className="sr-only">Toggle sidebar</span>
        </button>
      </div>

      {/* Horizontal line to separate toggle from navigation */}
      <div className="border-b border-gray-200/30 dark:border-gray-800/30 my-2"></div>

      <div className="flex-1 overflow-y-auto p-4 h-full">
        <nav className="space-y-6">
          {/* Group items by their group property */}
          {Object.entries(
            navigationItems.reduce((acc, item) => {
              const group = item.group || 'Other';
              if (!acc[group]) acc[group] = [];
              acc[group].push(item);
              return acc;
            }, {} as Record<string, NavItem[]>)
          ).map(([group, items]) => (
            <div key={group} className="space-y-2">
              {/* Show group name only when sidebar is expanded */}
              {!isCollapsed && (
                <div className="text-container mb-2 px-3 text-xs font-semibold uppercase tracking-wider text-gray-500 dark:text-gray-400 whitespace-nowrap overflow-hidden">
                  {group}
                </div>
              )}

              <ul className="space-y-1">
                {items.map((item) => (
                  <li key={item.name}>
                    <Link
                      href={item.href}
                      className={`flex items-center rounded-md px-3 py-2 text-sm font-medium ${
                        pathname === item.href
                          ? 'bg-blue-50 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400'
                          : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800'
                      } ${isCollapsed ? 'justify-center' : ''}`}
                      title={isCollapsed ? item.name : undefined}
                    >
                      <span className={`icon-container ${isCollapsed ? '' : 'mr-3'}`}>{item.icon}</span>
                      {!isCollapsed && <span className="text-container whitespace-nowrap overflow-hidden">{item.name}</span>}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </nav>
      </div>
    </aside>
  );
}
