'use client';

import React from 'react';
import { usePathname } from 'next/navigation';
import Layout from './Layout';

export default function ConditionalLayout({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  
  // Check if the current path is an auth route
  const isAuthRoute = pathname?.startsWith('/auth');
  
  // If it's an auth route, render children directly without the main layout
  if (isAuthRoute) {
    return <>{children}</>;
  }
  
  // For all other routes, use the main layout with header and sidebar
  return <Layout>{children}</Layout>;
}
