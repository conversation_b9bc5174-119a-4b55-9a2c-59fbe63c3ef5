'use client';

import React, { useEffect } from 'react';
import Header from './Header';
import EnhancedSidebar from '../navigation/EnhancedSidebar';
import QuickActions from '../navigation/QuickActions';
import { NavigationProvider } from '@/contexts/NavigationContext';

export default function Layout({ children }: { children: React.ReactNode }) {
  // We don't need to track sidebar state anymore since it doesn't affect main content width
  // But we keep the event listeners for other components that might need it

  // Check if sidebar is collapsed on mount and listen for changes
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const updateSidebarState = () => {
      // We no longer need to update state, just dispatch events for other components
      // This function is kept for consistency with the event listeners
    };

    // Initial load
    updateSidebarState();

    // Listen for changes to the sidebar state
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'sidebarCollapsed') {
        updateSidebarState();
      }
    };

    // Listen for custom sidebar toggle event
    const handleSidebarToggle = () => {
      updateSidebarState();
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('sidebar-toggle', handleSidebarToggle);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('sidebar-toggle', handleSidebarToggle);
    };
  }, []);

  // Close sidebar when clicking outside on mobile
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const handleClickOutside = (event: MouseEvent) => {
      const sidebar = document.querySelector('aside');
      const sidebarButton = document.querySelector('button[aria-label="Toggle sidebar"]');
      const menuButton = document.querySelector('button[aria-label="Toggle menu"]');

      // Check if we clicked outside the sidebar and both toggle buttons
      if (
        sidebar &&
        !sidebar.contains(event.target as Node) &&
        sidebarButton &&
        !sidebarButton.contains(event.target as Node) &&
        menuButton &&
        !menuButton.contains(event.target as Node) &&
        document.documentElement.classList.contains('sidebar-open')
      ) {
        document.documentElement.classList.remove('sidebar-open');
      }
    };

    // Also close sidebar when clicking on the overlay (the ::before element)
    const handleOverlayClick = (event: MouseEvent) => {
      const html = document.documentElement;

      // Check if we clicked on the overlay (not on the sidebar or buttons)
      if (
        html.classList.contains('sidebar-open') &&
        event.target === html
      ) {
        html.classList.remove('sidebar-open');
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('click', handleOverlayClick, true);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('click', handleOverlayClick, true);
    };
  }, []);

  return (
    <NavigationProvider>
      <div className="flex min-h-screen flex-col overflow-hidden">
        <Header />
        <div className="flex flex-1 pt-16">
          <EnhancedSidebar />
          <main
            className="flex-1 transition-all duration-300 w-full overflow-y-auto h-[calc(100vh-64px)]"
          >
            <div className="container mx-auto px-4 py-8 pb-16 md:px-6 lg:px-8 min-h-[calc(100vh-64px)]">
              {children}
            </div>
          </main>
          <QuickActions />
        </div>
      </div>
    </NavigationProvider>
  );
}
