'use client';

// Custom event types
export const EVENTS = {
  FAVORITES_UPDATED: 'favorites-updated',
  RECENTLY_VIEWED_UPDATED: 'recently-viewed-updated',
};

// Function to dispatch a custom event
export function dispatchCustomEvent(eventName: string, detail?: any) {
  if (typeof window === 'undefined') return;
  
  const event = new CustomEvent(eventName, { detail });
  window.dispatchEvent(event);
}

// Function to add an event listener
export function addCustomEventListener(eventName: string, callback: (event: CustomEvent) => void) {
  if (typeof window === 'undefined') return () => {};
  
  const eventListener = (e: Event) => callback(e as CustomEvent);
  window.addEventListener(eventName, eventListener);
  
  // Return a function to remove the event listener
  return () => {
    window.removeEventListener(eventName, eventListener);
  };
}
