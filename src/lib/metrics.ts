'use client';

import { getAllAgents } from './agents';
import { 
  UsageMetric, 
  UsageOverTime, 
  CategoryDistribution, 
  UserFeedback, 
  UsageByTime, 
  UsageByDay, 
  TopQuery, 
  AgentComparison,
  AgentMetrics,
  TimeRange
} from '@/types/metrics';

// Generate random number between min and max
const randomNumber = (min: number, max: number): number => {
  return Math.floor(Math.random() * (max - min + 1)) + min;
};

// Generate random decimal between min and max with specified precision
const randomDecimal = (min: number, max: number, precision: number = 2): number => {
  const value = Math.random() * (max - min) + min;
  return Number(value.toFixed(precision));
};

// Generate usage metrics for an agent
export const generateUsageMetric = (agentId: string, popularity: number): UsageMetric => {
  // Scale usage based on popularity (0-100)
  const baseUses = popularity * 100;
  const variability = baseUses * 0.2; // 20% variability
  
  return {
    agentId,
    totalUses: randomNumber(baseUses - variability, baseUses + variability),
    averageRating: randomDecimal(3.5, 4.9, 1),
    completionRate: randomDecimal(0.75, 0.98, 2),
    averageSessionDuration: randomNumber(60, 600), // 1-10 minutes
    popularityScore: popularity
  };
};

// Generate usage over time data
export const generateUsageOverTime = (baseUses: number, days: number): UsageOverTime[] => {
  const data: UsageOverTime[] = [];
  const today = new Date();
  
  for (let i = days - 1; i >= 0; i--) {
    const date = new Date(today);
    date.setDate(date.getDate() - i);
    
    // Create some patterns in the data
    let modifier = 1;
    
    // Weekend dip
    const dayOfWeek = date.getDay();
    if (dayOfWeek === 0 || dayOfWeek === 6) {
      modifier = 0.7; // Less usage on weekends
    }
    
    // Gradual growth trend
    const trendFactor = 1 + (i / days) * 0.5;
    
    // Random daily fluctuation
    const fluctuation = randomDecimal(0.8, 1.2);
    
    const dailyUses = Math.round((baseUses / days) * modifier * fluctuation * trendFactor);
    
    data.push({
      date: date.toISOString().split('T')[0], // YYYY-MM-DD
      count: dailyUses
    });
  }
  
  return data;
};

// Generate user feedback distribution
export const generateUserFeedback = (): UserFeedback[] => {
  // Most agents should have good ratings
  const baseDistribution = [
    { rating: 1, weight: 5 },
    { rating: 2, weight: 10 },
    { rating: 3, weight: 20 },
    { rating: 4, weight: 35 },
    { rating: 5, weight: 30 }
  ];
  
  const totalWeight = baseDistribution.reduce((sum, item) => sum + item.weight, 0);
  const totalRatings = randomNumber(50, 500);
  
  return baseDistribution.map(item => {
    const percentage = item.weight / totalWeight;
    return {
      rating: item.rating,
      count: Math.round(totalRatings * percentage)
    };
  });
};

// Generate usage by time of day
export const generateUsageByTime = (): UsageByTime[] => {
  const data: UsageByTime[] = [];
  
  for (let hour = 0; hour < 24; hour++) {
    // Create a realistic distribution with peak during work hours
    let baseFactor = 1;
    
    // Early morning (low usage)
    if (hour >= 0 && hour < 6) {
      baseFactor = 0.2;
    }
    // Morning ramp-up
    else if (hour >= 6 && hour < 9) {
      baseFactor = 0.5 + (hour - 6) * 0.25;
    }
    // Work hours (peak usage)
    else if (hour >= 9 && hour < 17) {
      baseFactor = 1.0;
    }
    // Evening wind-down
    else if (hour >= 17 && hour < 22) {
      baseFactor = 1.0 - (hour - 17) * 0.15;
    }
    // Late night (low usage)
    else {
      baseFactor = 0.3;
    }
    
    // Add some randomness
    const factor = baseFactor * randomDecimal(0.8, 1.2);
    
    data.push({
      hour,
      count: Math.round(100 * factor)
    });
  }
  
  return data;
};

// Generate usage by day of week
export const generateUsageByDay = (): UsageByDay[] => {
  const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
  
  return days.map(day => {
    // Weekdays have higher usage than weekends
    const isWeekend = day === 'Saturday' || day === 'Sunday';
    const baseFactor = isWeekend ? 0.6 : 1.0;
    
    // Wednesday and Thursday slightly higher
    const dayFactor = (day === 'Wednesday' || day === 'Thursday') ? 1.1 : 1.0;
    
    // Add some randomness
    const factor = baseFactor * dayFactor * randomDecimal(0.9, 1.1);
    
    return {
      day,
      count: Math.round(100 * factor)
    };
  });
};

// Generate top queries
export const generateTopQueries = (agentId: string): TopQuery[] => {
  const dataQueries = [
    'How to analyze sales data',
    'Generate monthly report',
    'Forecast Q3 revenue',
    'Compare year-over-year growth',
    'Find outliers in dataset',
    'Visualize customer demographics',
    'Calculate profit margins'
  ];
  
  const codeQueries = [
    'Debug React useEffect',
    'Optimize SQL query',
    'Convert JSON to CSV',
    'Fix memory leak',
    'Write unit test for API',
    'Create Docker container',
    'Implement authentication'
  ];
  
  const researchQueries = [
    'Summarize latest AI papers',
    'Find studies on climate change',
    'Compare research methodologies',
    'Generate literature review',
    'Extract key findings',
    'Organize research notes',
    'Find citation for paper'
  ];
  
  const meetingQueries = [
    'Summarize team meeting',
    'Extract action items',
    'Generate meeting minutes',
    'Identify key decisions',
    'Create follow-up tasks',
    'Analyze meeting sentiment',
    'Schedule follow-up meeting'
  ];
  
  const documentQueries = [
    'Extract data from PDF',
    'Summarize legal document',
    'Find key contract terms',
    'Compare document versions',
    'Generate document outline',
    'Extract contact information',
    'Identify document type'
  ];
  
  const presentationQueries = [
    'Create sales pitch deck',
    'Design executive summary',
    'Generate presentation outline',
    'Create data visualizations',
    'Improve slide design',
    'Add speaker notes',
    'Create product demo slides'
  ];
  
  // Select appropriate queries based on agent ID
  let queries: string[] = [];
  
  switch (agentId) {
    case '1': // Data Analyst
      queries = dataQueries;
      break;
    case '2': // Code Assistant
      queries = codeQueries;
      break;
    case '3': // Research Companion
      queries = researchQueries;
      break;
    case '4': // Meeting Summarizer
      queries = meetingQueries;
      break;
    case '5': // Document Analyzer
      queries = documentQueries;
      break;
    case '6': // Presentation Creator
      queries = presentationQueries;
      break;
    default:
      // Mix of all queries for other agents
      queries = [
        ...dataQueries.slice(0, 2),
        ...codeQueries.slice(0, 2),
        ...researchQueries.slice(0, 2),
        ...meetingQueries.slice(0, 1)
      ];
  }
  
  // Randomize counts
  return queries.map(query => ({
    query,
    count: randomNumber(10, 100)
  }))
  .sort((a, b) => b.count - a.count) // Sort by count descending
  .slice(0, 5); // Take top 5
};

// Generate metrics for a specific agent
export const generateAgentMetrics = (agentId: string): AgentMetrics => {
  const agent = getAllAgents().find(a => a.id === agentId);
  
  if (!agent) {
    throw new Error(`Agent with ID ${agentId} not found`);
  }
  
  // Base popularity on usage count
  const popularity = Math.min(100, agent.usageCount / 50);
  
  const usageMetrics = generateUsageMetric(agentId, popularity);
  
  return {
    id: agent.id,
    name: agent.name,
    usageMetrics,
    usageOverTime: generateUsageOverTime(usageMetrics.totalUses, 30), // Last 30 days
    userFeedback: generateUserFeedback(),
    usageByTime: generateUsageByTime(),
    usageByDay: generateUsageByDay(),
    topQueries: generateTopQueries(agentId)
  };
};

// Get metrics for all agents
export const getAllAgentMetrics = (): AgentMetrics[] => {
  return getAllAgents().map(agent => generateAgentMetrics(agent.id));
};

// Get top agents by a specific metric
export const getTopAgentsByMetric = (metric: keyof UsageMetric, limit: number = 5): AgentComparison[] => {
  const allMetrics = getAllAgentMetrics();
  
  return allMetrics
    .map(agentMetric => ({
      agentId: agentMetric.id,
      metric,
      value: agentMetric.usageMetrics[metric] as number
    }))
    .sort((a, b) => b.value - a.value)
    .slice(0, limit);
};

// Get category distribution
export const getCategoryDistribution = (): CategoryDistribution[] => {
  const agents = getAllAgents();
  const categories: Record<string, number> = {};
  
  // Count agents by category
  agents.forEach(agent => {
    if (categories[agent.category]) {
      categories[agent.category]++;
    } else {
      categories[agent.category] = 1;
    }
  });
  
  // Calculate total
  const total = Object.values(categories).reduce((sum, count) => sum + count, 0);
  
  // Convert to array with percentages
  return Object.entries(categories).map(([category, count]) => ({
    category,
    count,
    percentage: (count / total) * 100
  }))
  .sort((a, b) => b.count - a.count); // Sort by count descending
};

// Get overall platform usage over time
export const getPlatformUsageOverTime = (days: number): UsageOverTime[] => {
  const allMetrics = getAllAgentMetrics();
  const totalUses = allMetrics.reduce((sum, agent) => sum + agent.usageMetrics.totalUses, 0);
  
  return generateUsageOverTime(totalUses, days);
};

// Get popular agents
export const getPopularAgents = (limit: number = 5): AgentMetrics[] => {
  const allMetrics = getAllAgentMetrics();
  
  return allMetrics
    .sort((a, b) => b.usageMetrics.popularityScore - a.usageMetrics.popularityScore)
    .slice(0, limit);
};
