'use client';

import { UserPreferences } from '@/types/auth';
import { dispatchCustomEvent, EVENTS } from './events';

// Default user preferences
const defaultPreferences: UserPreferences = {
  favoriteAgentIds: [],
  recentlyViewedAgentIds: [],
  darkMode: false,
  defaultView: 'grid',
  pageSize: 12,
};

// Get user preferences from localStorage
export function getUserPreferences(userId?: string): UserPreferences {
  if (typeof window === 'undefined') {
    return defaultPreferences;
  }

  // If userId is provided, use it to get user-specific preferences
  const storageKey = userId ? `userPreferences_${userId}` : 'userPreferences';
  const storedPreferences = localStorage.getItem(storageKey);
  if (!storedPreferences) {
    return defaultPreferences;
  }

  try {
    return JSON.parse(storedPreferences) as UserPreferences;
  } catch (error) {
    console.error('Error parsing user preferences:', error);
    return defaultPreferences;
  }
}

// Save user preferences to localStorage
export function saveUserPreferences(preferences: UserPreferences, userId?: string): void {
  if (typeof window === 'undefined') {
    return;
  }

  // If userId is provided, use it to save user-specific preferences
  const storageKey = userId ? `userPreferences_${userId}` : 'userPreferences';
  localStorage.setItem(storageKey, JSON.stringify(preferences));
}

// Add an agent to favorites
export function toggleFavoriteAgent(agentId: string, userId?: string): boolean {
  const preferences = getUserPreferences(userId);
  const isFavorite = preferences.favoriteAgentIds.includes(agentId);

  if (isFavorite) {
    preferences.favoriteAgentIds = preferences.favoriteAgentIds.filter(id => id !== agentId);
  } else {
    preferences.favoriteAgentIds.push(agentId);
  }

  saveUserPreferences(preferences, userId);

  // Dispatch event to notify other components
  dispatchCustomEvent(EVENTS.FAVORITES_UPDATED, {
    agentId,
    isFavorite: !isFavorite,
    userId
  });

  return !isFavorite; // Return the new favorite status
}

// Check if an agent is favorited
export function isAgentFavorited(agentId: string, userId?: string): boolean {
  const preferences = getUserPreferences(userId);
  return preferences.favoriteAgentIds.includes(agentId);
}

// Add an agent to recently viewed
export function addToRecentlyViewed(agentId: string, userId?: string): void {
  const preferences = getUserPreferences(userId);

  // Remove if already exists to avoid duplicates
  preferences.recentlyViewedAgentIds = preferences.recentlyViewedAgentIds.filter(id => id !== agentId);

  // Add to the beginning of the array
  preferences.recentlyViewedAgentIds.unshift(agentId);

  // Keep only the 10 most recent
  if (preferences.recentlyViewedAgentIds.length > 10) {
    preferences.recentlyViewedAgentIds = preferences.recentlyViewedAgentIds.slice(0, 10);
  }

  saveUserPreferences(preferences, userId);

  // Dispatch event to notify other components
  dispatchCustomEvent(EVENTS.RECENTLY_VIEWED_UPDATED, {
    agentId,
    recentlyViewedIds: preferences.recentlyViewedAgentIds,
    userId
  });
}

// Get recently viewed agents
export function getRecentlyViewedAgentIds(userId?: string): string[] {
  const preferences = getUserPreferences(userId);
  return preferences.recentlyViewedAgentIds;
}

// Update page size preference
export function updatePageSize(pageSize: number, userId?: string): void {
  const preferences = getUserPreferences(userId);
  preferences.pageSize = pageSize;
  saveUserPreferences(preferences, userId);
}

// Get page size preference
export function getPageSize(userId?: string): number {
  const preferences = getUserPreferences(userId);
  return preferences.pageSize;
}

// Update default view preference
export function updateDefaultView(view: 'grid' | 'list', userId?: string): void {
  const preferences = getUserPreferences(userId);
  preferences.defaultView = view;
  saveUserPreferences(preferences, userId);
}

// Get default view preference
export function getDefaultView(userId?: string): 'grid' | 'list' {
  const preferences = getUserPreferences(userId);
  return preferences.defaultView;
}

// Toggle dark mode
export function toggleDarkMode(userId?: string): boolean {
  const preferences = getUserPreferences(userId);
  preferences.darkMode = !preferences.darkMode;
  saveUserPreferences(preferences, userId);
  return preferences.darkMode;
}

// Get dark mode preference
export function getDarkMode(userId?: string): boolean {
  const preferences = getUserPreferences(userId);
  return preferences.darkMode;
}
