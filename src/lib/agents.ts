import { Agent, Message, Session } from '@/types/agent';

// Mock data for agents
const agents: Agent[] = [
  {
    id: '1',
    name: 'Data Analyst',
    description: 'Analyze data sets and generate insights with natural language queries.',
    longDescription: 'The Data Analyst agent helps you explore and understand your data through natural language. Ask questions about your data, request visualizations, or get statistical summaries without writing complex queries or code. This agent can work with various data formats including CSV, Excel, SQL databases, and more.',
    category: 'Analytics',
    capabilities: [
      'Natural language data queries',
      'Data visualization generation',
      'Statistical analysis',
      'Anomaly detection',
      'Trend identification',
      'Report generation'
    ],
    usageCount: 1245,
    isNew: false,
    isFeatured: true,
    createdAt: '2023-10-15',
    updatedAt: '2024-03-01',
    version: '2.3.0',
    creator: 'Data Science Team',
    avatarUrl: '/agents/data-analyst.svg',
    tags: ['data', 'analytics', 'visualization', 'statistics'],
    relatedAgentIds: ['5', '7', '8']
  },
  {
    id: '2',
    name: 'Code Assistant',
    description: 'Help with coding tasks, debugging, and code reviews across multiple languages.',
    longDescription: 'The Code Assistant agent is your programming partner. It can help write, debug, and optimize code in multiple languages including Python, JavaScript, Java, C++, and more. Ask for code examples, get help with debugging, or request code reviews. The agent can also explain complex code and suggest improvements.',
    category: 'Development',
    capabilities: [
      'Code generation',
      'Debugging assistance',
      'Code optimization',
      'Code explanation',
      'Multiple language support',
      'Best practices recommendations'
    ],
    usageCount: 3421,
    isNew: false,
    isFeatured: true,
    createdAt: '2023-08-10',
    updatedAt: '2024-02-15',
    version: '3.1.0',
    creator: 'Engineering Team',
    avatarUrl: '/agents/code-assistant.svg',
    tags: ['coding', 'programming', 'development', 'debugging'],
    relatedAgentIds: ['9', '10']
  },
  {
    id: '3',
    name: 'Research Companion',
    description: 'Find, summarize, and organize research papers and articles.',
    longDescription: 'The Research Companion agent helps you stay on top of the latest research in your field. It can find relevant papers, summarize key findings, and help you organize your research materials. Ask for papers on specific topics, get summaries of complex articles, or request literature reviews.',
    category: 'Research',
    capabilities: [
      'Research paper search',
      'Article summarization',
      'Literature review assistance',
      'Citation generation',
      'Research organization',
      'Key findings extraction'
    ],
    usageCount: 876,
    isNew: true,
    isFeatured: true,
    createdAt: '2024-01-20',
    updatedAt: '2024-03-10',
    version: '1.2.0',
    creator: 'Research & Development',
    avatarUrl: '/agents/research-companion.svg',
    tags: ['research', 'papers', 'academic', 'literature'],
    relatedAgentIds: ['11', '5']
  },
  {
    id: '4',
    name: 'Meeting Summarizer',
    description: 'Generate concise summaries and action items from meeting transcripts.',
    longDescription: 'The Meeting Summarizer agent helps you capture the important points from your meetings. Upload a transcript or recording, and the agent will identify key discussion points, decisions made, and action items assigned. It can also generate meeting minutes in various formats.',
    category: 'Productivity',
    capabilities: [
      'Meeting transcript analysis',
      'Key point extraction',
      'Action item identification',
      'Decision tracking',
      'Meeting minutes generation',
      'Follow-up reminder creation'
    ],
    usageCount: 2134,
    isNew: false,
    isFeatured: true,
    createdAt: '2023-11-05',
    updatedAt: '2024-02-28',
    version: '2.0.1',
    creator: 'Productivity Team',
    avatarUrl: '/agents/meeting-summarizer.svg',
    tags: ['meetings', 'productivity', 'transcription', 'summaries'],
    relatedAgentIds: ['5', '6', '12']
  },
  {
    id: '5',
    name: 'Document Analyzer',
    description: 'Extract key information from documents and generate summaries.',
    longDescription: 'The Document Analyzer agent helps you process and understand documents quickly. Upload any document (PDF, Word, etc.), and the agent will extract key information, generate summaries, and answer questions about the content. It can handle contracts, reports, articles, and more.',
    category: 'Productivity',
    capabilities: [
      'Document parsing',
      'Key information extraction',
      'Summary generation',
      'Question answering',
      'Multiple format support',
      'Entity recognition'
    ],
    usageCount: 567,
    isNew: false,
    isFeatured: false,
    createdAt: '2023-09-12',
    updatedAt: '2024-01-30',
    version: '1.5.2',
    creator: 'Content Team',
    avatarUrl: '/agents/document-analyzer.svg',
    tags: ['documents', 'analysis', 'extraction', 'summaries'],
    relatedAgentIds: ['1', '3', '4']
  },
  {
    id: '6',
    name: 'Presentation Creator',
    description: 'Generate professional presentations from outlines or topics.',
    longDescription: 'The Presentation Creator agent helps you build compelling presentations quickly. Provide a topic or outline, and the agent will generate slide content, suggest visuals, and help you structure your presentation for maximum impact. It can create presentations for various purposes including business, education, and sales.',
    category: 'Productivity',
    capabilities: [
      'Slide content generation',
      'Presentation structure suggestions',
      'Visual element recommendations',
      'Talking points creation',
      'Multiple template support',
      'Export to PowerPoint/Google Slides'
    ],
    usageCount: 321,
    isNew: true,
    isFeatured: false,
    createdAt: '2024-02-01',
    updatedAt: '2024-03-15',
    version: '1.0.0',
    creator: 'Marketing Team',
    avatarUrl: '/agents/presentation-creator.svg',
    tags: ['presentations', 'slides', 'design', 'content'],
    relatedAgentIds: ['4', '13']
  },
  {
    id: '7',
    name: 'Data Visualization Expert',
    description: 'Create beautiful and informative data visualizations from your datasets.',
    longDescription: 'The Data Visualization Expert agent helps you transform raw data into compelling visual stories. Upload your data and describe what you want to highlight, and the agent will generate appropriate charts, graphs, and interactive visualizations that make your data insights clear and impactful.',
    category: 'Analytics',
    capabilities: [
      'Chart and graph generation',
      'Interactive visualization creation',
      'Color palette optimization',
      'Data storytelling assistance',
      'Multiple export formats',
      'Accessibility considerations'
    ],
    usageCount: 892,
    isNew: false,
    isFeatured: false,
    createdAt: '2023-11-18',
    updatedAt: '2024-02-10',
    version: '1.8.0',
    creator: 'Data Science Team',
    avatarUrl: '/agents/data-analyst.svg',
    tags: ['visualization', 'charts', 'graphs', 'data'],
    relatedAgentIds: ['1', '8']
  },
  {
    id: '8',
    name: 'Predictive Analytics Agent',
    description: 'Forecast trends and make predictions based on historical data.',
    longDescription: 'The Predictive Analytics Agent uses machine learning algorithms to analyze your historical data and generate forecasts and predictions. It can identify patterns, trends, and anomalies to help you make data-driven decisions about future outcomes.',
    category: 'Analytics',
    capabilities: [
      'Time series forecasting',
      'Trend analysis',
      'Anomaly detection',
      'Predictive modeling',
      'Scenario planning',
      'Confidence interval calculation'
    ],
    usageCount: 754,
    isNew: true,
    isFeatured: false,
    createdAt: '2024-01-05',
    updatedAt: '2024-03-20',
    version: '1.2.1',
    creator: 'Data Science Team',
    avatarUrl: '/agents/data-analyst.svg',
    tags: ['predictions', 'forecasting', 'analytics', 'trends'],
    relatedAgentIds: ['1', '7']
  },
  {
    id: '9',
    name: 'API Documentation Generator',
    description: 'Automatically generate comprehensive API documentation from your code.',
    longDescription: 'The API Documentation Generator analyzes your API code and generates clear, comprehensive documentation. It can create reference docs, guides, examples, and interactive API explorers to help developers understand and use your APIs effectively.',
    category: 'Development',
    capabilities: [
      'API reference generation',
      'Code example creation',
      'Interactive API explorer',
      'Multiple format support',
      'Versioning assistance',
      'Consistency checking'
    ],
    usageCount: 623,
    isNew: false,
    isFeatured: false,
    createdAt: '2023-10-08',
    updatedAt: '2024-02-12',
    version: '2.1.0',
    creator: 'Engineering Team',
    avatarUrl: '/agents/code-assistant.svg',
    tags: ['documentation', 'API', 'development', 'reference'],
    relatedAgentIds: ['2', '10']
  },
  {
    id: '10',
    name: 'Code Reviewer',
    description: 'Get detailed code reviews with suggestions for improvements and best practices.',
    longDescription: 'The Code Reviewer agent analyzes your code for bugs, security issues, performance problems, and adherence to best practices. It provides detailed feedback with specific suggestions for improvements and explanations of potential issues.',
    category: 'Development',
    capabilities: [
      'Bug detection',
      'Security vulnerability scanning',
      'Performance optimization',
      'Best practice enforcement',
      'Code style consistency',
      'Refactoring suggestions'
    ],
    usageCount: 1876,
    isNew: false,
    isFeatured: false,
    createdAt: '2023-09-20',
    updatedAt: '2024-03-05',
    version: '2.4.0',
    creator: 'Engineering Team',
    avatarUrl: '/agents/code-assistant.svg',
    tags: ['code review', 'quality', 'security', 'best practices'],
    relatedAgentIds: ['2', '9']
  },
  {
    id: '11',
    name: 'Literature Review Assistant',
    description: 'Compile comprehensive literature reviews on any research topic.',
    longDescription: 'The Literature Review Assistant helps researchers compile thorough literature reviews by finding relevant papers, organizing them by themes, identifying key findings, and highlighting research gaps. It can save weeks of manual research work.',
    category: 'Research',
    capabilities: [
      'Research paper discovery',
      'Thematic organization',
      'Gap analysis',
      'Citation management',
      'Summary generation',
      'Trend identification'
    ],
    usageCount: 542,
    isNew: true,
    isFeatured: false,
    createdAt: '2024-02-15',
    updatedAt: '2024-03-18',
    version: '1.0.2',
    creator: 'Research & Development',
    avatarUrl: '/agents/research-companion.svg',
    tags: ['literature review', 'research', 'academic', 'papers'],
    relatedAgentIds: ['3']
  },
  {
    id: '12',
    name: 'Project Manager Assistant',
    description: 'Track projects, manage tasks, and coordinate team activities efficiently.',
    longDescription: 'The Project Manager Assistant helps you plan, track, and manage projects from start to finish. It can create project timelines, assign and track tasks, identify risks, generate status reports, and facilitate team communication to keep your projects on track.',
    category: 'Productivity',
    capabilities: [
      'Project planning',
      'Task management',
      'Timeline creation',
      'Risk assessment',
      'Status reporting',
      'Resource allocation'
    ],
    usageCount: 1432,
    isNew: false,
    isFeatured: false,
    createdAt: '2023-08-25',
    updatedAt: '2024-02-20',
    version: '2.2.0',
    creator: 'Productivity Team',
    avatarUrl: '/agents/meeting-summarizer.svg',
    tags: ['project management', 'tasks', 'planning', 'coordination'],
    relatedAgentIds: ['4', '13']
  },
  {
    id: '13',
    name: 'Content Creator',
    description: 'Generate high-quality content for blogs, social media, and marketing materials.',
    longDescription: 'The Content Creator agent helps you produce engaging content for various platforms. Provide a topic and target audience, and it will generate blog posts, social media updates, marketing copy, and more, tailored to your brand voice and content strategy.',
    category: 'Marketing',
    capabilities: [
      'Blog post generation',
      'Social media content creation',
      'Marketing copy writing',
      'SEO optimization',
      'Brand voice consistency',
      'Content strategy alignment'
    ],
    usageCount: 2187,
    isNew: false,
    isFeatured: false,
    createdAt: '2023-07-12',
    updatedAt: '2024-03-08',
    version: '3.0.1',
    creator: 'Marketing Team',
    avatarUrl: '/agents/presentation-creator.svg',
    tags: ['content', 'writing', 'marketing', 'social media'],
    relatedAgentIds: ['6', '12']
  }
];

// Mock data for sessions
const sessions: Session[] = [
  {
    id: 'session1',
    agentId: '2',
    title: 'JavaScript Debugging Help',
    messages: [
      {
        id: 'msg1',
        content: 'Hello! How can I help you with coding today?',
        role: 'assistant',
        timestamp: Date.now() - 3600000
      },
      {
        id: 'msg2',
        content: 'I have a bug in my JavaScript code. The event listener is not working.',
        role: 'user',
        timestamp: Date.now() - 3500000
      },
      {
        id: 'msg3',
        content: 'Let\'s take a look. Can you share the code that\'s not working?',
        role: 'assistant',
        timestamp: Date.now() - 3400000
      }
    ],
    createdAt: Date.now() - 3600000,
    updatedAt: Date.now() - 3400000,
    isSaved: true
  },
  {
    id: 'session2',
    agentId: '1',
    title: 'Sales Data Analysis',
    messages: [
      {
        id: 'msg1',
        content: 'Welcome to Data Analyst. What data would you like to analyze today?',
        role: 'assistant',
        timestamp: Date.now() - 86400000
      },
      {
        id: 'msg2',
        content: 'I need to analyze our Q1 sales data to find trends.',
        role: 'user',
        timestamp: Date.now() - 86300000
      },
      {
        id: 'msg3',
        content: 'I can help with that. Do you have the sales data file you can upload?',
        role: 'assistant',
        timestamp: Date.now() - 86200000
      }
    ],
    createdAt: Date.now() - 86400000,
    updatedAt: Date.now() - 86200000,
    isSaved: true
  }
];

// Function to get all agents
export function getAllAgents(): Agent[] {
  return agents;
}

// Function to get an agent by ID
export function getAgentById(id: string): Agent | undefined {
  return agents.find(agent => agent.id === id);
}

// Function to get featured agents
export function getFeaturedAgents(): Agent[] {
  return agents.filter(agent => agent.isFeatured);
}

// Function to get new agents
export function getNewAgents(): Agent[] {
  return agents.filter(agent => agent.isNew);
}

// Function to get agents by category
export function getAgentsByCategory(category: string): Agent[] {
  return agents.filter(agent => agent.category === category);
}

// Function to get agents by tag
export function getAgentsByTag(tag: string): Agent[] {
  return agents.filter(agent => agent.tags?.includes(tag));
}

// Function to get all unique categories
export function getAllCategories(): string[] {
  const categories = new Set<string>();
  agents.forEach(agent => categories.add(agent.category));
  return Array.from(categories).sort();
}

// Function to get all unique tags
export function getAllTags(): string[] {
  const tags = new Set<string>();
  agents.forEach(agent => {
    agent.tags?.forEach(tag => tags.add(tag));
  });
  return Array.from(tags).sort();
}

// Function to get related agents
export function getRelatedAgents(agentId: string): Agent[] {
  const agent = getAgentById(agentId);
  if (!agent || !agent.relatedAgentIds || agent.relatedAgentIds.length === 0) {
    return [];
  }

  return agent.relatedAgentIds
    .map(id => getAgentById(id))
    .filter((agent): agent is Agent => agent !== undefined);
}

// Function to get agents by IDs
export function getAgentsByIds(ids: string[]): Agent[] {
  return ids
    .map(id => getAgentById(id))
    .filter((agent): agent is Agent => agent !== undefined);
}

// Function to search agents
export function searchAgents(query: string): Agent[] {
  if (!query) return agents;

  const lowercaseQuery = query.toLowerCase();
  return agents.filter(agent =>
    agent.name.toLowerCase().includes(lowercaseQuery) ||
    agent.description.toLowerCase().includes(lowercaseQuery) ||
    agent.category.toLowerCase().includes(lowercaseQuery) ||
    agent.tags?.some(tag => tag.toLowerCase().includes(lowercaseQuery)) ||
    agent.capabilities.some(capability => capability.toLowerCase().includes(lowercaseQuery))
  );
}

// Function to get sessions for an agent
export function getSessionsForAgent(agentId: string): Session[] {
  return sessions.filter(session => session.agentId === agentId);
}

// Function to get a session by ID
export function getSessionById(sessionId: string): Session | undefined {
  return sessions.find(session => session.id === sessionId);
}

// Function to create a new message
export function createMessage(content: string, role: 'user' | 'assistant' | 'system'): Message {
  return {
    id: `msg_${Date.now()}`,
    content,
    role,
    timestamp: Date.now()
  };
}
