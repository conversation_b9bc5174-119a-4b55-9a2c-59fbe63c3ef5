'use client';

import { useAuth } from '@/contexts/AuthContext';
import { UserPreferences } from '@/types/auth';
import * as userPreferencesLib from '@/lib/userPreferences';

export function useUserPreferences() {
  const { user } = useAuth();
  const userId = user?.id;

  const getUserPreferences = (): UserPreferences => {
    return userPreferencesLib.getUserPreferences(userId);
  };

  const saveUserPreferences = (preferences: UserPreferences): void => {
    userPreferencesLib.saveUserPreferences(preferences, userId);
  };

  const toggleFavoriteAgent = (agentId: string): boolean => {
    return userPreferencesLib.toggleFavoriteAgent(agentId, userId);
  };

  const isAgentFavorited = (agentId: string): boolean => {
    return userPreferencesLib.isAgentFavorited(agentId, userId);
  };

  const addToRecentlyViewed = (agentId: string): void => {
    userPreferencesLib.addToRecentlyViewed(agentId, userId);
  };

  const getRecentlyViewedAgentIds = (): string[] => {
    return userPreferencesLib.getRecentlyViewedAgentIds(userId);
  };

  const updatePageSize = (pageSize: number): void => {
    userPreferencesLib.updatePageSize(pageSize, userId);
  };

  const getPageSize = (): number => {
    return userPreferencesLib.getPageSize(userId);
  };

  const updateDefaultView = (view: 'grid' | 'list'): void => {
    userPreferencesLib.updateDefaultView(view, userId);
  };

  const getDefaultView = (): 'grid' | 'list' => {
    return userPreferencesLib.getDefaultView(userId);
  };

  const toggleDarkMode = (): boolean => {
    return userPreferencesLib.toggleDarkMode(userId);
  };

  const getDarkMode = (): boolean => {
    return userPreferencesLib.getDarkMode(userId);
  };

  return {
    getUserPreferences,
    saveUserPreferences,
    toggleFavoriteAgent,
    isAgentFavorited,
    addToRecentlyViewed,
    getRecentlyViewedAgentIds,
    updatePageSize,
    getPageSize,
    updateDefaultView,
    getDefaultView,
    toggleDarkMode,
    getDarkMode,
  };
}
