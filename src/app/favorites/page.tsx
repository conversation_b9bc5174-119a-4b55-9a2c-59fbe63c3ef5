'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { getAllAgents } from '@/lib/agents';
import { Agent } from '@/types/agent';
import { getUserPreferences, isAgentFavorited } from '@/lib/userPreferences';
import { addCustomEventListener, EVENTS } from '@/lib/events';
import AgentGrid from '../browse/components/AgentGrid';
import AgentList from '../browse/components/AgentList';
import Pagination from '../browse/components/Pagination';
import Link from 'next/link';

export default function FavoritesPage() {
  const allAgents = useMemo(() => getAllAgents(), []);
  const [favoriteAgents, setFavoriteAgents] = useState<Agent[]>([]);
  const [displayedAgents, setDisplayedAgents] = useState<Agent[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // View state
  const [view, setView] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState('recent');

  // Pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(12);

  // Load favorite agents
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const loadFavorites = () => {
      const preferences = getUserPreferences();
      const favoriteIds = preferences.favoriteAgentIds;

      // Filter agents to only include favorites
      const favorites = allAgents.filter(agent =>
        favoriteIds.includes(agent.id)
      );

      setFavoriteAgents(favorites);
      setIsLoading(false);
    };

    loadFavorites();

    // Listen for favorites updates
    const removeFavoritesListener = addCustomEventListener(EVENTS.FAVORITES_UPDATED, () => {
      loadFavorites();
    });

    // Set up event listener for storage changes (in case favorites are updated in another tab)
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'userPreferences') {
        loadFavorites();
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => {
      window.removeEventListener('storage', handleStorageChange);
      removeFavoritesListener();
    };
  }, [allAgents]);

  // Apply sorting
  useEffect(() => {
    let sorted = [...favoriteAgents];

    switch (sortBy) {
      case 'recent':
        // For "recent", we'll use the order in the favorites array
        // which is already sorted by most recently added
        break;
      case 'name':
        sorted.sort((a, b) => a.name.localeCompare(b.name));
        break;
      case 'popular':
        sorted.sort((a, b) => b.usageCount - a.usageCount);
        break;
      case 'category':
        sorted.sort((a, b) => a.category.localeCompare(b.category));
        break;
      default:
        break;
    }

    setFavoriteAgents(sorted);
  }, [sortBy]);

  // Apply pagination
  useEffect(() => {
    // Ensure current page is valid
    const maxPage = Math.max(1, Math.ceil(favoriteAgents.length / pageSize));
    const validCurrentPage = Math.min(currentPage, maxPage);

    if (validCurrentPage !== currentPage) {
      setCurrentPage(validCurrentPage);
      return;
    }

    const startIndex = (validCurrentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    setDisplayedAgents(favoriteAgents.slice(startIndex, endIndex));
  }, [favoriteAgents, currentPage, pageSize]);

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // Handle page size change
  const handlePageSizeChange = (size: number) => {
    setPageSize(size);
    setCurrentPage(1); // Reset to first page when page size changes
  };

  // Handle view change
  const handleViewChange = (newView: 'grid' | 'list') => {
    setView(newView);
  };

  // Handle sort change
  const handleSortChange = (newSortBy: string) => {
    setSortBy(newSortBy);
  };

  // Render loading state
  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <h1 className="mb-6 text-2xl font-bold text-gray-900 dark:text-white md:text-3xl">Favorite Agents</h1>
        <div className="flex h-64 items-center justify-center">
          <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  // Render empty state
  if (favoriteAgents.length === 0) {
    return (
      <div className="container mx-auto px-4 py-8">
        <h1 className="mb-6 text-2xl font-bold text-gray-900 dark:text-white md:text-3xl">Favorite Agents</h1>
        <div className="flex h-64 flex-col items-center justify-center rounded-lg border border-gray-200 bg-white p-6 text-center dark:border-gray-800 dark:bg-gray-900">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="mb-4 h-12 w-12 text-gray-400"
          >
            <path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z" />
          </svg>
          <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">No favorite agents yet</h3>
          <p className="mb-6 text-gray-600 dark:text-gray-400">
            Start adding agents to your favorites to see them here.
          </p>
          <Link
            href="/browse"
            className="rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800"
          >
            Browse Agents
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8 flex flex-col justify-between gap-4 sm:flex-row sm:items-center">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white md:text-3xl">Favorite Agents</h1>

        <div className="flex flex-wrap items-center gap-2 sm:gap-4">
          {/* Sort options */}
          <div className="relative">
            <select
              value={sortBy}
              onChange={(e) => handleSortChange(e.target.value)}
              className="rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700"
            >
              <option value="recent">Recently Added</option>
              <option value="name">Name (A-Z)</option>
              <option value="popular">Most Popular</option>
              <option value="category">Category</option>
            </select>
          </div>

          {/* View toggle */}
          <div className="flex rounded-md border border-gray-300 dark:border-gray-600">
            <button
              onClick={() => handleViewChange('grid')}
              className={`flex items-center justify-center rounded-l-md px-3 py-2 ${
                view === 'grid'
                  ? 'bg-gray-100 text-gray-900 dark:bg-gray-700 dark:text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-50 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700'
              }`}
              aria-label="Grid view"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="h-4 w-4"
              >
                <rect width="7" height="7" x="3" y="3" rx="1" />
                <rect width="7" height="7" x="14" y="3" rx="1" />
                <rect width="7" height="7" x="14" y="14" rx="1" />
                <rect width="7" height="7" x="3" y="14" rx="1" />
              </svg>
            </button>
            <button
              onClick={() => handleViewChange('list')}
              className={`flex items-center justify-center rounded-r-md px-3 py-2 ${
                view === 'list'
                  ? 'bg-gray-100 text-gray-900 dark:bg-gray-700 dark:text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-50 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700'
              }`}
              aria-label="List view"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="h-4 w-4"
              >
                <line x1="8" x2="21" y1="6" y2="6" />
                <line x1="8" x2="21" y1="12" y2="12" />
                <line x1="8" x2="21" y1="18" y2="18" />
                <line x1="3" x2="3.01" y1="6" y2="6" />
                <line x1="3" x2="3.01" y1="12" y2="12" />
                <line x1="3" x2="3.01" y1="18" y2="18" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {view === 'grid' ? (
        <AgentGrid agents={displayedAgents} />
      ) : (
        <AgentList agents={displayedAgents} />
      )}

      {favoriteAgents.length > 0 && (
        <Pagination
          totalItems={favoriteAgents.length}
          currentPage={currentPage}
          onPageChange={handlePageChange}
          onPageSizeChange={handlePageSizeChange}
        />
      )}
    </div>
  );
}
