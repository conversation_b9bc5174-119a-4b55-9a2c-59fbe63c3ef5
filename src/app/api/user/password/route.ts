import { NextRequest, NextResponse } from 'next/server';
import bcrypt from 'bcryptjs';
import { User } from '@/types/auth';

// Mock user database - in a real app, this would be a database
// This should be in a shared file with the NextAuth route
const users: User[] = [
  {
    id: '1',
    name: 'Admin User',
    email: '<EMAIL>',
    // Password: admin123
    password: '$2a$10$GQH.xZIBGI0yCJfLcAEd0OWKQoEvm/tL.rKFn9ug/BTvXcU.5Uwxa',
    role: 'admin',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: '2',
    name: 'Test User',
    email: '<EMAIL>',
    // Password: password123
    password: '$2a$10$GQH.xZIBGI0yCJfLcAEd0OdQiCJtGQjbZ9m4wy8hMqOJPSHBbhKIS',
    role: 'user',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
];

export async function POST(request: NextRequest) {
  try {
    // Get the user from the cookie
    const userCookie = request.cookies.get('user');

    if (!userCookie || !userCookie.value) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse the user from the cookie
    const userData = JSON.parse(decodeURIComponent(userCookie.value));

    // Find the user in the database
    const userIndex = users.findIndex((u) => u.email === userData.email);

    if (userIndex === -1) {
      return NextResponse.json(
        { message: 'User not found' },
        { status: 404 }
      );
    }

    // Get the request body
    const { currentPassword, newPassword } = await request.json();

    // Validate input
    if (!currentPassword || !newPassword) {
      return NextResponse.json(
        { message: 'Current password and new password are required' },
        { status: 400 }
      );
    }

    // Check if the current password is correct
    const isPasswordValid = await bcrypt.compare(
      currentPassword,
      users[userIndex].password!
    );

    if (!isPasswordValid) {
      return NextResponse.json(
        { message: 'Current password is incorrect' },
        { status: 401 }
      );
    }

    // Validate new password
    if (newPassword.length < 8) {
      return NextResponse.json(
        { message: 'New password must be at least 8 characters long' },
        { status: 400 }
      );
    }

    // Hash the new password
    const hashedPassword = await bcrypt.hash(newPassword, 10);

    // Update the user's password
    users[userIndex] = {
      ...users[userIndex],
      password: hashedPassword,
      updatedAt: new Date().toISOString(),
    };

    return NextResponse.json({ message: 'Password updated successfully' });
  } catch (error) {
    console.error('Error changing password:', error);
    return NextResponse.json(
      { message: 'An error occurred while changing the password' },
      { status: 500 }
    );
  }
}
