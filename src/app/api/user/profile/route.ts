import { NextRequest, NextResponse } from 'next/server';
import bcrypt from 'bcryptjs';
import { User } from '@/types/auth';

// Mock user database - in a real app, this would be a database
// This should be in a shared file with the NextAuth route
const users: User[] = [
  {
    id: '1',
    name: 'Admin User',
    email: '<EMAIL>',
    // Password: admin123
    password: '$2a$10$GQH.xZIBGI0yCJfLcAEd0OWKQoEvm/tL.rKFn9ug/BTvXcU.5Uwxa',
    role: 'admin',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: '2',
    name: 'Test User',
    email: '<EMAIL>',
    // Password: password123
    password: '$2a$10$GQH.xZIBGI0yCJfLcAEd0OdQiCJtGQjbZ9m4wy8hMqOJPSHBbhKIS',
    role: 'user',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
];

export async function GET(request: NextRequest) {
  try {
    // Get the user from the cookie
    const userCookie = request.cookies.get('user');

    if (!userCookie || !userCookie.value) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse the user from the cookie
    const userData = JSON.parse(decodeURIComponent(userCookie.value));

    // Find the user in the database
    const user = users.find((u) => u.email === userData.email);

    if (!user) {
      return NextResponse.json(
        { message: 'User not found' },
        { status: 404 }
      );
    }

    // Return the user without the password
    const { password, ...userWithoutPassword } = user;
    return NextResponse.json(userWithoutPassword);
  } catch (error) {
    console.error('Error fetching user profile:', error);
    return NextResponse.json(
      { message: 'An error occurred while fetching the user profile' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    // Get the user from the cookie
    const userCookie = request.cookies.get('user');

    if (!userCookie || !userCookie.value) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse the user from the cookie
    const userData = JSON.parse(decodeURIComponent(userCookie.value));

    // Find the user in the database
    const userIndex = users.findIndex((u) => u.email === userData.email);

    if (userIndex === -1) {
      return NextResponse.json(
        { message: 'User not found' },
        { status: 404 }
      );
    }

    // Get the request body
    const { name, email } = await request.json();

    // Validate input
    if (!name || !email) {
      return NextResponse.json(
        { message: 'Name and email are required' },
        { status: 400 }
      );
    }

    // Check if email is already taken by another user
    if (email !== users[userIndex].email) {
      const emailExists = users.some((u, i) => i !== userIndex && u.email === email);
      if (emailExists) {
        return NextResponse.json(
          { message: 'Email is already taken' },
          { status: 409 }
        );
      }
    }

    // Update the user
    users[userIndex] = {
      ...users[userIndex],
      name,
      email,
      updatedAt: new Date().toISOString(),
    };

    // Return the updated user without the password
    const { password, ...userWithoutPassword } = users[userIndex];
    return NextResponse.json(userWithoutPassword);
  } catch (error) {
    console.error('Error updating user profile:', error);
    return NextResponse.json(
      { message: 'An error occurred while updating the user profile' },
      { status: 500 }
    );
  }
}
