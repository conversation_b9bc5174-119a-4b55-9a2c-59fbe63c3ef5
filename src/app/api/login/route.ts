import { NextRequest, NextResponse } from 'next/server';
import { User } from '@/types/auth';

// Mock user database - in a real app, this would be a database
const users: User[] = [
  {
    id: '1',
    name: 'Admin User',
    email: '<EMAIL>',
    // Password: admin123
    password: 'admin123',
    role: 'admin',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: '2',
    name: 'Test User',
    email: '<EMAIL>',
    // Password: password123
    password: 'password123',
    role: 'user',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
];

export async function POST(request: NextRequest) {
  console.log('Login API called');
  try {
    const { email, password } = await request.json();

    // Validate input
    if (!email || !password) {
      return NextResponse.json(
        { success: false, message: 'Email and password are required' },
        { status: 400 }
      );
    }

    // Find the user
    console.log('Looking for user with email:', email);
    const user = users.find((u) => u.email === email);

    console.log('User found:', user ? 'Yes' : 'No');

    if (!user) {
      console.log('No user found with email:', email);
      return NextResponse.json(
        { success: false, message: 'Invalid email or password' },
        { status: 401 }
      );
    }

    // Check password
    console.log('Checking password...');
    console.log('Input password:', password);
    console.log('Stored password:', user.password);
    console.log('Password match:', user.password === password);

    if (user.password !== password) {
      console.log('Password does not match');
      return NextResponse.json(
        { success: false, message: 'Invalid email or password' },
        { status: 401 }
      );
    }

    console.log('Login successful');

    // Return user without password
    const { password: _, ...userWithoutPassword } = user;

    return NextResponse.json({
      success: true,
      user: userWithoutPassword,
    });
  } catch (error) {
    console.error('Login error:', error);
    return NextResponse.json(
      { success: false, message: 'An error occurred during login' },
      { status: 500 }
    );
  }
}
