import { NextRequest, NextResponse } from 'next/server';
import bcrypt from 'bcryptjs';
import { User } from '@/types/auth';

// Mock user database - in a real app, this would be a database
// This should be in a shared file with the NextAuth route
const users: User[] = [
  {
    id: '1',
    name: 'Admin User',
    email: '<EMAIL>',
    // Password: admin123
    password: '$2a$10$GQH.xZIBGI0yCJfLcAEd0OWKQoEvm/tL.rKFn9ug/BTvXcU.5Uwxa',
    role: 'admin',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: '2',
    name: 'Test User',
    email: '<EMAIL>',
    // Password: password123
    password: '$2a$10$GQH.xZIBGI0yCJfLcAEd0OdQiCJtGQjbZ9m4wy8hMqOJPSHBbhKIS',
    role: 'user',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
];

export async function POST(request: NextRequest) {
  try {
    const { name, email, password } = await request.json();
    
    // Validate input
    if (!name || !email || !password) {
      return NextResponse.json(
        { message: 'Name, email, and password are required' },
        { status: 400 }
      );
    }
    
    // Check if user already exists
    const existingUser = users.find((user) => user.email === email);
    if (existingUser) {
      return NextResponse.json(
        { message: 'User with this email already exists' },
        { status: 409 }
      );
    }
    
    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);
    
    // Create new user
    const newUser: User = {
      id: (users.length + 1).toString(),
      name,
      email,
      password: hashedPassword,
      role: 'user', // Default role for new users
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    
    // In a real app, this would save to a database
    users.push(newUser);
    
    // Return success without exposing the password
    const { password: _, ...userWithoutPassword } = newUser;
    return NextResponse.json(
      { message: 'User registered successfully', user: userWithoutPassword },
      { status: 201 }
    );
  } catch (error) {
    console.error('Registration error:', error);
    return NextResponse.json(
      { message: 'An error occurred during registration' },
      { status: 500 }
    );
  }
}
