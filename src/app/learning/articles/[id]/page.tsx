'use client';

import React from 'react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faArrowLeft, faUser, faCalendarAlt, faTags } from '@fortawesome/free-solid-svg-icons';
import { getResourceById } from '@/data/learning-resources';
import ReactMarkdown from 'react-markdown';

export default function ArticlePage() {
  const params = useParams();
  const id = params.id as string;
  const article = getResourceById(id);

  if (!article || article.type !== 'article') {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <h2 className="mb-4 text-2xl font-bold">Article Not Found</h2>
        <p className="mb-6 text-gray-600 dark:text-gray-400">The article you're looking for doesn't exist or has been removed.</p>
        <Link
          href="/learning/articles"
          className="flex items-center rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700"
        >
          <FontAwesomeIcon icon={faArrowLeft} className="mr-2 h-4 w-4" />
          Back to Articles
        </Link>
      </div>
    );
  }

  return (
    <div className="mx-auto max-w-4xl">
      <Link
        href="/learning/articles"
        className="mb-6 inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
      >
        <FontAwesomeIcon icon={faArrowLeft} className="mr-2 h-3 w-3" />
        Back to Articles
      </Link>

      <div className="overflow-hidden rounded-lg border border-gray-200 bg-white shadow-md dark:border-gray-800 dark:bg-gray-900">
        <div className="relative h-64 overflow-hidden sm:h-80 md:h-96">
          <img
            src={article.thumbnail || 'https://via.placeholder.com/1200x600.png?text=Article+Image'}
            alt={article.title}
            className="h-full w-full object-cover"
          />
        </div>

        <div className="p-6 md:p-8">
          <div className="mb-6 flex flex-wrap items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
            <div className="flex items-center">
              <FontAwesomeIcon icon={faUser} className="mr-2 h-4 w-4" />
              <span>{article.author}</span>
            </div>
            <div className="flex items-center">
              <FontAwesomeIcon icon={faCalendarAlt} className="mr-2 h-4 w-4" />
              <span>{article.publishedAt.toLocaleDateString()}</span>
            </div>
            <div className="flex items-center">
              <FontAwesomeIcon icon={faTags} className="mr-2 h-4 w-4" />
              <div className="flex flex-wrap gap-2">
                {article.tags.map((tag) => (
                  <span
                    key={tag}
                    className="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800 dark:bg-blue-900/30 dark:text-blue-300"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            </div>
          </div>

          <h1 className="mb-4 text-3xl font-bold text-gray-900 dark:text-white">{article.title}</h1>
          <p className="mb-8 text-lg text-gray-600 dark:text-gray-400">{article.description}</p>

          <div className="prose prose-blue max-w-none dark:prose-invert">
            <ReactMarkdown>{article.content}</ReactMarkdown>
          </div>
        </div>
      </div>
    </div>
  );
}
