'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSearch, faTags, faUser, faCalendarAlt } from '@fortawesome/free-solid-svg-icons';
import { getArticles } from '@/data/learning-resources';

export default function ArticlesPage() {
  const articles = getArticles();
  const [searchTerm, setSearchTerm] = useState('');

  const filteredArticles = articles.filter(
    (article) =>
      article.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      article.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      article.tags.some((tag) => tag.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  return (
    <div className="space-y-8">
      <div className="rounded-lg bg-gradient-to-r from-indigo-600 to-purple-700 p-8 text-white shadow-lg">
        <h1 className="mb-4 text-3xl font-bold">Articles & Guides</h1>
        <p className="mb-6 text-lg">
          Explore in-depth articles, guides, and best practices to help you get the most out of AI agents.
        </p>
        <div className="relative max-w-md">
          <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
            <FontAwesomeIcon icon={faSearch} className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            className="block w-full rounded-md border-0 bg-white bg-opacity-20 py-2 pl-10 pr-4 text-white placeholder-gray-300 backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50"
            placeholder="Search articles..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {filteredArticles.length > 0 ? (
          filteredArticles.map((article) => (
            <Link
              key={article.id}
              href={`/learning/articles/${article.id}`}
              className="group overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm transition hover:shadow-md dark:border-gray-800 dark:bg-gray-900"
            >
              <div className="relative h-48 overflow-hidden">
                <img
                  src={article.thumbnail || 'https://via.placeholder.com/800x400.png?text=Article'}
                  alt={article.title}
                  className="h-full w-full object-cover transition duration-300 group-hover:scale-105"
                />
              </div>
              <div className="p-4">
                <div className="mb-3 flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
                  <div className="flex items-center">
                    <FontAwesomeIcon icon={faUser} className="mr-1 h-3 w-3" />
                    <span>{article.author}</span>
                  </div>
                  <div className="flex items-center">
                    <FontAwesomeIcon icon={faCalendarAlt} className="mr-1 h-3 w-3" />
                    <span>{article.publishedAt.toLocaleDateString()}</span>
                  </div>
                </div>
                <h3 className="mb-2 text-lg font-semibold text-gray-900 group-hover:text-blue-600 dark:text-white dark:group-hover:text-blue-400">
                  {article.title}
                </h3>
                <p className="mb-3 text-sm text-gray-600 dark:text-gray-400">{article.description}</p>
                <div className="mb-3 flex flex-wrap gap-2">
                  {article.tags.map((tag) => (
                    <span
                      key={tag}
                      className="inline-flex items-center rounded-full bg-indigo-100 px-2.5 py-0.5 text-xs font-medium text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-300"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
                <div className="text-right">
                  <span className="text-sm font-medium text-blue-600 dark:text-blue-400">Read article →</span>
                </div>
              </div>
            </Link>
          ))
        ) : (
          <div className="col-span-full flex flex-col items-center justify-center py-12 text-center">
            <FontAwesomeIcon icon={faTags} className="mb-4 h-12 w-12 text-gray-400" />
            <h3 className="mb-2 text-lg font-medium text-gray-900 dark:text-white">No articles found</h3>
            <p className="text-gray-500 dark:text-gray-400">
              We couldn't find any articles matching your search. Try different keywords or browse all articles.
            </p>
            <button
              onClick={() => setSearchTerm('')}
              className="mt-4 rounded-md bg-indigo-600 px-4 py-2 text-sm font-medium text-white hover:bg-indigo-700"
            >
              Clear Search
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
