'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faArrowLeft, 
  faSearch, 
  faExternalLinkAlt, 
  faBolt, 
  faTasks, 
  faPalette, 
  faCode, 
  faMagnifyingGlassChart, 
  faChartLine, 
  faComments, 
  faUsersGear,
  faFilter,
  faTag
} from '@fortawesome/free-solid-svg-icons';
import { getResourcesByCategory, getCategoryById } from '@/data/resources';

// Map category IDs to FontAwesome icons
const categoryIcons: Record<string, any> = {
  'productivity': faBolt,
  'project-management': faTasks,
  'design': faPalette,
  'development': faCode,
  'research': faMagnifyingGlassChart,
  'analytics': faChartLine,
  'communication': faComments,
  'collaboration': faUsersGear
};

export default function CategoryPage() {
  const params = useParams();
  const categoryId = params.category as string;
  const category = getCategoryById(categoryId);
  const resources = getResourcesByCategory(categoryId);

  const [searchTerm, setSearchTerm] = useState('');
  const [selectedPricing, setSelectedPricing] = useState<string[]>([]);

  // Get all unique tags from resources in this category
  const allTags = Array.from(
    new Set(resources.flatMap(resource => resource.tags || []))
  ).sort();

  // Get all unique pricing options from resources in this category
  const allPricingOptions = Array.from(
    new Set(resources.map(resource => resource.pricing))
  ).filter(Boolean) as string[];

  // Filter resources based on search term and pricing filter
  const filteredResources = resources.filter(resource => {
    const matchesSearch = 
      resource.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      resource.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (resource.tags && resource.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase())));
    
    const matchesPricing = 
      selectedPricing.length === 0 || 
      (resource.pricing && selectedPricing.includes(resource.pricing));
    
    return matchesSearch && matchesPricing;
  });

  // Toggle pricing filter
  const togglePricingFilter = (pricing: string) => {
    if (selectedPricing.includes(pricing)) {
      setSelectedPricing(selectedPricing.filter(p => p !== pricing));
    } else {
      setSelectedPricing([...selectedPricing, pricing]);
    }
  };

  if (!category) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <h2 className="mb-4 text-2xl font-bold">Category Not Found</h2>
        <p className="mb-6 text-gray-600 dark:text-gray-400">The resource category you're looking for doesn't exist.</p>
        <Link
          href="/learning/resources"
          className="flex items-center rounded-md bg-teal-600 px-4 py-2 text-white hover:bg-teal-700"
        >
          <FontAwesomeIcon icon={faArrowLeft} className="mr-2 h-4 w-4" />
          Back to Resources
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <div className="rounded-lg bg-gradient-to-r from-teal-600 to-emerald-700 p-8 text-white shadow-lg">
        <Link
          href="/learning/resources"
          className="mb-4 inline-flex items-center rounded-md bg-white bg-opacity-20 px-3 py-1 text-sm font-medium text-white backdrop-blur-sm transition hover:bg-opacity-30"
        >
          <FontAwesomeIcon icon={faArrowLeft} className="mr-2 h-3 w-3" />
          Back to Resources
        </Link>
        
        <div className="flex items-center">
          <div className="mr-4 flex h-16 w-16 items-center justify-center rounded-full bg-white bg-opacity-20 backdrop-blur-sm">
            <FontAwesomeIcon icon={categoryIcons[category.id]} className="h-8 w-8" />
          </div>
          <div>
            <h1 className="mb-2 text-3xl font-bold">{category.name}</h1>
            <p className="text-lg">{category.description}</p>
          </div>
        </div>
      </div>

      <div className="flex flex-col space-y-4 md:flex-row md:space-y-0 md:space-x-4">
        {/* Search and filters */}
        <div className="w-full md:w-1/4">
          <div className="rounded-lg border border-gray-200 bg-white p-4 shadow-sm dark:border-gray-800 dark:bg-gray-900">
            <h3 className="mb-4 font-semibold">Search & Filter</h3>
            
            {/* Search input */}
            <div className="mb-4">
              <label htmlFor="search" className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
                Search
              </label>
              <div className="relative">
                <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                  <FontAwesomeIcon icon={faSearch} className="h-4 w-4 text-gray-400" />
                </div>
                <input
                  type="text"
                  id="search"
                  className="block w-full rounded-md border-gray-300 pl-10 focus:border-teal-500 focus:ring-teal-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white sm:text-sm"
                  placeholder="Search resources..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
            
            {/* Pricing filter */}
            <div className="mb-4">
              <h4 className="mb-2 flex items-center text-sm font-medium text-gray-700 dark:text-gray-300">
                <FontAwesomeIcon icon={faFilter} className="mr-2 h-3 w-3" />
                Pricing
              </h4>
              <div className="space-y-2">
                {allPricingOptions.map((pricing) => (
                  <div key={pricing} className="flex items-center">
                    <input
                      id={`pricing-${pricing}`}
                      type="checkbox"
                      className="h-4 w-4 rounded border-gray-300 text-teal-600 focus:ring-teal-500 dark:border-gray-700 dark:bg-gray-800"
                      checked={selectedPricing.includes(pricing)}
                      onChange={() => togglePricingFilter(pricing)}
                    />
                    <label
                      htmlFor={`pricing-${pricing}`}
                      className="ml-2 text-sm text-gray-700 dark:text-gray-300"
                    >
                      {pricing.charAt(0).toUpperCase() + pricing.slice(1)}
                    </label>
                  </div>
                ))}
              </div>
            </div>
            
            {/* Popular tags */}
            <div>
              <h4 className="mb-2 flex items-center text-sm font-medium text-gray-700 dark:text-gray-300">
                <FontAwesomeIcon icon={faTag} className="mr-2 h-3 w-3" />
                Popular Tags
              </h4>
              <div className="flex flex-wrap gap-2">
                {allTags.slice(0, 10).map((tag) => (
                  <button
                    key={tag}
                    onClick={() => setSearchTerm(tag)}
                    className="inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700"
                  >
                    {tag}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
        
        {/* Resources table */}
        <div className="w-full md:w-3/4">
          <div className="rounded-lg border border-gray-200 bg-white shadow-sm dark:border-gray-800 dark:bg-gray-900">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-800">
                <thead className="bg-gray-50 dark:bg-gray-800">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                      Name
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                      Description
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                      Pricing
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                      Link
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 bg-white dark:divide-gray-800 dark:bg-gray-900">
                  {filteredResources.length > 0 ? (
                    filteredResources.map((resource) => (
                      <tr key={resource.id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                        <td className="whitespace-nowrap px-6 py-4">
                          <div className="flex items-center">
                            {resource.logoUrl && (
                              <img
                                src={resource.logoUrl}
                                alt={`${resource.name} logo`}
                                className="mr-3 h-8 w-8 rounded-full object-contain"
                              />
                            )}
                            <div className="font-medium text-gray-900 dark:text-white">{resource.name}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="text-sm text-gray-600 dark:text-gray-400">
                            {resource.description}
                            {resource.tags && resource.tags.length > 0 && (
                              <div className="mt-2 flex flex-wrap gap-1">
                                {resource.tags.map((tag) => (
                                  <span
                                    key={tag}
                                    className="inline-flex items-center rounded-full bg-teal-100 px-2 py-0.5 text-xs font-medium text-teal-800 dark:bg-teal-900/30 dark:text-teal-300"
                                  >
                                    {tag}
                                  </span>
                                ))}
                              </div>
                            )}
                          </div>
                        </td>
                        <td className="whitespace-nowrap px-6 py-4">
                          <span className="inline-flex rounded-full px-2 text-xs font-semibold leading-5">
                            {resource.pricing === 'free' && (
                              <span className="rounded-full bg-green-100 px-2 py-0.5 text-xs font-medium text-green-800 dark:bg-green-900/30 dark:text-green-300">
                                Free
                              </span>
                            )}
                            {resource.pricing === 'freemium' && (
                              <span className="rounded-full bg-blue-100 px-2 py-0.5 text-xs font-medium text-blue-800 dark:bg-blue-900/30 dark:text-blue-300">
                                Freemium
                              </span>
                            )}
                            {resource.pricing === 'paid' && (
                              <span className="rounded-full bg-purple-100 px-2 py-0.5 text-xs font-medium text-purple-800 dark:bg-purple-900/30 dark:text-purple-300">
                                Paid
                              </span>
                            )}
                            {resource.pricing === 'enterprise' && (
                              <span className="rounded-full bg-gray-100 px-2 py-0.5 text-xs font-medium text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                                Enterprise
                              </span>
                            )}
                          </span>
                        </td>
                        <td className="whitespace-nowrap px-6 py-4 text-sm font-medium">
                          <a
                            href={resource.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-teal-600 hover:text-teal-900 dark:text-teal-400 dark:hover:text-teal-300"
                          >
                            <FontAwesomeIcon icon={faExternalLinkAlt} className="mr-1 h-3 w-3" />
                            Visit
                          </a>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={4} className="px-6 py-10 text-center">
                        <p className="text-gray-500 dark:text-gray-400">No resources found matching your criteria.</p>
                        <button
                          onClick={() => {
                            setSearchTerm('');
                            setSelectedPricing([]);
                          }}
                          className="mt-2 text-sm font-medium text-teal-600 hover:text-teal-500 dark:text-teal-400"
                        >
                          Clear filters
                        </button>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
