'use client';

import React from 'react';
import Link from 'next/link';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faBolt, 
  faTasks, 
  faPalette, 
  faCode, 
  faMagnifyingGlassChart, 
  faChartLine, 
  faComments, 
  faUsersGear,
  faArrowRight
} from '@fortawesome/free-solid-svg-icons';
import { resourceCategories } from '@/data/resources';

// Map category IDs to FontAwesome icons
const categoryIcons: Record<string, any> = {
  'productivity': faBolt,
  'project-management': faTasks,
  'design': faPalette,
  'development': faCode,
  'research': faMagnifyingGlass<PERSON>hart,
  'analytics': faChartLine,
  'communication': faComments,
  'collaboration': faUsersGear
};

export default function ResourcesPage() {
  return (
    <div className="space-y-8">
      <div className="rounded-lg bg-gradient-to-r from-teal-600 to-emerald-700 p-8 text-white shadow-lg">
        <h1 className="mb-4 text-3xl font-bold">Resources Directory</h1>
        <p className="mb-6 text-lg">
          Explore our curated collection of tools and services for consulting professionals. Find resources for productivity, project management, design, development, research, and more.
        </p>
      </div>

      <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        {resourceCategories.map((category) => (
          <Link
            key={category.id}
            href={`/learning/resources/${category.id}`}
            className="group flex flex-col rounded-lg border border-gray-200 bg-white p-6 shadow-sm transition hover:shadow-md dark:border-gray-800 dark:bg-gray-900"
          >
            <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-teal-100 text-teal-600 dark:bg-teal-900/30 dark:text-teal-400">
              <FontAwesomeIcon icon={categoryIcons[category.id]} className="h-6 w-6" />
            </div>
            <h3 className="mb-2 text-xl font-bold text-gray-900 group-hover:text-teal-600 dark:text-white dark:group-hover:text-teal-400">
              {category.name}
            </h3>
            <p className="mb-4 flex-1 text-gray-600 dark:text-gray-400">{category.description}</p>
            <div className="mt-auto flex items-center text-sm font-medium text-teal-600 dark:text-teal-400">
              Browse resources
              <FontAwesomeIcon icon={faArrowRight} className="ml-1 h-3 w-3" />
            </div>
          </Link>
        ))}
      </div>

      <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-800 dark:bg-gray-900">
        <h2 className="mb-4 text-2xl font-bold">About Our Resources Directory</h2>
        <p className="mb-4 text-gray-600 dark:text-gray-400">
          This directory is regularly updated with the latest tools and services that can help consulting professionals work more efficiently and deliver better results for clients. Our team carefully evaluates each resource before adding it to the directory.
        </p>
        <p className="mb-4 text-gray-600 dark:text-gray-400">
          Each category includes a variety of tools with different pricing models, from free to enterprise-level solutions, allowing you to find options that fit your specific needs and budget.
        </p>
        <div className="mt-4 rounded-md bg-teal-50 p-4 dark:bg-teal-900/20">
          <h3 className="mb-2 font-semibold text-teal-800 dark:text-teal-300">Have a suggestion?</h3>
          <p className="text-teal-700 dark:text-teal-400">
            If you know of a great tool that should be included in our directory, please let us know. We're always looking to expand our collection with valuable resources.
          </p>
        </div>
      </div>
    </div>
  );
}
