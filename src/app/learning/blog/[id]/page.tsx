'use client';

import React from 'react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faArrowLeft, faUser, faCalendarAlt, faTags, faClock } from '@fortawesome/free-solid-svg-icons';
import { getResourceById } from '@/data/learning-resources';
import ReactMarkdown from 'react-markdown';

export default function BlogPostPage() {
  const params = useParams();
  const id = params.id as string;
  const post = getResourceById(id);

  // Calculate estimated reading time
  const getReadingTime = (content: string) => {
    const wordsPerMinute = 200;
    const wordCount = content.split(/\s+/).length;
    const readingTime = Math.ceil(wordCount / wordsPerMinute);
    return readingTime;
  };

  if (!post || post.type !== 'blog') {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <h2 className="mb-4 text-2xl font-bold">Blog Post Not Found</h2>
        <p className="mb-6 text-gray-600 dark:text-gray-400">The blog post you're looking for doesn't exist or has been removed.</p>
        <Link
          href="/learning/blog"
          className="flex items-center rounded-md bg-purple-600 px-4 py-2 text-white hover:bg-purple-700"
        >
          <FontAwesomeIcon icon={faArrowLeft} className="mr-2 h-4 w-4" />
          Back to Blog
        </Link>
      </div>
    );
  }

  return (
    <div className="mx-auto max-w-4xl">
      <Link
        href="/learning/blog"
        className="mb-6 inline-flex items-center text-sm font-medium text-purple-600 hover:text-purple-800 dark:text-purple-400 dark:hover:text-purple-300"
      >
        <FontAwesomeIcon icon={faArrowLeft} className="mr-2 h-3 w-3" />
        Back to Blog
      </Link>

      <div className="overflow-hidden rounded-lg border border-gray-200 bg-white shadow-md dark:border-gray-800 dark:bg-gray-900">
        <div className="relative h-64 overflow-hidden sm:h-80 md:h-96">
          <img
            src={post.thumbnail || 'https://via.placeholder.com/1200x600.png?text=Blog+Post+Image'}
            alt={post.title}
            className="h-full w-full object-cover"
          />
        </div>

        <div className="p-6 md:p-8">
          <div className="mb-6 flex flex-wrap items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
            <div className="flex items-center">
              <FontAwesomeIcon icon={faUser} className="mr-2 h-4 w-4" />
              <span>{post.author}</span>
            </div>
            <div className="flex items-center">
              <FontAwesomeIcon icon={faCalendarAlt} className="mr-2 h-4 w-4" />
              <span>{post.publishedAt.toLocaleDateString()}</span>
            </div>
            <div className="flex items-center">
              <FontAwesomeIcon icon={faClock} className="mr-2 h-4 w-4" />
              <span>{getReadingTime(post.content)} min read</span>
            </div>
            <div className="flex items-center">
              <FontAwesomeIcon icon={faTags} className="mr-2 h-4 w-4" />
              <div className="flex flex-wrap gap-2">
                {post.tags.map((tag) => (
                  <span
                    key={tag}
                    className="inline-flex items-center rounded-full bg-purple-100 px-2.5 py-0.5 text-xs font-medium text-purple-800 dark:bg-purple-900/30 dark:text-purple-300"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            </div>
          </div>

          <h1 className="mb-4 text-3xl font-bold text-gray-900 dark:text-white">{post.title}</h1>
          <p className="mb-8 text-lg text-gray-600 dark:text-gray-400">{post.description}</p>

          <div className="prose prose-purple max-w-none dark:prose-invert">
            <ReactMarkdown>{post.content}</ReactMarkdown>
          </div>
        </div>
      </div>
    </div>
  );
}
