'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSearch, faTags, faUser, faCalendarAlt, faClock } from '@fortawesome/free-solid-svg-icons';
import { getBlogPosts } from '@/data/learning-resources';

export default function BlogPage() {
  const blogPosts = getBlogPosts();
  const [searchTerm, setSearchTerm] = useState('');

  const filteredPosts = blogPosts.filter(
    (post) =>
      post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      post.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      post.tags.some((tag) => tag.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Calculate estimated reading time (very simple implementation)
  const getReadingTime = (content: string) => {
    const wordsPerMinute = 200;
    const wordCount = content.split(/\s+/).length;
    const readingTime = Math.ceil(wordCount / wordsPerMinute);
    return readingTime;
  };

  return (
    <div className="space-y-8">
      <div className="rounded-lg bg-gradient-to-r from-purple-600 to-pink-700 p-8 text-white shadow-lg">
        <h1 className="mb-4 text-3xl font-bold">Blog</h1>
        <p className="mb-6 text-lg">
          Stay up-to-date with the latest platform updates, case studies, and insights from our team.
        </p>
        <div className="relative max-w-md">
          <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
            <FontAwesomeIcon icon={faSearch} className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            className="block w-full rounded-md border-0 bg-white bg-opacity-20 py-2 pl-10 pr-4 text-white placeholder-gray-300 backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50"
            placeholder="Search blog posts..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      <div className="space-y-8">
        {filteredPosts.length > 0 ? (
          filteredPosts.map((post) => (
            <Link
              key={post.id}
              href={`/learning/blog/${post.id}`}
              className="group block overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm transition hover:shadow-md dark:border-gray-800 dark:bg-gray-900"
            >
              <div className="md:flex">
                <div className="md:w-1/3">
                  <div className="h-48 overflow-hidden md:h-full">
                    <img
                      src={post.thumbnail || 'https://via.placeholder.com/800x400.png?text=Blog+Post'}
                      alt={post.title}
                      className="h-full w-full object-cover transition duration-300 group-hover:scale-105"
                    />
                  </div>
                </div>
                <div className="p-6 md:w-2/3">
                  <div className="mb-3 flex flex-wrap items-center gap-4 text-xs text-gray-500 dark:text-gray-400">
                    <div className="flex items-center">
                      <FontAwesomeIcon icon={faUser} className="mr-1 h-3 w-3" />
                      <span>{post.author}</span>
                    </div>
                    <div className="flex items-center">
                      <FontAwesomeIcon icon={faCalendarAlt} className="mr-1 h-3 w-3" />
                      <span>{post.publishedAt.toLocaleDateString()}</span>
                    </div>
                    <div className="flex items-center">
                      <FontAwesomeIcon icon={faClock} className="mr-1 h-3 w-3" />
                      <span>{getReadingTime(post.content)} min read</span>
                    </div>
                  </div>
                  <h3 className="mb-2 text-xl font-semibold text-gray-900 group-hover:text-purple-600 dark:text-white dark:group-hover:text-purple-400">
                    {post.title}
                  </h3>
                  <p className="mb-4 text-gray-600 dark:text-gray-400">{post.description}</p>
                  <div className="mb-4 flex flex-wrap gap-2">
                    {post.tags.map((tag) => (
                      <span
                        key={tag}
                        className="inline-flex items-center rounded-full bg-purple-100 px-2.5 py-0.5 text-xs font-medium text-purple-800 dark:bg-purple-900/30 dark:text-purple-300"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                  <div className="text-right">
                    <span className="text-sm font-medium text-purple-600 dark:text-purple-400">Read post →</span>
                  </div>
                </div>
              </div>
            </Link>
          ))
        ) : (
          <div className="flex flex-col items-center justify-center py-12 text-center">
            <FontAwesomeIcon icon={faTags} className="mb-4 h-12 w-12 text-gray-400" />
            <h3 className="mb-2 text-lg font-medium text-gray-900 dark:text-white">No blog posts found</h3>
            <p className="text-gray-500 dark:text-gray-400">
              We couldn't find any blog posts matching your search. Try different keywords or browse all posts.
            </p>
            <button
              onClick={() => setSearchTerm('')}
              className="mt-4 rounded-md bg-purple-600 px-4 py-2 text-sm font-medium text-white hover:bg-purple-700"
            >
              Clear Search
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
