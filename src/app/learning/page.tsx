'use client';

import React from 'react';
import Link from 'next/link';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faVideo, faNewspaper, faBlog, faArrowRight } from '@fortawesome/free-solid-svg-icons';
import { getFeaturedResources } from '@/data/learning-resources';

export default function LearningHub() {
  const featuredResources = getFeaturedResources(3);

  return (
    <div className="space-y-8">
      <div className="rounded-lg bg-gradient-to-r from-blue-600 to-indigo-700 p-8 text-white shadow-lg">
        <h1 className="mb-4 text-3xl font-bold">Learning Hub</h1>
        <p className="mb-6 text-lg">
          Explore our collection of educational resources to help you master AI agents and get the most out of our platform.
        </p>
        <div className="flex flex-wrap gap-4">
          <Link
            href="/learning/videos"
            className="flex items-center rounded-md bg-indigo-900 bg-opacity-50 px-4 py-2 font-medium text-white backdrop-blur-sm transition hover:bg-opacity-70"
          >
            <FontAwesomeIcon icon={faVideo} className="mr-2 h-4 w-4" />
            Browse Videos
          </Link>
          <Link
            href="/learning/articles"
            className="flex items-center rounded-md bg-indigo-900 bg-opacity-50 px-4 py-2 font-medium text-white backdrop-blur-sm transition hover:bg-opacity-70"
          >
            <FontAwesomeIcon icon={faNewspaper} className="mr-2 h-4 w-4" />
            Read Articles
          </Link>
          <Link
            href="/learning/blog"
            className="flex items-center rounded-md bg-indigo-900 bg-opacity-50 px-4 py-2 font-medium text-white backdrop-blur-sm transition hover:bg-opacity-70"
          >
            <FontAwesomeIcon icon={faBlog} className="mr-2 h-4 w-4" />
            Visit Blog
          </Link>
        </div>
      </div>

      <section>
        <div className="mb-6 flex items-center justify-between">
          <h2 className="text-2xl font-bold">Featured Resources</h2>
          <Link
            href="/learning/all"
            className="flex items-center text-sm font-medium text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
          >
            View all resources
            <FontAwesomeIcon icon={faArrowRight} className="ml-1 h-3 w-3" />
          </Link>
        </div>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {featuredResources.map((resource) => (
            <Link
              key={resource.id}
              href={`/learning/${resource.type}s/${resource.id}`}
              className="group overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm transition hover:shadow-md dark:border-gray-800 dark:bg-gray-900"
            >
              <div className="relative h-48 overflow-hidden">
                <img
                  src={resource.thumbnail || 'https://via.placeholder.com/800x400.png?text=Resource'}
                  alt={resource.title}
                  className="h-full w-full object-cover transition duration-300 group-hover:scale-105"
                />
                <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-4 text-white">
                  <div className="mb-1 inline-block rounded bg-blue-600 px-2 py-1 text-xs font-medium uppercase">
                    {resource.type}
                  </div>
                  <h3 className="text-lg font-semibold">{resource.title}</h3>
                </div>
              </div>
              <div className="p-4">
                <p className="mb-3 text-sm text-gray-600 dark:text-gray-400">{resource.description}</p>
                <div className="flex items-center justify-between">
                  <span className="text-xs text-gray-500 dark:text-gray-500">
                    {resource.publishedAt.toLocaleDateString()}
                  </span>
                  <span className="text-sm font-medium text-blue-600 dark:text-blue-400">Read more</span>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </section>

      <section className="grid gap-6 md:grid-cols-3">
        <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-800 dark:bg-gray-900">
          <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400">
            <FontAwesomeIcon icon={faVideo} className="h-6 w-6" />
          </div>
          <h3 className="mb-2 text-xl font-bold">Video Tutorials</h3>
          <p className="mb-4 text-gray-600 dark:text-gray-400">
            Watch step-by-step tutorials and demonstrations to help you get started with AI agents.
          </p>
          <Link
            href="/learning/videos"
            className="inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
          >
            Browse videos
            <FontAwesomeIcon icon={faArrowRight} className="ml-1 h-3 w-3" />
          </Link>
        </div>

        <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-800 dark:bg-gray-900">
          <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-indigo-100 text-indigo-600 dark:bg-indigo-900/30 dark:text-indigo-400">
            <FontAwesomeIcon icon={faNewspaper} className="h-6 w-6" />
          </div>
          <h3 className="mb-2 text-xl font-bold">In-Depth Articles</h3>
          <p className="mb-4 text-gray-600 dark:text-gray-400">
            Explore comprehensive guides and articles about AI agent capabilities, best practices, and use cases.
          </p>
          <Link
            href="/learning/articles"
            className="inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
          >
            Read articles
            <FontAwesomeIcon icon={faArrowRight} className="ml-1 h-3 w-3" />
          </Link>
        </div>

        <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-800 dark:bg-gray-900">
          <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-purple-100 text-purple-600 dark:bg-purple-900/30 dark:text-purple-400">
            <FontAwesomeIcon icon={faBlog} className="h-6 w-6" />
          </div>
          <h3 className="mb-2 text-xl font-bold">Blog Updates</h3>
          <p className="mb-4 text-gray-600 dark:text-gray-400">
            Stay up-to-date with the latest platform updates, case studies, and insights from our team.
          </p>
          <Link
            href="/learning/blog"
            className="inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
          >
            Visit blog
            <FontAwesomeIcon icon={faArrowRight} className="ml-1 h-3 w-3" />
          </Link>
        </div>
      </section>
    </div>
  );
}
