'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { getAgentById, getSessionsForAgent } from '@/lib/agents';
import { Session, Message } from '@/types/agent';
import AgentHeader from './components/AgentHeader';
import AgentInfo from './components/AgentInfo';
import ChatInterface from './components/ChatInterface';
import SessionHistory from './components/SessionHistory';
import SessionControls from './components/SessionControls';
import RelatedAgents from './components/RelatedAgents';
import { createMessage, getRelatedAgents } from '@/lib/agents';
import { addToRecentlyViewed } from '@/lib/userPreferences';

export default function AgentPage() {
  const params = useParams();
  const agentId = params.id as string;

  const [agent, setAgent] = useState(getAgentById(agentId));
  const [sessions, setSessions] = useState(getSessionsForAgent(agentId));
  const [currentSession, setCurrentSession] = useState<Session | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);

  useEffect(() => {
    // Initialize with a welcome message if no session is selected
    if (!currentSession) {
      setMessages([
        createMessage(`Hello! I'm ${agent?.name}. How can I help you today?`, 'assistant')
      ]);
    }

    // Track this agent as recently viewed
    if (agent) {
      addToRecentlyViewed(agentId);
    }
  }, [agent, currentSession, agentId]);

  // Handle selecting a session from history
  const handleSelectSession = (session: Session) => {
    setCurrentSession(session);
    setMessages(session.messages);
  };

  // Handle saving the current session
  const handleSaveSession = () => {
    if (messages.length <= 1) {
      alert('Cannot save an empty session. Please start a conversation first.');
      return;
    }

    const userMessages = messages.filter(m => m.role === 'user');
    if (userMessages.length === 0) {
      alert('Cannot save a session without any user messages.');
      return;
    }

    const sessionTitle = userMessages[0].content.substring(0, 30) + (userMessages[0].content.length > 30 ? '...' : '');

    const newSession: Session = {
      id: `session_${Date.now()}`,
      agentId,
      title: sessionTitle,
      messages: [...messages],
      createdAt: messages[0].timestamp,
      updatedAt: Date.now(),
      isSaved: true
    };

    setSessions(prev => [newSession, ...prev]);
    setCurrentSession(newSession);
    alert('Session saved successfully!');
  };

  // Handle clearing the chat
  const handleClearChat = () => {
    if (confirm('Are you sure you want to clear the current chat? This cannot be undone.')) {
      setMessages([
        createMessage(`Hello! I'm ${agent?.name}. How can I help you today?`, 'assistant')
      ]);
      setCurrentSession(null);
    }
  };

  // Handle exporting the chat
  const handleExportChat = () => {
    if (messages.length <= 1) {
      alert('There is nothing to export. Please start a conversation first.');
      return;
    }

    const chatContent = messages.map(msg => {
      const role = msg.role === 'assistant' ? agent?.name || 'Assistant' : 'You';
      const time = new Date(msg.timestamp).toLocaleString();
      return `${role} (${time}):\n${msg.content}\n`;
    }).join('\n');

    const blob = new Blob([chatContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `Chat with ${agent?.name} - ${new Date().toLocaleDateString()}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  if (!agent) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <h1 className="mb-4 text-2xl font-bold text-gray-900 dark:text-white">Agent Not Found</h1>
          <p className="mb-6 text-gray-600 dark:text-gray-400">
            The agent you're looking for doesn't exist or has been removed.
          </p>
          <a
            href="/"
            className="rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800"
          >
            Return to Home
          </a>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <AgentHeader agent={agent} />

      <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
        <div className="lg:col-span-2">
          <SessionControls
            onSaveSession={handleSaveSession}
            onClearChat={handleClearChat}
            onExportChat={handleExportChat}
          />
          <ChatInterface
            agentName={agent.name}
            initialMessages={messages}
          />
        </div>

        <div>
          <SessionHistory
            sessions={sessions}
            onSelectSession={handleSelectSession}
          />
          <AgentInfo agent={agent} />
          <div className="mt-6">
            <RelatedAgents agentId={agentId} />
          </div>
        </div>
      </div>
    </div>
  );
}
