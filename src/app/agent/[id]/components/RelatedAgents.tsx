'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Agent } from '@/types/agent';
import { getRelatedAgents } from '@/lib/agents';
import { addToRecentlyViewed } from '@/lib/userPreferences';

interface RelatedAgentsProps {
  agentId: string;
}

export default function RelatedAgents({ agentId }: RelatedAgentsProps) {
  const [relatedAgents, setRelatedAgents] = useState<Agent[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Get related agents
    const agents = getRelatedAgents(agentId);
    setRelatedAgents(agents);
    setIsLoading(false);
  }, [agentId]);

  // Handle agent click to track recently viewed
  const handleAgentClick = (id: string) => {
    addToRecentlyViewed(id);
  };

  if (isLoading) {
    return (
      <div className="rounded-lg border border-gray-200 bg-white p-6 dark:border-gray-800 dark:bg-gray-900">
        <h2 className="mb-4 text-xl font-bold text-gray-900 dark:text-white">Related Agents</h2>
        <div className="flex justify-center py-4">
          <div className="h-6 w-6 animate-spin rounded-full border-b-2 border-t-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  if (relatedAgents.length === 0) {
    return null; // Don't show the section if there are no related agents
  }

  return (
    <div className="rounded-lg border border-gray-200 bg-white p-6 dark:border-gray-800 dark:bg-gray-900">
      <h2 className="mb-4 text-xl font-bold text-gray-900 dark:text-white">Related Agents</h2>
      <div className="space-y-4">
        {relatedAgents.map((agent) => (
          <Link
            key={agent.id}
            href={`/agent/${agent.id}`}
            className="flex items-center rounded-lg border border-gray-200 p-3 transition-colors hover:bg-gray-50 dark:border-gray-700 dark:hover:bg-gray-800"
            onClick={() => handleAgentClick(agent.id)}
          >
            {agent.avatarUrl && (
              <div className="mr-3 h-10 w-10 flex-shrink-0 overflow-hidden rounded-full bg-gray-100 dark:bg-gray-800">
                <Image
                  src={agent.avatarUrl}
                  alt={`${agent.name} icon`}
                  width={40}
                  height={40}
                  className="h-full w-full object-cover"
                />
              </div>
            )}
            <div className="flex-1">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium text-gray-900 dark:text-white">{agent.name}</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">{agent.category}</p>
                </div>
                {agent.isNew && (
                  <span className="rounded-full bg-green-100 px-2 py-0.5 text-xs font-medium text-green-800 dark:bg-green-900/30 dark:text-green-400">
                    New
                  </span>
                )}
              </div>
            </div>
          </Link>
        ))}
      </div>
    </div>
  );
}
