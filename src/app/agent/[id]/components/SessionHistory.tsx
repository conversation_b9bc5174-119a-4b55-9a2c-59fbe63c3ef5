'use client';

import { useState } from 'react';
import { Session } from '@/types/agent';

interface SessionHistoryProps {
  sessions: Session[];
  onSelectSession: (session: Session) => void;
}

export default function SessionHistory({ sessions, onSelectSession }: SessionHistoryProps) {
  const [isExpanded, setIsExpanded] = useState(true);

  // Format timestamp to readable date
  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    if (date.toDateString() === today.toDateString()) {
      return `Today at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
    } else if (date.toDateString() === yesterday.toDateString()) {
      return `Yesterday at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' }) + 
             ` at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
    }
  };

  if (sessions.length === 0) {
    return (
      <div className="mb-6 rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Session History</h2>
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="rounded p-1 text-gray-500 hover:bg-gray-100 hover:text-gray-700 dark:text-gray-400 dark:hover:bg-gray-800 dark:hover:text-gray-300"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className={`h-5 w-5 transition-transform ${isExpanded ? 'rotate-180' : ''}`}
            >
              <path d="m18 15-6-6-6 6" />
            </svg>
          </button>
        </div>
        {isExpanded && (
          <div className="mt-4 text-center text-gray-500 dark:text-gray-400">
            <p>No previous sessions found.</p>
            <p className="mt-2 text-sm">Start a new chat to begin.</p>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="mb-6 rounded-lg border border-gray-200 bg-white dark:border-gray-800 dark:bg-gray-900">
      <div className="flex items-center justify-between border-b border-gray-200 p-4 dark:border-gray-800">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Session History</h2>
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="rounded p-1 text-gray-500 hover:bg-gray-100 hover:text-gray-700 dark:text-gray-400 dark:hover:bg-gray-800 dark:hover:text-gray-300"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className={`h-5 w-5 transition-transform ${isExpanded ? 'rotate-180' : ''}`}
          >
            <path d="m18 15-6-6-6 6" />
          </svg>
        </button>
      </div>
      
      {isExpanded && (
        <div className="max-h-80 overflow-y-auto p-4">
          <div className="space-y-2">
            {sessions.map((session) => (
              <button
                key={session.id}
                onClick={() => onSelectSession(session)}
                className="w-full rounded-md border border-gray-200 bg-white p-3 text-left hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:hover:bg-gray-700"
              >
                <div className="flex items-center justify-between">
                  <h3 className="font-medium text-gray-900 dark:text-white">{session.title}</h3>
                  {session.isSaved && (
                    <span className="rounded-full bg-blue-100 px-2 py-0.5 text-xs font-medium text-blue-800 dark:bg-blue-900/30 dark:text-blue-400">
                      Saved
                    </span>
                  )}
                </div>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  {formatDate(session.updatedAt)}
                </p>
                <p className="mt-2 line-clamp-2 text-sm text-gray-600 dark:text-gray-300">
                  {session.messages[session.messages.length - 1]?.content || 'No messages'}
                </p>
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
