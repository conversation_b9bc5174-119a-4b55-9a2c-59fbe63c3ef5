import { Agent } from '@/types/agent';

interface AgentInfoProps {
  agent: Agent;
}

export default function AgentInfo({ agent }: AgentInfoProps) {
  return (
    <div className="mb-8 space-y-6">
      {/* About section - now full width */}
      <div className="rounded-lg border border-gray-200 bg-white p-6 dark:border-gray-800 dark:bg-gray-900">
        <h2 className="mb-4 text-xl font-bold text-gray-900 dark:text-white">About</h2>
        <p className="mb-4 text-gray-700 dark:text-gray-300">
          {agent.longDescription || agent.description}
        </p>
        <h3 className="mb-2 mt-6 text-lg font-semibold text-gray-900 dark:text-white">Capabilities</h3>
        <ul className="list-inside list-disc space-y-1 text-gray-700 dark:text-gray-300">
          {agent.capabilities.map((capability, index) => (
            <li key={index}>{capability}</li>
          ))}
        </ul>
      </div>

      {/* Details section - now full width */}
      <div className="rounded-lg border border-gray-200 bg-white p-6 dark:border-gray-800 dark:bg-gray-900">
        <h2 className="mb-4 text-xl font-bold text-gray-900 dark:text-white">Details</h2>
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3">
          <div>
            <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Created by</h3>
            <p className="text-gray-900 dark:text-white">{agent.creator || 'Internal Team'}</p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Created on</h3>
            <p className="text-gray-900 dark:text-white">{new Date(agent.createdAt).toLocaleDateString()}</p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Last updated</h3>
            <p className="text-gray-900 dark:text-white">{new Date(agent.updatedAt).toLocaleDateString()}</p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Version</h3>
            <p className="text-gray-900 dark:text-white">{agent.version}</p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Category</h3>
            <p className="text-gray-900 dark:text-white">{agent.category}</p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Usage</h3>
            <p className="text-gray-900 dark:text-white">{agent.usageCount.toLocaleString()} times</p>
          </div>
        </div>
      </div>
    </div>
  );
}
