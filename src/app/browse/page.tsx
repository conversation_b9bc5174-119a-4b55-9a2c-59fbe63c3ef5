'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { getAllAgents, getAllCategories } from '@/lib/agents';
import { Agent } from '@/types/agent';
import FilterBar from './components/FilterBar';
import SearchBar from './components/SearchBar';
import AgentGrid from './components/AgentGrid';
import AgentList from './components/AgentList';
import Pagination from './components/Pagination';
import RecentlyViewed from './components/RecentlyViewed';

export default function BrowsePage() {
  const allAgents = useMemo(() => getAllAgents(), []);
  const [filteredAgents, setFilteredAgents] = useState<Agent[]>(allAgents);
  const [displayedAgents, setDisplayedAgents] = useState<Agent[]>([]);

  // Filter states
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [showNewOnly, setShowNewOnly] = useState(false);
  const [sortBy, setSortBy] = useState('popular');
  const [searchQuery, setSearchQuery] = useState('');
  const [view, setView] = useState<'grid' | 'list'>('grid');

  // Advanced filter states
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [selectedCapabilities, setSelectedCapabilities] = useState<string[]>([]);
  const [selectedDateRange, setSelectedDateRange] = useState<{from: string; to: string} | null>(null);

  // Pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(() => {
    // Initialize from user preferences if available
    if (typeof window !== 'undefined') {
      try {
        const savedPreferences = localStorage.getItem('userPreferences');
        if (savedPreferences) {
          const preferences = JSON.parse(savedPreferences);
          return preferences.pageSize || 12;
        }
      } catch (error) {
        console.error('Error loading page size from preferences:', error);
      }
    }
    return 12;
  });

  // Get unique categories from all agents
  const categories = useMemo(() => getAllCategories(), []);

  // Apply filters and sorting
  useEffect(() => {
    let result = [...allAgents];

    // Apply category filter
    if (selectedCategories.length > 0) {
      result = result.filter((agent) => selectedCategories.includes(agent.category));
    }

    // Apply new only filter
    if (showNewOnly) {
      result = result.filter((agent) => agent.isNew);
    }

    // Apply tag filter
    if (selectedTags.length > 0) {
      result = result.filter((agent) =>
        agent.tags?.some(tag => selectedTags.includes(tag))
      );
    }

    // Apply capabilities filter
    if (selectedCapabilities.length > 0) {
      result = result.filter((agent) =>
        agent.capabilities.some(capability =>
          selectedCapabilities.some(selectedCap =>
            capability.toLowerCase().includes(selectedCap.toLowerCase())
          )
        )
      );
    }

    // Apply date range filter
    if (selectedDateRange && selectedDateRange.from && selectedDateRange.to) {
      const fromDate = new Date(selectedDateRange.from).getTime();
      const toDate = new Date(selectedDateRange.to).getTime();

      result = result.filter((agent) => {
        const createdDate = new Date(agent.createdAt).getTime();
        return createdDate >= fromDate && createdDate <= toDate;
      });
    }

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      result = result.filter(
        (agent) =>
          agent.name.toLowerCase().includes(query) ||
          agent.description.toLowerCase().includes(query) ||
          agent.category.toLowerCase().includes(query) ||
          agent.tags?.some(tag => tag.toLowerCase().includes(query)) ||
          agent.capabilities.some(capability => capability.toLowerCase().includes(query))
      );
    }

    // Apply sorting
    switch (sortBy) {
      case 'popular':
        result.sort((a, b) => b.usageCount - a.usageCount);
        break;
      case 'newest':
        result.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
        break;
      case 'alphabetical':
        result.sort((a, b) => a.name.localeCompare(b.name));
        break;
      default:
        break;
    }

    setFilteredAgents(result);
    setCurrentPage(1); // Reset to first page when filters change
  }, [
    allAgents,
    selectedCategories,
    showNewOnly,
    sortBy,
    searchQuery,
    selectedTags,
    selectedCapabilities,
    selectedDateRange
  ]);

  // Apply pagination
  useEffect(() => {
    // Ensure current page is valid
    const maxPage = Math.max(1, Math.ceil(filteredAgents.length / pageSize));
    const validCurrentPage = Math.min(currentPage, maxPage);

    if (validCurrentPage !== currentPage) {
      setCurrentPage(validCurrentPage);
      return;
    }

    const startIndex = (validCurrentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    setDisplayedAgents(filteredAgents.slice(startIndex, endIndex));
  }, [filteredAgents, currentPage, pageSize]);

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // Handle page size change
  const handlePageSizeChange = (size: number) => {
    setPageSize(size);
    setCurrentPage(1); // Reset to first page when page size changes
  };

  // Handle advanced filters
  const handleAdvancedFiltersChange = (filters: {
    tags: string[];
    dateRange: {from: string; to: string} | null;
    capabilities: string[];
  }) => {
    setSelectedTags(filters.tags);
    setSelectedDateRange(filters.dateRange);
    setSelectedCapabilities(filters.capabilities);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="grid grid-cols-1 gap-8 lg:grid-cols-4">
        <div className="lg:col-span-3">
          <SearchBar onSearch={setSearchQuery} />

          <FilterBar
            categories={categories}
            onCategoryChange={setSelectedCategories}
            onShowNewOnly={setShowNewOnly}
            onSortChange={setSortBy}
            onViewChange={setView}
            onAdvancedFiltersChange={handleAdvancedFiltersChange}
          />

          {filteredAgents.length > 0 ? (
            view === 'grid' ? (
              <AgentGrid agents={displayedAgents} />
            ) : (
              <AgentList agents={displayedAgents} />
            )
          ) : (
            <div className="flex h-64 flex-col items-center justify-center rounded-lg border border-gray-200 bg-white p-6 text-center dark:border-gray-800 dark:bg-gray-900">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="mb-4 h-12 w-12 text-gray-400"
              >
                <circle cx="12" cy="12" r="10" />
                <line x1="12" x2="12" y1="8" y2="12" />
                <line x1="12" x2="12.01" y1="16" y2="16" />
              </svg>
              <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">No agents found</h3>
              <p className="text-gray-600 dark:text-gray-400">
                Try adjusting your search or filter criteria to find what you're looking for.
              </p>
            </div>
          )}

          {filteredAgents.length > 0 && (
            <Pagination
              totalItems={filteredAgents.length}
              currentPage={currentPage}
              onPageChange={handlePageChange}
              onPageSizeChange={handlePageSizeChange}
            />
          )}
        </div>

        <div className="lg:col-span-1">
          <RecentlyViewed />
        </div>
      </div>
    </div>
  );
}
