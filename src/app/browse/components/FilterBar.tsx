'use client';

import React, { useState, useEffect } from 'react';
import { getDefaultView, updateDefaultView } from '@/lib/userPreferences';
import AdvancedFilters from './AdvancedFilters';

type SortOption = {
  label: string;
  value: string;
};

type DateRange = {
  from: string;
  to: string;
};

type FilterBarProps = {
  categories: string[];
  onCategoryChange: (categories: string[]) => void;
  onShowNewOnly: (showNewOnly: boolean) => void;
  onSortChange: (sortBy: string) => void;
  onViewChange: (view: 'grid' | 'list') => void;
  onAdvancedFiltersChange: (filters: {
    tags: string[];
    dateRange: DateRange | null;
    capabilities: string[];
  }) => void;
};

export default function FilterBar({
  categories,
  onCategoryChange,
  onShowNewOnly,
  onSortChange,
  onViewChange,
  onAdvancedFiltersChange,
}: FilterBarProps) {
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [showNewOnly, setShowNewOnly] = useState(false);
  const [sortBy, setSortBy] = useState('popular');
  const [view, setView] = useState<'grid' | 'list'>('grid');
  const [isCategoryDropdownOpen, setIsCategoryDropdownOpen] = useState(false);
  const [isSortDropdownOpen, setIsSortDropdownOpen] = useState(false);
  const [isAdvancedFiltersOpen, setIsAdvancedFiltersOpen] = useState(false);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [selectedCapabilities, setSelectedCapabilities] = useState<string[]>([]);
  const [selectedDateRange, setSelectedDateRange] = useState<DateRange | null>(null);

  const sortOptions: SortOption[] = [
    { label: 'Most Popular', value: 'popular' },
    { label: 'Newest', value: 'newest' },
    { label: 'Alphabetical (A-Z)', value: 'alphabetical' },
  ];

  // Handle category selection
  const handleCategoryToggle = (category: string) => {
    setSelectedCategories((prev) =>
      prev.includes(category)
        ? prev.filter((c) => c !== category)
        : [...prev, category]
    );
  };

  // Handle "New Only" toggle
  const handleNewOnlyToggle = () => {
    setShowNewOnly(!showNewOnly);
  };

  // Handle sort change
  const handleSortChange = (value: string) => {
    setSortBy(value);
    setIsSortDropdownOpen(false);
  };

  // Handle view change
  const handleViewChange = (newView: 'grid' | 'list') => {
    setView(newView);
    updateDefaultView(newView);
  };

  // Handle advanced filters
  const handleAdvancedFiltersChange = (filters: {
    tags: string[];
    dateRange: DateRange | null;
    capabilities: string[];
  }) => {
    setSelectedTags(filters.tags);
    setSelectedDateRange(filters.dateRange);
    setSelectedCapabilities(filters.capabilities);
    onAdvancedFiltersChange(filters);
  };

  // Load default view from user preferences
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const defaultView = getDefaultView();
      setView(defaultView);
      onViewChange(defaultView);
    }
  }, [onViewChange]);

  // Propagate changes to parent component
  useEffect(() => {
    onCategoryChange(selectedCategories);
  }, [selectedCategories, onCategoryChange]);

  useEffect(() => {
    onShowNewOnly(showNewOnly);
  }, [showNewOnly, onShowNewOnly]);

  useEffect(() => {
    onSortChange(sortBy);
  }, [sortBy, onSortChange]);

  useEffect(() => {
    onViewChange(view);
  }, [view, onViewChange]);

  return (
    <div className="mb-8 space-y-4">
      <div className="flex flex-col justify-between gap-4 sm:flex-row sm:items-center">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white md:text-3xl">Browse Agents</h1>

        <div className="flex flex-wrap items-center gap-2 sm:gap-4">
          {/* Advanced filters button */}
          <button
            onClick={() => setIsAdvancedFiltersOpen(true)}
            className="flex items-center rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700"
          >
            Advanced Filters
            {(selectedTags.length > 0 || selectedCapabilities.length > 0 || selectedDateRange) && (
              <span className="ml-1.5 rounded-full bg-blue-100 px-2 py-0.5 text-xs font-medium text-blue-800 dark:bg-blue-900/30 dark:text-blue-400">
                {selectedTags.length + selectedCapabilities.length + (selectedDateRange ? 1 : 0)}
              </span>
            )}
          </button>

          {/* Category filter */}
          <div className="relative">
            <button
              onClick={() => setIsCategoryDropdownOpen(!isCategoryDropdownOpen)}
              className="flex items-center rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700"
            >
              Categories
              {selectedCategories.length > 0 && (
                <span className="ml-1.5 rounded-full bg-blue-100 px-2 py-0.5 text-xs font-medium text-blue-800 dark:bg-blue-900/30 dark:text-blue-400">
                  {selectedCategories.length}
                </span>
              )}
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="ml-1 h-4 w-4"
              >
                <path d="m6 9 6 6 6-6" />
              </svg>
            </button>

            {isCategoryDropdownOpen && (
              <div className="absolute right-0 z-10 mt-2 w-56 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-gray-800 dark:ring-gray-700">
                <div className="p-2">
                  {categories.map((category) => (
                    <div key={category} className="flex items-center px-3 py-2">
                      <input
                        type="checkbox"
                        id={`category-${category}`}
                        checked={selectedCategories.includes(category)}
                        onChange={() => handleCategoryToggle(category)}
                        className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:focus:ring-blue-600"
                      />
                      <label
                        htmlFor={`category-${category}`}
                        className="ml-2 text-sm font-medium text-gray-700 dark:text-gray-200"
                      >
                        {category}
                      </label>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* New only filter */}
          <div className="flex items-center">
            <input
              type="checkbox"
              id="new-only"
              checked={showNewOnly}
              onChange={handleNewOnlyToggle}
              className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:focus:ring-blue-600"
            />
            <label
              htmlFor="new-only"
              className="ml-2 text-sm font-medium text-gray-700 dark:text-gray-200"
            >
              New Only
            </label>
          </div>

          {/* Sort options */}
          <div className="relative">
            <button
              onClick={() => setIsSortDropdownOpen(!isSortDropdownOpen)}
              className="flex items-center rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700"
            >
              Sort: {sortOptions.find((option) => option.value === sortBy)?.label}
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="ml-1 h-4 w-4"
              >
                <path d="m6 9 6 6 6-6" />
              </svg>
            </button>

            {isSortDropdownOpen && (
              <div className="absolute right-0 z-10 mt-2 w-56 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-gray-800 dark:ring-gray-700">
                <div className="py-1">
                  {sortOptions.map((option) => (
                    <button
                      key={option.value}
                      onClick={() => handleSortChange(option.value)}
                      className={`block w-full px-4 py-2 text-left text-sm ${
                        sortBy === option.value
                          ? 'bg-gray-100 text-gray-900 dark:bg-gray-700 dark:text-white'
                          : 'text-gray-700 hover:bg-gray-50 dark:text-gray-200 dark:hover:bg-gray-700'
                      }`}
                    >
                      {option.label}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* View toggle */}
          <div className="flex rounded-md border border-gray-300 dark:border-gray-600">
            <button
              onClick={() => handleViewChange('grid')}
              className={`flex items-center justify-center rounded-l-md px-3 py-2 ${
                view === 'grid'
                  ? 'bg-gray-100 text-gray-900 dark:bg-gray-700 dark:text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-50 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700'
              }`}
              aria-label="Grid view"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="h-4 w-4"
              >
                <rect width="7" height="7" x="3" y="3" rx="1" />
                <rect width="7" height="7" x="14" y="3" rx="1" />
                <rect width="7" height="7" x="14" y="14" rx="1" />
                <rect width="7" height="7" x="3" y="14" rx="1" />
              </svg>
            </button>
            <button
              onClick={() => handleViewChange('list')}
              className={`flex items-center justify-center rounded-r-md px-3 py-2 ${
                view === 'list'
                  ? 'bg-gray-100 text-gray-900 dark:bg-gray-700 dark:text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-50 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700'
              }`}
              aria-label="List view"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="h-4 w-4"
              >
                <line x1="8" x2="21" y1="6" y2="6" />
                <line x1="8" x2="21" y1="12" y2="12" />
                <line x1="8" x2="21" y1="18" y2="18" />
                <line x1="3" x2="3.01" y1="6" y2="6" />
                <line x1="3" x2="3.01" y1="12" y2="12" />
                <line x1="3" x2="3.01" y1="18" y2="18" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Active filters */}
      {(selectedCategories.length > 0 || showNewOnly) && (
        <div className="flex flex-wrap items-center gap-2">
          <span className="text-sm text-gray-500 dark:text-gray-400">Active filters:</span>

          {selectedCategories.map((category) => (
            <span
              key={category}
              className="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800 dark:bg-blue-900/30 dark:text-blue-400"
            >
              {category}
              <button
                type="button"
                onClick={() => handleCategoryToggle(category)}
                className="ml-1 inline-flex h-4 w-4 items-center justify-center rounded-full text-blue-400 hover:bg-blue-200 hover:text-blue-600 focus:outline-none dark:hover:bg-blue-800"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-3 w-3"
                >
                  <path d="M18 6 6 18" />
                  <path d="m6 6 12 12" />
                </svg>
              </button>
            </span>
          ))}

          {showNewOnly && (
            <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800 dark:bg-green-900/30 dark:text-green-400">
              New Only
              <button
                type="button"
                onClick={handleNewOnlyToggle}
                className="ml-1 inline-flex h-4 w-4 items-center justify-center rounded-full text-green-400 hover:bg-green-200 hover:text-green-600 focus:outline-none dark:hover:bg-green-800"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-3 w-3"
                >
                  <path d="M18 6 6 18" />
                  <path d="m6 6 12 12" />
                </svg>
              </button>
            </span>
          )}

          <button
            type="button"
            onClick={() => {
              setSelectedCategories([]);
              setShowNewOnly(false);
            }}
            className="text-xs text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
          >
            Clear all
          </button>
        </div>
      )}

      {/* Advanced filters modal */}
      <AdvancedFilters
        isOpen={isAdvancedFiltersOpen}
        onClose={() => setIsAdvancedFiltersOpen(false)}
        onApplyFilters={handleAdvancedFiltersChange}
        selectedTags={selectedTags}
        selectedCapabilities={selectedCapabilities}
        selectedDateRange={selectedDateRange}
      />
    </div>
  );
}
