'use client';

import React, { useState, useEffect } from 'react';
import { getAllTags } from '@/lib/agents';

interface DateRange {
  from: string;
  to: string;
}

interface AdvancedFiltersProps {
  isOpen: boolean;
  onClose: () => void;
  onApplyFilters: (filters: {
    tags: string[];
    dateRange: DateRange | null;
    capabilities: string[];
  }) => void;
  selectedTags: string[];
  selectedCapabilities: string[];
  selectedDateRange: DateRange | null;
}

export default function AdvancedFilters({
  isOpen,
  onClose,
  onApplyFilters,
  selectedTags,
  selectedCapabilities,
  selectedDateRange,
}: AdvancedFiltersProps) {
  const [tags, setTags] = useState<string[]>([]);
  const [selectedTagsState, setSelectedTagsState] = useState<string[]>(selectedTags);
  const [selectedCapabilitiesState, setSelectedCapabilitiesState] = useState<string[]>(selectedCapabilities);
  const [dateRange, setDateRange] = useState<DateRange | null>(selectedDateRange);

  // Common capabilities across agents
  const commonCapabilities = [
    'Natural language processing',
    'Data analysis',
    'Content generation',
    'Document processing',
    'Code generation',
    'Summarization',
    'Visualization',
    'Question answering',
  ];

  // Load tags
  useEffect(() => {
    setTags(getAllTags());
  }, []);

  // Handle tag selection
  const handleTagToggle = (tag: string) => {
    setSelectedTagsState((prev) =>
      prev.includes(tag) ? prev.filter((t) => t !== tag) : [...prev, tag]
    );
  };

  // Handle capability selection
  const handleCapabilityToggle = (capability: string) => {
    setSelectedCapabilitiesState((prev) =>
      prev.includes(capability) ? prev.filter((c) => c !== capability) : [...prev, capability]
    );
  };

  // Handle date range change
  const handleDateFromChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setDateRange((prev) => ({
      from: e.target.value,
      to: prev?.to || '',
    }));
  };

  const handleDateToChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setDateRange((prev) => ({
      from: prev?.from || '',
      to: e.target.value,
    }));
  };

  // Handle apply filters
  const handleApplyFilters = () => {
    onApplyFilters({
      tags: selectedTagsState,
      capabilities: selectedCapabilitiesState,
      dateRange: dateRange && dateRange.from && dateRange.to ? dateRange : null,
    });
    onClose();
  };

  // Handle reset filters
  const handleResetFilters = () => {
    setSelectedTagsState([]);
    setSelectedCapabilitiesState([]);
    setDateRange(null);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="relative max-h-[90vh] w-full max-w-3xl overflow-auto rounded-lg bg-white p-6 shadow-xl dark:bg-gray-900">
        <button
          onClick={onClose}
          className="absolute right-4 top-4 rounded-full p-1 text-gray-500 hover:bg-gray-100 hover:text-gray-700 dark:text-gray-400 dark:hover:bg-gray-800 dark:hover:text-gray-300"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="h-6 w-6"
          >
            <path d="M18 6 6 18" />
            <path d="m6 6 12 12" />
          </svg>
        </button>

        <h2 className="mb-6 text-2xl font-bold text-gray-900 dark:text-white">Advanced Filters</h2>

        <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
          {/* Tags filter */}
          <div>
            <h3 className="mb-3 text-lg font-semibold text-gray-900 dark:text-white">Tags</h3>
            <div className="max-h-60 overflow-y-auto rounded-md border border-gray-200 p-3 dark:border-gray-700">
              {tags.length > 0 ? (
                <div className="flex flex-wrap gap-2">
                  {tags.map((tag) => (
                    <button
                      key={tag}
                      onClick={() => handleTagToggle(tag)}
                      className={`rounded-full px-3 py-1 text-sm ${
                        selectedTagsState.includes(tag)
                          ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400'
                          : 'bg-gray-100 text-gray-800 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700'
                      }`}
                    >
                      {tag}
                    </button>
                  ))}
                </div>
              ) : (
                <p className="text-center text-gray-500 dark:text-gray-400">Loading tags...</p>
              )}
            </div>
          </div>

          {/* Capabilities filter */}
          <div>
            <h3 className="mb-3 text-lg font-semibold text-gray-900 dark:text-white">Capabilities</h3>
            <div className="max-h-60 overflow-y-auto rounded-md border border-gray-200 p-3 dark:border-gray-700">
              <div className="space-y-2">
                {commonCapabilities.map((capability) => (
                  <div key={capability} className="flex items-center">
                    <input
                      type="checkbox"
                      id={`capability-${capability}`}
                      checked={selectedCapabilitiesState.includes(capability)}
                      onChange={() => handleCapabilityToggle(capability)}
                      className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:focus:ring-blue-600"
                    />
                    <label
                      htmlFor={`capability-${capability}`}
                      className="ml-2 text-sm font-medium text-gray-700 dark:text-gray-200"
                    >
                      {capability}
                    </label>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Date range filter */}
        <div className="mt-8">
          <h3 className="mb-3 text-lg font-semibold text-gray-900 dark:text-white">Date Range</h3>
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <div>
              <label htmlFor="date-from" className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-200">
                From
              </label>
              <input
                type="date"
                id="date-from"
                value={dateRange?.from || ''}
                onChange={handleDateFromChange}
                className="block w-full rounded-md border border-gray-300 p-2 text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:focus:border-blue-600 dark:focus:ring-blue-600"
              />
            </div>
            <div>
              <label htmlFor="date-to" className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-200">
                To
              </label>
              <input
                type="date"
                id="date-to"
                value={dateRange?.to || ''}
                onChange={handleDateToChange}
                className="block w-full rounded-md border border-gray-300 p-2 text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:focus:border-blue-600 dark:focus:ring-blue-600"
              />
            </div>
          </div>
        </div>

        {/* Action buttons */}
        <div className="mt-8 flex justify-end space-x-4">
          <button
            onClick={handleResetFilters}
            className="rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700"
          >
            Reset
          </button>
          <button
            onClick={handleApplyFilters}
            className="rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800"
          >
            Apply Filters
          </button>
        </div>
      </div>
    </div>
  );
}
