'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Agent } from '@/types/agent';
import { isAgentFavorited, toggleFavoriteAgent, addToRecentlyViewed } from '@/lib/userPreferences';
import { addCustomEventListener, EVENTS } from '@/lib/events';

interface AgentListProps {
  agents: Agent[];
}

export default function AgentList({ agents }: AgentListProps) {
  const [favoriteAgents, setFavoriteAgents] = useState<Record<string, boolean>>({});

  // Load favorite status for each agent
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const loadFavorites = () => {
      const favorites: Record<string, boolean> = {};
      agents.forEach(agent => {
        favorites[agent.id] = isAgentFavorited(agent.id);
      });
      setFavoriteAgents(favorites);
    };

    loadFavorites();

    // Listen for favorites updates
    const removeListener = addCustomEventListener(EVENTS.FAVORITES_UPDATED, (event) => {
      const { agentId, isFavorite } = event.detail;
      setFavoriteAgents(prev => ({
        ...prev,
        [agentId]: isFavorite
      }));
    });

    return () => {
      removeListener();
    };
  }, [agents]);

  // Handle favorite toggle
  const handleFavoriteToggle = (agentId: string, e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    const newStatus = toggleFavoriteAgent(agentId);
    setFavoriteAgents(prev => ({
      ...prev,
      [agentId]: newStatus
    }));
  };

  // Handle agent click to track recently viewed
  const handleAgentClick = (agentId: string) => {
    addToRecentlyViewed(agentId);
  };
  if (agents.length === 0) {
    return (
      <div className="flex h-64 flex-col items-center justify-center rounded-lg border border-gray-200 bg-white p-6 text-center dark:border-gray-800 dark:bg-gray-900">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="mb-4 h-12 w-12 text-gray-400"
        >
          <circle cx="12" cy="12" r="10" />
          <line x1="12" x2="12" y1="8" y2="12" />
          <line x1="12" x2="12.01" y1="16" y2="16" />
        </svg>
        <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">No agents found</h3>
        <p className="text-gray-600 dark:text-gray-400">
          Try adjusting your search or filter criteria to find what you're looking for.
        </p>
      </div>
    );
  }

  return (
    <div className="overflow-hidden rounded-lg border border-gray-200 dark:border-gray-800">
      <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-800">
        <thead className="bg-gray-50 dark:bg-gray-800">
          <tr>
            <th
              scope="col"
              className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400"
            >
              Agent
            </th>
            <th
              scope="col"
              className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400"
            >
              Category
            </th>
            <th
              scope="col"
              className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400"
            >
              Usage
            </th>
            <th
              scope="col"
              className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400"
            >
              Last Updated
            </th>
            <th scope="col" className="relative px-6 py-3">
              <span className="sr-only">View</span>
            </th>
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-200 bg-white dark:divide-gray-800 dark:bg-gray-900">
          {agents.map((agent) => (
            <tr key={agent.id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
              <td className="whitespace-nowrap px-6 py-4">
                <div className="flex items-start">
                  {agent.avatarUrl && (
                    <div className="mr-3 h-10 w-10 min-w-[40px] flex-shrink-0 overflow-hidden rounded-full bg-gray-100 dark:bg-gray-800">
                      <Image
                        src={agent.avatarUrl}
                        alt={`${agent.name} icon`}
                        width={40}
                        height={40}
                        className="h-full w-full object-cover"
                      />
                    </div>
                  )}
                  <div className="flex-1">
                    <div className="flex items-center">
                      <div className="font-medium text-gray-900 dark:text-white self-center">{agent.name}</div>
                      <div className="ml-2 flex items-center space-x-2">
                        {agent.isNew && (
                          <span className="rounded-full bg-green-100 px-2 py-0.5 text-xs font-medium text-green-800 dark:bg-green-900/30 dark:text-green-400">
                            New
                          </span>
                        )}
                        <button
                          onClick={(e) => handleFavoriteToggle(agent.id, e)}
                          className="rounded-full p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-600 dark:hover:bg-gray-800 dark:hover:text-gray-300"
                          aria-label={favoriteAgents[agent.id] ? 'Remove from favorites' : 'Add to favorites'}
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill={favoriteAgents[agent.id] ? 'currentColor' : 'none'}
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            className={`h-4 w-4 ${favoriteAgents[agent.id] ? 'text-yellow-400' : ''}`}
                          >
                            <path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z" />
                          </svg>
                        </button>
                      </div>
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400 line-clamp-2 max-w-md">{agent.description}</div>
                  </div>
                </div>
              </td>
              <td className="whitespace-nowrap px-6 py-4">
                <span className="rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800 dark:bg-gray-800 dark:text-gray-200">
                  {agent.category}
                </span>
              </td>
              <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                {agent.usageCount.toLocaleString()}
              </td>
              <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                {new Date(agent.updatedAt).toLocaleDateString()}
              </td>
              <td className="whitespace-nowrap px-6 py-4 text-right text-sm font-medium">
                <Link
                  href={`/agent/${agent.id}`}
                  className="rounded-md bg-blue-600 px-3 py-1.5 text-sm font-medium text-white hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800"
                  onClick={() => handleAgentClick(agent.id)}
                >
                  View Agent
                </Link>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}
