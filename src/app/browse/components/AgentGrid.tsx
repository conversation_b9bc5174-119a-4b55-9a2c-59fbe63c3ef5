'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Agent } from '@/types/agent';
import { isAgentFavorited, toggleFavoriteAgent, addToRecentlyViewed } from '@/lib/userPreferences';
import { addCustomEventListener, EVENTS } from '@/lib/events';

interface AgentGridProps {
  agents: Agent[];
}

export default function AgentGrid({ agents }: AgentGridProps) {
  const [favoriteAgents, setFavoriteAgents] = useState<Record<string, boolean>>({});

  // Load favorite status for each agent
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const loadFavorites = () => {
      const favorites: Record<string, boolean> = {};
      agents.forEach(agent => {
        favorites[agent.id] = isAgentFavorited(agent.id);
      });
      setFavoriteAgents(favorites);
    };

    loadFavorites();

    // Listen for favorites updates
    const removeListener = addCustomEventListener(EVENTS.FAVORITES_UPDATED, (event) => {
      const { agentId, isFavorite } = event.detail;
      setFavoriteAgents(prev => ({
        ...prev,
        [agentId]: isFavorite
      }));
    });

    return () => {
      removeListener();
    };
  }, [agents]);

  // Handle favorite toggle
  const handleFavoriteToggle = (agentId: string, e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    const newStatus = toggleFavoriteAgent(agentId);
    setFavoriteAgents(prev => ({
      ...prev,
      [agentId]: newStatus
    }));
  };

  // Handle agent click to track recently viewed
  const handleAgentClick = (agentId: string) => {
    addToRecentlyViewed(agentId);
  };
  if (agents.length === 0) {
    return (
      <div className="flex h-64 flex-col items-center justify-center rounded-lg border border-gray-200 bg-white p-6 text-center dark:border-gray-800 dark:bg-gray-900">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="mb-4 h-12 w-12 text-gray-400"
        >
          <circle cx="12" cy="12" r="10" />
          <line x1="12" x2="12" y1="8" y2="12" />
          <line x1="12" x2="12.01" y1="16" y2="16" />
        </svg>
        <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">No agents found</h3>
        <p className="text-gray-600 dark:text-gray-400">
          Try adjusting your search or filter criteria to find what you're looking for.
        </p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
      {agents.map((agent) => (
        <div
          key={agent.id}
          className="flex flex-col rounded-lg border border-gray-200 bg-white p-6 shadow-sm transition-shadow hover:shadow-md dark:border-gray-800 dark:bg-gray-900 h-full min-h-[280px]"
        >
          <div className="mb-4 flex items-center justify-between">
            <span className="rounded-full bg-gray-100 px-3 py-1 text-xs font-medium text-gray-800 dark:bg-gray-800 dark:text-gray-200">
              {agent.category}
            </span>
            <div className="flex items-center space-x-2">
              {agent.isNew && (
                <span className="rounded-full bg-green-100 px-3 py-1 text-xs font-medium text-green-800 dark:bg-green-900/30 dark:text-green-400">
                  New
                </span>
              )}
              <button
                onClick={(e) => handleFavoriteToggle(agent.id, e)}
                className="rounded-full p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-600 dark:hover:bg-gray-800 dark:hover:text-gray-300"
                aria-label={favoriteAgents[agent.id] ? 'Remove from favorites' : 'Add to favorites'}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill={favoriteAgents[agent.id] ? 'currentColor' : 'none'}
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className={`h-5 w-5 ${favoriteAgents[agent.id] ? 'text-yellow-400' : ''}`}
                >
                  <path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z" />
                </svg>
              </button>
            </div>
          </div>

          <div className="h-[60px] mb-4">
            <div className="flex items-start">
              {agent.avatarUrl && (
                <div className="mr-3 flex-shrink-0 h-10 w-10 min-w-[40px] overflow-hidden rounded-full bg-gray-100 dark:bg-gray-800">
                  <Image
                    src={agent.avatarUrl}
                    alt={`${agent.name} icon`}
                    width={40}
                    height={40}
                    className="h-full w-full object-cover"
                  />
                </div>
              )}
              <h3 className="text-xl font-bold text-gray-900 dark:text-white self-start pt-1 line-clamp-2">{agent.name}</h3>
            </div>
          </div>

          <div className="h-[72px] mb-4">
            <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-3">{agent.description}</p>
          </div>

          <div className="mt-auto pt-4 flex items-center justify-between">
            <div className="flex flex-col">
              <span className="text-xs text-gray-500 dark:text-gray-500">
                {agent.usageCount.toLocaleString()} uses
              </span>
              <span className="text-xs text-gray-500 dark:text-gray-500">
                Updated {new Date(agent.updatedAt).toLocaleDateString()}
              </span>
            </div>
            <Link
              href={`/agent/${agent.id}`}
              className="rounded-md bg-blue-600 px-3 py-1.5 text-sm font-medium text-white hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800"
              onClick={() => handleAgentClick(agent.id)}
            >
              View Agent
            </Link>
          </div>
        </div>
      ))}
    </div>
  );
}
