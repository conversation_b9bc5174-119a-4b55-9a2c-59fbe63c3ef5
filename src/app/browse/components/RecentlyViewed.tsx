'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Agent } from '@/types/agent';
import { getRecentlyViewedAgentIds } from '@/lib/userPreferences';
import { getAgentsByIds } from '@/lib/agents';

export default function RecentlyViewed() {
  const [recentAgents, setRecentAgents] = useState<Agent[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Get recently viewed agent IDs
    const recentIds = getRecentlyViewedAgentIds();
    if (recentIds.length === 0) {
      setIsLoading(false);
      return;
    }

    // Get agent details for each ID
    const agents = getAgentsByIds(recentIds);
    setRecentAgents(agents);
    setIsLoading(false);
  }, []);

  if (isLoading) {
    return (
      <div className="mb-8 rounded-lg border border-gray-200 bg-white p-6 dark:border-gray-800 dark:bg-gray-900">
        <h2 className="mb-4 text-xl font-bold text-gray-900 dark:text-white">Recently Viewed</h2>
        <div className="flex justify-center py-4">
          <div className="h-6 w-6 animate-spin rounded-full border-b-2 border-t-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  if (recentAgents.length === 0) {
    return null; // Don't show the section if there are no recently viewed agents
  }

  return (
    <div className="mb-8 rounded-lg border border-gray-200 bg-white p-6 dark:border-gray-800 dark:bg-gray-900">
      <h2 className="mb-4 text-xl font-bold text-gray-900 dark:text-white">Recently Viewed</h2>
      <div className="space-y-4">
        {recentAgents.slice(0, 3).map((agent) => (
          <Link
            key={agent.id}
            href={`/agent/${agent.id}`}
            className="flex items-center rounded-lg border border-gray-200 p-3 transition-colors hover:bg-gray-50 dark:border-gray-700 dark:hover:bg-gray-800"
          >
            {agent.avatarUrl && (
              <div className="mr-3 h-10 w-10 flex-shrink-0 overflow-hidden rounded-full bg-gray-100 dark:bg-gray-800">
                <Image
                  src={agent.avatarUrl}
                  alt={`${agent.name} icon`}
                  width={40}
                  height={40}
                  className="h-full w-full object-cover"
                />
              </div>
            )}
            <div>
              <div className="flex items-center">
                <h3 className="font-medium text-gray-900 dark:text-white">{agent.name}</h3>
                {agent.isNew && (
                  <span className="ml-2 rounded-full bg-green-100 px-2 py-0.5 text-xs font-medium text-green-800 dark:bg-green-900/30 dark:text-green-400">
                    New
                  </span>
                )}
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400">{agent.category}</p>
            </div>
          </Link>
        ))}

        {recentAgents.length > 3 && (
          <Link
            href="/recent"
            className="block text-center text-sm text-blue-600 hover:underline dark:text-blue-400"
          >
            View all {recentAgents.length} recently viewed agents
          </Link>
        )}
      </div>
    </div>
  );
}
