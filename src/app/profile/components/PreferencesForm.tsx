'use client';

import React, { useState, useEffect } from 'react';
import { useUserPreferences } from '@/hooks/useUserPreferences';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCheck, faSpinner } from '@fortawesome/free-solid-svg-icons';

export default function PreferencesForm() {
  const {
    getUserPreferences,
    updateDefaultView,
    updatePageSize,
    toggleDarkMode,
  } = useUserPreferences();
  
  const [defaultView, setDefaultView] = useState<'grid' | 'list'>('grid');
  const [pageSize, setPageSize] = useState(12);
  const [darkMode, setDarkMode] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [success, setSuccess] = useState<string | null>(null);
  
  // Load user preferences
  useEffect(() => {
    const preferences = getUserPreferences();
    setDefaultView(preferences.defaultView);
    setPageSize(preferences.pageSize);
    setDarkMode(preferences.theme === 'dark');
  }, [getUserPreferences]);
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    setIsSubmitting(true);
    setSuccess(null);
    
    try {
      // Update preferences
      updateDefaultView(defaultView);
      updatePageSize(pageSize);
      
      // Show success message
      setSuccess('Preferences updated successfully');
      
      // Hide success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (error) {
      console.error('Error updating preferences:', error);
    } finally {
      setIsSubmitting(false);
    }
  };
  
  return (
    <div className="mb-8 rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-800 dark:bg-gray-900">
      <h2 className="mb-4 text-xl font-bold text-gray-900 dark:text-white">
        Preferences
      </h2>
      
      {success && (
        <div className="mb-4 rounded-md bg-green-50 p-4 dark:bg-green-900/30">
          <div className="flex">
            <FontAwesomeIcon
              icon={faCheck}
              className="h-5 w-5 text-green-400"
            />
            <div className="ml-3">
              <p className="text-sm text-green-700 dark:text-green-300">{success}</p>
            </div>
          </div>
        </div>
      )}
      
      <form onSubmit={handleSubmit}>
        <div className="mb-4">
          <label
            htmlFor="defaultView"
            className="block text-sm font-medium text-gray-700 dark:text-gray-300"
          >
            Default View
          </label>
          <select
            id="defaultView"
            value={defaultView}
            onChange={(e) => setDefaultView(e.target.value as 'grid' | 'list')}
            className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-blue-500 focus:outline-none focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white sm:text-sm"
          >
            <option value="grid">Grid</option>
            <option value="list">List</option>
          </select>
          <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
            Choose how agents are displayed on the browse page
          </p>
        </div>
        
        <div className="mb-4">
          <label
            htmlFor="pageSize"
            className="block text-sm font-medium text-gray-700 dark:text-gray-300"
          >
            Items Per Page
          </label>
          <select
            id="pageSize"
            value={pageSize}
            onChange={(e) => setPageSize(Number(e.target.value))}
            className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-blue-500 focus:outline-none focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white sm:text-sm"
          >
            <option value="6">6</option>
            <option value="12">12</option>
            <option value="24">24</option>
            <option value="48">48</option>
          </select>
          <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
            Number of agents to display per page
          </p>
        </div>
        
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <label
              htmlFor="darkMode"
              className="block text-sm font-medium text-gray-700 dark:text-gray-300"
            >
              Dark Mode
            </label>
            <div className="relative inline-block h-6 w-11 flex-shrink-0">
              <input
                type="checkbox"
                id="darkMode"
                checked={darkMode}
                onChange={() => setDarkMode(!darkMode)}
                className="peer sr-only"
              />
              <span className="absolute inset-0 cursor-pointer rounded-full bg-gray-300 transition peer-checked:bg-blue-600 dark:bg-gray-600 dark:peer-checked:bg-blue-700"></span>
              <span className="absolute inset-y-0 left-0 ml-1 flex h-5 w-5 translate-y-0.5 items-center justify-center rounded-full bg-white transition peer-checked:translate-x-5 dark:bg-gray-200"></span>
            </div>
          </div>
          <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
            Enable dark mode for the entire application
          </p>
        </div>
        
        <div className="flex justify-end">
          <button
            type="submit"
            disabled={isSubmitting}
            className="inline-flex items-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-75 dark:bg-blue-700 dark:hover:bg-blue-800"
          >
            {isSubmitting ? (
              <>
                <FontAwesomeIcon icon={faSpinner} className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              'Save Preferences'
            )}
          </button>
        </div>
      </form>
    </div>
  );
}
