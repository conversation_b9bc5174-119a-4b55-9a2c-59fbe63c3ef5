'use client';

import React from 'react';
import Image from 'next/image';
import { User } from '@/types/auth';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faUser, faCamera } from '@fortawesome/free-solid-svg-icons';

interface ProfileHeaderProps {
  user: User;
}

export default function ProfileHeader({ user }: ProfileHeaderProps) {
  return (
    <div className="mb-8 rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-800 dark:bg-gray-900">
      <div className="flex flex-col items-center space-y-4 sm:flex-row sm:space-x-6 sm:space-y-0">
        <div className="relative">
          <div className="relative h-24 w-24 overflow-hidden rounded-full bg-gray-200 dark:bg-gray-700">
            {user.image ? (
              <Image
                src={user.image}
                alt={user.name}
                width={96}
                height={96}
                className="h-full w-full object-cover"
              />
            ) : (
              <FontAwesomeIcon
                icon={faUser}
                className="h-full w-full p-4 text-gray-400"
              />
            )}
          </div>
          <button
            type="button"
            className="absolute bottom-0 right-0 rounded-full bg-blue-600 p-2 text-white hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800"
            aria-label="Change profile picture"
          >
            <FontAwesomeIcon icon={faCamera} className="h-4 w-4" />
          </button>
        </div>
        
        <div className="text-center sm:text-left">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            {user.name}
          </h1>
          <p className="text-gray-600 dark:text-gray-400">{user.email}</p>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-500">
            Member since {new Date(user.createdAt).toLocaleDateString()}
          </p>
        </div>
      </div>
    </div>
  );
}
