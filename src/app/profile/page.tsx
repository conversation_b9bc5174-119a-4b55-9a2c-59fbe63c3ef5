'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { User } from '@/types/auth';
import ProfileHeader from './components/ProfileHeader';
import ProfileForm from './components/ProfileForm';
import PasswordForm from './components/PasswordForm';
import PreferencesForm from './components/PreferencesForm';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSpinner } from '@fortawesome/free-solid-svg-icons';

export default function ProfilePage() {
  const { user, isLoading } = useAuth();
  const router = useRouter();

  const [profileUser, setProfileUser] = useState<User | null>(null);
  const [profileError, setProfileError] = useState<string | null>(null);

  useEffect(() => {
    // Redirect to login if not authenticated
    if (!isLoading && !user) {
      router.push('/auth/login?callbackUrl=/profile');
      return;
    }

    // Set user from auth context
    if (user) {
      setProfileUser(user);
    }
  }, [user, isLoading, router]);

  // For debugging
  useEffect(() => {
    console.log('Profile page user:', user);
    console.log('Profile page profileUser:', profileUser);
  }, [user, profileUser]);

  const handleProfileUpdate = (updatedUser: User) => {
    setProfileUser(updatedUser);
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex h-64 items-center justify-center">
          <FontAwesomeIcon icon={faSpinner} className="h-8 w-8 animate-spin text-blue-600" />
        </div>
      </div>
    );
  }

  // Show error state
  if (profileError) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="rounded-lg border border-red-200 bg-red-50 p-6 text-center dark:border-red-800 dark:bg-red-900/30">
          <h2 className="mb-2 text-lg font-semibold text-red-800 dark:text-red-200">
            Error
          </h2>
          <p className="text-red-700 dark:text-red-300">{profileError}</p>
        </div>
      </div>
    );
  }

  // Show profile if user is loaded
  if (profileUser) {
    return (
      <div className="container mx-auto px-4 py-8">
        <h1 className="mb-6 text-2xl font-bold text-gray-900 dark:text-white md:text-3xl">
          My Profile
        </h1>

        <ProfileHeader user={profileUser} />

        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          <div>
            <ProfileForm user={profileUser} onUpdate={handleProfileUpdate} />
            <PasswordForm />
          </div>
          <div>
            <PreferencesForm />
          </div>
        </div>
      </div>
    );
  }

  // Fallback
  return null;
}
