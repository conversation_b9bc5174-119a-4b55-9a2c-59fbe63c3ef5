'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Agent } from '@/types/agent';
import { UsageMetric } from '@/types/metrics';
import { isAgentFavorited, toggleFavoriteAgent, addToRecentlyViewed } from '@/lib/userPreferences';
import { addCustomEventListener, EVENTS } from '@/lib/events';

interface PopularAgentCardProps {
  agent: Agent;
  metrics: UsageMetric;
  rank: number;
}

export default function PopularAgentCard({ agent, metrics, rank }: PopularAgentCardProps) {
  const [isFavorite, setIsFavorite] = useState(false);
  
  // Load favorite status
  useEffect(() => {
    if (typeof window === 'undefined') return;
    
    const loadFavoriteStatus = () => {
      setIsFavorite(isAgentFavorited(agent.id));
    };
    
    loadFavoriteStatus();
    
    // Listen for favorites updates
    const removeListener = addCustomEventListener(EVENTS.FAVORITES_UPDATED, (event) => {
      const { agentId, isFavorite } = event.detail;
      if (agentId === agent.id) {
        setIsFavorite(isFavorite);
      }
    });
    
    return () => {
      removeListener();
    };
  }, [agent.id]);
  
  // Handle favorite toggle
  const handleFavoriteToggle = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    const newStatus = toggleFavoriteAgent(agent.id);
    setIsFavorite(newStatus);
  };
  
  // Handle agent click to track recently viewed
  const handleAgentClick = () => {
    addToRecentlyViewed(agent.id);
  };
  
  return (
    <div className="flex rounded-lg border border-gray-200 bg-white p-4 shadow-sm transition-shadow hover:shadow-md dark:border-gray-800 dark:bg-gray-900">
      <div className="mr-4 flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-blue-100 text-xl font-bold text-blue-600 dark:bg-blue-900/30 dark:text-blue-400">
        #{rank}
      </div>
      
      <div className="flex flex-1 flex-col">
        <div className="mb-2 flex items-start justify-between">
          <div className="flex items-center">
            {agent.avatarUrl && (
              <div className="mr-3 h-10 w-10 flex-shrink-0 overflow-hidden rounded-full bg-gray-100 dark:bg-gray-800">
                <Image
                  src={agent.avatarUrl}
                  alt={`${agent.name} icon`}
                  width={40}
                  height={40}
                  className="h-full w-full object-cover"
                />
              </div>
            )}
            <div>
              <h3 className="font-bold text-gray-900 dark:text-white">{agent.name}</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">{agent.category}</p>
            </div>
          </div>
          
          <button
            onClick={handleFavoriteToggle}
            className="rounded-full p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-600 dark:hover:bg-gray-800 dark:hover:text-gray-300"
            aria-label={isFavorite ? 'Remove from favorites' : 'Add to favorites'}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill={isFavorite ? 'currentColor' : 'none'}
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className={`h-5 w-5 ${isFavorite ? 'text-yellow-400' : ''}`}
            >
              <path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z" />
            </svg>
          </button>
        </div>
        
        <div className="mb-3 grid grid-cols-3 gap-2 text-center">
          <div>
            <p className="text-xs text-gray-500 dark:text-gray-400">Uses</p>
            <p className="font-semibold text-gray-900 dark:text-white">{metrics.totalUses.toLocaleString()}</p>
          </div>
          <div>
            <p className="text-xs text-gray-500 dark:text-gray-400">Rating</p>
            <p className="font-semibold text-gray-900 dark:text-white">{metrics.averageRating.toFixed(1)}/5</p>
          </div>
          <div>
            <p className="text-xs text-gray-500 dark:text-gray-400">Completion</p>
            <p className="font-semibold text-gray-900 dark:text-white">{(metrics.completionRate * 100).toFixed(0)}%</p>
          </div>
        </div>
        
        <div className="mt-auto flex items-center justify-between">
          <div className="text-sm text-gray-500 dark:text-gray-400">
            Popularity Score: {metrics.popularityScore.toFixed(0)}/100
          </div>
          <Link
            href={`/agent/${agent.id}`}
            className="rounded-md bg-blue-600 px-3 py-1 text-sm font-medium text-white hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800"
            onClick={handleAgentClick}
          >
            View Agent
          </Link>
        </div>
      </div>
    </div>
  );
}
