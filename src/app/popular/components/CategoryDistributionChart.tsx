'use client';

import React from 'react';
import { CategoryDistribution } from '@/types/metrics';

interface CategoryDistributionChartProps {
  data: CategoryDistribution[];
  title: string;
}

export default function CategoryDistributionChart({ data, title }: CategoryDistributionChartProps) {
  // Generate colors for each category
  const colors = [
    'bg-blue-500 dark:bg-blue-600',
    'bg-green-500 dark:bg-green-600',
    'bg-purple-500 dark:bg-purple-600',
    'bg-yellow-500 dark:bg-yellow-600',
    'bg-red-500 dark:bg-red-600',
    'bg-indigo-500 dark:bg-indigo-600',
    'bg-pink-500 dark:bg-pink-600',
    'bg-teal-500 dark:bg-teal-600',
  ];
  
  return (
    <div className="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900">
      <h3 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">{title}</h3>
      
      <div className="mb-4 h-48 w-full">
        <div className="relative h-full w-full">
          <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 transform">
            <div className="h-32 w-32 rounded-full border-8 border-gray-100 dark:border-gray-800"></div>
          </div>
          
          {/* Pie chart segments */}
          <svg viewBox="0 0 100 100" className="h-full w-full">
            <circle cx="50" cy="50" r="40" fill="transparent" stroke="#e5e7eb" strokeWidth="20" />
            
            {data.map((category, index) => {
              // Calculate the segment's start and end angles
              const totalPercentage = data.reduce((sum, item) => sum + item.percentage, 0);
              const startPercentage = data.slice(0, index).reduce((sum, item) => sum + item.percentage, 0);
              const endPercentage = startPercentage + category.percentage;
              
              const startAngle = (startPercentage / totalPercentage) * 360;
              const endAngle = (endPercentage / totalPercentage) * 360;
              
              // Convert angles to radians
              const startRad = (startAngle - 90) * (Math.PI / 180);
              const endRad = (endAngle - 90) * (Math.PI / 180);
              
              // Calculate the SVG arc path
              const x1 = 50 + 40 * Math.cos(startRad);
              const y1 = 50 + 40 * Math.sin(startRad);
              const x2 = 50 + 40 * Math.cos(endRad);
              const y2 = 50 + 40 * Math.sin(endRad);
              
              // Determine if the arc should be drawn the long way around
              const largeArcFlag = endAngle - startAngle <= 180 ? "0" : "1";
              
              // Create the SVG path
              const d = `M 50 50 L ${x1} ${y1} A 40 40 0 ${largeArcFlag} 1 ${x2} ${y2} Z`;
              
              return (
                <path
                  key={index}
                  d={d}
                  fill={colors[index % colors.length].split(' ')[0]}
                  className="hover:opacity-80"
                />
              );
            })}
          </svg>
        </div>
      </div>
      
      <div className="space-y-2">
        {data.map((category, index) => (
          <div key={index} className="flex items-center justify-between">
            <div className="flex items-center">
              <div className={`mr-2 h-3 w-3 rounded-full ${colors[index % colors.length].split(' ')[0]}`}></div>
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                {category.category}
              </span>
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              {category.percentage.toFixed(1)}% ({category.count})
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
