'use client';

import React from 'react';
import { TopQuery } from '@/types/metrics';

interface TopQueriesProps {
  data: TopQuery[];
  title: string;
}

export default function TopQueries({ data, title }: TopQueriesProps) {
  // Find the maximum count for scaling
  const maxCount = Math.max(...data.map(item => item.count));
  
  return (
    <div className="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900">
      <h3 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">{title}</h3>
      
      <div className="space-y-3">
        {data.map((item, index) => {
          const percentage = (item.count / maxCount) * 100;
          
          return (
            <div key={index} className="space-y-1">
              <div className="flex items-center justify-between">
                <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {item.query}
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  {item.count} queries
                </div>
              </div>
              <div className="relative h-2 w-full rounded-full bg-gray-200 dark:bg-gray-700">
                <div
                  className="absolute left-0 top-0 h-full rounded-full bg-blue-500 dark:bg-blue-600"
                  style={{ width: `${percentage}%` }}
                ></div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
