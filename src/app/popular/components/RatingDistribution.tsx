'use client';

import React from 'react';
import { UserFeedback } from '@/types/metrics';

interface RatingDistributionProps {
  data: UserFeedback[];
  title: string;
}

export default function RatingDistribution({ data, title }: RatingDistributionProps) {
  // Calculate total ratings
  const totalRatings = data.reduce((sum, item) => sum + item.count, 0);
  
  // Calculate average rating
  const weightedSum = data.reduce((sum, item) => sum + (item.rating * item.count), 0);
  const averageRating = totalRatings > 0 ? (weightedSum / totalRatings).toFixed(1) : '0.0';
  
  return (
    <div className="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900">
      <div className="mb-4 flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{title}</h3>
        <div className="flex items-center">
          <div className="mr-1 text-xl font-bold text-yellow-500">{averageRating}</div>
          <div className="text-sm text-gray-500 dark:text-gray-400">/ 5</div>
        </div>
      </div>
      
      <div className="space-y-3">
        {[5, 4, 3, 2, 1].map(rating => {
          const ratingData = data.find(item => item.rating === rating);
          const count = ratingData ? ratingData.count : 0;
          const percentage = totalRatings > 0 ? (count / totalRatings) * 100 : 0;
          
          return (
            <div key={rating} className="flex items-center">
              <div className="mr-2 w-3 text-sm font-medium text-gray-700 dark:text-gray-300">
                {rating}
              </div>
              <div className="relative h-4 flex-1 rounded-full bg-gray-200 dark:bg-gray-700">
                <div
                  className="absolute left-0 top-0 h-full rounded-full bg-yellow-500"
                  style={{ width: `${percentage}%` }}
                ></div>
              </div>
              <div className="ml-2 w-12 text-right text-xs text-gray-500 dark:text-gray-400">
                {percentage.toFixed(1)}%
              </div>
            </div>
          );
        })}
      </div>
      
      <div className="mt-4 text-center text-sm text-gray-500 dark:text-gray-400">
        Based on {totalRatings} ratings
      </div>
    </div>
  );
}
