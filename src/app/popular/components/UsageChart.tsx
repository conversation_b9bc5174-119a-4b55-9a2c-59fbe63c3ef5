'use client';

import React from 'react';
import { UsageOverTime } from '@/types/metrics';

interface UsageChartProps {
  data: UsageOverTime[];
  title: string;
  height?: number;
}

export default function UsageChart({ data, title, height = 200 }: UsageChartProps) {
  // Find the maximum value for scaling
  const maxValue = Math.max(...data.map(item => item.count));
  
  // Calculate the width of each bar based on the number of data points
  const barWidth = 100 / data.length;
  
  return (
    <div className="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900">
      <h3 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">{title}</h3>
      
      <div style={{ height: `${height}px` }} className="relative w-full">
        {/* Y-axis labels */}
        <div className="absolute bottom-0 left-0 top-0 flex flex-col justify-between text-xs text-gray-500 dark:text-gray-400">
          <span>100%</span>
          <span>75%</span>
          <span>50%</span>
          <span>25%</span>
          <span>0%</span>
        </div>
        
        {/* Chart area */}
        <div className="absolute bottom-0 left-6 right-0 top-0">
          {/* Horizontal grid lines */}
          <div className="absolute bottom-0 left-0 right-0 top-0 flex flex-col justify-between">
            <div className="h-px w-full bg-gray-200 dark:bg-gray-700"></div>
            <div className="h-px w-full bg-gray-200 dark:bg-gray-700"></div>
            <div className="h-px w-full bg-gray-200 dark:bg-gray-700"></div>
            <div className="h-px w-full bg-gray-200 dark:bg-gray-700"></div>
            <div className="h-px w-full bg-gray-200 dark:bg-gray-700"></div>
          </div>
          
          {/* Bars */}
          <div className="absolute bottom-0 left-0 right-0 flex h-full items-end">
            {data.map((item, index) => {
              const height = (item.count / maxValue) * 100;
              
              return (
                <div
                  key={index}
                  className="group flex flex-col items-center"
                  style={{ width: `${barWidth}%` }}
                >
                  <div
                    className="w-4/5 rounded-t bg-blue-500 transition-all group-hover:bg-blue-600 dark:bg-blue-600 dark:group-hover:bg-blue-700"
                    style={{ height: `${height}%` }}
                  ></div>
                  
                  {/* Tooltip */}
                  <div className="invisible absolute bottom-full mb-2 rounded bg-gray-800 p-2 text-xs text-white opacity-0 transition-opacity group-hover:visible group-hover:opacity-100 dark:bg-gray-700">
                    <div className="font-semibold">{new Date(item.date).toLocaleDateString()}</div>
                    <div>{item.count} uses</div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
      
      {/* X-axis labels */}
      <div className="mt-2 flex justify-between text-xs text-gray-500 dark:text-gray-400">
        <span>{new Date(data[0].date).toLocaleDateString()}</span>
        <span>{new Date(data[Math.floor(data.length / 2)].date).toLocaleDateString()}</span>
        <span>{new Date(data[data.length - 1].date).toLocaleDateString()}</span>
      </div>
    </div>
  );
}
