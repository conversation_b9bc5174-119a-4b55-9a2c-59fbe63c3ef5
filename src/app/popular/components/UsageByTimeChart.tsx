'use client';

import React from 'react';
import { UsageByTime } from '@/types/metrics';

interface UsageByTimeChartProps {
  data: UsageByTime[];
  title: string;
}

export default function UsageByTimeChart({ data, title }: UsageByTimeChartProps) {
  // Find the maximum value for scaling
  const maxValue = Math.max(...data.map(item => item.count));
  
  // Format hour for display
  const formatHour = (hour: number): string => {
    if (hour === 0) return '12 AM';
    if (hour === 12) return '12 PM';
    return hour < 12 ? `${hour} AM` : `${hour - 12} PM`;
  };
  
  return (
    <div className="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900">
      <h3 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">{title}</h3>
      
      <div className="h-48 w-full">
        <div className="relative h-full w-full">
          {/* Y-axis grid lines */}
          <div className="absolute bottom-0 left-0 right-0 top-0 flex flex-col justify-between">
            {[0, 1, 2, 3, 4].map((_, index) => (
              <div key={index} className="h-px w-full bg-gray-200 dark:bg-gray-700"></div>
            ))}
          </div>
          
          {/* Bars */}
          <div className="absolute bottom-0 left-0 right-0 flex h-full items-end">
            {data.map((item, index) => {
              const height = (item.count / maxValue) * 100;
              
              // Determine color based on time of day
              let colorClass = 'bg-blue-500 group-hover:bg-blue-600 dark:bg-blue-600 dark:group-hover:bg-blue-700';
              
              // Work hours (9 AM - 5 PM)
              if (item.hour >= 9 && item.hour < 17) {
                colorClass = 'bg-green-500 group-hover:bg-green-600 dark:bg-green-600 dark:group-hover:bg-green-700';
              }
              // Evening (5 PM - 10 PM)
              else if (item.hour >= 17 && item.hour < 22) {
                colorClass = 'bg-purple-500 group-hover:bg-purple-600 dark:bg-purple-600 dark:group-hover:bg-purple-700';
              }
              // Night (10 PM - 6 AM)
              else if (item.hour >= 22 || item.hour < 6) {
                colorClass = 'bg-indigo-500 group-hover:bg-indigo-600 dark:bg-indigo-600 dark:group-hover:bg-indigo-700';
              }
              
              return (
                <div
                  key={index}
                  className="group flex flex-1 flex-col items-center"
                >
                  <div
                    className={`w-4/5 rounded-t transition-all ${colorClass}`}
                    style={{ height: `${height}%` }}
                  ></div>
                  
                  {/* Tooltip */}
                  <div className="invisible absolute bottom-full mb-2 rounded bg-gray-800 p-2 text-xs text-white opacity-0 transition-opacity group-hover:visible group-hover:opacity-100 dark:bg-gray-700">
                    <div className="font-semibold">{formatHour(item.hour)}</div>
                    <div>{item.count} uses</div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
      
      {/* X-axis labels */}
      <div className="mt-2 flex justify-between text-xs text-gray-500 dark:text-gray-400">
        {[0, 6, 12, 18, 23].map(hour => (
          <span key={hour}>{formatHour(hour)}</span>
        ))}
      </div>
    </div>
  );
}
