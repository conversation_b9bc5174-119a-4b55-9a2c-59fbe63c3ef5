'use client';

import React from 'react';
import { UsageByDay } from '@/types/metrics';

interface UsageByDayChartProps {
  data: UsageByDay[];
  title: string;
}

export default function UsageByDayChart({ data, title }: UsageByDayChartProps) {
  // Find the maximum value for scaling
  const maxValue = Math.max(...data.map(item => item.count));
  
  // Sort days in correct order
  const daysOrder = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
  const sortedData = [...data].sort((a, b) => 
    daysOrder.indexOf(a.day) - daysOrder.indexOf(b.day)
  );
  
  return (
    <div className="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900">
      <h3 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">{title}</h3>
      
      <div className="h-48 w-full">
        <div className="relative h-full w-full">
          {/* Y-axis grid lines */}
          <div className="absolute bottom-0 left-0 right-0 top-0 flex flex-col justify-between">
            {[0, 1, 2, 3, 4].map((_, index) => (
              <div key={index} className="h-px w-full bg-gray-200 dark:bg-gray-700"></div>
            ))}
          </div>
          
          {/* Bars */}
          <div className="absolute bottom-0 left-0 right-0 flex h-full items-end justify-between">
            {sortedData.map((item, index) => {
              const height = (item.count / maxValue) * 100;
              
              // Weekend days get a different color
              const isWeekend = item.day === 'Saturday' || item.day === 'Sunday';
              const colorClass = isWeekend 
                ? 'bg-purple-500 group-hover:bg-purple-600 dark:bg-purple-600 dark:group-hover:bg-purple-700'
                : 'bg-blue-500 group-hover:bg-blue-600 dark:bg-blue-600 dark:group-hover:bg-blue-700';
              
              return (
                <div
                  key={index}
                  className="group flex w-10 flex-col items-center"
                >
                  <div
                    className={`w-8 rounded-t transition-all ${colorClass}`}
                    style={{ height: `${height}%` }}
                  ></div>
                  
                  {/* Tooltip */}
                  <div className="invisible absolute bottom-full mb-2 rounded bg-gray-800 p-2 text-xs text-white opacity-0 transition-opacity group-hover:visible group-hover:opacity-100 dark:bg-gray-700">
                    <div className="font-semibold">{item.day}</div>
                    <div>{item.count} uses</div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
      
      {/* X-axis labels */}
      <div className="mt-2 flex justify-between text-xs text-gray-500 dark:text-gray-400">
        {sortedData.map((item, index) => (
          <span key={index}>{item.day.substring(0, 3)}</span>
        ))}
      </div>
    </div>
  );
}
