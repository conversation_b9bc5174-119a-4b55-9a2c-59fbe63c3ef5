'use client';

import React, { useState, useEffect } from 'react';
import { 
  getPopularAgents, 
  getPlatformUsageOverTime, 
  getCategoryDistribution,
  getTopAgentsByMetric
} from '@/lib/metrics';
import { getAllAgents } from '@/lib/agents';
import { TimeRange } from '@/types/metrics';
import UsageChart from './components/UsageChart';
import RatingDistribution from './components/RatingDistribution';
import TopQueries from './components/TopQueries';
import UsageByTimeChart from './components/UsageByTimeChart';
import UsageByDayChart from './components/UsageByDayChart';
import MetricCard from './components/MetricCard';
import PopularAgentCard from './components/PopularAgentCard';
import CategoryDistributionChart from './components/CategoryDistributionChart';

export default function PopularPage() {
  const [timeRange, setTimeRange] = useState<TimeRange>('month');
  const [isLoading, setIsLoading] = useState(true);
  
  // Load data
  const [popularAgents, setPopularAgents] = useState(getPopularAgents(5));
  const [platformUsage, setPlatformUsage] = useState(getPlatformUsageOverTime(30));
  const [categoryDistribution, setCategoryDistribution] = useState(getCategoryDistribution());
  
  // Set loading state
  useEffect(() => {
    setIsLoading(false);
  }, []);
  
  // Handle time range change
  const handleTimeRangeChange = (range: TimeRange) => {
    setTimeRange(range);
    
    // Update platform usage data based on time range
    let days = 30;
    switch (range) {
      case 'day':
        days = 1;
        break;
      case 'week':
        days = 7;
        break;
      case 'month':
        days = 30;
        break;
      case 'year':
        days = 365;
        break;
    }
    
    setPlatformUsage(getPlatformUsageOverTime(days));
  };
  
  // Calculate total platform metrics
  const totalAgents = getAllAgents().length;
  const totalUses = popularAgents.reduce((sum, agent) => sum + agent.usageMetrics.totalUses, 0);
  const averageRating = popularAgents.reduce((sum, agent) => sum + agent.usageMetrics.averageRating, 0) / popularAgents.length;
  const averageCompletionRate = popularAgents.reduce((sum, agent) => sum + agent.usageMetrics.completionRate, 0) / popularAgents.length;
  
  // Render loading state
  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <h1 className="mb-6 text-2xl font-bold text-gray-900 dark:text-white md:text-3xl">Popular Agents</h1>
        <div className="flex h-64 items-center justify-center">
          <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-blue-600"></div>
        </div>
      </div>
    );
  }
  
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6 flex flex-col justify-between gap-4 sm:flex-row sm:items-center">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white md:text-3xl">Popular Agents</h1>
        
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-500 dark:text-gray-400">Time Range:</span>
          <div className="rounded-md border border-gray-300 dark:border-gray-600">
            {(['day', 'week', 'month', 'year'] as TimeRange[]).map((range) => (
              <button
                key={range}
                onClick={() => handleTimeRangeChange(range)}
                className={`px-3 py-1.5 text-sm font-medium ${
                  timeRange === range
                    ? 'bg-blue-600 text-white dark:bg-blue-700'
                    : 'bg-white text-gray-700 hover:bg-gray-50 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700'
                }`}
              >
                {range.charAt(0).toUpperCase() + range.slice(1)}
              </button>
            ))}
          </div>
        </div>
      </div>
      
      {/* Overview metrics */}
      <div className="mb-8 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
        <MetricCard
          title="Total Agents"
          value={totalAgents}
          change={8.5}
          icon={
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="h-6 w-6"
            >
              <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
              <circle cx="9" cy="7" r="4" />
              <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
              <path d="M16 3.13a4 4 0 0 1 0 7.75" />
            </svg>
          }
        />
        
        <MetricCard
          title="Total Uses"
          value={totalUses.toLocaleString()}
          change={12.3}
          icon={
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="h-6 w-6"
            >
              <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z" />
              <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z" />
            </svg>
          }
        />
        
        <MetricCard
          title="Average Rating"
          value={`${averageRating.toFixed(1)}/5`}
          change={3.2}
          icon={
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="h-6 w-6"
            >
              <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2" />
            </svg>
          }
        />
        
        <MetricCard
          title="Completion Rate"
          value={`${(averageCompletionRate * 100).toFixed(0)}%`}
          change={1.8}
          icon={
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="h-6 w-6"
            >
              <polyline points="20 6 9 17 4 12" />
            </svg>
          }
        />
      </div>
      
      {/* Usage chart */}
      <div className="mb-8">
        <UsageChart
          data={platformUsage}
          title={`Platform Usage (${timeRange.charAt(0).toUpperCase() + timeRange.slice(1)})`}
          height={300}
        />
      </div>
      
      {/* Popular agents */}
      <h2 className="mb-4 text-xl font-bold text-gray-900 dark:text-white">Top 5 Agents</h2>
      <div className="mb-8 grid grid-cols-1 gap-4">
        {popularAgents.map((agent, index) => (
          <PopularAgentCard
            key={agent.id}
            agent={getAllAgents().find(a => a.id === agent.id)!}
            metrics={agent.usageMetrics}
            rank={index + 1}
          />
        ))}
      </div>
      
      {/* Usage patterns */}
      <h2 className="mb-4 text-xl font-bold text-gray-900 dark:text-white">Usage Patterns</h2>
      <div className="mb-8 grid grid-cols-1 gap-6 md:grid-cols-2">
        <UsageByTimeChart
          data={popularAgents[0].usageByTime}
          title="Usage by Time of Day"
        />
        
        <UsageByDayChart
          data={popularAgents[0].usageByDay}
          title="Usage by Day of Week"
        />
      </div>
      
      {/* Additional metrics */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
        <CategoryDistributionChart
          data={categoryDistribution}
          title="Agent Categories"
        />
        
        <RatingDistribution
          data={popularAgents[0].userFeedback}
          title="User Ratings"
        />
        
        <TopQueries
          data={popularAgents[0].topQueries}
          title="Top Queries"
        />
      </div>
    </div>
  );
}
