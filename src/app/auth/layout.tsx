'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faRobot } from '@fortawesome/free-solid-svg-icons';

export default function AuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="flex min-h-screen flex-col bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto flex flex-1 items-center justify-center px-4 py-12 sm:px-6 lg:px-8">
        <div className="w-full max-w-md space-y-8">
          <div className="text-center">
            <Link href="/" className="inline-block">
              <div className="flex items-center justify-center">
                <div className="mr-2 flex h-10 w-10 items-center justify-center rounded-full bg-blue-600 text-white">
                  <FontAwesomeIcon icon={faRobot} className="h-6 w-6" />
                </div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">AI Agent Hub</h1>
              </div>
            </Link>
            <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
              Your central platform for AI agent discovery and interaction
            </p>
          </div>
          
          <div className="rounded-lg border border-gray-200 bg-white p-8 shadow-md dark:border-gray-700 dark:bg-gray-800">
            {children}
          </div>
          
          <div className="text-center text-sm text-gray-500 dark:text-gray-400">
            <p>
              &copy; {new Date().getFullYear()} AI Agent Hub. All rights reserved.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
