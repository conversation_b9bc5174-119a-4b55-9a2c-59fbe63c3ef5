'use client';

import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import Link from 'next/link';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faArrowLeft,
  faUser,
  faSave,
  faTrash,
  faLock,
  faUnlock,
  faEnvelope,
  faIdCard,
  faBuilding,
  faMapMarkerAlt,
  faPhone,
  faShieldAlt
} from '@fortawesome/free-solid-svg-icons';
import { getUserById } from '@/data/mock-users';
import { User, UserRole, UserStatus } from '@/types/user';

export default function UserDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const userId = params.id as string;

  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'profile' | 'security' | 'activity'>('profile');

  // Load user data
  useEffect(() => {
    if (userId) {
      const userData = getUserById(userId);
      if (userData) {
        setUser(userData);
      }
      setIsLoading(false);
    }
  }, [userId]);

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // In a real app, this would save the user data to the database
    alert('User data saved successfully!');
  };

  // Handle user deletion
  const handleDelete = () => {
    // In a real app, this would delete the user from the database
    const confirmed = window.confirm('Are you sure you want to delete this user? This action cannot be undone.');
    if (confirmed) {
      alert('User deleted successfully!');
      router.push('/admin/users');
    }
  };

  // Handle user status change
  const handleStatusChange = (newStatus: UserStatus) => {
    if (!user) return;

    // In a real app, this would update the user status in the database
    setUser({
      ...user,
      status: newStatus
    });

    alert(`User status changed to ${newStatus}`);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="mb-4 h-12 w-12 animate-spin rounded-full border-b-2 border-t-2 border-indigo-600"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading user data...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <h2 className="mb-4 text-2xl font-bold">User Not Found</h2>
        <p className="mb-6 text-gray-600 dark:text-gray-400">The user you're looking for doesn't exist or has been removed.</p>
        <Link
          href="/admin/users"
          className="flex items-center rounded-md bg-indigo-600 px-4 py-2 text-white hover:bg-indigo-700"
        >
          <FontAwesomeIcon icon={faArrowLeft} className="mr-2 h-4 w-4" />
          Back to Users
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <Link
            href="/admin/users"
            className="mb-4 inline-flex items-center text-sm font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
          >
            <FontAwesomeIcon icon={faArrowLeft} className="mr-2 h-4 w-4" />
            Back to Users
          </Link>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            {user.profile.firstName} {user.profile.lastName}
          </h1>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            User ID: {user.id}
          </p>
        </div>

        <div className="flex space-x-3">
          {user.status === 'active' ? (
            <button
              onClick={() => handleStatusChange('suspended')}
              className="inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700"
            >
              <FontAwesomeIcon icon={faLock} className="mr-2 h-4 w-4" />
              Suspend User
            </button>
          ) : (
            <button
              onClick={() => handleStatusChange('active')}
              className="inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700"
            >
              <FontAwesomeIcon icon={faUnlock} className="mr-2 h-4 w-4" />
              Activate User
            </button>
          )}

          <button
            onClick={handleDelete}
            className="inline-flex items-center rounded-md border border-red-300 bg-white px-4 py-2 text-sm font-medium text-red-700 shadow-sm hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:border-red-700 dark:bg-gray-800 dark:text-red-400 dark:hover:bg-red-900/20"
          >
            <FontAwesomeIcon icon={faTrash} className="mr-2 h-4 w-4" />
            Delete User
          </button>

          <button
            form="user-form"
            type="submit"
            className="inline-flex items-center rounded-md bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:bg-indigo-500 dark:hover:bg-indigo-600"
          >
            <FontAwesomeIcon icon={faSave} className="mr-2 h-4 w-4" />
            Save Changes
          </button>
        </div>
      </div>

      {/* User info card */}
      <div className="overflow-hidden rounded-lg bg-white shadow dark:bg-gray-900">
        <div className="flex items-center space-x-6 border-b border-gray-200 px-6 py-4 dark:border-gray-800">
          <div className="h-24 w-24 flex-shrink-0">
            <img
              className="h-24 w-24 rounded-full"
              src={user.profile.avatarUrl || `https://ui-avatars.com/api/?name=${user.profile.firstName}+${user.profile.lastName}&background=random`}
              alt={`${user.profile.firstName} ${user.profile.lastName}`}
            />
          </div>
          <div>
            <h2 className="text-xl font-bold text-gray-900 dark:text-white">
              {user.profile.firstName} {user.profile.lastName}
            </h2>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {user.profile.jobTitle}{user.profile.department ? `, ${user.profile.department}` : ''}
            </p>
            <div className="mt-2 flex items-center space-x-2">
              <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                user.status === 'active'
                  ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                  : user.status === 'pending'
                    ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300'
                    : user.status === 'suspended'
                      ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                      : 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300'
              }`}>
                {user.status.charAt(0).toUpperCase() + user.status.slice(1)}
              </span>
              <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                user.role === 'admin'
                  ? 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300'
                  : user.role === 'manager'
                    ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'
                    : user.role === 'user'
                      ? 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-300'
                      : 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300'
              }`}>
                {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
              </span>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200 dark:border-gray-800">
          <nav className="-mb-px flex">
            <button
              className={`w-1/3 border-b-2 py-4 px-1 text-center text-sm font-medium ${
                activeTab === 'profile'
                  ? 'border-indigo-500 text-indigo-600 dark:border-indigo-400 dark:text-indigo-400'
                  : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 dark:text-gray-400 dark:hover:border-gray-700 dark:hover:text-gray-300'
              }`}
              onClick={() => setActiveTab('profile')}
            >
              Profile
            </button>
            <button
              className={`w-1/3 border-b-2 py-4 px-1 text-center text-sm font-medium ${
                activeTab === 'security'
                  ? 'border-indigo-500 text-indigo-600 dark:border-indigo-400 dark:text-indigo-400'
                  : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 dark:text-gray-400 dark:hover:border-gray-700 dark:hover:text-gray-300'
              }`}
              onClick={() => setActiveTab('security')}
            >
              Security & Permissions
            </button>
            <button
              className={`w-1/3 border-b-2 py-4 px-1 text-center text-sm font-medium ${
                activeTab === 'activity'
                  ? 'border-indigo-500 text-indigo-600 dark:border-indigo-400 dark:text-indigo-400'
                  : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 dark:text-gray-400 dark:hover:border-gray-700 dark:hover:text-gray-300'
              }`}
              onClick={() => setActiveTab('activity')}
            >
              Activity
            </button>
          </nav>
        </div>

        {/* Tab content */}
        <div className="p-6">
          {/* Profile Tab */}
          {activeTab === 'profile' && (
            <form id="user-form" onSubmit={handleSubmit} className="space-y-6">
              <div className="grid gap-6 md:grid-cols-2">
                {/* Basic Information */}
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white">Basic Information</h3>
                    <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">Update the user's profile information.</p>
                  </div>

                  <div className="grid gap-6">
                    <div className="grid grid-cols-2 gap-6">
                      <div>
                        <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                          First Name
                        </label>
                        <input
                          type="text"
                          name="firstName"
                          id="firstName"
                          defaultValue={user.profile.firstName}
                          className="mt-1 block w-full rounded-md border-gray-300 py-2 px-3 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white sm:text-sm"
                        />
                      </div>
                      <div>
                        <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                          Last Name
                        </label>
                        <input
                          type="text"
                          name="lastName"
                          id="lastName"
                          defaultValue={user.profile.lastName}
                          className="mt-1 block w-full rounded-md border-gray-300 py-2 px-3 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white sm:text-sm"
                        />
                      </div>
                    </div>

                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Email
                      </label>
                      <div className="mt-1 flex rounded-md shadow-sm">
                        <span className="inline-flex items-center rounded-l-md border border-r-0 border-gray-300 bg-gray-50 px-3 text-gray-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 sm:text-sm">
                          <FontAwesomeIcon icon={faEnvelope} className="h-4 w-4" />
                        </span>
                        <input
                          type="email"
                          name="email"
                          id="email"
                          defaultValue={user.email}
                          className="block w-full flex-1 rounded-none rounded-r-md border-gray-300 py-2 px-3 focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white sm:text-sm"
                        />
                      </div>
                    </div>

                    <div>
                      <label htmlFor="username" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Username
                      </label>
                      <div className="mt-1 flex rounded-md shadow-sm">
                        <span className="inline-flex items-center rounded-l-md border border-r-0 border-gray-300 bg-gray-50 px-3 text-gray-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 sm:text-sm">
                          <FontAwesomeIcon icon={faUser} className="h-4 w-4" />
                        </span>
                        <input
                          type="text"
                          name="username"
                          id="username"
                          defaultValue={user.username}
                          className="block w-full flex-1 rounded-none rounded-r-md border-gray-300 py-2 px-3 focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white sm:text-sm"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-6">
                      <div>
                        <label htmlFor="role" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                          Role
                        </label>
                        <select
                          id="role"
                          name="role"
                          defaultValue={user.role}
                          className="mt-1 block w-full rounded-md border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white sm:text-sm"
                        >
                          <option value="admin">Admin</option>
                          <option value="manager">Manager</option>
                          <option value="user">User</option>
                          <option value="guest">Guest</option>
                        </select>
                      </div>
                      <div>
                        <label htmlFor="status" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                          Status
                        </label>
                        <select
                          id="status"
                          name="status"
                          defaultValue={user.status}
                          className="mt-1 block w-full rounded-md border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white sm:text-sm"
                        >
                          <option value="active">Active</option>
                          <option value="inactive">Inactive</option>
                          <option value="pending">Pending</option>
                          <option value="suspended">Suspended</option>
                        </select>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Professional Information */}
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white">Professional Information</h3>
                    <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">Update the user's job and location details.</p>
                  </div>

                  <div className="grid gap-6">
                    <div>
                      <label htmlFor="jobTitle" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Job Title
                      </label>
                      <div className="mt-1 flex rounded-md shadow-sm">
                        <span className="inline-flex items-center rounded-l-md border border-r-0 border-gray-300 bg-gray-50 px-3 text-gray-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 sm:text-sm">
                          <FontAwesomeIcon icon={faIdCard} className="h-4 w-4" />
                        </span>
                        <input
                          type="text"
                          name="jobTitle"
                          id="jobTitle"
                          defaultValue={user.profile.jobTitle || ''}
                          className="block w-full flex-1 rounded-none rounded-r-md border-gray-300 py-2 px-3 focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white sm:text-sm"
                        />
                      </div>
                    </div>

                    <div>
                      <label htmlFor="department" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Department
                      </label>
                      <div className="mt-1 flex rounded-md shadow-sm">
                        <span className="inline-flex items-center rounded-l-md border border-r-0 border-gray-300 bg-gray-50 px-3 text-gray-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 sm:text-sm">
                          <FontAwesomeIcon icon={faBuilding} className="h-4 w-4" />
                        </span>
                        <input
                          type="text"
                          name="department"
                          id="department"
                          defaultValue={user.profile.department || ''}
                          className="block w-full flex-1 rounded-none rounded-r-md border-gray-300 py-2 px-3 focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white sm:text-sm"
                        />
                      </div>
                    </div>

                    <div>
                      <label htmlFor="location" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Location
                      </label>
                      <div className="mt-1 flex rounded-md shadow-sm">
                        <span className="inline-flex items-center rounded-l-md border border-r-0 border-gray-300 bg-gray-50 px-3 text-gray-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 sm:text-sm">
                          <FontAwesomeIcon icon={faMapMarkerAlt} className="h-4 w-4" />
                        </span>
                        <input
                          type="text"
                          name="location"
                          id="location"
                          defaultValue={user.profile.location || ''}
                          className="block w-full flex-1 rounded-none rounded-r-md border-gray-300 py-2 px-3 focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white sm:text-sm"
                        />
                      </div>
                    </div>

                    <div>
                      <label htmlFor="phoneNumber" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Phone Number
                      </label>
                      <div className="mt-1 flex rounded-md shadow-sm">
                        <span className="inline-flex items-center rounded-l-md border border-r-0 border-gray-300 bg-gray-50 px-3 text-gray-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 sm:text-sm">
                          <FontAwesomeIcon icon={faPhone} className="h-4 w-4" />
                        </span>
                        <input
                          type="text"
                          name="phoneNumber"
                          id="phoneNumber"
                          defaultValue={user.profile.phoneNumber || ''}
                          className="block w-full flex-1 rounded-none rounded-r-md border-gray-300 py-2 px-3 focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white sm:text-sm"
                        />
                      </div>
                    </div>

                    <div>
                      <label htmlFor="bio" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Bio
                      </label>
                      <div className="mt-1">
                        <textarea
                          id="bio"
                          name="bio"
                          rows={3}
                          defaultValue={user.profile.bio || ''}
                          className="block w-full rounded-md border-gray-300 py-2 px-3 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white sm:text-sm"
                        />
                      </div>
                      <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">Brief description of the user's role and expertise.</p>
                    </div>
                  </div>
                </div>
              </div>
            </form>
          )}

          {activeTab === 'security' && (
            <div className="space-y-6">
              {/* Permissions */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">Permissions</h3>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">Configure what actions this user can perform.</p>

                <div className="mt-6 space-y-6">
                  <div className="relative flex items-start">
                    <div className="flex h-5 items-center">
                      <input
                        id="canCreateAgents"
                        name="canCreateAgents"
                        type="checkbox"
                        defaultChecked={user.permissions.canCreateAgents}
                        className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800"
                      />
                    </div>
                    <div className="ml-3 text-sm">
                      <label htmlFor="canCreateAgents" className="font-medium text-gray-700 dark:text-gray-300">
                        Create Agents
                      </label>
                      <p className="text-gray-500 dark:text-gray-400">Allow user to create new AI agents.</p>
                    </div>
                  </div>

                  <div className="relative flex items-start">
                    <div className="flex h-5 items-center">
                      <input
                        id="canEditAgents"
                        name="canEditAgents"
                        type="checkbox"
                        defaultChecked={user.permissions.canEditAgents}
                        className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800"
                      />
                    </div>
                    <div className="ml-3 text-sm">
                      <label htmlFor="canEditAgents" className="font-medium text-gray-700 dark:text-gray-300">
                        Edit Agents
                      </label>
                      <p className="text-gray-500 dark:text-gray-400">Allow user to modify existing AI agents.</p>
                    </div>
                  </div>

                  <div className="relative flex items-start">
                    <div className="flex h-5 items-center">
                      <input
                        id="canDeleteAgents"
                        name="canDeleteAgents"
                        type="checkbox"
                        defaultChecked={user.permissions.canDeleteAgents}
                        className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800"
                      />
                    </div>
                    <div className="ml-3 text-sm">
                      <label htmlFor="canDeleteAgents" className="font-medium text-gray-700 dark:text-gray-300">
                        Delete Agents
                      </label>
                      <p className="text-gray-500 dark:text-gray-400">Allow user to delete AI agents.</p>
                    </div>
                  </div>

                  <div className="relative flex items-start">
                    <div className="flex h-5 items-center">
                      <input
                        id="canManageUsers"
                        name="canManageUsers"
                        type="checkbox"
                        defaultChecked={user.permissions.canManageUsers}
                        className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800"
                      />
                    </div>
                    <div className="ml-3 text-sm">
                      <label htmlFor="canManageUsers" className="font-medium text-gray-700 dark:text-gray-300">
                        Manage Users
                      </label>
                      <p className="text-gray-500 dark:text-gray-400">Allow user to create, edit, and delete other users.</p>
                    </div>
                  </div>

                  <div className="relative flex items-start">
                    <div className="flex h-5 items-center">
                      <input
                        id="canAccessAdmin"
                        name="canAccessAdmin"
                        type="checkbox"
                        defaultChecked={user.permissions.canAccessAdmin}
                        className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800"
                      />
                    </div>
                    <div className="ml-3 text-sm">
                      <label htmlFor="canAccessAdmin" className="font-medium text-gray-700 dark:text-gray-300">
                        Access Admin Panel
                      </label>
                      <p className="text-gray-500 dark:text-gray-400">Allow user to access the admin panel and settings.</p>
                    </div>
                  </div>

                  <div className="relative flex items-start">
                    <div className="flex h-5 items-center">
                      <input
                        id="canAccessAnalytics"
                        name="canAccessAnalytics"
                        type="checkbox"
                        defaultChecked={user.permissions.canAccessAnalytics}
                        className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800"
                      />
                    </div>
                    <div className="ml-3 text-sm">
                      <label htmlFor="canAccessAnalytics" className="font-medium text-gray-700 dark:text-gray-300">
                        Access Analytics
                      </label>
                      <p className="text-gray-500 dark:text-gray-400">Allow user to view analytics and reports.</p>
                    </div>
                  </div>

                  <div className="relative flex items-start">
                    <div className="flex h-5 items-center">
                      <input
                        id="canManageContent"
                        name="canManageContent"
                        type="checkbox"
                        defaultChecked={user.permissions.canManageContent}
                        className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800"
                      />
                    </div>
                    <div className="ml-3 text-sm">
                      <label htmlFor="canManageContent" className="font-medium text-gray-700 dark:text-gray-300">
                        Manage Content
                      </label>
                      <p className="text-gray-500 dark:text-gray-400">Allow user to create and edit content in the learning section.</p>
                    </div>
                  </div>

                  <div className="relative flex items-start">
                    <div className="flex h-5 items-center">
                      <input
                        id="canApproveContent"
                        name="canApproveContent"
                        type="checkbox"
                        defaultChecked={user.permissions.canApproveContent}
                        className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800"
                      />
                    </div>
                    <div className="ml-3 text-sm">
                      <label htmlFor="canApproveContent" className="font-medium text-gray-700 dark:text-gray-300">
                        Approve Content
                      </label>
                      <p className="text-gray-500 dark:text-gray-400">Allow user to approve or reject content submitted by others.</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Security Settings */}
              <div className="pt-6">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">Security Settings</h3>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">Manage user's security settings and preferences.</p>

                <div className="mt-6 space-y-6">
                  <div>
                    <label htmlFor="password" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Reset Password
                    </label>
                    <div className="mt-1">
                      <input
                        type="password"
                        name="password"
                        id="password"
                        className="block w-full rounded-md border-gray-300 py-2 px-3 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white sm:text-sm"
                        placeholder="Enter new password"
                      />
                    </div>
                    <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                      Leave blank to keep the current password. Last changed: {user.lastPasswordChange ? new Date(user.lastPasswordChange).toLocaleDateString() : 'Never'}
                    </p>
                  </div>

                  <div className="relative flex items-start">
                    <div className="flex h-5 items-center">
                      <input
                        id="twoFactorEnabled"
                        name="twoFactorEnabled"
                        type="checkbox"
                        defaultChecked={user.settings.twoFactorEnabled}
                        className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800"
                      />
                    </div>
                    <div className="ml-3 text-sm">
                      <label htmlFor="twoFactorEnabled" className="font-medium text-gray-700 dark:text-gray-300">
                        Two-Factor Authentication
                      </label>
                      <p className="text-gray-500 dark:text-gray-400">Require two-factor authentication for this user.</p>
                    </div>
                  </div>

                  <div>
                    <label htmlFor="sessionTimeout" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Session Timeout (minutes)
                    </label>
                    <div className="mt-1">
                      <input
                        type="number"
                        name="sessionTimeout"
                        id="sessionTimeout"
                        defaultValue={30}
                        min={5}
                        max={240}
                        className="block w-full rounded-md border-gray-300 py-2 px-3 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white sm:text-sm"
                      />
                    </div>
                    <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                      Time in minutes before the user is automatically logged out due to inactivity.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'activity' && (
            <div className="space-y-6">
              {/* Activity Overview */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">Activity Overview</h3>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">View user's activity and usage statistics.</p>

                <div className="mt-6 grid gap-6 sm:grid-cols-2 lg:grid-cols-4">
                  <div className="overflow-hidden rounded-lg bg-white shadow dark:bg-gray-800">
                    <div className="px-4 py-5 sm:p-6">
                      <dt className="truncate text-sm font-medium text-gray-500 dark:text-gray-400">Total Logins</dt>
                      <dd className="mt-1 text-3xl font-semibold text-gray-900 dark:text-white">{user.activity.totalLogins}</dd>
                    </div>
                  </div>

                  <div className="overflow-hidden rounded-lg bg-white shadow dark:bg-gray-800">
                    <div className="px-4 py-5 sm:p-6">
                      <dt className="truncate text-sm font-medium text-gray-500 dark:text-gray-400">Total Sessions</dt>
                      <dd className="mt-1 text-3xl font-semibold text-gray-900 dark:text-white">{user.activity.totalSessions}</dd>
                    </div>
                  </div>

                  <div className="overflow-hidden rounded-lg bg-white shadow dark:bg-gray-800">
                    <div className="px-4 py-5 sm:p-6">
                      <dt className="truncate text-sm font-medium text-gray-500 dark:text-gray-400">Avg. Session Duration</dt>
                      <dd className="mt-1 text-3xl font-semibold text-gray-900 dark:text-white">
                        {Math.floor(user.activity.averageSessionDuration / 60)}m
                      </dd>
                    </div>
                  </div>

                  <div className="overflow-hidden rounded-lg bg-white shadow dark:bg-gray-800">
                    <div className="px-4 py-5 sm:p-6">
                      <dt className="truncate text-sm font-medium text-gray-500 dark:text-gray-400">Last Active</dt>
                      <dd className="mt-1 text-xl font-semibold text-gray-900 dark:text-white">
                        {new Date(user.activity.lastActive).toLocaleDateString()}
                      </dd>
                    </div>
                  </div>
                </div>
              </div>

              {/* Favorite Sections & Agents */}
              <div className="pt-6">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">Preferences & Favorites</h3>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">User's preferred sections and favorite agents.</p>

                <div className="mt-6 grid gap-6 md:grid-cols-2">
                  <div>
                    <h4 className="text-base font-medium text-gray-900 dark:text-white">Favorite Section</h4>
                    <div className="mt-2 rounded-md bg-gray-50 p-4 dark:bg-gray-800">
                      <p className="text-gray-700 dark:text-gray-300">
                        {user.activity.favoriteSection.split('_').map(word =>
                          word.charAt(0).toUpperCase() + word.slice(1)
                        ).join(' ')}
                      </p>
                    </div>
                    <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                      The section of the application this user spends the most time in.
                    </p>
                  </div>

                  <div>
                    <h4 className="text-base font-medium text-gray-900 dark:text-white">Favorite Agents</h4>
                    <div className="mt-2 rounded-md bg-gray-50 p-4 dark:bg-gray-800">
                      {user.activity.favoriteAgents.length > 0 ? (
                        <ul className="list-inside list-disc space-y-1 text-gray-700 dark:text-gray-300">
                          {user.activity.favoriteAgents.map((agentId) => (
                            <li key={agentId}>Agent {agentId}</li>
                          ))}
                        </ul>
                      ) : (
                        <p className="text-gray-500 dark:text-gray-400">No favorite agents yet.</p>
                      )}
                    </div>
                    <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                      The AI agents this user interacts with most frequently.
                    </p>
                  </div>
                </div>
              </div>

              {/* Recent Activity */}
              <div className="pt-6">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">Recent Activity</h3>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">User's recent actions and interactions.</p>

                <div className="mt-6 overflow-hidden rounded-md border border-gray-200 dark:border-gray-700">
                  <ul className="divide-y divide-gray-200 dark:divide-gray-700">
                    <li className="px-6 py-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-900 dark:text-white">Logged in</p>
                          <p className="text-sm text-gray-500 dark:text-gray-400">User logged in from Chrome on Windows</p>
                        </div>
                        <p className="text-sm text-gray-500 dark:text-gray-400">{new Date(user.activity.lastLogin).toLocaleString()}</p>
                      </div>
                    </li>
                    <li className="px-6 py-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-900 dark:text-white">Viewed agent details</p>
                          <p className="text-sm text-gray-500 dark:text-gray-400">Viewed details for Agent 1</p>
                        </div>
                        <p className="text-sm text-gray-500 dark:text-gray-400">{new Date(user.activity.lastActive).toLocaleString()}</p>
                      </div>
                    </li>
                    <li className="px-6 py-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-900 dark:text-white">Updated profile</p>
                          <p className="text-sm text-gray-500 dark:text-gray-400">Updated job title and department</p>
                        </div>
                        <p className="text-sm text-gray-500 dark:text-gray-400">{new Date(user.updatedAt).toLocaleString()}</p>
                      </div>
                    </li>
                  </ul>
                </div>
                <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                  Note: In a real application, this would show actual user activity from a database.
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
