'use client';

import React from 'react';
import Link from 'next/link';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faChartLine,
  faUsers,
  faRobot,
  faGear,
  faShieldAlt,
  faBook,
  faSitemap
} from '@fortawesome/free-solid-svg-icons';

export default function AdminPage() {
  return (
    <div className="space-y-8">
      <div className="rounded-lg bg-gradient-to-r from-purple-600 to-indigo-700 p-8 text-white shadow-lg">
        <h1 className="mb-4 text-3xl font-bold">Admin Dashboard</h1>
        <p className="mb-6 text-lg">
          Manage your AI Hub platform, monitor usage, and analyze performance metrics.
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <Link
          href="/admin/analytics"
          className="group flex flex-col rounded-lg border border-gray-200 bg-white p-6 shadow-sm transition hover:shadow-md dark:border-gray-800 dark:bg-gray-900"
        >
          <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-purple-100 text-purple-600 dark:bg-purple-900/30 dark:text-purple-400">
            <FontAwesomeIcon icon={faChartLine} className="h-6 w-6" />
          </div>
          <h3 className="mb-2 text-xl font-bold text-gray-900 group-hover:text-purple-600 dark:text-white dark:group-hover:text-purple-400">
            Analytics
          </h3>
          <p className="mb-4 flex-1 text-gray-600 dark:text-gray-400">
            View detailed analytics on platform usage, agent interactions, and user behavior.
          </p>
        </Link>

        <Link
          href="/admin/users"
          className="group flex flex-col rounded-lg border border-gray-200 bg-white p-6 shadow-sm transition hover:shadow-md dark:border-gray-800 dark:bg-gray-900"
        >
          <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400">
            <FontAwesomeIcon icon={faUsers} className="h-6 w-6" />
          </div>
          <h3 className="mb-2 text-xl font-bold text-gray-900 group-hover:text-blue-600 dark:text-white dark:group-hover:text-blue-400">
            User Management
          </h3>
          <p className="mb-4 flex-1 text-gray-600 dark:text-gray-400">
            Manage users, roles, and permissions. View user activity and engagement metrics.
          </p>
        </Link>

        <Link
          href="/admin/agents"
          className="group flex flex-col rounded-lg border border-gray-200 bg-white p-6 shadow-sm transition hover:shadow-md dark:border-gray-800 dark:bg-gray-900"
        >
          <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400">
            <FontAwesomeIcon icon={faRobot} className="h-6 w-6" />
          </div>
          <h3 className="mb-2 text-xl font-bold text-gray-900 group-hover:text-green-600 dark:text-white dark:group-hover:text-green-400">
            Agent Management
          </h3>
          <p className="mb-4 flex-1 text-gray-600 dark:text-gray-400">
            Configure, deploy, and monitor AI agents. Analyze agent performance and usage.
          </p>
        </Link>

        <Link
          href="/admin/ai-org"
          className="group flex flex-col rounded-lg border border-gray-200 bg-white p-6 shadow-sm transition hover:shadow-md dark:border-gray-800 dark:bg-gray-900"
        >
          <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-indigo-100 text-indigo-600 dark:bg-indigo-900/30 dark:text-indigo-400">
            <FontAwesomeIcon icon={faSitemap} className="h-6 w-6" />
          </div>
          <h3 className="mb-2 text-xl font-bold text-gray-900 group-hover:text-indigo-600 dark:text-white dark:group-hover:text-indigo-400">
            AI Organization
          </h3>
          <p className="mb-4 flex-1 text-gray-600 dark:text-gray-400">
            View and manage the AI agent organization structure, roles, and implementation status.
          </p>
        </Link>

        <Link
          href="/admin/content"
          className="group flex flex-col rounded-lg border border-gray-200 bg-white p-6 shadow-sm transition hover:shadow-md dark:border-gray-800 dark:bg-gray-900"
        >
          <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-amber-100 text-amber-600 dark:bg-amber-900/30 dark:text-amber-400">
            <FontAwesomeIcon icon={faBook} className="h-6 w-6" />
          </div>
          <h3 className="mb-2 text-xl font-bold text-gray-900 group-hover:text-amber-600 dark:text-white dark:group-hover:text-amber-400">
            Content Management
          </h3>
          <p className="mb-4 flex-1 text-gray-600 dark:text-gray-400">
            Manage learning content, resources, and other platform content. Track content engagement.
          </p>
        </Link>

        <Link
          href="/admin/security"
          className="group flex flex-col rounded-lg border border-gray-200 bg-white p-6 shadow-sm transition hover:shadow-md dark:border-gray-800 dark:bg-gray-900"
        >
          <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100 text-red-600 dark:bg-red-900/30 dark:text-red-400">
            <FontAwesomeIcon icon={faShieldAlt} className="h-6 w-6" />
          </div>
          <h3 className="mb-2 text-xl font-bold text-gray-900 group-hover:text-red-600 dark:text-white dark:group-hover:text-red-400">
            Security & Compliance
          </h3>
          <p className="mb-4 flex-1 text-gray-600 dark:text-gray-400">
            Monitor sensitive data usage, manage compliance settings, and view security alerts.
          </p>
        </Link>

        <Link
          href="/admin/settings"
          className="group flex flex-col rounded-lg border border-gray-200 bg-white p-6 shadow-sm transition hover:shadow-md dark:border-gray-800 dark:bg-gray-900"
        >
          <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400">
            <FontAwesomeIcon icon={faGear} className="h-6 w-6" />
          </div>
          <h3 className="mb-2 text-xl font-bold text-gray-900 group-hover:text-gray-600 dark:text-white dark:group-hover:text-gray-400">
            Platform Settings
          </h3>
          <p className="mb-4 flex-1 text-gray-600 dark:text-gray-400">
            Configure global platform settings, integrations, and appearance options.
          </p>
        </Link>
      </div>
    </div>
  );
}
