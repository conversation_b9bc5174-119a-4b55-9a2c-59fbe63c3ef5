'use client';

import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faExclamationTriangle, faCheckCircle, faInfoCircle } from '@fortawesome/free-solid-svg-icons';
import { AgentAnalytics } from '@/types/analytics';

interface AgentUsageSectionProps {
  agentAnalytics: AgentAnalytics[];
}

export default function AgentUsageSection({ agentAnalytics }: AgentUsageSectionProps) {
  // Helper function to format duration
  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  // Helper function to get sensitivity level
  const getSensitivityLevel = (score: number) => {
    if (score >= 75) return { level: 'High', color: 'text-red-500 dark:text-red-400' };
    if (score >= 50) return { level: 'Medium', color: 'text-amber-500 dark:text-amber-400' };
    if (score >= 25) return { level: 'Low', color: 'text-yellow-500 dark:text-yellow-400' };
    return { level: 'Minimal', color: 'text-green-500 dark:text-green-400' };
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Agent Usage Analytics</h2>
        <div className="text-sm text-gray-500 dark:text-gray-400">
          Showing data for all agents
        </div>
      </div>

      {/* Agent usage metrics table */}
      <div className="overflow-hidden rounded-lg border border-gray-200 dark:border-gray-800">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-800">
            <thead className="bg-gray-50 dark:bg-gray-800">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  Agent
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  Interactions
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  Unique Users
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  Avg. Duration
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  Sensitivity
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  External APIs
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 bg-white dark:divide-gray-800 dark:bg-gray-900">
              {agentAnalytics.map((agent) => {
                const sensitivityInfo = getSensitivityLevel(agent.sensitivityScore);
                return (
                  <tr key={agent.agentId} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                    <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900 dark:text-white">
                      {agent.agentName}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                      {agent.interactionCount.toLocaleString()}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                      {agent.uniqueUsers.toLocaleString()}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                      {formatDuration(agent.averageDuration)}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm">
                      <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${sensitivityInfo.color}`}>
                        {agent.sensitivityScore >= 75 && (
                          <FontAwesomeIcon icon={faExclamationTriangle} className="mr-1 h-3 w-3" />
                        )}
                        {sensitivityInfo.level} ({agent.sensitivityScore})
                      </span>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                      <div className="flex flex-col space-y-1">
                        {Object.entries(agent.externalApiUsage).map(([api, count]) => (
                          <div key={api} className="flex items-center text-xs">
                            <span className="font-medium">{api}:</span>
                            <span className="ml-1">{count.toLocaleString()}</span>
                          </div>
                        ))}
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>

      {/* Popular queries section */}
      <div className="grid gap-6 md:grid-cols-2">
        <div className="rounded-lg border border-gray-200 bg-white p-6 dark:border-gray-800 dark:bg-gray-900">
          <h3 className="mb-4 text-base font-semibold text-gray-900 dark:text-white">Popular Queries</h3>
          
          <div className="space-y-4">
            {agentAnalytics.slice(0, 3).map((agent) => (
              <div key={agent.agentId} className="space-y-2">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white">{agent.agentName}</h4>
                <ul className="space-y-1 text-sm text-gray-600 dark:text-gray-400">
                  {agent.popularQueries.map((query, index) => (
                    <li key={index} className="flex items-center justify-between">
                      <span className="truncate">{query.query}</span>
                      <span className="ml-2 text-xs text-gray-500 dark:text-gray-500">{query.count} times</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>

        <div className="rounded-lg border border-gray-200 bg-white p-6 dark:border-gray-800 dark:bg-gray-900">
          <h3 className="mb-4 text-base font-semibold text-gray-900 dark:text-white">Sensitivity Insights</h3>
          
          <div className="space-y-3">
            <div className="flex items-start">
              <FontAwesomeIcon icon={faExclamationTriangle} className="mr-2 mt-0.5 h-4 w-4 text-red-500" />
              <div className="text-sm text-gray-600 dark:text-gray-400">
                <p className="font-medium text-gray-900 dark:text-white">High Sensitivity Detected</p>
                <p>The <strong>Customer Support</strong> and <strong>Data Analyst</strong> agents show high sensitivity scores, indicating potential exposure to sensitive information.</p>
              </div>
            </div>
            
            <div className="flex items-start">
              <FontAwesomeIcon icon={faInfoCircle} className="mr-2 mt-0.5 h-4 w-4 text-blue-500" />
              <div className="text-sm text-gray-600 dark:text-gray-400">
                <p className="font-medium text-gray-900 dark:text-white">External API Usage</p>
                <p>OpenAI GPT-4 is the most commonly used external API across all agents, accounting for 62% of all external API calls.</p>
              </div>
            </div>
            
            <div className="flex items-start">
              <FontAwesomeIcon icon={faCheckCircle} className="mr-2 mt-0.5 h-4 w-4 text-green-500" />
              <div className="text-sm text-gray-600 dark:text-gray-400">
                <p className="font-medium text-gray-900 dark:text-white">Low Risk Agents</p>
                <p>The <strong>Writing Assistant</strong> and <strong>Code Assistant</strong> agents show the lowest sensitivity scores, making them safer for general use.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
