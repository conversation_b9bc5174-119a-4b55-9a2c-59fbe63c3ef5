'use client';

import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faUser, 
  faClock, 
  faCalendarAlt,
  faHeart,
  faRobot
} from '@fortawesome/free-solid-svg-icons';
import { UserAnalytics, ContentAnalytics } from '@/types/analytics';

interface UserBehaviorSectionProps {
  userAnalytics: UserAnalytics[];
  contentAnalytics: ContentAnalytics[];
}

export default function UserBehaviorSection({ 
  userAnalytics, 
  contentAnalytics 
}: UserBehaviorSectionProps) {
  // Helper function to format date
  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString();
  };

  // Helper function to format time
  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  // Helper function to format section name
  const formatSectionName = (section: string) => {
    return section
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white">User Behavior Analytics</h2>
        <div className="text-sm text-gray-500 dark:text-gray-400">
          Showing data for active users
        </div>
      </div>

      {/* User activity metrics */}
      <div className="overflow-hidden rounded-lg border border-gray-200 dark:border-gray-800">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-800">
            <thead className="bg-gray-50 dark:bg-gray-800">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  User
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  Last Active
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  Sessions
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  Total Time
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  Favorite Section
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  Favorite Agents
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 bg-white dark:divide-gray-800 dark:bg-gray-900">
              {userAnalytics.map((user) => (
                <tr key={user.userId} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                  <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900 dark:text-white">
                    <div className="flex items-center">
                      <FontAwesomeIcon icon={faUser} className="mr-2 h-4 w-4 text-gray-400" />
                      {user.userName}
                    </div>
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                    <div className="flex items-center">
                      <FontAwesomeIcon icon={faCalendarAlt} className="mr-2 h-4 w-4 text-gray-400" />
                      {formatDate(user.lastActive)}
                    </div>
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                    {user.totalSessions}
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                    <div className="flex items-center">
                      <FontAwesomeIcon icon={faClock} className="mr-2 h-4 w-4 text-gray-400" />
                      {formatTime(user.totalTimeSpent)}
                    </div>
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                    {formatSectionName(user.favoriteSection)}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                    <div className="flex items-center space-x-1">
                      <FontAwesomeIcon icon={faHeart} className="h-4 w-4 text-red-400" />
                      <span>{user.favoriteAgents.join(', ')}</span>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Content engagement */}
      <div>
        <h3 className="mb-4 text-base font-semibold text-gray-900 dark:text-white">Content Engagement</h3>
        <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
          {contentAnalytics.map((content) => (
            <div 
              key={content.contentId}
              className="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900"
            >
              <div className="mb-2 flex items-center justify-between">
                <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                  content.contentType === 'video' 
                    ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300' 
                    : content.contentType === 'article'
                      ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                      : content.contentType === 'blog'
                        ? 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300'
                        : 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300'
                }`}>
                  {content.contentType}
                </span>
                <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                  <FontAwesomeIcon icon={faUser} className="mr-1 h-3 w-3" />
                  {content.uniqueUsers.toLocaleString()}
                </div>
              </div>
              
              <h4 className="mb-2 text-sm font-medium text-gray-900 dark:text-white line-clamp-1">
                {content.contentTitle}
              </h4>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between text-xs">
                  <span className="text-gray-500 dark:text-gray-400">Views</span>
                  <span className="font-medium text-gray-900 dark:text-white">{content.viewCount.toLocaleString()}</span>
                </div>
                
                <div className="flex items-center justify-between text-xs">
                  <span className="text-gray-500 dark:text-gray-400">Avg. Duration</span>
                  <span className="font-medium text-gray-900 dark:text-white">{formatTime(content.averageDuration)}</span>
                </div>
                
                <div className="flex items-center justify-between text-xs">
                  <span className="text-gray-500 dark:text-gray-400">Completion Rate</span>
                  <span className="font-medium text-gray-900 dark:text-white">{(content.completionRate * 100).toFixed(0)}%</span>
                </div>
                
                <div className="flex items-center justify-between text-xs">
                  <span className="text-gray-500 dark:text-gray-400">Engagement Score</span>
                  <span className={`font-medium ${
                    content.engagementScore >= 80 
                      ? 'text-green-600 dark:text-green-400' 
                      : content.engagementScore >= 60
                        ? 'text-blue-600 dark:text-blue-400'
                        : 'text-amber-600 dark:text-amber-400'
                  }`}>
                    {content.engagementScore}/100
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* User journey insights */}
      <div className="grid gap-6 md:grid-cols-2">
        <div className="rounded-lg border border-gray-200 bg-white p-6 dark:border-gray-800 dark:bg-gray-900">
          <h3 className="mb-4 text-base font-semibold text-gray-900 dark:text-white">User Journey Insights</h3>
          <ul className="space-y-3 text-sm text-gray-600 dark:text-gray-400">
            <li className="flex items-start">
              <span className="mr-2 text-blue-500">•</span>
              <span>Most users start on the <strong>Home</strong> page, then navigate to either <strong>Browse</strong> or <strong>Learning Resources</strong>.</span>
            </li>
            <li className="flex items-start">
              <span className="mr-2 text-blue-500">•</span>
              <span>Users who engage with <strong>Learning Videos</strong> spend 42% more time on the platform overall.</span>
            </li>
            <li className="flex items-start">
              <span className="mr-2 text-blue-500">•</span>
              <span>The most common user path is: Home → Browse → Agent Detail → Chat.</span>
            </li>
            <li className="flex items-start">
              <span className="mr-2 text-blue-500">•</span>
              <span>Users who favorite at least one agent are 3.5x more likely to return within 7 days.</span>
            </li>
          </ul>
        </div>

        <div className="rounded-lg border border-gray-200 bg-white p-6 dark:border-gray-800 dark:bg-gray-900">
          <h3 className="mb-4 text-base font-semibold text-gray-900 dark:text-white">Recommendations</h3>
          <ul className="space-y-3 text-sm text-gray-600 dark:text-gray-400">
            <li className="flex items-start">
              <span className="mr-2 text-blue-500">•</span>
              <span>Encourage users to favorite agents by highlighting this feature during onboarding.</span>
            </li>
            <li className="flex items-start">
              <span className="mr-2 text-blue-500">•</span>
              <span>Promote learning videos more prominently to increase overall engagement time.</span>
            </li>
            <li className="flex items-start">
              <span className="mr-2 text-blue-500">•</span>
              <span>Consider implementing personalized content recommendations based on user behavior.</span>
            </li>
            <li className="flex items-start">
              <span className="mr-2 text-blue-500">•</span>
              <span>Add more interactive elements to blog posts to increase their relatively low engagement scores.</span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
}
