'use client';

import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faExclamationTriangle, 
  faShieldAlt, 
  faEye, 
  faLock,
  faUserSecret,
  faCreditCard,
  faFileContract,
  faIdCard
} from '@fortawesome/free-solid-svg-icons';
import { AgentInteraction, UserAnalytics } from '@/types/analytics';

interface SensitiveDataSectionProps {
  sensitiveInteractions: AgentInteraction[];
  highRiskUsers: UserAnalytics[];
}

export default function SensitiveDataSection({ 
  sensitiveInteractions, 
  highRiskUsers 
}: SensitiveDataSectionProps) {
  // Helper function to format date
  const formatDate = (date: Date) => {
    return new Date(date).toLocaleString();
  };

  // Helper function to get sensitivity flag icon
  const getSensitivityFlagIcon = (flag: string) => {
    switch (flag) {
      case 'pii':
      case 'personal_data':
        return <FontAwesomeIcon icon={faIdCard} className="h-3 w-3" />;
      case 'credit_card':
      case 'financial_data':
        return <FontAwesomeIcon icon={faCreditCard} className="h-3 w-3" />;
      case 'confidential':
      case 'internal_project':
        return <FontAwesomeIcon icon={faUserSecret} className="h-3 w-3" />;
      case 'address_data':
      case 'client_information':
        return <FontAwesomeIcon icon={faFileContract} className="h-3 w-3" />;
      default:
        return <FontAwesomeIcon icon={faLock} className="h-3 w-3" />;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Sensitive Data Monitoring</h2>
        <div className="rounded-full bg-red-100 px-3 py-1 text-xs font-medium text-red-800 dark:bg-red-900/30 dark:text-red-300">
          <FontAwesomeIcon icon={faExclamationTriangle} className="mr-1 h-3 w-3" />
          {sensitiveInteractions.length} alerts detected
        </div>
      </div>

      {/* Alert summary */}
      <div className="rounded-lg border border-red-200 bg-red-50 p-4 dark:border-red-900/50 dark:bg-red-900/10">
        <div className="flex">
          <div className="flex-shrink-0">
            <FontAwesomeIcon icon={faShieldAlt} className="h-5 w-5 text-red-600 dark:text-red-400" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800 dark:text-red-300">Sensitive Data Alert</h3>
            <div className="mt-2 text-sm text-red-700 dark:text-red-300">
              <p>
                Our system has detected potential sensitive data being shared with AI agents. 
                This may include personal information, financial data, or confidential business information.
                Review the interactions below and take appropriate action.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Recent sensitive interactions */}
      <div>
        <h3 className="mb-4 text-base font-semibold text-gray-900 dark:text-white">Recent Sensitive Interactions</h3>
        <div className="space-y-4">
          {sensitiveInteractions.map((interaction) => (
            <div 
              key={interaction.id} 
              className="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900"
            >
              <div className="mb-2 flex items-center justify-between">
                <div className="flex items-center">
                  <FontAwesomeIcon icon={faEye} className="mr-2 h-4 w-4 text-amber-500" />
                  <span className="font-medium text-gray-900 dark:text-white">
                    Interaction with {interaction.agentId}
                  </span>
                </div>
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {formatDate(interaction.timestamp)}
                </span>
              </div>
              
              <div className="mb-2 rounded bg-gray-50 p-3 dark:bg-gray-800">
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  <span className="font-medium text-gray-900 dark:text-white">User Query: </span>
                  {interaction.queryText}
                </p>
              </div>
              
              <div className="mb-3 rounded bg-gray-50 p-3 dark:bg-gray-800">
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  <span className="font-medium text-gray-900 dark:text-white">Agent Response: </span>
                  {interaction.responseText}
                </p>
              </div>
              
              <div className="flex flex-wrap items-center justify-between">
                <div className="flex items-center">
                  <span className="mr-2 text-xs font-medium text-gray-500 dark:text-gray-400">
                    User ID: {interaction.userId}
                  </span>
                  <span className="text-xs font-medium text-gray-500 dark:text-gray-400">
                    External API: {interaction.externalApiName}
                  </span>
                </div>
                
                <div className="flex flex-wrap gap-1">
                  {interaction.sensitivityFlags.map((flag) => (
                    <span 
                      key={flag}
                      className="inline-flex items-center rounded-full bg-red-100 px-2 py-0.5 text-xs font-medium text-red-800 dark:bg-red-900/30 dark:text-red-300"
                    >
                      {getSensitivityFlagIcon(flag)}
                      <span className="ml-1">{flag.replace('_', ' ')}</span>
                    </span>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* High risk users */}
      <div>
        <h3 className="mb-4 text-base font-semibold text-gray-900 dark:text-white">Users with High Sensitivity Risk</h3>
        <div className="overflow-hidden rounded-lg border border-gray-200 dark:border-gray-800">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-800">
              <thead className="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                    User
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                    Last Active
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                    Sessions
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                    Favorite Section
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                    Risk Level
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 bg-white dark:divide-gray-800 dark:bg-gray-900">
                {highRiskUsers.map((user) => (
                  <tr key={user.userId} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                    <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900 dark:text-white">
                      {user.userName}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                      {formatDate(user.lastActive)}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                      {user.totalSessions}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                      {user.favoriteSection.replace('_', ' ')}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm">
                      <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                        user.riskLevel === 'high' 
                          ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300' 
                          : user.riskLevel === 'medium'
                            ? 'bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300'
                            : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300'
                      }`}>
                        {user.riskLevel}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Recommendations */}
      <div className="rounded-lg border border-gray-200 bg-white p-6 dark:border-gray-800 dark:bg-gray-900">
        <h3 className="mb-4 text-base font-semibold text-gray-900 dark:text-white">Recommendations</h3>
        <ul className="space-y-3 text-sm text-gray-600 dark:text-gray-400">
          <li className="flex items-start">
            <span className="mr-2 text-blue-500">•</span>
            <span>Consider implementing additional data filters for the <strong>Customer Support</strong> and <strong>Data Analyst</strong> agents to prevent sensitive data processing.</span>
          </li>
          <li className="flex items-start">
            <span className="mr-2 text-blue-500">•</span>
            <span>Schedule a security review with high-risk users to ensure they understand data handling policies.</span>
          </li>
          <li className="flex items-start">
            <span className="mr-2 text-blue-500">•</span>
            <span>Update agent prompts to include stronger warnings about sharing sensitive information.</span>
          </li>
          <li className="flex items-start">
            <span className="mr-2 text-blue-500">•</span>
            <span>Consider implementing a pre-submission review for interactions with external APIs to prevent data leakage.</span>
          </li>
        </ul>
      </div>
    </div>
  );
}
