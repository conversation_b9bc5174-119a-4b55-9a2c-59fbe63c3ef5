'use client';

import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faArrowUp, faArrowDown, faMinus } from '@fortawesome/free-solid-svg-icons';
import { SectionAnalytics } from '@/types/analytics';

interface PlatformUsageSectionProps {
  sectionAnalytics: SectionAnalytics[];
}

export default function PlatformUsageSection({ sectionAnalytics }: PlatformUsageSectionProps) {
  // Helper function to format section names for display
  const formatSectionName = (section: string) => {
    return section
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  // Helper function to get trend icon
  const getTrendIcon = (trend: 'up' | 'down' | 'stable') => {
    if (trend === 'up') return <FontAwesomeIcon icon={faArrowUp} className="h-3 w-3 text-green-500" />;
    if (trend === 'down') return <FontAwesomeIcon icon={faArrowDown} className="h-3 w-3 text-red-500" />;
    return <FontAwesomeIcon icon={faMinus} className="h-3 w-3 text-gray-500" />;
  };

  // Helper function to format duration
  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Platform Usage Analytics</h2>
        <div className="text-sm text-gray-500 dark:text-gray-400">
          Showing data for all platform sections
        </div>
      </div>

      {/* Section usage metrics table */}
      <div className="overflow-hidden rounded-lg border border-gray-200 dark:border-gray-800">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-800">
            <thead className="bg-gray-50 dark:bg-gray-800">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  Section
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  Views
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  Unique Users
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  Avg. Duration
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  Bounce Rate
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  Trend
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 bg-white dark:divide-gray-800 dark:bg-gray-900">
              {sectionAnalytics.map((section) => (
                <tr key={section.section} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                  <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900 dark:text-white">
                    {formatSectionName(section.section)}
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                    {section.viewCount.toLocaleString()}
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                    {section.uniqueUsers.toLocaleString()}
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                    {formatDuration(section.averageDuration)}
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                    {(section.bounceRate * 100).toFixed(1)}%
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                    <div className="flex items-center">
                      {getTrendIcon(section.trend)}
                      <span className={`ml-1 ${
                        section.trend === 'up' 
                          ? 'text-green-600 dark:text-green-400' 
                          : section.trend === 'down' 
                            ? 'text-red-600 dark:text-red-400' 
                            : 'text-gray-600 dark:text-gray-400'
                      }`}>
                        {section.percentChange}%
                      </span>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Usage insights */}
      <div className="grid gap-6 md:grid-cols-2">
        <div className="rounded-lg border border-gray-200 bg-white p-6 dark:border-gray-800 dark:bg-gray-900">
          <h3 className="mb-4 text-base font-semibold text-gray-900 dark:text-white">Key Insights</h3>
          <ul className="space-y-3 text-sm text-gray-600 dark:text-gray-400">
            <li className="flex items-start">
              <span className="mr-2 text-green-500">•</span>
              <span>The <strong>Learning Resources</strong> section has seen the highest growth at <strong>42.7%</strong>, indicating strong interest in AI tools and resources.</span>
            </li>
            <li className="flex items-start">
              <span className="mr-2 text-green-500">•</span>
              <span>Users spend the most time in the <strong>Admin</strong> section (9m 3s on average), followed by <strong>Video</strong> content (6m 52s).</span>
            </li>
            <li className="flex items-start">
              <span className="mr-2 text-amber-500">•</span>
              <span>The <strong>Blog</strong> section has the highest bounce rate at <strong>28%</strong>, suggesting content may need improvement or better integration with other sections.</span>
            </li>
            <li className="flex items-start">
              <span className="mr-2 text-blue-500">•</span>
              <span>The <strong>Home</strong> page receives the most traffic but has a relatively high bounce rate of <strong>32%</strong>, indicating potential for optimization.</span>
            </li>
          </ul>
        </div>

        <div className="rounded-lg border border-gray-200 bg-white p-6 dark:border-gray-800 dark:bg-gray-900">
          <h3 className="mb-4 text-base font-semibold text-gray-900 dark:text-white">Recommendations</h3>
          <ul className="space-y-3 text-sm text-gray-600 dark:text-gray-400">
            <li className="flex items-start">
              <span className="mr-2 text-blue-500">•</span>
              <span>Consider featuring more learning resources on the home page to capitalize on growing interest.</span>
            </li>
            <li className="flex items-start">
              <span className="mr-2 text-blue-500">•</span>
              <span>Improve blog content engagement by adding related resources and agent recommendations.</span>
            </li>
            <li className="flex items-start">
              <span className="mr-2 text-blue-500">•</span>
              <span>Optimize the home page with clearer calls-to-action to reduce bounce rate and guide users to high-value sections.</span>
            </li>
            <li className="flex items-start">
              <span className="mr-2 text-blue-500">•</span>
              <span>Consider adding more interactive elements to the learning videos section to increase engagement time.</span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
}
