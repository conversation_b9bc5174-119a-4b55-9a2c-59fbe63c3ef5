'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faChartLine,
  faUsers,
  faRobot,
  faShieldAlt,
  faArrowLeft,
  faCalendarAlt,
  faExclamationTriangle,
  faChartBar
} from '@fortawesome/free-solid-svg-icons';
import {
  mockDashboardAnalytics,
  mockSectionAnalytics,
  mockAgentAnalytics,
  mockSensitiveInteractions,
  mockUserAnalytics,
  mockContentAnalytics
} from '@/data/mock-analytics';

// Import component sections
import PlatformUsageSection from './components/PlatformUsageSection';
import AgentUsageSection from './components/AgentUsageSection';
import SensitiveDataSection from './components/SensitiveDataSection';
import UserBehaviorSection from './components/UserBehaviorSection';

export default function AnalyticsPage() {
  const [timeRange, setTimeRange] = useState<'day' | 'week' | 'month'>('week');
  const [activeTab, setActiveTab] = useState<'overview' | 'platform' | 'agent' | 'sensitive' | 'user'>('overview');
  const analytics = mockDashboardAnalytics;

  // Filter high risk users
  const highRiskUsers = mockUserAnalytics.filter(user => user.riskLevel === 'high' || user.riskLevel === 'medium');

  // Map timeRange to the keys in the analytics object
  const timeRangeMap = {
    'day': 'daily',
    'week': 'weekly',
    'month': 'monthly'
  } as const;

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <Link
            href="/admin"
            className="mb-4 inline-flex items-center text-sm font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
          >
            <FontAwesomeIcon icon={faArrowLeft} className="mr-2 h-4 w-4" />
            Back to Admin Dashboard
          </Link>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Analytics Dashboard</h1>
        </div>

        {/* Time range selector */}
        <div className="flex items-center rounded-lg border border-gray-200 bg-white p-1 dark:border-gray-700 dark:bg-gray-800">
          <button
            onClick={() => setTimeRange('day')}
            className={`rounded px-3 py-1.5 text-sm font-medium ${
              timeRange === 'day'
                ? 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900/50 dark:text-indigo-300'
                : 'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200'
            }`}
          >
            Day
          </button>
          <button
            onClick={() => setTimeRange('week')}
            className={`rounded px-3 py-1.5 text-sm font-medium ${
              timeRange === 'week'
                ? 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900/50 dark:text-indigo-300'
                : 'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200'
            }`}
          >
            Week
          </button>
          <button
            onClick={() => setTimeRange('month')}
            className={`rounded px-3 py-1.5 text-sm font-medium ${
              timeRange === 'month'
                ? 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900/50 dark:text-indigo-300'
                : 'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200'
            }`}
          >
            Month
          </button>
        </div>
      </div>

      {/* Overview stats */}
      <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-4">
        {/* Total users */}
        <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-800 dark:bg-gray-900">
          <div className="flex items-center">
            <div className="mr-4 flex h-12 w-12 items-center justify-center rounded-full bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400">
              <FontAwesomeIcon icon={faUsers} className="h-6 w-6" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Users</p>
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white">{analytics.totalUsers.toLocaleString()}</h3>
            </div>
          </div>
          <div className="mt-4 flex items-center text-sm">
            <span className="font-medium text-green-600 dark:text-green-400">
              +{analytics.newUsers[timeRangeMap[timeRange]]} new
            </span>
            <span className="ml-1 text-gray-500 dark:text-gray-400">
              in the last {timeRange === 'day' ? '24 hours' : timeRange === 'week' ? '7 days' : '30 days'}
            </span>
          </div>
        </div>

        {/* Active users */}
        <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-800 dark:bg-gray-900">
          <div className="flex items-center">
            <div className="mr-4 flex h-12 w-12 items-center justify-center rounded-full bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400">
              <FontAwesomeIcon icon={faUsers} className="h-6 w-6" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Active Users</p>
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white">{analytics.activeUsers[timeRangeMap[timeRange]].toLocaleString()}</h3>
            </div>
          </div>
          <div className="mt-4 flex items-center text-sm">
            <span className="font-medium text-gray-900 dark:text-white">
              {Math.round((analytics.activeUsers[timeRangeMap[timeRange]] / analytics.totalUsers) * 100)}%
            </span>
            <span className="ml-1 text-gray-500 dark:text-gray-400">
              of total users
            </span>
          </div>
        </div>

        {/* Agent interactions */}
        <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-800 dark:bg-gray-900">
          <div className="flex items-center">
            <div className="mr-4 flex h-12 w-12 items-center justify-center rounded-full bg-purple-100 text-purple-600 dark:bg-purple-900/30 dark:text-purple-400">
              <FontAwesomeIcon icon={faRobot} className="h-6 w-6" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Agent Interactions</p>
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white">{analytics.totalAgentInteractions.toLocaleString()}</h3>
            </div>
          </div>
          <div className="mt-4 flex items-center text-sm">
            <span className="font-medium text-gray-900 dark:text-white">
              {Math.round(analytics.totalAgentInteractions / analytics.activeUsers[timeRangeMap[timeRange]])}
            </span>
            <span className="ml-1 text-gray-500 dark:text-gray-400">
              per active user
            </span>
          </div>
        </div>

        {/* Sensitivity alerts */}
        <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-800 dark:bg-gray-900">
          <div className="flex items-center">
            <div className="mr-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100 text-red-600 dark:bg-red-900/30 dark:text-red-400">
              <FontAwesomeIcon icon={faExclamationTriangle} className="h-6 w-6" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Sensitivity Alerts</p>
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white">{analytics.sensitivityAlerts.toLocaleString()}</h3>
            </div>
          </div>
          <div className="mt-4 flex items-center text-sm">
            <span className={`font-medium ${
              analytics.sensitivityAlerts > 100 ? 'text-red-600 dark:text-red-400' : 'text-yellow-600 dark:text-yellow-400'
            }`}>
              {analytics.sensitivityAlerts > 100 ? 'High' : 'Moderate'}
            </span>
            <span className="ml-1 text-gray-500 dark:text-gray-400">
              alert level
            </span>
          </div>
        </div>
      </div>

      {/* Navigation tabs for different analytics sections */}
      <div className="border-b border-gray-200 dark:border-gray-800">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('overview')}
            className={`py-4 px-1 text-sm font-medium border-b-2 ${
              activeTab === 'overview'
                ? 'border-indigo-500 text-indigo-600 dark:border-indigo-400 dark:text-indigo-400'
                : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 dark:text-gray-400 dark:hover:border-gray-700 dark:hover:text-gray-300'
            }`}
          >
            Overview
          </button>
          <button
            onClick={() => setActiveTab('platform')}
            className={`py-4 px-1 text-sm font-medium border-b-2 ${
              activeTab === 'platform'
                ? 'border-indigo-500 text-indigo-600 dark:border-indigo-400 dark:text-indigo-400'
                : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 dark:text-gray-400 dark:hover:border-gray-700 dark:hover:text-gray-300'
            }`}
          >
            Platform Usage
          </button>
          <button
            onClick={() => setActiveTab('agent')}
            className={`py-4 px-1 text-sm font-medium border-b-2 ${
              activeTab === 'agent'
                ? 'border-indigo-500 text-indigo-600 dark:border-indigo-400 dark:text-indigo-400'
                : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 dark:text-gray-400 dark:hover:border-gray-700 dark:hover:text-gray-300'
            }`}
          >
            Agent Usage
          </button>
          <button
            onClick={() => setActiveTab('sensitive')}
            className={`py-4 px-1 text-sm font-medium border-b-2 ${
              activeTab === 'sensitive'
                ? 'border-indigo-500 text-indigo-600 dark:border-indigo-400 dark:text-indigo-400'
                : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 dark:text-gray-400 dark:hover:border-gray-700 dark:hover:text-gray-300'
            }`}
          >
            Sensitive Data
          </button>
          <button
            onClick={() => setActiveTab('user')}
            className={`py-4 px-1 text-sm font-medium border-b-2 ${
              activeTab === 'user'
                ? 'border-indigo-500 text-indigo-600 dark:border-indigo-400 dark:text-indigo-400'
                : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 dark:text-gray-400 dark:hover:border-gray-700 dark:hover:text-gray-300'
            }`}
          >
            User Behavior
          </button>
        </nav>
      </div>

      {/* Tab content */}
      {activeTab === 'overview' && (
        <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-800 dark:bg-gray-900">
          <h2 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">Analytics Overview</h2>
          <p className="mb-4 text-gray-600 dark:text-gray-400">
            This dashboard provides an overview of platform usage, agent interactions, and potential sensitive data concerns.
            Use the tabs above to explore detailed analytics for specific areas.
          </p>

          <div className="mt-6 grid gap-6 md:grid-cols-2">
            <div>
              <h3 className="mb-3 text-base font-medium text-gray-900 dark:text-white">Key Metrics</h3>
              <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                <li className="flex items-center justify-between">
                  <span>Total Users:</span>
                  <span className="font-medium text-gray-900 dark:text-white">{analytics.totalUsers.toLocaleString()}</span>
                </li>
                <li className="flex items-center justify-between">
                  <span>Active Users (Weekly):</span>
                  <span className="font-medium text-gray-900 dark:text-white">{analytics.activeUsers.weekly.toLocaleString()}</span>
                </li>
                <li className="flex items-center justify-between">
                  <span>Total Agent Interactions:</span>
                  <span className="font-medium text-gray-900 dark:text-white">{analytics.totalAgentInteractions.toLocaleString()}</span>
                </li>
                <li className="flex items-center justify-between">
                  <span>Total Content Views:</span>
                  <span className="font-medium text-gray-900 dark:text-white">{analytics.totalContentViews.toLocaleString()}</span>
                </li>
                <li className="flex items-center justify-between">
                  <span>Average Session Duration:</span>
                  <span className="font-medium text-gray-900 dark:text-white">
                    {Math.floor(analytics.averageSessionDuration / 60)}m {analytics.averageSessionDuration % 60}s
                  </span>
                </li>
                <li className="flex items-center justify-between">
                  <span>Sensitivity Alerts:</span>
                  <span className="font-medium text-red-600 dark:text-red-400">{analytics.sensitivityAlerts.toLocaleString()}</span>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="mb-3 text-base font-medium text-gray-900 dark:text-white">Quick Summary</h3>
              <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                <li className="flex items-start">
                  <span className="mr-2 text-green-500">•</span>
                  <span>The platform has seen a <strong>12.5%</strong> increase in user activity over the past week.</span>
                </li>
                <li className="flex items-start">
                  <span className="mr-2 text-green-500">•</span>
                  <span>The <strong>Learning Resources</strong> section has the highest growth rate at <strong>42.7%</strong>.</span>
                </li>
                <li className="flex items-start">
                  <span className="mr-2 text-amber-500">•</span>
                  <span>The <strong>Customer Support</strong> agent has the highest sensitivity score and requires monitoring.</span>
                </li>
                <li className="flex items-start">
                  <span className="mr-2 text-red-500">•</span>
                  <span>There are <strong>{analytics.sensitivityAlerts}</strong> sensitivity alerts that need review.</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'platform' && (
        <PlatformUsageSection sectionAnalytics={mockSectionAnalytics} />
      )}

      {activeTab === 'agent' && (
        <AgentUsageSection agentAnalytics={mockAgentAnalytics} />
      )}

      {activeTab === 'sensitive' && (
        <SensitiveDataSection
          sensitiveInteractions={mockSensitiveInteractions}
          highRiskUsers={highRiskUsers}
        />
      )}

      {activeTab === 'user' && (
        <UserBehaviorSection
          userAnalytics={mockUserAnalytics}
          contentAnalytics={mockContentAnalytics}
        />
      )}
    </div>
  );
}
