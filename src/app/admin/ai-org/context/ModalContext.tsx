'use client';

import React, { createContext, useContext, useState, ReactNode } from 'react';
import { Tool } from '@/types/ai-org';

interface ModalContextType {
  isToolModalOpen: boolean;
  selectedTool: Tool | null;
  isInstructionsModalOpen: boolean;
  openToolModal: (tool: Tool) => void;
  closeToolModal: () => void;
  openInstructionsModal: () => void;
  closeInstructionsModal: () => void;
}

const ModalContext = createContext<ModalContextType | undefined>(undefined);

export const ModalProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [isToolModalOpen, setIsToolModalOpen] = useState(false);
  const [selectedTool, setSelectedTool] = useState<Tool | null>(null);
  const [isInstructionsModalOpen, setIsInstructionsModalOpen] = useState(false);

  const openToolModal = (tool: Tool) => {
    setSelectedTool(tool);
    setIsToolModalOpen(true);
  };

  const closeToolModal = () => {
    setIsToolModalOpen(false);
  };

  const openInstructionsModal = () => {
    setIsInstructionsModalOpen(true);
  };

  const closeInstructionsModal = () => {
    setIsInstructionsModalOpen(false);
  };

  return (
    <ModalContext.Provider
      value={{
        isToolModalOpen,
        selectedTool,
        isInstructionsModalOpen,
        openToolModal,
        closeToolModal,
        openInstructionsModal,
        closeInstructionsModal,
      }}
    >
      {children}
    </ModalContext.Provider>
  );
};

export const useModal = (): ModalContextType => {
  const context = useContext(ModalContext);
  if (context === undefined) {
    throw new Error('useModal must be used within a ModalProvider');
  }
  return context;
};
