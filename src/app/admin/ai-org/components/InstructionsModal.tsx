'use client';

import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/free-solid-svg-icons';

interface InstructionsModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const InstructionsModal: React.FC<InstructionsModalProps> = ({ isOpen, onClose }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[100] flex items-center justify-center backdrop-blur-sm bg-black/30 dark:bg-gray-900/30">
      <div className="relative w-full max-w-md rounded-lg bg-white/90 dark:bg-gray-800/90 backdrop-blur-md p-6 shadow-xl border border-gray-200 dark:border-gray-700">
        <button
          onClick={onClose}
          className="absolute top-2 right-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
        >
          <FontAwesomeIcon icon={faTimes} className="h-5 w-5" />
        </button>

        <h2 className="text-xl font-bold mb-4">Organization Chart Instructions</h2>

        <ul className="list-disc pl-5 space-y-2 text-sm text-gray-600 dark:text-gray-400">
          <li>Click the + / - buttons on nodes to expand or collapse individual departments</li>
          <li>Use the "Expand All" button to see the entire organization at once</li>
          <li>Click "Fullscreen" to view the chart in fullscreen mode</li>
          <li>In fullscreen mode, click and drag to pan around the chart</li>
          <li>Use the zoom buttons to adjust the view size</li>
          <li>Use the search and filters to find specific agents</li>
          <li>The colored dots indicate implementation status (green = active, blue = in development, etc.)</li>
        </ul>

        <button
          onClick={onClose}
          className="w-full mt-6 rounded-md bg-indigo-600 py-2 px-4 text-white hover:bg-indigo-700 transition-colors"
        >
          Close
        </button>
      </div>
    </div>
  );
};

export default InstructionsModal;
