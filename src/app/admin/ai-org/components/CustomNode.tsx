'use client';

import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCircle, faPlus, faMinus } from '@fortawesome/free-solid-svg-icons';
import { AgentStatus, OrgNode, Tool } from '@/types/ai-org';

interface CustomNodeProps {
  node: OrgNode;
  depth?: number;
  getNodeIcon: (node: OrgNode) => any;
  getStatusColor: (status: AgentStatus) => string;
  getStatusLabel: (status: AgentStatus) => string;
  toggleNode: (id: string) => void;
  showAllNodes: boolean;
  expandedNodes: Set<string>;
  onToolClick?: (tool: Tool) => void;
  getToolIcon?: (tool: Tool) => any;
}

const CustomNode: React.FC<CustomNodeProps> = ({
  node,
  depth = 0,
  getNodeIcon,
  getStatusColor,
  getStatusLabel,
  toggleNode,
  showAllNodes,
  expandedNodes,
  onToolClick,
  getToolIcon
}) => {
  const handleToolClick = (e: React.MouseEvent, tool: Tool) => {
    e.stopPropagation();
    if (onToolClick) {
      onToolClick(tool);
    }
  };
  const hasChildren = node.children && node.children.length > 0;
  const isExpanded = showAllNodes || expandedNodes.has(node.id);
  const nodeIcon = getNodeIcon(node);
  const hasTools = node.tools && node.tools.length > 0;

  return (
    <div className="flex flex-col items-center">
      <div
        className={`relative p-4 rounded-lg border-2 ${
          depth === 0
            ? 'bg-gradient-to-r from-indigo-600 to-purple-600 text-white border-indigo-700'
            : 'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700'
        } shadow-md w-64 h-[240px] flex flex-col transition-all duration-300 hover:shadow-lg`}
      >
        <div className="absolute -top-2 -right-2">
          <FontAwesomeIcon
            icon={faCircle}
            className={`h-4 w-4 ${getStatusColor(node.status)}`}
            title={getStatusLabel(node.status)}
          />
        </div>

        <div className="flex-grow">
          <div className="flex items-center mb-2">
            {nodeIcon && (
              <div className={`mr-3 flex-shrink-0 rounded-full p-2 ${
                depth === 0
                  ? 'bg-indigo-500 bg-opacity-50'
                  : 'bg-indigo-100 dark:bg-indigo-900 dark:bg-opacity-30'
              }`}>
                <FontAwesomeIcon
                  icon={nodeIcon}
                  className={`h-5 w-5 ${
                    depth === 0
                      ? 'text-white'
                      : 'text-indigo-600 dark:text-indigo-400'
                  }`}
                />
              </div>
            )}
            <h3 className="font-bold text-lg truncate">{node.name}</h3>
          </div>

          <p className={`text-sm ${depth === 0 ? 'text-indigo-200' : 'text-gray-600 dark:text-gray-300'}`}>
            {node.title}
          </p>
          <p className={`text-xs mt-2 ${depth === 0 ? 'text-indigo-200' : 'text-gray-500 dark:text-gray-400'}`}>
            {node.department}
          </p>
          <div className="h-[60px] overflow-hidden">
            <p className={`text-xs mt-2 line-clamp-3 ${depth === 0 ? 'text-indigo-100' : 'text-gray-500 dark:text-gray-400'}`}>
              {node.description}
            </p>
          </div>
        </div>

        {/* Tools section */}
        <div className="mt-auto">
          <div className={`mt-2 pt-2 border-t ${depth === 0 ? 'border-indigo-500' : 'border-gray-200 dark:border-gray-700'}`}>
            {hasTools ? (
              <div className="flex flex-wrap gap-2 justify-center h-[40px] overflow-y-auto">
                {node.tools?.map((tool: Tool) => (
                  <div
                    key={tool.id}
                    className={`p-1 rounded-md ${depth === 0 ? 'bg-indigo-500 bg-opacity-30' : 'bg-gray-100 dark:bg-gray-700'}`}
                    title={tool.name}
                  >
                    {getToolIcon && (
                      <div
                        className="tool-icon cursor-pointer"
                        title={tool.name}
                        onClick={(e: React.MouseEvent) => handleToolClick(e, tool)}
                      >
                        {tool.productIcon ? (
                          <img
                            src={tool.productIcon}
                            alt={tool.name}
                            className="h-5 w-5 rounded-sm object-contain"
                          />
                        ) : (
                          <FontAwesomeIcon
                            icon={getToolIcon(tool)}
                            className="h-4 w-4 text-gray-600 dark:text-gray-400"
                          />
                        )}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center h-[40px] flex items-center justify-center">
                <p className={`text-xs italic ${depth === 0 ? 'text-indigo-300' : 'text-gray-400 dark:text-gray-500'}`}>
                  Tools in progress...
                </p>
              </div>
            )}
          </div>
        </div>

        {hasChildren && !showAllNodes && (
          <button
            onClick={(e: React.MouseEvent) => {
              e.stopPropagation();
              toggleNode(node.id);
            }}
            className={`absolute bottom-2 right-2 p-1 rounded-full ${
              depth === 0
                ? 'bg-indigo-500 hover:bg-indigo-400 text-white'
                : 'bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300'
            }`}
            aria-label={isExpanded ? 'Collapse' : 'Expand'}
          >
            <FontAwesomeIcon icon={isExpanded ? faMinus : faPlus} className="h-3 w-3" />
          </button>
        )}
      </div>
    </div>
  );
};

export default CustomNode;
