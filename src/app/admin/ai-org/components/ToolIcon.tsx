'use client';

import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Tool } from '@/types/ai-org';

interface ToolIconProps {
  tool: Tool;
  getToolIcon: (tool: Tool) => any;
  onToolClick: (tool: Tool) => void;
}

const ToolIcon: React.FC<ToolIconProps> = ({ tool, getToolIcon, onToolClick }) => {
  const icon = getToolIcon(tool);
  
  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onToolClick(tool);
  };
  
  if (tool.productIcon) {
    return (
      <div 
        className="tool-icon cursor-pointer" 
        title={tool.name}
        onClick={handleClick}
      >
        <img 
          src={tool.productIcon} 
          alt={tool.name} 
          className="h-5 w-5 rounded-sm object-contain"
        />
      </div>
    );
  }
  
  if (icon) {
    return (
      <div 
        className="tool-icon cursor-pointer" 
        title={tool.name}
        onClick={handleClick}
      >
        <FontAwesomeIcon 
          icon={icon} 
          className="h-4 w-4 text-gray-600 dark:text-gray-400" 
        />
      </div>
    );
  }
  
  return null;
};

export default ToolIcon;
