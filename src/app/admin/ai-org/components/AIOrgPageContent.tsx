'use client';

import React, { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSearch, faCircle } from '@fortawesome/free-solid-svg-icons';
import { mockOrgData } from '@/data/mock-org-data';
import { AgentStatus, OrgNode } from '@/types/ai-org';
import OrgChart from './OrgChart';
import ToolDetailModal from './ToolDetailModal';
import InstructionsModal from './InstructionsModal';
import { useModal } from '../context/ModalContext';
import { getNodeIcon, getToolIcon, getStatusColor, getStatusLabel } from '@/utils/icon-helpers';

export default function AIOrgPageContent() {
  const { isToolModalOpen, selectedTool, isInstructionsModalOpen, closeToolModal, closeInstructionsModal } = useModal();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<AgentStatus | 'all'>('all');
  const [departmentFilter, setDepartmentFilter] = useState<string>('all');
  const [filteredOrgData, setFilteredOrgData] = useState<OrgNode | null>(mockOrgData);
  const [departments, setDepartments] = useState<string[]>([]);

  // Extract unique departments from the org data
  useEffect(() => {
    const deptSet = new Set<string>();
    
    const extractDepartments = (node: OrgNode) => {
      deptSet.add(node.department);
      if (node.children) {
        node.children.forEach(extractDepartments);
      }
    };
    
    extractDepartments(mockOrgData);
    setDepartments(Array.from(deptSet).sort());
  }, []);

  // Filter the org data based on search term and filters
  useEffect(() => {
    if (!searchTerm && statusFilter === 'all' && departmentFilter === 'all') {
      setFilteredOrgData(mockOrgData);
      return;
    }
    
    const searchTermLower = searchTerm.toLowerCase();
    
    const filterNode = (node: OrgNode): OrgNode | null => {
      // Check if this node matches the filters
      const matchesSearch = searchTerm === '' || 
        node.name.toLowerCase().includes(searchTermLower) || 
        node.title.toLowerCase().includes(searchTermLower) || 
        node.description.toLowerCase().includes(searchTermLower);
      
      const matchesStatus = statusFilter === 'all' || node.status === statusFilter;
      const matchesDepartment = departmentFilter === 'all' || node.department === departmentFilter;
      
      const nodeMatches = matchesSearch && matchesStatus && matchesDepartment;
      
      // If this node has children, filter them too
      let filteredChildren: OrgNode[] = [];
      if (node.children) {
        filteredChildren = node.children
          .map(filterNode)
          .filter((child): child is OrgNode => child !== null);
      }
      
      // If this node matches or has matching children, include it
      if (nodeMatches || filteredChildren.length > 0) {
        return {
          ...node,
          children: filteredChildren.length > 0 ? filteredChildren : undefined
        };
      }
      
      return null;
    };
    
    const filtered = filterNode(mockOrgData);
    setFilteredOrgData(filtered);
  }, [searchTerm, statusFilter, departmentFilter]);

  return (
    <div className="space-y-6">
      {/* Modals */}
      {isToolModalOpen && (
        <ToolDetailModal
          tool={selectedTool}
          isOpen={isToolModalOpen}
          onClose={closeToolModal}
          getToolIcon={getToolIcon}
        />
      )}
      {isInstructionsModalOpen && (
        <InstructionsModal
          isOpen={isInstructionsModalOpen}
          onClose={closeInstructionsModal}
        />
      )}
      <div className="rounded-lg bg-gradient-to-r from-indigo-600 to-purple-600 p-8 text-white shadow-lg">
        <h1 className="mb-2 text-3xl font-bold">AI Organization</h1>
        <p className="text-lg">
          Manage and visualize your AI agent organization structure, roles, and implementation status.
        </p>
      </div>

      {/* Controls */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4 sticky top-20 z-10 backdrop-blur-sm bg-opacity-90 dark:bg-opacity-90">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <FontAwesomeIcon icon={faSearch} className="text-gray-500 dark:text-gray-400" />
              </div>
              <input
                type="text"
                className="block w-full rounded-md border-gray-300 py-2 px-3 pl-10 focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white sm:text-sm"
                placeholder="Search agents..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>

          <div className="flex gap-4">
            <div>
              <label htmlFor="status-filter" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Status
              </label>
              <select
                id="status-filter"
                className="block w-full rounded-md border-gray-300 py-2 px-3 focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white sm:text-sm"
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value as AgentStatus | 'all')}
              >
                <option value="all">All Statuses</option>
                <option value="active">Active</option>
                <option value="in-development">In Development</option>
                <option value="planned">Planned</option>
                <option value="concept">Concept</option>
              </select>
            </div>

            <div>
              <label htmlFor="department-filter" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Department
              </label>
              <select
                id="department-filter"
                className="block w-full rounded-md border-gray-300 py-2 px-3 focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white sm:text-sm"
                value={departmentFilter}
                onChange={(e) => setDepartmentFilter(e.target.value)}
              >
                <option value="all">All Departments</option>
                {departments.map(dept => (
                  <option key={dept} value={dept}>{dept}</option>
                ))}
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Legend */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
        <h2 className="text-lg font-semibold mb-2">Status Legend</h2>
        <div className="flex flex-wrap gap-4">
          <div className="flex items-center">
            <FontAwesomeIcon icon={faCircle} className="h-3 w-3 text-green-500 mr-2" />
            <span className="text-sm">Active</span>
          </div>
          <div className="flex items-center">
            <FontAwesomeIcon icon={faCircle} className="h-3 w-3 text-blue-500 mr-2" />
            <span className="text-sm">In Development</span>
          </div>
          <div className="flex items-center">
            <FontAwesomeIcon icon={faCircle} className="h-3 w-3 text-amber-500 mr-2" />
            <span className="text-sm">Planned</span>
          </div>
          <div className="flex items-center">
            <FontAwesomeIcon icon={faCircle} className="h-3 w-3 text-gray-500 mr-2" />
            <span className="text-sm">Concept</span>
          </div>
        </div>
      </div>

      {/* Org Chart Component */}
      <OrgChart
        orgData={filteredOrgData}
        getNodeIcon={getNodeIcon}
        getToolIcon={getToolIcon}
        getStatusColor={getStatusColor}
        getStatusLabel={getStatusLabel}
      />
    </div>
  );
}
