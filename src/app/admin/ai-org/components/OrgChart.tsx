'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Tree, TreeNode } from 'react-organizational-chart';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faPlus,
  faMinus,
  faExpand,
  faArrowsUpDownLeftRight,
  faMaximize,
  faMinimize,
  faHandPaper,
  faQuestionCircle
} from '@fortawesome/free-solid-svg-icons';
import { AgentStatus, OrgNode, Tool } from '@/types/ai-org';
import CustomNode from './CustomNode';
import { useModal } from '../context/ModalContext';

interface OrgChartProps {
  orgData: OrgNode | null;
  getNodeIcon: (node: OrgNode) => any;
  getToolIcon: (tool: Tool) => any;
  getStatusColor: (status: AgentStatus) => string;
  getStatusLabel: (status: AgentStatus) => string;
}

const OrgChart: React.FC<OrgChartProps> = ({
  orgData,
  getNodeIcon,
  getToolIcon,
  getStatusColor,
  getStatusLabel
}) => {
  const [zoomLevel, setZoomLevel] = useState(1);
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set());
  const [showAllNodes, setShowAllNodes] = useState(false);
  const { openToolModal, openInstructionsModal } = useModal();
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isPanning, setIsPanning] = useState(false);
  const [panPosition, setPanPosition] = useState({ x: 0, y: 0 });
  const [panStart, setPanStart] = useState({ x: 0, y: 0 });

  const chartRef = useRef<HTMLDivElement>(null);
  const chartContainerRef = useRef<HTMLDivElement>(null);

  // Handle zoom
  const handleZoom = (direction: number) => {
    setZoomLevel(prev => {
      const newZoom = prev + direction * 0.1;
      return Math.max(0.5, Math.min(2, newZoom));
    });
  };

  // Toggle node expansion
  const toggleNode = (id: string) => {
    setExpandedNodes(prev => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  };

  // Handle fullscreen toggle
  const toggleFullscreen = () => {
    if (typeof document === 'undefined' || !chartContainerRef.current) return;

    if (!isFullscreen) {
      if (chartContainerRef.current.requestFullscreen) {
        chartContainerRef.current.requestFullscreen();
      }
      setIsFullscreen(true);
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      }
      setIsFullscreen(false);
      // Reset pan position when exiting fullscreen
      setPanPosition({ x: 0, y: 0 });
    }
  };

  // Handle panning start
  const handlePanStart = (e: React.MouseEvent) => {
    // Ignore clicks on buttons or their children
    if (e.target instanceof Element) {
      const targetElement = e.target as Element;
      if (targetElement.closest('button') || targetElement.tagName === 'BUTTON') {
        return;
      }
    }

    if (e.button === 0) { // Left mouse button
      setIsPanning(true);
      setPanStart({ x: e.clientX - panPosition.x, y: e.clientY - panPosition.y });

      // Disable text selection during panning
      if (chartContainerRef.current && typeof document !== 'undefined') {
        chartContainerRef.current.style.userSelect = 'none';
        // Add a class to disable text selection across all browsers
        document.body.classList.add('no-select');
      }
    }
  };

  // Handle panning movement
  const handlePanMove = (e: React.MouseEvent) => {
    if (isPanning) {
      setPanPosition({
        x: e.clientX - panStart.x,
        y: e.clientY - panStart.y
      });
    }
  };

  // Handle panning end
  const handlePanEnd = () => {
    setIsPanning(false);

    // Re-enable text selection
    if (chartContainerRef.current && typeof document !== 'undefined') {
      chartContainerRef.current.style.userSelect = '';
      // Remove the no-select class
      document.body.classList.remove('no-select');
    }
  };

  // Handle mouse wheel for zooming
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const handleWheel = (e: WheelEvent) => {
        if (e.ctrlKey || e.metaKey) {
          e.preventDefault();
          handleZoom(e.deltaY > 0 ? -1 : 1);
        }
      };

      const chart = chartRef.current;
      if (chart) {
        chart.addEventListener('wheel', handleWheel, { passive: false });
      }

      return () => {
        if (chart) {
          chart.removeEventListener('wheel', handleWheel);
        }
      };
    }
  }, []);

  // Handle fullscreen change events
  useEffect(() => {
    if (typeof document !== 'undefined') {
      const handleFullscreenChange = () => {
        setIsFullscreen(!!document.fullscreenElement);
        if (!document.fullscreenElement) {
          // Reset pan position when exiting fullscreen
          setPanPosition({ x: 0, y: 0 });
        }
      };

      document.addEventListener('fullscreenchange', handleFullscreenChange);

      return () => {
        document.removeEventListener('fullscreenchange', handleFullscreenChange);
      };
    }
  }, []);

  // Handle tool click
  const handleToolClick = (tool: Tool) => {
    openToolModal(tool);
  };

  // Recursive function to render the org chart
  const renderOrgChart = (node: OrgNode, depth = 0) => {
    if (!node) return null;

    const hasChildren = node.children && node.children.length > 0;
    const isExpanded = showAllNodes || expandedNodes.has(node.id);

    return (
      <TreeNode
        key={node.id}
        label={
          <CustomNode
            node={node}
            depth={depth}
            getNodeIcon={getNodeIcon}
            getStatusColor={getStatusColor}
            getStatusLabel={getStatusLabel}
            toggleNode={toggleNode}
            showAllNodes={showAllNodes}
            expandedNodes={expandedNodes}
            onToolClick={handleToolClick}
            getToolIcon={getToolIcon}
          />
        }
      >
        {hasChildren && isExpanded && node.children &&
          node.children.map(child => renderOrgChart(child, depth + 1))
        }
      </TreeNode>
    );
  };

  return (
    <div
        ref={chartContainerRef}
        className={`bg-white dark:bg-gray-800 rounded-lg shadow p-4 ${isFullscreen ? 'fixed top-16 left-0 right-0 bottom-0 z-50' : 'flex flex-col'} ${isPanning ? 'no-select' : ''}`}
        style={{
          height: isFullscreen ? 'calc(100vh - 64px)' : 'calc(100vh - 400px)', /* 64px is the height of the top bar */
          minHeight: isFullscreen ? 'calc(100vh - 64px)' : '500px',
          cursor: isPanning ? 'grabbing' : 'default',
          display: 'flex',
          flexDirection: 'column'
        }}
        onMouseDown={isFullscreen ? handlePanStart : undefined}
        onMouseMove={isFullscreen ? handlePanMove : undefined}
        onMouseUp={isFullscreen ? handlePanEnd : undefined}
        onMouseLeave={isFullscreen ? handlePanEnd : undefined}
      >
        <div className="relative flex justify-center mb-4 gap-2">
          <button
            onClick={() => openInstructionsModal()}
            className="absolute right-0 top-0 p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            aria-label="View instructions"
          >
            <FontAwesomeIcon icon={faQuestionCircle} className="h-5 w-5" />
          </button>
          <button
            onClick={() => setShowAllNodes(prev => !prev)}
            className="flex items-center gap-2 px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors"
          >
            <FontAwesomeIcon icon={showAllNodes ? faMinus : faExpand} className="h-4 w-4" />
            {showAllNodes ? 'Collapse All' : 'Expand All'}
          </button>

          <button
            onClick={toggleFullscreen}
            className="flex items-center gap-2 px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors"
          >
            <FontAwesomeIcon icon={isFullscreen ? faMinimize : faMaximize} className="h-4 w-4" />
            {isFullscreen ? 'Exit Fullscreen' : 'Fullscreen'}
          </button>

          {isFullscreen && (
            <div className="ml-4 text-sm text-gray-500 dark:text-gray-400 flex items-center">
              <FontAwesomeIcon icon={faHandPaper} className="h-4 w-4 mr-2" />
              Click and drag to pan around
            </div>
          )}
        </div>

        <div
          className="flex-grow flex items-center justify-center w-full overflow-auto"
          style={{
            minHeight: '0',
            maxHeight: '100%',
            overflow: 'auto',
            scrollbarWidth: 'thin',
            scrollbarColor: '#d1d5db transparent',
            marginBottom: '0'
          }}
        >
          <div
            ref={chartRef}
            className="relative mx-auto"
            style={{
              transform: `scale(${zoomLevel})`,
              transformOrigin: 'center center',
              transition: 'transform 0.3s ease',
              ...(isFullscreen && {
                transform: `translate(${panPosition.x}px, ${panPosition.y}px) scale(${zoomLevel})`,
              })
            }}
          >
            {orgData ? (
              <Tree
                lineWidth={'2px'}
                lineColor={'#d1d5db'}
                lineBorderRadius={'10px'}
                label={
                  <CustomNode
                    node={orgData}
                    depth={0}
                    getNodeIcon={getNodeIcon}
                    getStatusColor={getStatusColor}
                    getStatusLabel={getStatusLabel}
                    toggleNode={toggleNode}
                    showAllNodes={showAllNodes}
                    expandedNodes={expandedNodes}
                    onToolClick={handleToolClick}
                    getToolIcon={getToolIcon}
                  />
                }
              >
                {orgData.children && orgData.children.map(child =>
                  renderOrgChart(child, 1)
                )}
              </Tree>
            ) : (
              <div className="text-center p-8 text-gray-500 dark:text-gray-400">
                No agents match your search criteria
              </div>
            )}
          </div>
        </div>

        {isFullscreen && (
          <div className="absolute bottom-4 right-4 bg-white dark:bg-gray-800 rounded-lg shadow p-2 flex gap-2">
            <button
              onClick={() => handleZoom(1)}
              className="p-2 rounded-md bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300"
              aria-label="Zoom in"
            >
              <FontAwesomeIcon icon={faPlus} />
            </button>
            <button
              onClick={() => handleZoom(-1)}
              className="p-2 rounded-md bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300"
              aria-label="Zoom out"
            >
              <FontAwesomeIcon icon={faMinus} />
            </button>
            <button
              onClick={() => {
                setZoomLevel(1);
                setPanPosition({ x: 0, y: 0 });
              }}
              className="p-2 rounded-md bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300"
              aria-label="Reset view"
            >
              <FontAwesomeIcon icon={faArrowsUpDownLeftRight} />
            </button>
          </div>
        )}
      </div>
  );
};

export default OrgChart;
