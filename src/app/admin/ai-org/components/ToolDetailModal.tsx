'use client';

import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/free-solid-svg-icons';
import { Tool } from '@/types/ai-org';

interface ToolDetailModalProps {
  tool: Tool | null;
  isOpen: boolean;
  onClose: () => void;
  getToolIcon: (tool: Tool) => any;
}

const ToolDetailModal: React.FC<ToolDetailModalProps> = ({
  tool,
  isOpen,
  onClose,
  getToolIcon
}) => {
  if (!tool || !isOpen) return null;

  const icon = getToolIcon(tool);

  return (
    <div className="fixed inset-0 z-[100] flex items-center justify-center backdrop-blur-sm bg-black/30 dark:bg-gray-900/30">
      <div className="relative w-full max-w-md rounded-lg bg-white/90 dark:bg-gray-800/90 backdrop-blur-md p-6 shadow-xl border border-gray-200 dark:border-gray-700">
        <button
          onClick={onClose}
          className="absolute top-2 right-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
        >
          <FontAwesomeIcon icon={faTimes} className="h-5 w-5" />
        </button>

        <div className="flex items-center mb-4">
          {tool.productIcon ? (
            <img
              src={tool.productIcon}
              alt={tool.name}
              className="h-12 w-12 mr-4 rounded-md object-contain"
            />
          ) : icon ? (
            <div className="flex h-12 w-12 mr-4 items-center justify-center rounded-md bg-indigo-100 dark:bg-indigo-900">
              <FontAwesomeIcon
                icon={icon}
                className="h-8 w-8 text-indigo-600 dark:text-indigo-400"
              />
            </div>
          ) : null}

          <h2 className="text-xl font-bold">{tool.name}</h2>
        </div>

        <div className="mb-4">
          <h3 className="text-sm font-semibold text-gray-500 dark:text-gray-400 mb-2">Description</h3>
          <p className="text-gray-700 dark:text-gray-300">
            {tool.description || `A powerful tool used for ${tool.name} related tasks.`}
          </p>
        </div>

        {tool.url && (
          <div className="mb-4">
            <h3 className="text-sm font-semibold text-gray-500 dark:text-gray-400 mb-2">Website</h3>
            <a
              href={tool.url}
              target="_blank"
              rel="noopener noreferrer"
              className="text-indigo-600 hover:text-indigo-800 dark:text-indigo-400 dark:hover:text-indigo-300"
            >
              {tool.url}
            </a>
          </div>
        )}

        <div className="mb-4">
          <h3 className="text-sm font-semibold text-gray-500 dark:text-gray-400 mb-2">Used by</h3>
          <p className="text-gray-700 dark:text-gray-300">
            {tool.usedBy || 'Various AI agents in the organization'}
          </p>
        </div>

        <button
          onClick={onClose}
          className="w-full rounded-md bg-indigo-600 py-2 px-4 text-white hover:bg-indigo-700 transition-colors"
        >
          Close
        </button>
      </div>
    </div>
  );
};

export default ToolDetailModal;
