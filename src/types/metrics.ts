export type TimeRange = 'day' | 'week' | 'month' | 'year';

export type UsageMetric = {
  agentId: string;
  totalUses: number;
  averageRating: number;
  completionRate: number;
  averageSessionDuration: number; // in seconds
  popularityScore: number; // 0-100
};

export type UsageOverTime = {
  date: string;
  count: number;
};

export type CategoryDistribution = {
  category: string;
  count: number;
  percentage: number;
};

export type UserFeedback = {
  rating: number; // 1-5
  count: number;
};

export type UsageByTime = {
  hour: number; // 0-23
  count: number;
};

export type UsageByDay = {
  day: string; // Monday, Tuesday, etc.
  count: number;
};

export type TopQuery = {
  query: string;
  count: number;
};

export type AgentComparison = {
  agentId: string;
  metric: string;
  value: number;
};

export type AgentMetrics = {
  id: string;
  name: string;
  usageMetrics: UsageMetric;
  usageOverTime: UsageOverTime[];
  userFeedback: UserFeedback[];
  usageByTime: UsageByTime[];
  usageByDay: UsageByDay[];
  topQueries: TopQuery[];
};
