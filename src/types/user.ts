// User role types
export type UserRole = 
  | 'admin'
  | 'manager'
  | 'user'
  | 'guest';

// User status types
export type UserStatus = 
  | 'active'
  | 'inactive'
  | 'pending'
  | 'suspended';

// User permissions
export interface UserPermissions {
  canCreateAgents: boolean;
  canEditAgents: boolean;
  canDeleteAgents: boolean;
  canManageUsers: boolean;
  canAccessAdmin: boolean;
  canAccessAnalytics: boolean;
  canManageContent: boolean;
  canApproveContent: boolean;
}

// User profile information
export interface UserProfile {
  firstName: string;
  lastName: string;
  jobTitle?: string;
  department?: string;
  location?: string;
  bio?: string;
  avatarUrl?: string;
  phoneNumber?: string;
}

// User settings
export interface UserSettings {
  theme: 'light' | 'dark' | 'system';
  emailNotifications: boolean;
  twoFactorEnabled: boolean;
  sidebarCollapsed: boolean;
  language: string;
}

// User activity
export interface UserActivity {
  lastLogin: Date;
  lastActive: Date;
  totalLogins: number;
  totalSessions: number;
  averageSessionDuration: number; // in seconds
  favoriteAgents: string[];
  favoriteSection: string;
}

// Complete user object
export interface User {
  id: string;
  email: string;
  username: string;
  role: UserRole;
  status: UserStatus;
  permissions: UserPermissions;
  profile: UserProfile;
  settings: UserSettings;
  activity: UserActivity;
  createdAt: Date;
  updatedAt: Date;
  lastPasswordChange?: Date;
}

// User creation form data
export interface UserCreateData {
  email: string;
  username: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  jobTitle?: string;
  department?: string;
}

// User update form data
export interface UserUpdateData {
  email?: string;
  username?: string;
  role?: UserRole;
  status?: UserStatus;
  permissions?: Partial<UserPermissions>;
  profile?: Partial<UserProfile>;
  settings?: Partial<UserSettings>;
}
