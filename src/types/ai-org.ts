// Define agent status types
export type AgentStatus = 'active' | 'in-development' | 'planned' | 'concept';

// Define tool interface for AI agents
export interface Tool {
  id: string;
  name: string;
  icon?: string; // FontAwesome icon or product icon name
  productIcon?: string; // URL to product icon image
  description?: string; // Description of the tool
  url?: string; // URL to the tool's website
  usedBy?: string; // Information about who uses the tool
}

// Define organization node interface
export interface OrgNode {
  id: string;
  name: string;
  title: string;
  department: string;
  status: AgentStatus;
  description: string;
  icon?: string;
  tools?: Tool[];
  children?: OrgNode[];
}
