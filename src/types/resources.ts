export type ResourceCategory = 
  | 'productivity' 
  | 'project-management' 
  | 'design' 
  | 'development' 
  | 'research' 
  | 'analytics' 
  | 'communication' 
  | 'collaboration';

export interface Resource {
  id: string;
  name: string;
  description: string;
  url: string;
  category: ResourceCategory;
  tags?: string[];
  pricing?: 'free' | 'freemium' | 'paid' | 'enterprise';
  logoUrl?: string;
}

export interface ResourceCategoryInfo {
  id: ResourceCategory;
  name: string;
  description: string;
  icon: string; // Font Awesome icon name
}
