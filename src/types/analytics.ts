// User activity types
export type UserActivityType = 
  | 'page_view'
  | 'agent_interaction'
  | 'content_view'
  | 'search'
  | 'login'
  | 'registration'
  | 'feature_use';

// Platform section types
export type PlatformSection = 
  | 'home'
  | 'browse'
  | 'agent_detail'
  | 'learning'
  | 'learning_videos'
  | 'learning_articles'
  | 'learning_blog'
  | 'learning_resources'
  | 'profile'
  | 'admin';

// Content types for analytics
export type ContentType = 
  | 'video'
  | 'article'
  | 'blog'
  | 'resource'
  | 'agent';

// Sensitivity level for data monitoring
export type SensitivityLevel = 
  | 'none'
  | 'low'
  | 'medium'
  | 'high'
  | 'critical';

// User activity record
export interface UserActivity {
  id: string;
  userId: string;
  timestamp: Date;
  activityType: UserActivityType;
  section: PlatformSection;
  details: Record<string, any>;
  duration?: number; // in seconds
}

// Agent interaction record
export interface AgentInteraction {
  id: string;
  userId: string;
  agentId: string;
  timestamp: Date;
  queryText: string;
  responseText: string;
  duration: number; // in seconds
  sensitivityScore: number; // 0-100
  sensitivityFlags: string[];
  externalApiUsed: boolean;
  externalApiName?: string;
}

// Content engagement record
export interface ContentEngagement {
  id: string;
  userId: string;
  contentId: string;
  contentType: ContentType;
  timestamp: Date;
  duration: number; // in seconds
  completed: boolean;
  engagementScore: number; // 0-100 (calculated based on interaction)
}

// Aggregated analytics for platform sections
export interface SectionAnalytics {
  section: PlatformSection;
  viewCount: number;
  uniqueUsers: number;
  averageDuration: number;
  bounceRate: number;
  trend: 'up' | 'down' | 'stable';
  percentChange: number;
}

// Aggregated analytics for agents
export interface AgentAnalytics {
  agentId: string;
  agentName: string;
  interactionCount: number;
  uniqueUsers: number;
  averageDuration: number;
  sensitivityScore: number;
  popularQueries: Array<{query: string, count: number}>;
  externalApiUsage: Record<string, number>;
}

// Aggregated analytics for content
export interface ContentAnalytics {
  contentId: string;
  contentTitle: string;
  contentType: ContentType;
  viewCount: number;
  uniqueUsers: number;
  averageDuration: number;
  completionRate: number;
  engagementScore: number;
}

// User analytics
export interface UserAnalytics {
  userId: string;
  userName: string;
  lastActive: Date;
  totalSessions: number;
  totalTimeSpent: number;
  favoriteSection: PlatformSection;
  favoriteAgents: string[];
  sensitivityScore: number;
  riskLevel: SensitivityLevel;
}

// Dashboard summary analytics
export interface DashboardAnalytics {
  totalUsers: number;
  activeUsers: {
    daily: number;
    weekly: number;
    monthly: number;
  };
  newUsers: {
    daily: number;
    weekly: number;
    monthly: number;
  };
  totalAgentInteractions: number;
  totalContentViews: number;
  averageSessionDuration: number;
  sensitivityAlerts: number;
  topSections: SectionAnalytics[];
  topAgents: AgentAnalytics[];
  topContent: ContentAnalytics[];
  recentSensitiveInteractions: AgentInteraction[];
}
