export type Agent = {
  id: string;
  name: string;
  description: string;
  longDescription?: string;
  category: string;
  capabilities: string[];
  usageCount: number;
  isNew: boolean;
  isFeatured: boolean;
  createdAt: string;
  updatedAt: string;
  version: string;
  creator?: string;
  avatarUrl?: string;
  tags?: string[];
  relatedAgentIds?: string[];
};

export type UserPreferences = {
  favoriteAgentIds: string[];
  recentlyViewedAgentIds: string[];
  darkMode: boolean;
  defaultView: 'grid' | 'list';
  pageSize: number;
};

export type Message = {
  id: string;
  content: string;
  role: 'user' | 'assistant' | 'system';
  timestamp: number;
};

export type Session = {
  id: string;
  agentId: string;
  title: string;
  messages: Message[];
  createdAt: number;
  updatedAt: number;
  isSaved: boolean;
};
