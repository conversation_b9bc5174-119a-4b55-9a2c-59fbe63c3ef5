export interface User {
  id: string;
  name: string;
  email: string;
  image?: string;
  password?: string; // Optional in the interface, but required in the database
  role: 'user' | 'admin';
  createdAt: string;
  updatedAt: string;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  favoriteAgentIds: string[];
  recentlyViewedAgentIds: string[];
  defaultView: 'grid' | 'list';
  pageSize: number;
}

export interface UserSession {
  user: User;
  expires: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterCredentials extends LoginCredentials {
  name: string;
}

export interface AuthState {
  user: User | null;
  isLoading: boolean;
  error: string | null;
}
