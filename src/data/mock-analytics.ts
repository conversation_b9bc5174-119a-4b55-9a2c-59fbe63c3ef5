import {
  SectionAnalytics,
  AgentAnalytics,
  ContentAnalytics,
  UserAnalytics,
  DashboardAnalytics,
  AgentInteraction
} from '@/types/analytics';

// Mock section analytics
export const mockSectionAnalytics: SectionAnalytics[] = [
  {
    section: 'home',
    viewCount: 12453,
    uniqueUsers: 3827,
    averageDuration: 124, // seconds
    bounceRate: 0.32,
    trend: 'up',
    percentChange: 12.5
  },
  {
    section: 'browse',
    viewCount: 8932,
    uniqueUsers: 2945,
    averageDuration: 205,
    bounceRate: 0.18,
    trend: 'up',
    percentChange: 8.3
  },
  {
    section: 'agent_detail',
    viewCount: 6721,
    uniqueUsers: 2103,
    averageDuration: 342,
    bounceRate: 0.22,
    trend: 'up',
    percentChange: 15.7
  },
  {
    section: 'learning',
    viewCount: 5438,
    uniqueUsers: 1876,
    averageDuration: 278,
    bounceRate: 0.25,
    trend: 'up',
    percentChange: 32.1
  },
  {
    section: 'learning_videos',
    viewCount: 2876,
    uniqueUsers: 1243,
    averageDuration: 412,
    bounceRate: 0.15,
    trend: 'up',
    percentChange: 28.4
  },
  {
    section: 'learning_articles',
    viewCount: 2143,
    uniqueUsers: 987,
    averageDuration: 325,
    bounceRate: 0.21,
    trend: 'up',
    percentChange: 18.9
  },
  {
    section: 'learning_blog',
    viewCount: 1876,
    uniqueUsers: 754,
    averageDuration: 298,
    bounceRate: 0.28,
    trend: 'stable',
    percentChange: 2.3
  },
  {
    section: 'learning_resources',
    viewCount: 3254,
    uniqueUsers: 1432,
    averageDuration: 245,
    bounceRate: 0.19,
    trend: 'up',
    percentChange: 42.7
  },
  {
    section: 'profile',
    viewCount: 3876,
    uniqueUsers: 2143,
    averageDuration: 187,
    bounceRate: 0.12,
    trend: 'stable',
    percentChange: 1.8
  },
  {
    section: 'admin',
    viewCount: 876,
    uniqueUsers: 32,
    averageDuration: 543,
    bounceRate: 0.05,
    trend: 'up',
    percentChange: 7.2
  }
];

// Mock agent analytics
export const mockAgentAnalytics: AgentAnalytics[] = [
  {
    agentId: '1',
    agentName: 'Research Assistant',
    interactionCount: 8765,
    uniqueUsers: 2143,
    averageDuration: 432,
    sensitivityScore: 42,
    popularQueries: [
      { query: 'research on market trends', count: 342 },
      { query: 'analyze competitor data', count: 287 },
      { query: 'summarize this report', count: 254 }
    ],
    externalApiUsage: {
      'OpenAI GPT-4': 5432,
      'Google Search': 2143,
      'Bing Search': 876
    }
  },
  {
    agentId: '2',
    agentName: 'Data Analyst',
    interactionCount: 7654,
    uniqueUsers: 1876,
    averageDuration: 387,
    sensitivityScore: 68,
    popularQueries: [
      { query: 'analyze this dataset', count: 432 },
      { query: 'create visualization for', count: 321 },
      { query: 'find patterns in data', count: 287 }
    ],
    externalApiUsage: {
      'OpenAI GPT-4': 4321,
      'Azure OpenAI': 2876,
      'Tableau API': 1243
    }
  },
  {
    agentId: '3',
    agentName: 'Code Assistant',
    interactionCount: 6543,
    uniqueUsers: 1543,
    averageDuration: 456,
    sensitivityScore: 35,
    popularQueries: [
      { query: 'debug this code', count: 543 },
      { query: 'optimize this function', count: 432 },
      { query: 'explain this error', count: 321 }
    ],
    externalApiUsage: {
      'OpenAI Codex': 3876,
      'GitHub Copilot': 2143,
      'Anthropic Claude': 987
    }
  },
  {
    agentId: '4',
    agentName: 'Writing Assistant',
    interactionCount: 5432,
    uniqueUsers: 1432,
    averageDuration: 321,
    sensitivityScore: 28,
    popularQueries: [
      { query: 'write an email about', count: 654 },
      { query: 'improve this paragraph', count: 543 },
      { query: 'summarize this document', count: 432 }
    ],
    externalApiUsage: {
      'OpenAI GPT-4': 3254,
      'Anthropic Claude': 1876,
      'Grammarly API': 987
    }
  },
  {
    agentId: '5',
    agentName: 'Customer Support',
    interactionCount: 4321,
    uniqueUsers: 1243,
    averageDuration: 276,
    sensitivityScore: 72,
    popularQueries: [
      { query: 'how to reset password', count: 765 },
      { query: 'billing issue with', count: 654 },
      { query: 'cancel my subscription', count: 543 }
    ],
    externalApiUsage: {
      'OpenAI GPT-4': 2143,
      'Zendesk API': 1876,
      'Salesforce API': 987
    }
  }
];

// Mock content analytics
export const mockContentAnalytics: ContentAnalytics[] = [
  {
    contentId: 'video-1',
    contentTitle: 'Introduction to AI Agents',
    contentType: 'video',
    viewCount: 3254,
    uniqueUsers: 2143,
    averageDuration: 432,
    completionRate: 0.78,
    engagementScore: 87
  },
  {
    contentId: 'article-1',
    contentTitle: 'The Future of AI Agents in Enterprise',
    contentType: 'article',
    viewCount: 2876,
    uniqueUsers: 1876,
    averageDuration: 387,
    completionRate: 0.65,
    engagementScore: 72
  },
  {
    contentId: 'blog-1',
    contentTitle: 'How We Built Our First AI Agent',
    contentType: 'blog',
    viewCount: 2143,
    uniqueUsers: 1543,
    averageDuration: 321,
    completionRate: 0.82,
    engagementScore: 84
  },
  {
    contentId: 'resource-github-copilot',
    contentTitle: 'GitHub Copilot',
    contentType: 'resource',
    viewCount: 1876,
    uniqueUsers: 1432,
    averageDuration: 187,
    completionRate: 0.91,
    engagementScore: 76
  },
  {
    contentId: 'video-2',
    contentTitle: 'Advanced AI Agent Techniques',
    contentType: 'video',
    viewCount: 1654,
    uniqueUsers: 1243,
    averageDuration: 456,
    completionRate: 0.72,
    engagementScore: 81
  }
];

// Mock user analytics
export const mockUserAnalytics: UserAnalytics[] = [
  {
    userId: 'user-1',
    userName: 'John Smith',
    lastActive: new Date('2023-05-15T14:32:21'),
    totalSessions: 87,
    totalTimeSpent: 24321, // seconds
    favoriteSection: 'learning',
    favoriteAgents: ['1', '3'],
    sensitivityScore: 28,
    riskLevel: 'low'
  },
  {
    userId: 'user-2',
    userName: 'Emily Johnson',
    lastActive: new Date('2023-05-15T16:45:32'),
    totalSessions: 132,
    totalTimeSpent: 43265,
    favoriteSection: 'agent_detail',
    favoriteAgents: ['2', '4'],
    sensitivityScore: 65,
    riskLevel: 'medium'
  },
  {
    userId: 'user-3',
    userName: 'Michael Brown',
    lastActive: new Date('2023-05-15T12:21:43'),
    totalSessions: 54,
    totalTimeSpent: 18765,
    favoriteSection: 'browse',
    favoriteAgents: ['1', '5'],
    sensitivityScore: 42,
    riskLevel: 'low'
  },
  {
    userId: 'user-4',
    userName: 'Sarah Davis',
    lastActive: new Date('2023-05-15T09:54:12'),
    totalSessions: 76,
    totalTimeSpent: 28943,
    favoriteSection: 'learning_resources',
    favoriteAgents: ['3', '4'],
    sensitivityScore: 18,
    riskLevel: 'none'
  },
  {
    userId: 'user-5',
    userName: 'David Wilson',
    lastActive: new Date('2023-05-15T17:32:45'),
    totalSessions: 112,
    totalTimeSpent: 38765,
    favoriteSection: 'learning_videos',
    favoriteAgents: ['2', '3'],
    sensitivityScore: 87,
    riskLevel: 'high'
  }
];

// Mock sensitive interactions
export const mockSensitiveInteractions: AgentInteraction[] = [
  {
    id: 'interaction-1',
    userId: 'user-5',
    agentId: '2',
    timestamp: new Date('2023-05-15T16:32:21'),
    queryText: 'Analyze this customer data with credit card information',
    responseText: 'I\'ve detected sensitive information in your request. I\'ve redacted the credit card numbers for security purposes. Please avoid sharing sensitive financial information.',
    duration: 32,
    sensitivityScore: 92,
    sensitivityFlags: ['credit_card', 'financial_data', 'pii'],
    externalApiUsed: true,
    externalApiName: 'OpenAI GPT-4'
  },
  {
    id: 'interaction-2',
    userId: 'user-2',
    agentId: '5',
    timestamp: new Date('2023-05-15T14:21:43'),
    queryText: 'Help me draft an email to our clients about their account information including their addresses',
    responseText: 'I can help draft an email template, but I notice you\'re mentioning client addresses which could be sensitive. Consider using placeholder text instead of actual addresses in your draft.',
    duration: 45,
    sensitivityScore: 78,
    sensitivityFlags: ['pii', 'address_data', 'client_information'],
    externalApiUsed: true,
    externalApiName: 'Anthropic Claude'
  },
  {
    id: 'interaction-3',
    userId: 'user-5',
    agentId: '1',
    timestamp: new Date('2023-05-15T11:43:32'),
    queryText: 'Research competitive analysis for our confidential Project Falcon',
    responseText: 'I can help with competitive analysis research. I notice you mentioned "confidential Project Falcon" - please be careful about sharing internal project names or confidential information.',
    duration: 67,
    sensitivityScore: 82,
    sensitivityFlags: ['confidential', 'internal_project', 'competitive_data'],
    externalApiUsed: true,
    externalApiName: 'OpenAI GPT-4'
  }
];

// Mock dashboard analytics
export const mockDashboardAnalytics: DashboardAnalytics = {
  totalUsers: 5432,
  activeUsers: {
    daily: 1243,
    weekly: 2876,
    monthly: 4321
  },
  newUsers: {
    daily: 87,
    weekly: 432,
    monthly: 1876
  },
  totalAgentInteractions: 32543,
  totalContentViews: 28765,
  averageSessionDuration: 324, // seconds
  sensitivityAlerts: 187,
  topSections: mockSectionAnalytics.slice(0, 5),
  topAgents: mockAgentAnalytics.slice(0, 3),
  topContent: mockContentAnalytics.slice(0, 3),
  recentSensitiveInteractions: mockSensitiveInteractions
};
