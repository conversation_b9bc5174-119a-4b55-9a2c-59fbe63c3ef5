import { LearningResource } from '@/types/learning';
import { Resource } from '@/types/resources';
import { getRecentResources, getPopularResources, getCombinedLearningResources } from './learning-resources';
import { resources } from './resources';

// Combined resource type for the home page
export interface CombinedResource {
  id: string;
  title: string;
  description: string;
  type: string;
  thumbnail?: string;
  url: string;
  category?: string;
  tags?: string[];
  publishedAt?: Date;
  author?: string;
}

// Get recent AI tools from the resources section
export const getRecentAITools = (count: number = 4): CombinedResource[] => {
  // In a real app, these would be sorted by date added
  // For now, we'll just take a random selection
  const shuffled = [...resources].sort(() => 0.5 - Math.random());
  const selected = shuffled.slice(0, count);
  
  return selected.map(resource => ({
    id: resource.id,
    title: resource.name,
    description: resource.description,
    type: 'resource',
    thumbnail: resource.logoUrl,
    url: `/learning/resources/${resource.category}`,
    category: resource.category,
    tags: resource.tags || [],
    // Simulate a recent date
    publishedAt: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000)
  }));
};

// Get popular AI tools from the resources section
export const getPopularAITools = (count: number = 4): CombinedResource[] => {
  // In a real app, these would be sorted by popularity
  // For now, we'll just take a different random selection
  const shuffled = [...resources].sort(() => 0.5 - Math.random());
  const selected = shuffled.slice(0, count);
  
  return selected.map(resource => ({
    id: resource.id,
    title: resource.name,
    description: resource.description,
    type: 'resource',
    thumbnail: resource.logoUrl,
    url: `/learning/resources/${resource.category}`,
    category: resource.category,
    tags: resource.tags || []
  }));
};

// Get combined recent content from all learning sections
export const getCombinedRecentContent = (count: number = 8): CombinedResource[] => {
  const learningContent = getCombinedLearningResources(getRecentResources(count / 2)).map(resource => ({
    id: resource.id,
    title: resource.title,
    description: resource.description,
    type: resource.type,
    thumbnail: resource.thumbnail,
    url: resource.viewUrl,
    tags: resource.tags,
    publishedAt: resource.publishedAt,
    author: resource.author
  }));
  
  const aiTools = getRecentAITools(count / 2);
  
  // Combine and sort by date
  return [...learningContent, ...aiTools]
    .sort((a, b) => {
      if (!a.publishedAt) return 1;
      if (!b.publishedAt) return -1;
      return b.publishedAt.getTime() - a.publishedAt.getTime();
    })
    .slice(0, count);
};

// Get combined popular content from all learning sections
export const getCombinedPopularContent = (count: number = 8): CombinedResource[] => {
  const learningContent = getCombinedLearningResources(getPopularResources(count / 2)).map(resource => ({
    id: resource.id,
    title: resource.title,
    description: resource.description,
    type: resource.type,
    thumbnail: resource.thumbnail,
    url: resource.viewUrl,
    tags: resource.tags,
    publishedAt: resource.publishedAt,
    author: resource.author
  }));
  
  const aiTools = getPopularAITools(count / 2);
  
  // Combine and shuffle
  return [...learningContent, ...aiTools]
    .sort(() => 0.5 - Math.random())
    .slice(0, count);
};
