import { Resource, ResourceCategoryInfo } from '@/types/resources';

// Resource categories information
export const resourceCategories: ResourceCategoryInfo[] = [
  {
    id: 'productivity',
    name: 'Productivity Tools',
    description: 'Tools to enhance personal and team productivity, including task management, note-taking, and time tracking.',
    icon: 'bolt'
  },
  {
    id: 'project-management',
    name: 'Project Management',
    description: 'Software and platforms for planning, executing, and monitoring projects of all sizes.',
    icon: 'tasks'
  },
  {
    id: 'design',
    name: 'Design Tools',
    description: 'Resources for UI/UX design, graphic design, prototyping, and visual collaboration.',
    icon: 'palette'
  },
  {
    id: 'development',
    name: 'Development Tools',
    description: 'Tools and services for software development, coding, testing, and deployment.',
    icon: 'code'
  },
  {
    id: 'research',
    name: 'Research Tools',
    description: 'Resources for market research, user research, academic research, and data collection.',
    icon: 'magnifying-glass-chart'
  },
  {
    id: 'analytics',
    name: 'Analytics & Data',
    description: 'Tools for data analysis, visualization, business intelligence, and reporting.',
    icon: 'chart-line'
  },
  {
    id: 'communication',
    name: 'Communication',
    description: 'Platforms for team communication, client meetings, presentations, and email management.',
    icon: 'comments'
  },
  {
    id: 'collaboration',
    name: 'Collaboration',
    description: 'Tools that facilitate teamwork, knowledge sharing, and cross-functional collaboration.',
    icon: 'users-gear'
  }
];

// Sample resources data
export const resources: Resource[] = [
  {
    id: 'notion',
    name: 'Notion',
    description: 'All-in-one workspace for notes, tasks, wikis, and databases.',
    url: 'https://www.notion.so',
    category: 'productivity',
    tags: ['note-taking', 'project-management', 'wiki'],
    pricing: 'freemium',
    logoUrl: 'https://www.notion.so/images/favicon.ico'
  },
  {
    id: 'figma',
    name: 'Figma',
    description: 'Collaborative interface design tool for teams.',
    url: 'https://www.figma.com',
    category: 'design',
    tags: ['ui-design', 'prototyping', 'collaboration'],
    pricing: 'freemium',
    logoUrl: 'https://static.figma.com/app/icon/1/favicon.png'
  },
  {
    id: 'vscode',
    name: 'Visual Studio Code',
    description: 'Free, open-source code editor with powerful development features.',
    url: 'https://code.visualstudio.com',
    category: 'development',
    tags: ['code-editor', 'debugging', 'extensions'],
    pricing: 'free',
    logoUrl: 'https://code.visualstudio.com/favicon.ico'
  },
  {
    id: 'slack',
    name: 'Slack',
    description: 'Channel-based messaging platform for teams and workplaces.',
    url: 'https://slack.com',
    category: 'communication',
    tags: ['messaging', 'team-communication', 'integrations'],
    pricing: 'freemium',
    logoUrl: 'https://a.slack-edge.com/80588/marketing/img/meta/favicon-32.png'
  },
  {
    id: 'trello',
    name: 'Trello',
    description: 'Visual tool for organizing work with boards, lists, and cards.',
    url: 'https://trello.com',
    category: 'project-management',
    tags: ['kanban', 'task-management', 'collaboration'],
    pricing: 'freemium',
    logoUrl: 'https://a.trellocdn.com/prgb/dist/images/ios/apple-touch-icon-152x152-precomposed.0307bc39ec6c9ff499c8.png'
  },
  {
    id: 'google-analytics',
    name: 'Google Analytics',
    description: 'Web analytics service that tracks and reports website traffic.',
    url: 'https://analytics.google.com',
    category: 'analytics',
    tags: ['web-analytics', 'reporting', 'user-behavior'],
    pricing: 'freemium',
    logoUrl: 'https://www.google.com/analytics/images/ga_icon_256.png'
  },
  {
    id: 'miro',
    name: 'Miro',
    description: 'Online collaborative whiteboard platform for teams.',
    url: 'https://miro.com',
    category: 'collaboration',
    tags: ['whiteboard', 'brainstorming', 'visual-collaboration'],
    pricing: 'freemium',
    logoUrl: 'https://miro.com/static/images/favicon/apple-touch-icon.png'
  },
  {
    id: 'google-scholar',
    name: 'Google Scholar',
    description: 'Search engine for academic literature and research papers.',
    url: 'https://scholar.google.com',
    category: 'research',
    tags: ['academic-research', 'citations', 'literature-search'],
    pricing: 'free',
    logoUrl: 'https://scholar.google.com/favicon.ico'
  }
];
