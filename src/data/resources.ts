import { Resource, ResourceCategoryInfo } from '@/types/resources';
import { resources as existingResources, resourceCategories } from './resources-original';

// New resources from the AI Presentation Series Summary PDF
const newResources: Resource[] = [
  // Productivity Tools
  {
    id: 'perplexity-ai',
    name: 'Perplexity AI',
    description: 'An AI-powered search engine with a chatbot interface that understands and responds to user queries using GPT-3.5.',
    url: 'https://www.perplexity.ai/',
    category: 'productivity',
    tags: ['search-engine', 'ai-assistant', 'research'],
    pricing: 'freemium',
    logoUrl: 'https://www.perplexity.ai/favicon.ico'
  },
  {
    id: 'claude-ai',
    name: 'Claude <PERSON>',
    description: 'Analyzes and suggests improvements for very long content, similar to ChatGPT but with enhanced capabilities for handling lengthy documents.',
    url: 'https://claude.ai/',
    category: 'productivity',
    tags: ['ai-assistant', 'content-generation', 'document-analysis'],
    pricing: 'freemium',
    logoUrl: 'https://claude.ai/favicon.ico'
  },
  {
    id: 'fathom-ai',
    name: 'Fathom AI',
    description: 'Zoom app that records, transcribes, and highlights key moments from calls, making meeting follow-up more efficient.',
    url: 'https://fathom.video/',
    category: 'productivity',
    tags: ['meeting-assistant', 'transcription', 'video-conferencing'],
    pricing: 'freemium',
    logoUrl: 'https://fathom.video/favicon.ico'
  },
  {
    id: 'plaud-ai',
    name: 'Plaud.ai',
    description: 'AI tool for note-taking and transcription that helps capture and organize information from meetings and conversations.',
    url: 'https://www.plaud.ai/',
    category: 'productivity',
    tags: ['note-taking', 'transcription', 'meeting-assistant'],
    pricing: 'freemium',
    logoUrl: 'https://www.plaud.ai/favicon.ico'
  },
  {
    id: 'whisper',
    name: 'Whisper',
    description: 'Converts audio to text and vice versa using AI, providing accurate transcription for various languages and accents.',
    url: 'https://openai.com/index/whisper/',
    category: 'productivity',
    tags: ['transcription', 'audio-processing', 'speech-to-text'],
    pricing: 'freemium',
    logoUrl: 'https://openai.com/favicon.ico'
  },
  {
    id: 'notebooklm',
    name: 'NotebookLM',
    description: 'Tool for data management and note-taking that uses AI to help organize and retrieve information efficiently.',
    url: 'https://notebooklm.google.com',
    category: 'productivity',
    tags: ['note-taking', 'knowledge-management', 'ai-organization'],
    pricing: 'free',
    logoUrl: 'https://notebooklm.google.com/favicon.ico'
  },
  {
    id: 'deepl',
    name: 'DeepL',
    description: 'Translation tool for efficient and accurate translations between multiple languages, powered by advanced AI.',
    url: 'https://www.deepl.com',
    category: 'productivity',
    tags: ['translation', 'language-processing', 'communication'],
    pricing: 'freemium',
    logoUrl: 'https://www.deepl.com/favicon.ico'
  },

  // Design Tools
  {
    id: 'midjourney',
    name: 'Midjourney',
    description: 'Generates images from descriptive language, similar to DALL-E and Stable Diffusion, with a focus on artistic quality.',
    url: 'https://www.midjourney.com/',
    category: 'design',
    tags: ['image-generation', 'ai-art', 'creative-tools'],
    pricing: 'paid',
    logoUrl: 'https://www.midjourney.com/favicon.ico'
  },
  {
    id: 'bing-images',
    name: 'Bing Image Creator',
    description: 'Provides tools for creating images from text descriptions, potentially offering more features than Midjourney.',
    url: 'https://www.bing.com/images/create',
    category: 'design',
    tags: ['image-generation', 'ai-art', 'creative-tools'],
    pricing: 'free',
    logoUrl: 'https://www.bing.com/favicon.ico'
  },
  {
    id: 'meta-imagine',
    name: 'Meta Imagine',
    description: 'Uses models to generate images from textual prompts provided by the user, created by Meta (Facebook).',
    url: 'https://imagine.meta.com',
    category: 'design',
    tags: ['image-generation', 'ai-art', 'creative-tools'],
    pricing: 'free',
    logoUrl: 'https://imagine.meta.com/favicon.ico'
  },
  {
    id: 'designer-microsoft',
    name: 'Microsoft Designer',
    description: 'Microsoft\'s answer to Canva, focusing on design solutions with AI-powered features for creating professional graphics.',
    url: 'https://designer.microsoft.com/',
    category: 'design',
    tags: ['graphic-design', 'presentation', 'marketing-materials'],
    pricing: 'freemium',
    logoUrl: 'https://designer.microsoft.com/favicon.ico'
  },
  {
    id: 'runway',
    name: 'Runway',
    description: 'A creative platform for video production and editing with AI-powered tools for visual effects and content creation.',
    url: 'https://app.runwayml.com/',
    category: 'design',
    tags: ['video-editing', 'visual-effects', 'content-creation'],
    pricing: 'freemium',
    logoUrl: 'https://app.runwayml.com/favicon.ico'
  },
  {
    id: 'clipdrop',
    name: 'Clipdrop',
    description: 'Developed by Stability AI, for various image and video editing tasks with AI-powered features.',
    url: 'https://clipdrop.co/',
    category: 'design',
    tags: ['image-editing', 'video-editing', 'content-creation'],
    pricing: 'freemium',
    logoUrl: 'https://clipdrop.co/favicon.ico'
  },
  {
    id: 'interior-ai',
    name: 'Interior AI',
    description: 'Interior design tool for generating and visualizing room layouts using AI to create realistic interior designs.',
    url: 'https://interiorai.com/',
    category: 'design',
    tags: ['interior-design', 'visualization', 'architecture'],
    pricing: 'freemium',
    logoUrl: 'https://interiorai.com/favicon.ico'
  },
  {
    id: 'meshy-ai',
    name: 'Meshy.ai',
    description: 'For 3D modeling, used in architecture and design to create and manipulate 3D objects with AI assistance.',
    url: 'https://www.meshy.ai',
    category: 'design',
    tags: ['3d-modeling', 'architecture', 'design'],
    pricing: 'freemium',
    logoUrl: 'https://www.meshy.ai/favicon.ico'
  },
  {
    id: 'mnml-ai',
    name: 'MNML AI',
    description: 'Architecture design assistant that helps create minimalist architectural designs with AI guidance.',
    url: 'https://mnml.ai',
    category: 'design',
    tags: ['architecture', 'design', 'minimalism'],
    pricing: 'freemium',
    logoUrl: 'https://mnml.ai/favicon.ico'
  },
  {
    id: 'ulama-tech',
    name: 'Ulama.tech',
    description: 'For architectural design, specifically for structure and planning with AI-powered tools.',
    url: 'http://ulama.tech',
    category: 'design',
    tags: ['architecture', 'structural-design', 'planning'],
    pricing: 'freemium',
    logoUrl: 'http://ulama.tech/favicon.ico'
  },
  {
    id: 'weshop-ai',
    name: 'WeShop',
    description: 'Produce high-quality product images inexpensively and quickly using AI-generated visuals.',
    url: 'https://www.weshop.ai/',
    category: 'design',
    tags: ['product-photography', 'e-commerce', 'marketing'],
    pricing: 'paid',
    logoUrl: 'https://www.weshop.ai/favicon.ico'
  },
  {
    id: 'botika',
    name: 'Botika',
    description: 'Helps fashion retailers save on photo costs and boost sales using AI-generated models for product visualization.',
    url: 'https://botika.io/',
    category: 'design',
    tags: ['fashion', 'e-commerce', 'product-visualization'],
    pricing: 'paid',
    logoUrl: 'https://botika.io/favicon.ico'
  },
  {
    id: 'flux-ai',
    name: 'Flux AI',
    description: 'Enables creative image generation and animation with AI-powered tools for designers and artists.',
    url: 'https://flux-ai.io/',
    category: 'design',
    tags: ['image-generation', 'animation', 'creative-tools'],
    pricing: 'freemium',
    logoUrl: 'https://flux-ai.io/favicon.ico'
  },

  // Development Tools
  {
    id: '10web',
    name: '10Web',
    description: 'An AI-Powered WordPress Platform for website development and management with automated features.',
    url: 'https://10web.io/',
    category: 'development',
    tags: ['wordpress', 'website-builder', 'automation'],
    pricing: 'paid',
    logoUrl: 'https://10web.io/favicon.ico'
  },
  {
    id: 'framer',
    name: 'Framer',
    description: 'A tool for building interactive websites and web applications with a focus on design and user experience.',
    url: 'https://framer.com/',
    category: 'development',
    tags: ['website-builder', 'prototyping', 'design'],
    pricing: 'freemium',
    logoUrl: 'https://framer.com/favicon.ico'
  },
  {
    id: 'github-copilot',
    name: 'GitHub Copilot',
    description: 'AI pair programmer that assists in code completion and suggestions within code editors, powered by OpenAI Codex.',
    url: 'https://github.com/features/copilot',
    category: 'development',
    tags: ['coding-assistant', 'pair-programming', 'code-completion'],
    pricing: 'paid',
    logoUrl: 'https://github.com/favicon.ico'
  },
  {
    id: 'github-spark',
    name: 'GitHub Spark',
    description: 'AI tool for building web applications using natural language, aiming to lower the barrier to software development.',
    url: 'https://github.com/features',
    category: 'development',
    tags: ['web-development', 'no-code', 'ai-coding'],
    pricing: 'paid',
    logoUrl: 'https://github.com/favicon.ico'
  },
  {
    id: 'langchain',
    name: 'LangChain',
    description: 'Builds AI-powered applications by connecting large language models with external data sources and tools.',
    url: 'https://www.langchain.com/',
    category: 'development',
    tags: ['llm-framework', 'ai-development', 'integration'],
    pricing: 'free',
    logoUrl: 'https://www.langchain.com/favicon.ico'
  },

  // Communication
  {
    id: 'heygen',
    name: 'HeyGen',
    description: 'Produces studio quality videos with AI-generated avatars and voices for professional communication.',
    url: 'https://www.heygen.com',
    category: 'communication',
    tags: ['video-creation', 'avatars', 'presentation'],
    pricing: 'paid',
    logoUrl: 'https://www.heygen.com/favicon.ico'
  },
  {
    id: 'beautiful-ai',
    name: 'Beautiful.ai',
    description: 'Presentation tool that simplifies the creation of professional presentations using AI to handle design elements.',
    url: 'http://beautiful.ai',
    category: 'communication',
    tags: ['presentation', 'slides', 'design'],
    pricing: 'freemium',
    logoUrl: 'http://beautiful.ai/favicon.ico'
  },
  {
    id: 'gamma-app',
    name: 'Gamma.app',
    description: 'Creates engaging presentations by transforming ideas into visually appealing slides with AI assistance.',
    url: 'https://gamma.app/',
    category: 'communication',
    tags: ['presentation', 'slides', 'design'],
    pricing: 'freemium',
    logoUrl: 'https://gamma.app/favicon.ico'
  },
  {
    id: 'decktopus',
    name: 'Decktopus',
    description: 'An AI-powered tool that assists in creating presentation starting points with professional templates and designs.',
    url: 'https://decktopus.com',
    category: 'communication',
    tags: ['presentation', 'slides', 'design'],
    pricing: 'freemium',
    logoUrl: 'https://decktopus.com/favicon.ico'
  },
  {
    id: 'opus',
    name: 'Opus',
    description: 'Transforms long videos into short clips with a single click using generative AI for more effective communication.',
    url: 'https://www.opus.pro/',
    category: 'communication',
    tags: ['video-editing', 'content-creation', 'summarization'],
    pricing: 'paid',
    logoUrl: 'https://www.opus.pro/favicon.ico'
  },
  {
    id: 'vapi',
    name: 'VAPI',
    description: 'Builds and optimizes voice agents for customer service and communication applications.',
    url: 'https://vapi.ai/',
    category: 'communication',
    tags: ['voice-agents', 'customer-service', 'automation'],
    pricing: 'paid',
    logoUrl: 'https://vapi.ai/favicon.ico'
  },
  {
    id: 'sora',
    name: 'Sora',
    description: 'Turn text instructions into detailed video scenes for communication and presentation purposes.',
    url: 'https://openai.com/sora',
    category: 'communication',
    tags: ['video-generation', 'content-creation', 'presentation'],
    pricing: 'paid',
    logoUrl: 'https://openai.com/favicon.ico'
  },

  // Collaboration
  {
    id: 'rancelab',
    name: 'RanceLab',
    description: 'Integrates WhatsApp with other platforms for better team collaboration and customer communication.',
    url: 'https://www.rancelab.com/',
    category: 'collaboration',
    tags: ['whatsapp-integration', 'communication', 'customer-service'],
    pricing: 'paid',
    logoUrl: 'https://www.rancelab.com/favicon.ico'
  },
  {
    id: 'lawgeex',
    name: 'LawGeex',
    description: 'Legal automation platform that uses AI to review contracts and facilitate legal collaboration.',
    url: 'https://www.lawgeex.com/',
    category: 'collaboration',
    tags: ['legal', 'contract-review', 'automation'],
    pricing: 'paid',
    logoUrl: 'https://www.lawgeex.com/favicon.ico'
  },

  // Analytics & Data
  {
    id: 'browse-ai',
    name: 'BrowseAI',
    description: 'Facilitates data extraction and monitoring from websites for easy data acquisition and analysis.',
    url: 'https://www.browse.ai/',
    category: 'analytics',
    tags: ['data-extraction', 'web-scraping', 'monitoring'],
    pricing: 'freemium',
    logoUrl: 'https://www.browse.ai/favicon.ico'
  },
  {
    id: 'relevance-ai',
    name: 'Relevance AI',
    description: 'Platform providing AI-driven insights and analytics to enhance business decision-making.',
    url: 'https://relevanceai.com/',
    category: 'analytics',
    tags: ['data-analysis', 'insights', 'business-intelligence'],
    pricing: 'freemium',
    logoUrl: 'https://relevanceai.com/favicon.ico'
  },

  // Project Management
  {
    id: 'make-com',
    name: 'Make.com',
    description: 'Automation platform for streamlining workflows and processes using AI to connect apps and automate tasks.',
    url: 'https://make.com',
    category: 'project-management',
    tags: ['automation', 'workflow', 'integration'],
    pricing: 'freemium',
    logoUrl: 'https://make.com/favicon.ico'
  },
  {
    id: 'zapier-central',
    name: 'Zapier Central',
    description: 'Automating tasks and workflows using AI-powered integrations between different applications and services.',
    url: 'https://zapier.com/central',
    category: 'project-management',
    tags: ['automation', 'workflow', 'integration'],
    pricing: 'freemium',
    logoUrl: 'https://zapier.com/favicon.ico'
  },
  {
    id: 'agents-ai',
    name: 'Agents.ai',
    description: 'Professional network of AI agents for business automation and task management.',
    url: 'https://agents.ai/',
    category: 'project-management',
    tags: ['automation', 'ai-agents', 'task-management'],
    pricing: 'paid',
    logoUrl: 'https://agents.ai/favicon.ico'
  },
  {
    id: 'napkin-ai',
    name: 'Napkin.ai',
    description: 'Useful for generating content, proofreading, and ideation feedback for project planning and documentation.',
    url: 'http://napkin.ai',
    category: 'project-management',
    tags: ['content-generation', 'ideation', 'documentation'],
    pricing: 'freemium',
    logoUrl: 'http://napkin.ai/favicon.ico'
  }
];

// Combine existing resources with new resources, avoiding duplicates
const combinedResources: Resource[] = [
  ...existingResources,
  ...newResources.filter(newResource =>
    !existingResources.some(existingResource =>
      existingResource.id === newResource.id ||
      existingResource.name.toLowerCase() === newResource.name.toLowerCase()
    )
  )
];

export { resourceCategories, combinedResources as resources };

// Helper functions
export function getResourcesByCategory(categoryId: string): Resource[] {
  return combinedResources.filter(resource => resource.category === categoryId);
}

export function getCategoryById(categoryId: string): ResourceCategoryInfo | undefined {
  return resourceCategories.find(category => category.id === categoryId);
}
