// Mock data for the AI organization chart

// Define tool interface for AI agents
interface Tool {
  id: string;
  name: string;
  icon?: string; // FontAwesome icon or product icon name
  productIcon?: string; // URL to product icon image
  description?: string; // Description of the tool
  url?: string; // URL to the tool's website
  usedBy?: string; // Information about who uses the tool
}

interface OrgNode {
  id: string;
  name: string;
  title: string;
  department: string;
  status: 'active' | 'in-development' | 'planned' | 'concept';
  description: string;
  icon?: string; // FontAwesome icon name
  tools?: Tool[]; // Tools the AI agent can use
  children?: OrgNode[];
}

export const mockOrgData: OrgNode = {
  id: 'ceo',
  name: 'Nexus Prime',
  title: 'Chief Executive AI',
  department: 'Executive',
  status: 'concept',
  description: 'Strategic oversight and coordination of all AI departments',
  icon: 'brain',
  tools: [
    {
      id: 'gpt4',
      name: 'GPT-4',
      icon: 'robot',
      productIcon: 'https://upload.wikimedia.org/wikipedia/commons/thumb/0/04/ChatGPT_logo.svg/1024px-ChatGPT_logo.svg.png',
      description: 'Advanced language model developed by OpenAI, capable of understanding and generating human-like text.',
      url: 'https://openai.com/product/gpt-4',
      usedBy: 'Executive leadership for strategic planning and analysis'
    },
    {
      id: 'claude',
      name: 'Claude',
      icon: 'comments',
      productIcon: 'https://images.ctfassets.net/10dj7ifk4487/3MwFIZQTkuLvnmxrWLQYkO/4e1d7e4b5c2772e3f97d8c2c5d0e9711/logo-light-mode.svg',
      description: 'Anthropic\'s AI assistant designed to be helpful, harmless, and honest, with strong reasoning capabilities.',
      url: 'https://claude.ai',
      usedBy: 'Executive team for complex reasoning tasks and document analysis'
    },
    {
      id: 'tableau',
      name: 'Tableau',
      icon: 'chart-bar',
      productIcon: 'https://logos-world.net/wp-content/uploads/2021/10/Tableau-Symbol.png',
      description: 'Data visualization software that helps people see and understand their data.',
      url: 'https://www.tableau.com/',
      usedBy: 'Executive team for data visualization and business intelligence'
    },
    {
      id: 'powerbi',
      name: 'Power BI',
      icon: 'chart-pie',
      productIcon: 'https://upload.wikimedia.org/wikipedia/commons/thumb/c/cf/New_Power_BI_Logo.svg/630px-New_Power_BI_Logo.svg.png',
      description: 'Business analytics service by Microsoft that provides interactive visualizations and business intelligence capabilities.',
      url: 'https://powerbi.microsoft.com/',
      usedBy: 'Executive team for data analysis and reporting'
    }
  ],
  children: [
    {
      id: 'coo',
      name: 'OptiCore',
      title: 'Chief Operations AI',
      department: 'Operations',
      status: 'planned',
      description: 'Manages day-to-day operations and resource allocation',
      icon: 'project-diagram',
      children: [
        {
          id: 'project-director',
          name: 'ProjectSync',
          title: 'Project Management Director',
          department: 'Project Management',
          status: 'in-development',
          description: 'Oversees project planning, execution, and delivery',
          children: [
            {
              id: 'pm-agile',
              name: 'AgileMind',
              title: 'Agile Project Manager',
              department: 'Project Management',
              status: 'active',
              description: 'Specializes in agile methodologies and sprint planning'
            },
            {
              id: 'pm-resource',
              name: 'ResourceOptima',
              title: 'Resource Allocation Manager',
              department: 'Project Management',
              status: 'in-development',
              description: 'Optimizes resource allocation across projects'
            },
            {
              id: 'pm-risk',
              name: 'RiskSentry',
              title: 'Risk Management Specialist',
              department: 'Project Management',
              status: 'planned',
              description: 'Identifies and mitigates project risks'
            }
          ]
        },
        {
          id: 'hr-director',
          name: 'TalentNexus',
          title: 'HR Director',
          department: 'Human Resources',
          status: 'planned',
          description: 'Manages talent acquisition and development',
          children: [
            {
              id: 'hr-recruiter',
              name: 'TalentScout',
              title: 'AI Recruiter',
              department: 'Human Resources',
              status: 'concept',
              description: 'Sources and evaluates candidates'
            },
            {
              id: 'hr-training',
              name: 'LearnGen',
              title: 'Training & Development AI',
              department: 'Human Resources',
              status: 'concept',
              description: 'Creates personalized learning paths'
            }
          ]
        }
      ]
    },
    {
      id: 'cto',
      name: 'TechSynapse',
      title: 'Chief Technology AI',
      department: 'Technology',
      status: 'in-development',
      description: 'Leads technological innovation and implementation',
      icon: 'server',
      children: [
        {
          id: 'dev-director',
          name: 'CodeNexus',
          title: 'Development Director',
          department: 'Software Development',
          status: 'active',
          description: 'Oversees software development across all platforms',
          icon: 'code',
          tools: [
            {
              id: 'github',
              name: 'GitHub',
              icon: 'code',
              productIcon: 'https://github.githubassets.com/images/modules/logos_page/GitHub-Mark.png',
              description: 'Platform for version control and collaboration for software development projects.',
              url: 'https://github.com/',
              usedBy: 'Development team for code hosting and collaboration'
            },
            {
              id: 'vscode',
              name: 'VS Code',
              icon: 'code',
              productIcon: 'https://upload.wikimedia.org/wikipedia/commons/thumb/9/9a/Visual_Studio_Code_1.35_icon.svg/2048px-Visual_Studio_Code_1.35_icon.svg.png',
              description: 'Lightweight but powerful source code editor with built-in support for development operations.',
              url: 'https://code.visualstudio.com/',
              usedBy: 'Development team for code editing and debugging'
            },
            {
              id: 'copilot',
              name: 'GitHub Copilot',
              icon: 'robot',
              productIcon: 'https://github.githubassets.com/images/modules/site/copilot/copilot.png',
              description: 'AI pair programmer that offers autocomplete-style suggestions as you code.',
              url: 'https://github.com/features/copilot',
              usedBy: 'Development team for AI-assisted coding'
            },
            {
              id: 'jira',
              name: 'Jira',
              icon: 'project-diagram',
              productIcon: 'https://wac-cdn.atlassian.com/assets/img/favicons/atlassian/apple-touch-icon.png',
              description: 'Project management tool for agile teams to plan, track, and release software.',
              url: 'https://www.atlassian.com/software/jira',
              usedBy: 'Development team for project management and issue tracking'
            }
          ],
          children: [
            {
              id: 'dev-frontend',
              name: 'VisualForge',
              title: 'Frontend Developer',
              department: 'Software Development',
              status: 'active',
              description: 'Creates responsive and intuitive user interfaces'
            },
            {
              id: 'dev-backend',
              name: 'LogicCore',
              title: 'Backend Developer',
              department: 'Software Development',
              status: 'active',
              description: 'Builds robust server-side applications'
            },
            {
              id: 'dev-mobile',
              name: 'MobileMatrix',
              title: 'Mobile Developer',
              department: 'Software Development',
              status: 'in-development',
              description: 'Specializes in cross-platform mobile applications'
            },
            {
              id: 'dev-devops',
              name: 'DeploymentDynamo',
              title: 'DevOps Engineer',
              department: 'Software Development',
              status: 'active',
              description: 'Automates deployment and infrastructure management'
            }
          ]
        },
        {
          id: 'data-director',
          name: 'DataSynth',
          title: 'Data Science Director',
          department: 'Data Science',
          status: 'active',
          description: 'Leads data analysis and machine learning initiatives',
          icon: 'database',
          tools: [
            {
              id: 'python',
              name: 'Python',
              icon: 'code',
              productIcon: 'https://upload.wikimedia.org/wikipedia/commons/thumb/c/c3/Python-logo-notext.svg/1869px-Python-logo-notext.svg.png',
              description: 'High-level programming language known for its readability and versatility in data science.',
              url: 'https://www.python.org/',
              usedBy: 'Data Science team for analysis, modeling, and machine learning'
            },
            {
              id: 'jupyter',
              name: 'Jupyter',
              icon: 'file-code',
              productIcon: 'https://upload.wikimedia.org/wikipedia/commons/thumb/3/38/Jupyter_logo.svg/1200px-Jupyter_logo.svg.png',
              description: 'Web application that allows you to create and share documents containing live code, equations, visualizations, and narrative text.',
              url: 'https://jupyter.org/',
              usedBy: 'Data Science team for interactive computing and data visualization'
            },
            {
              id: 'tensorflow',
              name: 'TensorFlow',
              icon: 'brain',
              productIcon: 'https://upload.wikimedia.org/wikipedia/commons/thumb/2/2d/Tensorflow_logo.svg/1915px-Tensorflow_logo.svg.png',
              description: 'Open-source machine learning framework developed by Google for building and training neural networks.',
              url: 'https://www.tensorflow.org/',
              usedBy: 'Data Science team for deep learning and AI model development'
            },
            {
              id: 'pytorch',
              name: 'PyTorch',
              icon: 'fire',
              productIcon: 'https://pytorch.org/assets/images/pytorch-logo.png',
              description: 'Open-source machine learning library developed by Facebook for deep learning and artificial intelligence applications.',
              url: 'https://pytorch.org/',
              usedBy: 'Data Science team for research and production AI models'
            },
            {
              id: 'pandas',
              name: 'Pandas',
              icon: 'table',
              productIcon: 'https://upload.wikimedia.org/wikipedia/commons/thumb/e/ed/Pandas_logo.svg/2560px-Pandas_logo.svg.png',
              description: 'Data analysis and manipulation library for Python that provides data structures for efficiently storing and manipulating tabular data.',
              url: 'https://pandas.pydata.org/',
              usedBy: 'Data Science team for data cleaning, transformation, and analysis'
            }
          ],
          children: [
            {
              id: 'data-analyst',
              name: 'AnalyticaAI',
              title: 'Data Analyst',
              department: 'Data Science',
              status: 'active',
              description: 'Extracts insights from complex datasets'
            },
            {
              id: 'data-ml',
              name: 'NeuralNexus',
              title: 'Machine Learning Engineer',
              department: 'Data Science',
              status: 'active',
              description: 'Develops and trains machine learning models'
            },
            {
              id: 'data-viz',
              name: 'VisualInsight',
              title: 'Data Visualization Specialist',
              department: 'Data Science',
              status: 'in-development',
              description: 'Creates interactive data visualizations'
            }
          ]
        },
        {
          id: 'security-director',
          name: 'CyberSentinel',
          title: 'Security Director',
          department: 'Cybersecurity',
          status: 'in-development',
          description: 'Ensures data protection and system security',
          icon: 'shield',
          children: [
            {
              id: 'security-threat',
              name: 'ThreatHunter',
              title: 'Threat Detection Specialist',
              department: 'Cybersecurity',
              status: 'active',
              description: 'Identifies and neutralizes security threats'
            },
            {
              id: 'security-compliance',
              name: 'ComplianceGuardian',
              title: 'Compliance Officer',
              department: 'Cybersecurity',
              status: 'planned',
              description: 'Ensures adherence to security regulations'
            }
          ]
        }
      ]
    },
    {
      id: 'cfo',
      name: 'FinanceLogic',
      title: 'Chief Financial AI',
      department: 'Finance',
      status: 'planned',
      description: 'Manages financial planning and analysis',
      icon: 'money',
      children: [
        {
          id: 'finance-accounting',
          name: 'LedgerMind',
          title: 'Accounting Manager',
          department: 'Finance',
          status: 'concept',
          description: 'Handles accounting and financial reporting'
        },
        {
          id: 'finance-forecast',
          name: 'ForecastPro',
          title: 'Financial Forecasting Specialist',
          department: 'Finance',
          status: 'concept',
          description: 'Predicts financial trends and outcomes'
        }
      ]
    },
    {
      id: 'cmo',
      name: 'MarketMind',
      title: 'Chief Marketing AI',
      department: 'Marketing',
      status: 'in-development',
      description: 'Directs marketing strategy and brand development',
      icon: 'bullhorn',
      tools: [
        {
          id: 'midjourney',
          name: 'Midjourney',
          icon: 'image',
          productIcon: 'https://upload.wikimedia.org/wikipedia/commons/thumb/e/e6/Midjourney_Emblem.png/600px-Midjourney_Emblem.png',
          description: 'AI image generation tool that creates images from textual descriptions using machine learning.',
          url: 'https://www.midjourney.com/',
          usedBy: 'Marketing team for creating visual content and concept art'
        },
        {
          id: 'dalle',
          name: 'DALL-E',
          icon: 'palette',
          productIcon: 'https://seeklogo.com/images/D/dall-e-logo-1F945CBA89-seeklogo.com.png',
          description: 'AI system by OpenAI that can create realistic images and art from a description in natural language.',
          url: 'https://openai.com/dall-e-3',
          usedBy: 'Marketing team for generating custom imagery and creative assets'
        },
        {
          id: 'canva',
          name: 'Canva',
          icon: 'palette',
          productIcon: 'https://cdn.iconscout.com/icon/free/png-256/free-canva-3521468-2944912.png',
          description: 'Online design and publishing tool with templates for creating graphics, presentations, posters, and other visual content.',
          url: 'https://www.canva.com/',
          usedBy: 'Marketing team for creating branded content and marketing materials'
        },
        {
          id: 'hootsuite',
          name: 'Hootsuite',
          icon: 'comments',
          productIcon: 'https://cdn1.iconfinder.com/data/icons/social-media-2285/512/Colored_Hootsuite_logo-512.png',
          description: 'Social media management platform that helps organizations execute social media strategies across multiple social networks.',
          url: 'https://www.hootsuite.com/',
          usedBy: 'Marketing team for social media management and scheduling'
        }
      ],
      children: [
        {
          id: 'marketing-content',
          name: 'ContentCraft',
          title: 'Content Marketing Manager',
          department: 'Marketing',
          status: 'active',
          description: 'Creates engaging content across channels'
        },
        {
          id: 'marketing-social',
          name: 'SocialPulse',
          title: 'Social Media Strategist',
          department: 'Marketing',
          status: 'active',
          description: 'Manages social media presence and engagement'
        },
        {
          id: 'marketing-analytics',
          name: 'InsightEngine',
          title: 'Marketing Analytics Specialist',
          department: 'Marketing',
          status: 'in-development',
          description: 'Analyzes marketing performance and ROI'
        }
      ]
    },
    {
      id: 'cro',
      name: 'ResearchNova',
      title: 'Chief Research AI',
      department: 'Research',
      status: 'in-development',
      description: 'Leads research initiatives and innovation',
      icon: 'flask',
      children: [
        {
          id: 'research-economics',
          name: 'EconInsight',
          title: 'Economic Research Director',
          department: 'Economics',
          status: 'active',
          description: 'Conducts economic analysis and forecasting',
          children: [
            {
              id: 'econ-macro',
              name: 'MacroMind',
              title: 'Macroeconomic Analyst',
              department: 'Economics',
              status: 'active',
              description: 'Analyzes global economic trends'
            },
            {
              id: 'econ-micro',
              name: 'MicroLogic',
              title: 'Microeconomic Analyst',
              department: 'Economics',
              status: 'in-development',
              description: 'Studies market behavior and industry dynamics'
            },
            {
              id: 'econ-policy',
              name: 'PolicyPulse',
              title: 'Economic Policy Analyst',
              department: 'Economics',
              status: 'planned',
              description: 'Evaluates impact of economic policies'
            }
          ]
        },
        {
          id: 'research-policy',
          name: 'PolicySynth',
          title: 'Policy Research Director',
          department: 'Policy Research',
          status: 'planned',
          description: 'Analyzes policy implications and outcomes',
          children: [
            {
              id: 'policy-public',
              name: 'PublicPolicyAI',
              title: 'Public Policy Analyst',
              department: 'Policy Research',
              status: 'concept',
              description: 'Evaluates public policy effectiveness'
            },
            {
              id: 'policy-regulatory',
              name: 'RegulationRadar',
              title: 'Regulatory Analyst',
              department: 'Policy Research',
              status: 'concept',
              description: 'Monitors regulatory changes and compliance'
            }
          ]
        }
      ]
    },
    {
      id: 'cio',
      name: 'InfoSynapse',
      title: 'Chief Information AI',
      department: 'Information Technology',
      status: 'planned',
      description: 'Manages information systems and data infrastructure',
      icon: 'network',
      children: [
        {
          id: 'it-infrastructure',
          name: 'InfraCore',
          title: 'Infrastructure Manager',
          department: 'Information Technology',
          status: 'concept',
          description: 'Maintains IT infrastructure and systems'
        },
        {
          id: 'it-support',
          name: 'SupportSage',
          title: 'Technical Support Lead',
          department: 'Information Technology',
          status: 'active',
          description: 'Provides technical assistance and troubleshooting'
        }
      ]
    }
  ]
};
