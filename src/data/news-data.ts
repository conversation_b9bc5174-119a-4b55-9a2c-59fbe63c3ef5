export interface NewsItem {
  id: string;
  title: string;
  description: string;
  date: string;
  source: string;
  sourceUrl: string;
  imageUrl: string;
  url: string;
  category: 'research' | 'industry' | 'policy' | 'events';
  tags: string[];
  featured?: boolean;
}

const newsData: NewsItem[] = [
  {
    id: 'news-1',
    title: 'OpenAI Announces GPT-5 with Enhanced Reasoning Capabilities',
    description: 'OpenAI has unveiled GPT-5, featuring significant improvements in reasoning, planning, and factual accuracy compared to previous models.',
    date: '2023-11-15',
    source: 'TechCrunch',
    sourceUrl: 'https://techcrunch.com',
    imageUrl: '/images/news/gpt5-announcement.jpg',
    url: '/news/openai-announces-gpt5',
    category: 'research',
    tags: ['OpenAI', 'GPT-5', 'Language Models'],
    featured: true
  },
  {
    id: 'news-2',
    title: 'Google DeepMind Achieves Breakthrough in Protein Structure Prediction',
    description: 'Google DeepMind researchers have developed a new AI system that can predict protein structures with unprecedented accuracy, potentially revolutionizing drug discovery.',
    date: '2023-11-10',
    source: 'Nature',
    sourceUrl: 'https://nature.com',
    imageUrl: '/images/news/deepmind-protein.jpg',
    url: '/news/deepmind-protein-breakthrough',
    category: 'research',
    tags: ['Google DeepMind', 'Protein Folding', 'AlphaFold'],
    featured: true
  },
  {
    id: 'news-3',
    title: 'EU Passes Comprehensive AI Regulation Framework',
    description: 'The European Union has approved a landmark AI regulation framework that establishes rules for AI development and deployment across member states.',
    date: '2023-11-05',
    source: 'Reuters',
    sourceUrl: 'https://reuters.com',
    imageUrl: '/images/news/eu-ai-regulation.jpg',
    url: '/news/eu-ai-regulation',
    category: 'policy',
    tags: ['Regulation', 'EU', 'Policy'],
    featured: false
  },
  {
    id: 'news-4',
    title: 'Microsoft Integrates AI Assistants Across Office Suite',
    description: 'Microsoft has announced the integration of advanced AI assistants across its entire Office suite, enhancing productivity and creativity for users.',
    date: '2023-10-28',
    source: 'The Verge',
    sourceUrl: 'https://theverge.com',
    imageUrl: '/images/news/microsoft-ai-office.jpg',
    url: '/news/microsoft-ai-office-integration',
    category: 'industry',
    tags: ['Microsoft', 'Office', 'Productivity'],
    featured: false
  },
  {
    id: 'news-5',
    title: 'AI-Generated Art Wins Major International Competition',
    description: 'For the first time, an AI-generated artwork has won a prestigious international art competition, sparking debates about creativity and authorship.',
    date: '2023-10-20',
    source: 'Artnet',
    sourceUrl: 'https://artnet.com',
    imageUrl: '/images/news/ai-art-competition.jpg',
    url: '/news/ai-art-competition-winner',
    category: 'industry',
    tags: ['AI Art', 'Creativity', 'Competition'],
    featured: false
  },
  {
    id: 'news-6',
    title: 'World AI Summit 2023 Announces Speaker Lineup',
    description: 'The World AI Summit 2023 has announced its speaker lineup, featuring leading researchers, industry executives, and policymakers from around the globe.',
    date: '2023-10-15',
    source: 'AI News',
    sourceUrl: 'https://ainews.com',
    imageUrl: '/images/news/ai-summit-2023.jpg',
    url: '/news/world-ai-summit-2023',
    category: 'events',
    tags: ['Conference', 'Summit', 'Events'],
    featured: false
  },
  {
    id: 'news-7',
    title: 'Anthropic Releases Claude 3 with Multimodal Capabilities',
    description: 'Anthropic has released Claude 3, its latest AI assistant with enhanced multimodal capabilities, allowing it to process and generate text, images, and audio.',
    date: '2023-10-10',
    source: 'VentureBeat',
    sourceUrl: 'https://venturebeat.com',
    imageUrl: '/images/news/claude-3-release.jpg',
    url: '/news/anthropic-claude-3-release',
    category: 'research',
    tags: ['Anthropic', 'Claude', 'Multimodal AI'],
    featured: true
  },
  {
    id: 'news-8',
    title: 'AI-Powered Drug Discovery Leads to Breakthrough Treatment',
    description: 'A pharmaceutical company has announced a breakthrough treatment for a rare disease, developed with the help of AI-powered drug discovery platforms.',
    date: '2023-10-05',
    source: 'Science Daily',
    sourceUrl: 'https://sciencedaily.com',
    imageUrl: '/images/news/ai-drug-discovery.jpg',
    url: '/news/ai-drug-discovery-breakthrough',
    category: 'industry',
    tags: ['Healthcare', 'Drug Discovery', 'Medicine'],
    featured: false
  }
];

export function getAllNews(): NewsItem[] {
  return newsData;
}

export function getFeaturedNews(count: number = 3): NewsItem[] {
  return newsData
    .filter(item => item.featured)
    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
    .slice(0, count);
}

export function getLatestNews(count: number = 5): NewsItem[] {
  return newsData
    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
    .slice(0, count);
}

export function getNewsByCategory(category: string, count: number = 5): NewsItem[] {
  return newsData
    .filter(item => item.category === category)
    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
    .slice(0, count);
}
