import { User, UserRole, UserStatus } from '@/types/user';

// Helper function to generate default permissions based on role
const getDefaultPermissions = (role: UserRole) => {
  switch (role) {
    case 'admin':
      return {
        canCreateAgents: true,
        canEditAgents: true,
        canDeleteAgents: true,
        canManageUsers: true,
        canAccessAdmin: true,
        canAccessAnalytics: true,
        canManageContent: true,
        canApproveContent: true
      };
    case 'manager':
      return {
        canCreateAgents: true,
        canEditAgents: true,
        canDeleteAgents: false,
        canManageUsers: false,
        canAccessAdmin: true,
        canAccessAnalytics: true,
        canManageContent: true,
        canApproveContent: true
      };
    case 'user':
      return {
        canCreateAgents: false,
        canEditAgents: false,
        canDeleteAgents: false,
        canManageUsers: false,
        canAccessAdmin: false,
        canAccessAnalytics: false,
        canManageContent: false,
        canApproveContent: false
      };
    case 'guest':
      return {
        canCreateAgents: false,
        canEditAgents: false,
        canDeleteAgents: false,
        canManageUsers: false,
        canAccessAdmin: false,
        canAccessAnalytics: false,
        canManageContent: false,
        canApproveContent: false
      };
  }
};

// Mock users data
export const mockUsers: User[] = [
  {
    id: 'user-001',
    email: '<EMAIL>',
    username: 'admin',
    role: 'admin',
    status: 'active',
    permissions: getDefaultPermissions('admin'),
    profile: {
      firstName: 'Admin',
      lastName: 'User',
      jobTitle: 'System Administrator',
      department: 'IT',
      location: 'New York',
      bio: 'System administrator with full access to all platform features.',
      avatarUrl: 'https://randomuser.me/api/portraits/men/1.jpg',
      phoneNumber: '+****************'
    },
    settings: {
      theme: 'system',
      emailNotifications: true,
      twoFactorEnabled: true,
      sidebarCollapsed: false,
      language: 'en-US'
    },
    activity: {
      lastLogin: new Date('2023-05-15T08:30:00'),
      lastActive: new Date('2023-05-15T16:45:00'),
      totalLogins: 247,
      totalSessions: 312,
      averageSessionDuration: 3600, // 1 hour
      favoriteAgents: ['1', '3', '5'],
      favoriteSection: 'admin'
    },
    createdAt: new Date('2022-01-01T00:00:00'),
    updatedAt: new Date('2023-04-15T14:30:00'),
    lastPasswordChange: new Date('2023-03-01T00:00:00')
  },
  {
    id: 'user-002',
    email: '<EMAIL>',
    username: 'manager',
    role: 'manager',
    status: 'active',
    permissions: getDefaultPermissions('manager'),
    profile: {
      firstName: 'Sarah',
      lastName: 'Johnson',
      jobTitle: 'Project Manager',
      department: 'Operations',
      location: 'Chicago',
      bio: 'Project manager overseeing AI implementation projects.',
      avatarUrl: 'https://randomuser.me/api/portraits/women/2.jpg',
      phoneNumber: '+****************'
    },
    settings: {
      theme: 'light',
      emailNotifications: true,
      twoFactorEnabled: false,
      sidebarCollapsed: true,
      language: 'en-US'
    },
    activity: {
      lastLogin: new Date('2023-05-14T09:15:00'),
      lastActive: new Date('2023-05-15T17:30:00'),
      totalLogins: 183,
      totalSessions: 245,
      averageSessionDuration: 2700, // 45 minutes
      favoriteAgents: ['2', '4'],
      favoriteSection: 'browse'
    },
    createdAt: new Date('2022-02-15T00:00:00'),
    updatedAt: new Date('2023-04-10T11:20:00'),
    lastPasswordChange: new Date('2023-02-15T00:00:00')
  },
  {
    id: 'user-003',
    email: '<EMAIL>',
    username: 'johnsmith',
    role: 'user',
    status: 'active',
    permissions: getDefaultPermissions('user'),
    profile: {
      firstName: 'John',
      lastName: 'Smith',
      jobTitle: 'Data Analyst',
      department: 'Analytics',
      location: 'Boston',
      bio: 'Data analyst using AI tools to enhance data processing workflows.',
      avatarUrl: 'https://randomuser.me/api/portraits/men/3.jpg'
    },
    settings: {
      theme: 'dark',
      emailNotifications: false,
      twoFactorEnabled: false,
      sidebarCollapsed: false,
      language: 'en-US'
    },
    activity: {
      lastLogin: new Date('2023-05-15T10:00:00'),
      lastActive: new Date('2023-05-15T15:30:00'),
      totalLogins: 97,
      totalSessions: 142,
      averageSessionDuration: 1800, // 30 minutes
      favoriteAgents: ['1', '3'],
      favoriteSection: 'learning'
    },
    createdAt: new Date('2022-03-10T00:00:00'),
    updatedAt: new Date('2023-03-25T09:45:00'),
    lastPasswordChange: new Date('2023-01-20T00:00:00')
  },
  {
    id: 'user-004',
    email: '<EMAIL>',
    username: 'emilyd',
    role: 'user',
    status: 'active',
    permissions: getDefaultPermissions('user'),
    profile: {
      firstName: 'Emily',
      lastName: 'Davis',
      jobTitle: 'Content Strategist',
      department: 'Marketing',
      location: 'San Francisco',
      bio: 'Content strategist exploring AI tools for content creation and optimization.',
      avatarUrl: 'https://randomuser.me/api/portraits/women/4.jpg',
      phoneNumber: '+****************'
    },
    settings: {
      theme: 'light',
      emailNotifications: true,
      twoFactorEnabled: false,
      sidebarCollapsed: true,
      language: 'en-US'
    },
    activity: {
      lastLogin: new Date('2023-05-14T14:20:00'),
      lastActive: new Date('2023-05-15T11:45:00'),
      totalLogins: 76,
      totalSessions: 104,
      averageSessionDuration: 2100, // 35 minutes
      favoriteAgents: ['4', '5'],
      favoriteSection: 'learning_resources'
    },
    createdAt: new Date('2022-04-05T00:00:00'),
    updatedAt: new Date('2023-03-15T16:30:00'),
    lastPasswordChange: new Date('2023-01-10T00:00:00')
  },
  {
    id: 'user-005',
    email: '<EMAIL>',
    username: 'michaelb',
    role: 'user',
    status: 'inactive',
    permissions: getDefaultPermissions('user'),
    profile: {
      firstName: 'Michael',
      lastName: 'Brown',
      jobTitle: 'Research Scientist',
      department: 'R&D',
      location: 'Seattle',
      bio: 'Research scientist working on AI applications in healthcare.',
      avatarUrl: 'https://randomuser.me/api/portraits/men/5.jpg'
    },
    settings: {
      theme: 'system',
      emailNotifications: false,
      twoFactorEnabled: false,
      sidebarCollapsed: false,
      language: 'en-US'
    },
    activity: {
      lastLogin: new Date('2023-04-20T09:30:00'),
      lastActive: new Date('2023-04-20T14:15:00'),
      totalLogins: 42,
      totalSessions: 67,
      averageSessionDuration: 3300, // 55 minutes
      favoriteAgents: ['2'],
      favoriteSection: 'agent_detail'
    },
    createdAt: new Date('2022-05-12T00:00:00'),
    updatedAt: new Date('2023-04-20T14:15:00'),
    lastPasswordChange: new Date('2022-12-05T00:00:00')
  },
  {
    id: 'user-006',
    email: '<EMAIL>',
    username: 'jenniferw',
    role: 'user',
    status: 'pending',
    permissions: getDefaultPermissions('user'),
    profile: {
      firstName: 'Jennifer',
      lastName: 'Wilson',
      jobTitle: 'UX Designer',
      department: 'Design',
      location: 'Austin',
      avatarUrl: 'https://randomuser.me/api/portraits/women/6.jpg'
    },
    settings: {
      theme: 'light',
      emailNotifications: true,
      twoFactorEnabled: false,
      sidebarCollapsed: false,
      language: 'en-US'
    },
    activity: {
      lastLogin: new Date('2023-05-10T11:00:00'),
      lastActive: new Date('2023-05-10T11:30:00'),
      totalLogins: 2,
      totalSessions: 2,
      averageSessionDuration: 1800, // 30 minutes
      favoriteAgents: [],
      favoriteSection: 'home'
    },
    createdAt: new Date('2023-05-10T00:00:00'),
    updatedAt: new Date('2023-05-10T11:30:00'),
    lastPasswordChange: new Date('2023-05-10T00:00:00')
  },
  {
    id: 'user-007',
    email: '<EMAIL>',
    username: 'robertt',
    role: 'user',
    status: 'suspended',
    permissions: getDefaultPermissions('user'),
    profile: {
      firstName: 'Robert',
      lastName: 'Taylor',
      jobTitle: 'Sales Representative',
      department: 'Sales',
      location: 'Denver',
      avatarUrl: 'https://randomuser.me/api/portraits/men/7.jpg',
      phoneNumber: '+****************'
    },
    settings: {
      theme: 'dark',
      emailNotifications: false,
      twoFactorEnabled: false,
      sidebarCollapsed: true,
      language: 'en-US'
    },
    activity: {
      lastLogin: new Date('2023-03-15T13:45:00'),
      lastActive: new Date('2023-03-15T15:30:00'),
      totalLogins: 28,
      totalSessions: 35,
      averageSessionDuration: 1500, // 25 minutes
      favoriteAgents: ['1'],
      favoriteSection: 'browse'
    },
    createdAt: new Date('2022-06-20T00:00:00'),
    updatedAt: new Date('2023-03-20T10:00:00'),
    lastPasswordChange: new Date('2022-11-15T00:00:00')
  },
  {
    id: 'user-008',
    email: '<EMAIL>',
    username: 'guest',
    role: 'guest',
    status: 'active',
    permissions: getDefaultPermissions('guest'),
    profile: {
      firstName: 'Guest',
      lastName: 'User',
      jobTitle: 'External Consultant',
      department: 'External',
      location: 'Remote',
      bio: 'Guest account with limited access for demonstration purposes.',
      avatarUrl: 'https://randomuser.me/api/portraits/lego/1.jpg'
    },
    settings: {
      theme: 'light',
      emailNotifications: false,
      twoFactorEnabled: false,
      sidebarCollapsed: false,
      language: 'en-US'
    },
    activity: {
      lastLogin: new Date('2023-05-14T10:30:00'),
      lastActive: new Date('2023-05-14T11:15:00'),
      totalLogins: 5,
      totalSessions: 5,
      averageSessionDuration: 900, // 15 minutes
      favoriteAgents: [],
      favoriteSection: 'browse'
    },
    createdAt: new Date('2023-01-01T00:00:00'),
    updatedAt: new Date('2023-05-14T11:15:00'),
    lastPasswordChange: new Date('2023-01-01T00:00:00')
  }
];

// Helper functions for user management
export const getUserById = (id: string): User | undefined => {
  return mockUsers.find(user => user.id === id);
};

export const getUsersByRole = (role: UserRole): User[] => {
  return mockUsers.filter(user => user.role === role);
};

export const getUsersByStatus = (status: UserStatus): User[] => {
  return mockUsers.filter(user => user.status === status);
};

export const getActiveUsers = (): User[] => {
  return mockUsers.filter(user => user.status === 'active');
};

export const searchUsers = (query: string): User[] => {
  const lowercaseQuery = query.toLowerCase();
  return mockUsers.filter(user => 
    user.username.toLowerCase().includes(lowercaseQuery) ||
    user.email.toLowerCase().includes(lowercaseQuery) ||
    user.profile.firstName.toLowerCase().includes(lowercaseQuery) ||
    user.profile.lastName.toLowerCase().includes(lowercaseQuery) ||
    (user.profile.jobTitle && user.profile.jobTitle.toLowerCase().includes(lowercaseQuery)) ||
    (user.profile.department && user.profile.department.toLowerCase().includes(lowercaseQuery))
  );
};
