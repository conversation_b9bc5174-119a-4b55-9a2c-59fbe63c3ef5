'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { User, AuthState, LoginCredentials, RegisterCredentials } from '@/types/auth';

interface AuthContextType extends AuthState {
  login: (credentials: LoginCredentials) => Promise<void>;
  register: (credentials: RegisterCredentials) => Promise<void>;
  logout: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const router = useRouter();

  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    isLoading: true,
    error: null,
  });

  // Load user from localStorage on mount
  useEffect(() => {
    const loadUser = () => {
      try {
        console.log('Loading user from localStorage...');
        const userJson = localStorage.getItem('user');
        console.log('User JSON from localStorage:', userJson);

        if (userJson) {
          const user = JSON.parse(userJson) as User;
          console.log('Parsed user:', user);
          setAuthState({
            user,
            isLoading: false,
            error: null,
          });
        } else {
          console.log('No user found in localStorage');
          setAuthState({
            user: null,
            isLoading: false,
            error: null,
          });
        }
      } catch (error) {
        console.error('Error loading user from localStorage:', error);
        setAuthState({
          user: null,
          isLoading: false,
          error: 'Failed to load user data',
        });
      }
    };

    // Only run in browser environment
    if (typeof window !== 'undefined') {
      loadUser();
    }
  }, []);

  const login = async (credentials: LoginCredentials) => {
    console.log('Login function called with:', credentials.email);
    try {
      console.log('Setting loading state...');
      setAuthState((prev) => ({ ...prev, isLoading: true, error: null }));

      console.log('Sending login request to API...');
      const response = await fetch('/api/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials),
      });

      console.log('API response status:', response.status);
      const data = await response.json();
      console.log('API response data:', data);

      if (!response.ok || !data.success) {
        setAuthState((prev) => ({
          ...prev,
          isLoading: false,
          error: data.message || 'Login failed',
        }));
        return;
      }

      console.log('Login successful, storing user data...');
      // Store user in localStorage
      localStorage.setItem('user', JSON.stringify(data.user));

      // Also set a cookie for the middleware
      document.cookie = `user=${JSON.stringify(data.user)}; path=/; max-age=86400`;

      console.log('Updating auth state...');
      // Update auth state
      setAuthState({
        user: data.user,
        isLoading: false,
        error: null,
      });

      console.log('Redirecting to homepage...');
      router.push('/');
    } catch (error) {
      setAuthState((prev) => ({
        ...prev,
        isLoading: false,
        error: 'An unexpected error occurred',
      }));
    }
  };

  const register = async (credentials: RegisterCredentials) => {
    try {
      setAuthState((prev) => ({ ...prev, isLoading: true, error: null }));

      // In a real app, this would be an API call to create a new user
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials),
      });

      const data = await response.json();

      if (!response.ok) {
        setAuthState((prev) => ({
          ...prev,
          isLoading: false,
          error: data.message || 'Registration failed',
        }));
        return;
      }

      // Automatically log in after successful registration
      await login({
        email: credentials.email,
        password: credentials.password,
      });
    } catch (error) {
      setAuthState((prev) => ({
        ...prev,
        isLoading: false,
        error: 'An unexpected error occurred',
      }));
    }
  };

  const logout = async () => {
    try {
      setAuthState((prev) => ({ ...prev, isLoading: true }));

      // Remove user from localStorage
      localStorage.removeItem('user');

      // Also clear the cookie
      document.cookie = 'user=; path=/; max-age=0';

      // Update auth state
      setAuthState({
        user: null,
        isLoading: false,
        error: null,
      });

      router.push('/auth/login');
    } catch (error) {
      setAuthState((prev) => ({
        ...prev,
        isLoading: false,
        error: 'Logout failed',
      }));
    }
  };

  return (
    <AuthContext.Provider
      value={{
        ...authState,
        login,
        register,
        logout,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
