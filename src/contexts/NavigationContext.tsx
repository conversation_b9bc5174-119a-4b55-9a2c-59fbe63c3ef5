'use client';

import React, { createContext, useContext, useReducer, useEffect, useRef, ReactNode } from 'react';
import { usePathname } from 'next/navigation';
import {
  NavigationState,
  NavigationEvent,
  NavigationContext as NavContextType,
  QuickAction,
  ChatSession,
  DEFAULT_NAV_CONFIG
} from '@/types/navigation';
import RenderTracker from '@/components/debug/RenderTracker';

// Initial state
const initialState: NavigationState = {
  activeSection: null,
  activePath: '/',
  collapsedSections: [],
  sidebarCollapsed: false,
  quickActions: [],
  context: {
    currentArea: 'dashboard',
    hasActiveSession: false,
    isInKnowledgeArea: false,
    userRole: 'user'
  }
};

// Navigation reducer
function navigationReducer(state: NavigationState, action: NavigationEvent): NavigationState {
  switch (action.type) {
    case 'SECTION_TOGGLE':
      const isCollapsed = state.collapsedSections.includes(action.sectionId);
      return {
        ...state,
        collapsedSections: isCollapsed
          ? state.collapsedSections.filter(id => id !== action.sectionId)
          : [...state.collapsedSections, action.sectionId]
      };

    case 'SIDEBAR_TOGGLE':
      return {
        ...state,
        sidebarCollapsed: !state.sidebarCollapsed
      };

    case 'CONTEXT_CHANGE':
      return {
        ...state,
        context: { ...state.context, ...action.context }
      };

    case 'QUICK_ACTION':
      // Handle quick action execution
      const action_item = state.quickActions.find(qa => qa.id === action.actionId);
      if (action_item) {
        action_item.action();
      }
      return state;

    case 'CHAT_SELECT':
      return {
        ...state,
        context: {
          ...state.context,
          activeChat: action.chatId,
          hasActiveSession: true,
          currentArea: 'conversations'
        }
      };

    case 'DOCUMENT_SELECT':
      return {
        ...state,
        context: {
          ...state.context,
          currentArea: 'knowledge',
          isInKnowledgeArea: true
        }
      };

    default:
      return state;
  }
}

// Context type
interface NavigationContextType {
  state: NavigationState;
  dispatch: React.Dispatch<NavigationEvent>;
  toggleSection: (sectionId: string) => void;
  toggleSidebar: () => void;
  updateContext: (context: Partial<NavContextType>) => void;
  executeQuickAction: (actionId: string) => void;
  selectChat: (chatId: string) => void;
  selectDocument: (documentId: string) => void;
  getVisibleQuickActions: () => QuickAction[];
  isCurrentArea: (area: string) => boolean;
}

// Create context
const NavigationContext = createContext<NavigationContextType | undefined>(undefined);

// Provider component
export function NavigationProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(navigationReducer, initialState);
  const pathname = usePathname();
  const previousAreaRef = useRef<string>('dashboard');
  const previousKnowledgeRef = useRef<boolean>(false);
  const isInitialLoadRef = useRef<boolean>(true);

  // Update active path and context based on current route
  useEffect(() => {
    let currentArea: NavContextType['currentArea'] = 'dashboard';
    let isInKnowledgeArea = false;

    if (pathname.startsWith('/browse') || pathname.startsWith('/popular') || pathname.startsWith('/favorites')) {
      currentArea = 'agents';
    } else if (pathname.startsWith('/conversations') || pathname.startsWith('/chat')) {
      currentArea = 'conversations';
    } else if (pathname.startsWith('/knowledge') || pathname.startsWith('/documents') || pathname.startsWith('/learning')) {
      currentArea = 'knowledge';
      isInKnowledgeArea = true;
    } else if (pathname.startsWith('/tools') || pathname.startsWith('/settings')) {
      currentArea = 'tools';
    } else if (pathname.startsWith('/admin')) {
      currentArea = 'admin';
    }

    // Only dispatch if the area actually changed
    if (previousAreaRef.current !== currentArea || previousKnowledgeRef.current !== isInKnowledgeArea) {
      previousAreaRef.current = currentArea;
      previousKnowledgeRef.current = isInKnowledgeArea;

      dispatch({
        type: 'CONTEXT_CHANGE',
        context: {
          currentArea,
          isInKnowledgeArea
        }
      });
    }
  }, [pathname]);

  // Load persisted state from localStorage (only once on mount)
  useEffect(() => {
    if (typeof window !== 'undefined' && DEFAULT_NAV_CONFIG.persistState) {
      const savedCollapsed = localStorage.getItem('sidebarCollapsed');
      const savedSections = localStorage.getItem('collapsedSections');

      if (savedCollapsed === 'true') {
        dispatch({ type: 'SIDEBAR_TOGGLE' });
      }

      if (savedSections) {
        try {
          const sections = JSON.parse(savedSections);
          if (Array.isArray(sections)) {
            sections.forEach((sectionId: string) => {
              dispatch({ type: 'SECTION_TOGGLE', sectionId });
            });
          }
        } catch (error) {
          console.warn('Failed to parse saved collapsed sections:', error);
        }
      }

      // Mark initial load as complete
      isInitialLoadRef.current = false;
    }
  }, []); // Empty dependency array - only run once on mount

  // Persist state changes to localStorage (but not during initial load)
  useEffect(() => {
    if (typeof window !== 'undefined' && DEFAULT_NAV_CONFIG.persistState && !isInitialLoadRef.current) {
      localStorage.setItem('sidebarCollapsed', state.sidebarCollapsed.toString());
      localStorage.setItem('collapsedSections', JSON.stringify(state.collapsedSections));
    }
  }, [state.sidebarCollapsed, state.collapsedSections]);

  // Context methods
  const toggleSection = (sectionId: string) => {
    dispatch({ type: 'SECTION_TOGGLE', sectionId });
  };

  const toggleSidebar = () => {
    dispatch({ type: 'SIDEBAR_TOGGLE' });
  };

  const updateContext = (context: Partial<NavContextType>) => {
    dispatch({ type: 'CONTEXT_CHANGE', context });
  };

  const executeQuickAction = (actionId: string) => {
    dispatch({ type: 'QUICK_ACTION', actionId });
  };

  const selectChat = (chatId: string) => {
    dispatch({ type: 'CHAT_SELECT', chatId });
  };

  const selectDocument = (documentId: string) => {
    dispatch({ type: 'DOCUMENT_SELECT', documentId });
  };

  const getVisibleQuickActions = (): QuickAction[] => {
    return state.quickActions.filter(action => {
      if (action.contexts.includes('*')) return true;
      return action.contexts.includes(state.context.currentArea);
    });
  };

  const isCurrentArea = (area: string): boolean => {
    return state.context.currentArea === area;
  };

  const value: NavigationContextType = {
    state,
    dispatch,
    toggleSection,
    toggleSidebar,
    updateContext,
    executeQuickAction,
    selectChat,
    selectDocument,
    getVisibleQuickActions,
    isCurrentArea
  };

  return (
    <NavigationContext.Provider value={value}>
      <RenderTracker
        name="NavigationProvider"
        props={{
          pathname,
          currentArea: state.context.currentArea,
          collapsedSections: state.collapsedSections,
          sidebarCollapsed: state.sidebarCollapsed
        }}
      />
      {children}
    </NavigationContext.Provider>
  );
}

// Hook to use navigation context
export function useNavigation() {
  const context = useContext(NavigationContext);
  if (context === undefined) {
    throw new Error('useNavigation must be used within a NavigationProvider');
  }
  return context;
}

// Hook for quick actions
export function useQuickActions() {
  const { state, executeQuickAction, getVisibleQuickActions } = useNavigation();

  return {
    quickActions: getVisibleQuickActions(),
    executeAction: executeQuickAction,
    context: state.context
  };
}
