# AI Agent Hub

## Overview

AI Agent Hub is a modern web application that provides a centralized platform for discovering, interacting with, and managing AI agents. The application allows users to browse available agents, view detailed information about each agent, interact with agents through a chat interface, and maintain a list of favorite agents for quick access. Additionally, it features a comprehensive Learning section with educational resources including videos, articles, blog posts, and a curated directory of AI-powered tools and services.

![AI Agent Hub Screenshot](https://via.placeholder.com/800x450.png?text=AI+Agent+Hub)

## Features

### Authentication System
- User registration and login functionality
- Secure password handling with bcrypt
- Protected routes for authenticated users
- User profile management
- Session persistence across page refreshes

### Browse Agents
- View all available agents in grid or list view
- Filter agents by category, capabilities, and tags
- Search for agents by name, description, or category
- Sort agents by popularity, name, or date added
- Advanced filtering options including date range and capabilities
- Pagination for handling large numbers of agents

### Agent Details
- Detailed agent information including capabilities and metadata
- Interactive chat interface for communicating with agents
- Session management (save, export, clear)
- Related agents recommendations
- Favorite/unfavorite functionality

### Favorites
- Dedicated page for favorited agents
- Sort and filter favorite agents
- Quick access to frequently used agents
- User-specific favorites tied to user accounts

### User Profile
- View and edit user information
- Change password functionality
- Manage user preferences
- View account details

### Learning Hub
- Educational videos with searchable listings and filtering
- In-depth articles on AI topics and best practices
- Internal blog for updates, case studies, and insights
- Resources directory with categorized AI tools and services
- Filtering and search functionality for all learning content
- Featured content showcased on the home page

### User Experience
- Responsive design that works on desktop and mobile devices
- Dark mode support
- Collapsible sidebar navigation with icon-only view
- Frosted glass effect for header and sidebar
- Custom-styled scrollbars for main content
- Recently viewed agents tracking
- User preferences saved between sessions
- Real-time updates across components
- User-specific settings and preferences

## Technology Stack

- **Frontend Framework**: Next.js 14 (App Router)
- **Styling**: Tailwind CSS
- **State Management**: React Hooks and Context
- **Authentication**: Custom authentication system with secure password handling
- **Data Storage**: Local Storage and cookies for user data and preferences (with plans for PostgreSQL database integration)
- **Icons**: Font Awesome React components
- **Content Rendering**: React Markdown for article and blog content
- **UI Components**: Custom-built components with Tailwind CSS

## Getting Started

### Prerequisites

- Node.js 18.17.0 or later
- npm or yarn package manager

### Installation

1. Clone the repository:
   ```bash
   git clone https://dev.azure.com/WhiteshieldAi/EXPERIMENTS/_git/AI-HUB-augment-agent-experiment
   ```

2. Navigate to the project directory:
   ```bash
   cd AI-HUB-augment-agent-experiment/ai-hub
   ```

3. Install dependencies:
   ```bash
   npm install
   # or
   yarn install
   ```

### Running the Development Server

```bash
npm run dev
# or
yarn dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the application.

### Building for Production

```bash
npm run build
# or
yarn build
```

To start the production server after building:

```bash
npm run start
# or
yarn start
```

## Project Structure

```
ai-hub/
├── public/              # Static assets
│   └── agents/          # Agent icons
├── src/
│   ├── app/             # Next.js App Router pages
│   │   ├── agent/       # Agent detail pages
│   │   ├── api/         # API routes
│   │   │   ├── auth/    # Authentication API endpoints
│   │   │   ├── login/   # Login API endpoint
│   │   │   └── user/    # User-related API endpoints
│   │   ├── auth/        # Authentication pages
│   │   │   ├── login/   # Login page
│   │   │   ├── register/ # Registration page
│   │   │   └── forgot-password/ # Password recovery page
│   │   ├── browse/      # Browse agents page
│   │   ├── favorites/   # Favorites page
│   │   ├── learning/    # Learning hub pages
│   │   │   ├── articles/ # Articles pages
│   │   │   ├── blog/    # Blog pages
│   │   │   ├── videos/  # Videos pages
│   │   │   └── resources/ # Resources directory pages
│   │   ├── profile/     # User profile page
│   │   └── page.tsx     # Home page
│   ├── components/      # Shared components
│   │   └── layout/      # Layout components (Header, Sidebar)
│   ├── contexts/        # React contexts
│   │   └── AuthContext.tsx # Authentication context
│   ├── data/            # Data files
│   │   ├── agents.ts    # Agent data
│   │   ├── learning-resources.ts # Learning content data
│   │   ├── resources.ts # AI tools and services data
│   │   └── combined-resources.ts # Combined data utilities
│   ├── hooks/           # Custom React hooks
│   │   └── useUserPreferences.ts # Hook for user preferences
│   ├── lib/             # Utility functions
│   │   ├── agents.ts    # Agent data and functions
│   │   ├── events.ts    # Custom events system
│   │   ├── metrics.ts   # Analytics and metrics functions
│   │   └── userPreferences.ts # User preferences management
│   ├── middleware.ts    # Next.js middleware for route protection
│   └── types/           # TypeScript type definitions
│       ├── agent.ts     # Agent-related types
│       ├── auth.ts      # Authentication-related types
│       ├── learning.ts  # Learning content types
│       ├── resources.ts # Resources types
│       └── metrics.ts   # Metrics-related types
└── package.json         # Project dependencies and scripts
```

## Key Components

### Authentication Components
- **AuthContext**: Manages authentication state throughout the application
- **LoginPage**: Handles user login with validation
- **RegisterPage**: Manages user registration with validation
- **ProfilePage**: Displays and allows editing of user profile information
- **PasswordForm**: Allows users to change their password securely

### Layout Components
- **Header**: Global navigation header with user profile and search
- **Sidebar**: Collapsible navigation sidebar with sections for different parts of the app
- **Layout**: Main layout wrapper that combines Header and Sidebar

### Agent Components
- **AgentGrid/AgentList**: Display agents in grid or list format
- **AgentHeader**: Shows agent name, category, and actions
- **AgentInfo**: Displays detailed information about an agent
- **ChatInterface**: Provides chat functionality with an agent
- **RelatedAgents**: Shows related agents recommendations

### Browse Components
- **FilterBar**: Provides filtering and sorting options
- **SearchBar**: Allows searching for agents
- **Pagination**: Handles pagination for large sets of agents
- **AdvancedFilters**: Provides advanced filtering options

### Learning Components
- **LearningHub**: Main landing page for educational content
- **VideosList**: Displays video tutorials with filtering
- **ArticlesList**: Shows articles with search and filtering
- **BlogList**: Displays blog posts with search functionality
- **ResourcesDirectory**: Categorized listing of AI tools and services
- **ResourceCategory**: Displays resources within a specific category
- **ArticleView/BlogView**: Renders article and blog content with Markdown

### API Routes
- **/api/login**: Handles user authentication
- **/api/auth/register**: Manages user registration
- **/api/user/profile**: Handles user profile data
- **/api/user/password**: Manages password changes

## Future Enhancements

### Database Integration
- PostgreSQL database integration for persistent data storage
- User authentication data storage
- Learning content and resources management
- Agent data and interaction history

### Authentication Improvements
- Email verification for user registration
- OAuth integration for social login options
- Two-factor authentication for enhanced security

### Content Management
- Admin interface for managing learning content
- Content creation and editing tools
- User-generated content submissions
- Rating and review system for resources

### AI Integration
- Backend integration for real AI agent interactions
- Agent creation and management tools
- Analytics dashboard for agent usage
- AI-powered content recommendations

### Collaboration Features
- User comments on learning content
- Sharing functionality for resources
- Collaborative features for team environments
- Advanced user roles and permissions system

## Contributing

1. Create a new branch for your feature
2. Make your changes
3. Submit a pull request

## License

This project is proprietary and confidential. Unauthorized copying, distribution, or use is strictly prohibited.
