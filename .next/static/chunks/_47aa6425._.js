(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/_47aa6425._.js", {

"[project]/src/components/animations/FuturisticBackground.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>FuturisticBackground)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
function FuturisticBackground() {
    _s();
    const canvasRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const particles = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])([]);
    const animationFrameId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(0);
    const mousePosition = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])({
        x: 0,
        y: 0
    });
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "FuturisticBackground.useEffect": ()=>{
            const canvas = canvasRef.current;
            if (!canvas) return;
            const ctx = canvas.getContext('2d');
            if (!ctx) return;
            // Set canvas to full window size
            const handleResize = {
                "FuturisticBackground.useEffect.handleResize": ()=>{
                    canvas.width = window.innerWidth;
                    canvas.height = window.innerHeight;
                    initParticles();
                }
            }["FuturisticBackground.useEffect.handleResize"];
            // Track mouse movement
            const handleMouseMove = {
                "FuturisticBackground.useEffect.handleMouseMove": (e)=>{
                    mousePosition.current = {
                        x: e.clientX,
                        y: e.clientY
                    };
                }
            }["FuturisticBackground.useEffect.handleMouseMove"];
            // Initialize particles
            const initParticles = {
                "FuturisticBackground.useEffect.initParticles": ()=>{
                    particles.current = [];
                    const particleCount = Math.min(Math.floor(window.innerWidth * window.innerHeight / 15000), 100);
                    for(let i = 0; i < particleCount; i++){
                        particles.current.push({
                            x: Math.random() * canvas.width,
                            y: Math.random() * canvas.height,
                            size: Math.random() * 2 + 0.5,
                            speedX: (Math.random() - 0.5) * 0.3,
                            speedY: (Math.random() - 0.5) * 0.3,
                            opacity: Math.random() * 0.5 + 0.1,
                            color: getRandomColor()
                        });
                    }
                }
            }["FuturisticBackground.useEffect.initParticles"];
            // Get a random color from our futuristic palette
            const getRandomColor = {
                "FuturisticBackground.useEffect.getRandomColor": ()=>{
                    const colors = [
                        'rgba(64, 196, 255, 1)',
                        'rgba(120, 81, 255, 1)',
                        'rgba(255, 64, 129, 1)',
                        'rgba(0, 176, 255, 1)',
                        'rgba(124, 77, 255, 1)',
                        'rgba(29, 233, 182, 1)' // Teal
                    ];
                    return colors[Math.floor(Math.random() * colors.length)];
                }
            }["FuturisticBackground.useEffect.getRandomColor"];
            // Draw a single particle
            const drawParticle = {
                "FuturisticBackground.useEffect.drawParticle": (particle)=>{
                    if (!ctx) return;
                    ctx.beginPath();
                    ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
                    ctx.fillStyle = particle.color.replace('1)', `${particle.opacity})`);
                    ctx.fill();
                }
            }["FuturisticBackground.useEffect.drawParticle"];
            // Draw connections between particles that are close to each other
            const drawConnections = {
                "FuturisticBackground.useEffect.drawConnections": ()=>{
                    if (!ctx) return;
                    const maxDistance = 150;
                    for(let i = 0; i < particles.current.length; i++){
                        for(let j = i + 1; j < particles.current.length; j++){
                            const dx = particles.current[i].x - particles.current[j].x;
                            const dy = particles.current[i].y - particles.current[j].y;
                            const distance = Math.sqrt(dx * dx + dy * dy);
                            if (distance < maxDistance) {
                                const opacity = 0.2 * (1 - distance / maxDistance);
                                ctx.beginPath();
                                ctx.moveTo(particles.current[i].x, particles.current[i].y);
                                ctx.lineTo(particles.current[j].x, particles.current[j].y);
                                ctx.strokeStyle = `rgba(255, 255, 255, ${opacity})`;
                                ctx.lineWidth = 0.5;
                                ctx.stroke();
                            }
                        }
                    }
                }
            }["FuturisticBackground.useEffect.drawConnections"];
            // Update particle positions and draw them
            const animate = {
                "FuturisticBackground.useEffect.animate": ()=>{
                    if (!ctx || !canvas) return;
                    ctx.clearRect(0, 0, canvas.width, canvas.height);
                    // Update and draw particles
                    particles.current.forEach({
                        "FuturisticBackground.useEffect.animate": (particle)=>{
                            // Update position
                            particle.x += particle.speedX;
                            particle.y += particle.speedY;
                            // Bounce off edges
                            if (particle.x < 0 || particle.x > canvas.width) {
                                particle.speedX *= -1;
                            }
                            if (particle.y < 0 || particle.y > canvas.height) {
                                particle.speedY *= -1;
                            }
                            // Subtle mouse interaction
                            const dx = mousePosition.current.x - particle.x;
                            const dy = mousePosition.current.y - particle.y;
                            const distance = Math.sqrt(dx * dx + dy * dy);
                            if (distance < 150) {
                                const angle = Math.atan2(dy, dx);
                                const force = 0.1 * (1 - distance / 150);
                                particle.speedX -= Math.cos(angle) * force;
                                particle.speedY -= Math.sin(angle) * force;
                                // Limit speed
                                const speed = Math.sqrt(particle.speedX * particle.speedX + particle.speedY * particle.speedY);
                                if (speed > 1) {
                                    particle.speedX = particle.speedX / speed * 1;
                                    particle.speedY = particle.speedY / speed * 1;
                                }
                            }
                            // Draw the particle
                            drawParticle(particle);
                        }
                    }["FuturisticBackground.useEffect.animate"]);
                    // Draw connections
                    drawConnections();
                    // Continue animation
                    animationFrameId.current = requestAnimationFrame(animate);
                }
            }["FuturisticBackground.useEffect.animate"];
            // Set up event listeners
            window.addEventListener('resize', handleResize);
            window.addEventListener('mousemove', handleMouseMove);
            // Initialize and start animation
            handleResize();
            animate();
            // Clean up
            return ({
                "FuturisticBackground.useEffect": ()=>{
                    window.removeEventListener('resize', handleResize);
                    window.removeEventListener('mousemove', handleMouseMove);
                    cancelAnimationFrame(animationFrameId.current);
                }
            })["FuturisticBackground.useEffect"];
        }
    }["FuturisticBackground.useEffect"], []);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("canvas", {
        ref: canvasRef,
        className: "fixed top-0 left-0 -z-10 h-full w-full opacity-40 pointer-events-none"
    }, void 0, false, {
        fileName: "[project]/src/components/animations/FuturisticBackground.tsx",
        lineNumber: 178,
        columnNumber: 5
    }, this);
}
_s(FuturisticBackground, "OEXKgRAvnAX+8fhBWV/DAwF5fxI=");
_c = FuturisticBackground;
var _c;
__turbopack_context__.k.register(_c, "FuturisticBackground");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use client';
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "BailoutToCSR", {
    enumerable: true,
    get: function() {
        return BailoutToCSR;
    }
});
const _bailouttocsr = __turbopack_context__.r("[project]/node_modules/next/dist/shared/lib/lazy-dynamic/bailout-to-csr.js [app-client] (ecmascript)");
function BailoutToCSR(param) {
    let { reason, children } = param;
    if (typeof window === 'undefined') {
        throw Object.defineProperty(new _bailouttocsr.BailoutToCSRError(reason), "__NEXT_ERROR_CODE", {
            value: "E394",
            enumerable: false,
            configurable: true
        });
    }
    return children;
} //# sourceMappingURL=dynamic-bailout-to-csr.js.map
}}),
"[project]/node_modules/next/dist/shared/lib/encode-uri-path.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "encodeURIPath", {
    enumerable: true,
    get: function() {
        return encodeURIPath;
    }
});
function encodeURIPath(file) {
    return file.split('/').map((p)=>encodeURIComponent(p)).join('/');
} //# sourceMappingURL=encode-uri-path.js.map
}}),
"[project]/node_modules/next/dist/shared/lib/lazy-dynamic/preload-chunks.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
'use client';
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "PreloadChunks", {
    enumerable: true,
    get: function() {
        return PreloadChunks;
    }
});
const _jsxruntime = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
const _reactdom = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react-dom/index.js [app-client] (ecmascript)");
const _workasyncstorageexternal = __turbopack_context__.r("[project]/node_modules/next/dist/server/app-render/work-async-storage.external.js [app-client] (ecmascript)");
const _encodeuripath = __turbopack_context__.r("[project]/node_modules/next/dist/shared/lib/encode-uri-path.js [app-client] (ecmascript)");
function PreloadChunks(param) {
    let { moduleIds } = param;
    // Early return in client compilation and only load requestStore on server side
    if (typeof window !== 'undefined') {
        return null;
    }
    const workStore = _workasyncstorageexternal.workAsyncStorage.getStore();
    if (workStore === undefined) {
        return null;
    }
    const allFiles = [];
    // Search the current dynamic call unique key id in react loadable manifest,
    // and find the corresponding CSS files to preload
    if (workStore.reactLoadableManifest && moduleIds) {
        const manifest = workStore.reactLoadableManifest;
        for (const key of moduleIds){
            if (!manifest[key]) continue;
            const chunks = manifest[key].files;
            allFiles.push(...chunks);
        }
    }
    if (allFiles.length === 0) {
        return null;
    }
    const dplId = ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : '';
    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_jsxruntime.Fragment, {
        children: allFiles.map((chunk)=>{
            const href = workStore.assetPrefix + "/_next/" + (0, _encodeuripath.encodeURIPath)(chunk) + dplId;
            const isCss = chunk.endsWith('.css');
            // If it's stylesheet we use `precedence` o help hoist with React Float.
            // For stylesheets we actually need to render the CSS because nothing else is going to do it so it needs to be part of the component tree.
            // The `preload` for stylesheet is not optional.
            if (isCss) {
                return /*#__PURE__*/ (0, _jsxruntime.jsx)("link", {
                    // @ts-ignore
                    precedence: "dynamic",
                    href: href,
                    rel: "stylesheet",
                    as: "style"
                }, chunk);
            } else {
                // If it's script we use ReactDOM.preload to preload the resources
                (0, _reactdom.preload)(href, {
                    as: 'script',
                    fetchPriority: 'low'
                });
                return null;
            }
        })
    });
} //# sourceMappingURL=preload-chunks.js.map
}}),
}]);

//# sourceMappingURL=_47aa6425._.js.map