{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Image from 'next/image';\nimport Link from 'next/link';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport {\n  faBars,\n  faSearch,\n  faBell,\n  faUser,\n  faGear,\n  faRightFromBracket,\n  faMoon,\n  faSun\n} from '@fortawesome/free-solid-svg-icons';\n\nexport default function Header() {\n  const [isProfileOpen, setIsProfileOpen] = useState(false);\n\n  return (\n    <header className=\"sticky top-0 z-50 w-full bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-800 shadow-sm\">\n      <div className=\"flex h-16 items-center justify-between px-4 md:px-6\">\n        <div className=\"flex items-center\">\n          {/* Mobile menu button */}\n          <button\n            type=\"button\"\n            className=\"md:hidden p-2 text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300\"\n            onClick={() => {\n              if (typeof window !== 'undefined') {\n                document.documentElement.classList.toggle('sidebar-open');\n              }\n            }}\n          >\n            <FontAwesomeIcon icon={faBars} className=\"h-6 w-6\" />\n            <span className=\"sr-only\">Toggle menu</span>\n          </button>\n\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center ml-4 md:ml-0\">\n            <div className=\"relative h-8 w-8 mr-2\">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                viewBox=\"0 0 24 24\"\n                fill=\"none\"\n                stroke=\"currentColor\"\n                strokeWidth=\"2\"\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                className=\"h-8 w-8 text-blue-600\"\n              >\n                <path d=\"M12 2a4 4 0 0 1 4 4v4a4 4 0 0 1-4 4 4 4 0 0 1-4-4V6a4 4 0 0 1 4-4z\" />\n                <path d=\"M18 8a4 4 0 0 1 4 4v2a4 4 0 0 1-4 4 4 4 0 0 1-4-4v-2a4 4 0 0 1 4-4z\" />\n                <path d=\"M6 8a4 4 0 0 1 4 4v2a4 4 0 0 1-4 4 4 4 0 0 1-4-4v-2a4 4 0 0 1 4-4z\" />\n              </svg>\n            </div>\n            <span className=\"text-xl font-semibold text-gray-900 dark:text-white\">AI Hub</span>\n          </Link>\n        </div>\n\n        <div className=\"flex items-center space-x-4\">\n          {/* Search */}\n          <div className=\"hidden md:flex relative\">\n            <div className=\"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none\">\n              <FontAwesomeIcon icon={faSearch} className=\"h-4 w-4 text-gray-400\" />\n            </div>\n            <input\n              type=\"search\"\n              className=\"block w-full rounded-md border border-gray-200 bg-gray-50 py-2 pl-10 pr-3 text-sm text-gray-900 placeholder:text-gray-500 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white dark:placeholder:text-gray-400\"\n              placeholder=\"Search agents...\"\n            />\n          </div>\n\n          {/* Notifications */}\n          <button\n            type=\"button\"\n            className=\"p-2 text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300\"\n          >\n            <FontAwesomeIcon icon={faBell} className=\"h-6 w-6\" />\n            <span className=\"sr-only\">Notifications</span>\n          </button>\n\n          {/* Profile dropdown */}\n          <div className=\"relative\">\n            <button\n              type=\"button\"\n              className=\"flex items-center rounded-full\"\n              onClick={() => setIsProfileOpen(!isProfileOpen)}\n            >\n              <div className=\"relative h-8 w-8 rounded-full bg-gray-200 overflow-hidden\">\n                <FontAwesomeIcon icon={faUser} className=\"absolute h-8 w-8 text-gray-400\" />\n              </div>\n            </button>\n\n            {isProfileOpen && (\n              <div className=\"absolute right-0 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-gray-800 dark:ring-gray-700\">\n                <Link\n                  href=\"/profile\"\n                  className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700\"\n                  onClick={() => setIsProfileOpen(false)}\n                >\n                  Your Profile\n                </Link>\n                <Link\n                  href=\"/settings\"\n                  className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700\"\n                  onClick={() => setIsProfileOpen(false)}\n                >\n                  Settings\n                </Link>\n                <button\n                  className=\"block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700\"\n                  onClick={() => setIsProfileOpen(false)}\n                >\n                  Sign out\n                </button>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;;;AANA;;;;;AAiBe,SAAS;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BACC,MAAK;4BACL,WAAU;4BACV,SAAS;gCACP,wCAAmC;oCACjC,SAAS,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC;gCAC5C;4BACF;;8CAEA,6LAAC,uKAAA,CAAA,kBAAe;oCAAC,MAAM,2KAAA,CAAA,SAAM;oCAAE,WAAU;;;;;;8CACzC,6LAAC;oCAAK,WAAU;8CAAU;;;;;;;;;;;;sCAI5B,6LAAC,+<PERSON>AA<PERSON>,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,OAAM;wCACN,SAAQ;wCACR,MAAK;wCACL,QAAO;wCACP,aAAY;wCACZ,eAAc;wCACd,gBAAe;wCACf,WAAU;;0DAEV,6LAAC;gDAAK,GAAE;;;;;;0DACR,6LAAC;gDAAK,GAAE;;;;;;0DACR,6LAAC;gDAAK,GAAE;;;;;;;;;;;;;;;;;8CAGZ,6LAAC;oCAAK,WAAU;8CAAsD;;;;;;;;;;;;;;;;;;8BAI1E,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,uKAAA,CAAA,kBAAe;wCAAC,MAAM,2KAAA,CAAA,WAAQ;wCAAE,WAAU;;;;;;;;;;;8CAE7C,6LAAC;oCACC,MAAK;oCACL,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAKhB,6LAAC;4BACC,MAAK;4BACL,WAAU;;8CAEV,6LAAC,uKAAA,CAAA,kBAAe;oCAAC,MAAM,2KAAA,CAAA,SAAM;oCAAE,WAAU;;;;;;8CACzC,6LAAC;oCAAK,WAAU;8CAAU;;;;;;;;;;;;sCAI5B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,iBAAiB,CAAC;8CAEjC,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,uKAAA,CAAA,kBAAe;4CAAC,MAAM,2KAAA,CAAA,SAAM;4CAAE,WAAU;;;;;;;;;;;;;;;;gCAI5C,+BACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,iBAAiB;sDACjC;;;;;;sDAGD,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,iBAAiB;sDACjC;;;;;;sDAGD,6LAAC;4CACC,WAAU;4CACV,SAAS,IAAM,iBAAiB;sDACjC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;GA1GwB;KAAA", "debugId": null}}, {"offset": {"line": 296, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport {\n  faHome,\n  faTableCells,\n  faChartLine,\n  faHeart,\n  faGear,\n  faCircleQuestion,\n  faRightFromBracket,\n  faBars,\n  faChevronRight\n} from '@fortawesome/free-solid-svg-icons';\n\ntype NavItem = {\n  name: string;\n  href: string;\n  icon: React.ReactNode;\n};\n\ntype NavSection = {\n  title: string;\n  items: NavItem[];\n};\n\nexport default function Sidebar() {\n  const pathname = usePathname();\n  const [isCollapsed, setIsCollapsed] = useState(false);\n  const [expandedSections, setExpandedSections] = useState<string[]>(['Discover', 'My Agents']);\n\n  const toggleSection = (section: string) => {\n    setExpandedSections((prev) =>\n      prev.includes(section)\n        ? prev.filter((s) => s !== section)\n        : [...prev, section]\n    );\n  };\n\n  const navigation: NavSection[] = [\n    {\n      title: 'Discover',\n      items: [\n        {\n          name: 'Home',\n          href: '/',\n          icon: <FontAwesomeIcon icon={faHome} className=\"h-5 w-5\" />,\n        },\n        {\n          name: 'Browse Agents',\n          href: '/browse',\n          icon: <FontAwesomeIcon icon={faTableCells} className=\"h-5 w-5\" />,\n        },\n        {\n          name: 'Popular Agents',\n          href: '/popular',\n          icon: <FontAwesomeIcon icon={faChartLine} className=\"h-5 w-5\" />,\n        },\n        {\n          name: 'New Releases',\n          href: '/new-releases',\n          icon: <FontAwesomeIcon icon={faCircleQuestion} className=\"h-5 w-5\" />,\n        },\n      ],\n    },\n    {\n      title: 'My Agents',\n      items: [\n        {\n          name: 'Favorites',\n          href: '/favorites',\n          icon: <FontAwesomeIcon icon={faHeart} className=\"h-5 w-5\" />,\n        },\n        {\n          name: 'Recent Sessions',\n          href: '/recent',\n          icon: <FontAwesomeIcon icon={faChevronRight} className=\"h-5 w-5\" />,\n        },\n        {\n          name: 'Saved Sessions',\n          href: '/saved',\n          icon: <FontAwesomeIcon icon={faRightFromBracket} className=\"h-5 w-5\" />,\n        },\n      ],\n    },\n    {\n      title: 'Admin',\n      items: [\n        {\n          name: 'Analytics',\n          href: '/analytics',\n          icon: <FontAwesomeIcon icon={faChartLine} className=\"h-5 w-5\" />,\n        },\n        {\n          name: 'User Management',\n          href: '/users',\n          icon: <FontAwesomeIcon icon={faGear} className=\"h-5 w-5\" />,\n        },\n        {\n          name: 'Agent Management',\n          href: '/manage-agents',\n          icon: <FontAwesomeIcon icon={faGear} className=\"h-5 w-5\" />,\n        },\n      ],\n    },\n  ];\n\n  // Use useEffect to handle client-side only code\n  const [isMounted, setIsMounted] = useState(false);\n\n  React.useEffect(() => {\n    setIsMounted(true);\n  }, []);\n\n  return (\n    <aside\n      className={`fixed inset-y-0 left-0 z-40 flex flex-col border-r border-gray-200 bg-white transition-all dark:border-gray-800 dark:bg-gray-900 ${\n        isCollapsed ? 'w-16' : 'w-64'\n      } md:translate-x-0 ${\n        isMounted && typeof window !== 'undefined' && document.documentElement.classList.contains('sidebar-open')\n          ? 'translate-x-0'\n          : '-translate-x-full'\n      }`}\n    >\n      <div className=\"flex h-16 items-center justify-between border-b border-gray-200 px-4 dark:border-gray-800\">\n        {!isCollapsed && (\n          <span className=\"text-lg font-semibold text-gray-900 dark:text-white\">Navigation</span>\n        )}\n        <button\n          type=\"button\"\n          className={`p-2 text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300 ${\n            isCollapsed ? 'mx-auto' : ''\n          }`}\n          onClick={() => setIsCollapsed(!isCollapsed)}\n        >\n          <FontAwesomeIcon\n            icon={faBars}\n            className=\"h-5 w-5\"\n          />\n          <span className=\"sr-only\">Toggle sidebar</span>\n        </button>\n      </div>\n\n      <div className=\"flex-1 overflow-y-auto p-4\">\n        <nav className=\"space-y-6\">\n          {navigation.map((section) => (\n            <div key={section.title} className=\"space-y-2\">\n              {!isCollapsed && (\n                <button\n                  type=\"button\"\n                  className=\"flex w-full items-center justify-between text-sm font-medium text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200\"\n                  onClick={() => toggleSection(section.title)}\n                >\n                  {section.title}\n                  <FontAwesomeIcon\n                    icon={faChevronRight}\n                    className={`h-4 w-4 transition-transform ${\n                      expandedSections.includes(section.title) ? 'rotate-90' : ''\n                    }`}\n                  />\n                </button>\n              )}\n\n              {(isCollapsed || expandedSections.includes(section.title)) && (\n                <ul className=\"space-y-1\">\n                  {section.items.map((item) => (\n                    <li key={item.name}>\n                      <Link\n                        href={item.href}\n                        className={`flex items-center rounded-md px-3 py-2 text-sm font-medium ${\n                          pathname === item.href\n                            ? 'bg-blue-50 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400'\n                            : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800'\n                        } ${isCollapsed ? 'justify-center' : ''}`}\n                      >\n                        <span className=\"mr-3\">{item.icon}</span>\n                        {!isCollapsed && <span>{item.name}</span>}\n                      </Link>\n                    </li>\n                  ))}\n                </ul>\n              )}\n            </div>\n          ))}\n        </nav>\n      </div>\n    </aside>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AA6Be,SAAS;;IACtB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;QAAC;QAAY;KAAY;IAE5F,MAAM,gBAAgB,CAAC;QACrB,oBAAoB,CAAC,OACnB,KAAK,QAAQ,CAAC,WACV,KAAK,MAAM,CAAC,CAAC,IAAM,MAAM,WACzB;mBAAI;gBAAM;aAAQ;IAE1B;IAEA,MAAM,aAA2B;QAC/B;YACE,OAAO;YACP,OAAO;gBACL;oBACE,MAAM;oBACN,MAAM;oBACN,oBAAM,6LAAC,uKAAA,CAAA,kBAAe;wBAAC,MAAM,2KAAA,CAAA,SAAM;wBAAE,WAAU;;;;;;gBACjD;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,oBAAM,6LAAC,uKAAA,CAAA,kBAAe;wBAAC,MAAM,2KAAA,CAAA,eAAY;wBAAE,WAAU;;;;;;gBACvD;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,oBAAM,6LAAC,uKAAA,CAAA,kBAAe;wBAAC,MAAM,2KAAA,CAAA,cAAW;wBAAE,WAAU;;;;;;gBACtD;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,oBAAM,6LAAC,uKAAA,CAAA,kBAAe;wBAAC,MAAM,2KAAA,CAAA,mBAAgB;wBAAE,WAAU;;;;;;gBAC3D;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,MAAM;oBACN,MAAM;oBACN,oBAAM,6LAAC,uKAAA,CAAA,kBAAe;wBAAC,MAAM,2KAAA,CAAA,UAAO;wBAAE,WAAU;;;;;;gBAClD;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,oBAAM,6LAAC,uKAAA,CAAA,kBAAe;wBAAC,MAAM,2KAAA,CAAA,iBAAc;wBAAE,WAAU;;;;;;gBACzD;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,oBAAM,6LAAC,uKAAA,CAAA,kBAAe;wBAAC,MAAM,2KAAA,CAAA,qBAAkB;wBAAE,WAAU;;;;;;gBAC7D;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,MAAM;oBACN,MAAM;oBACN,oBAAM,6LAAC,uKAAA,CAAA,kBAAe;wBAAC,MAAM,2KAAA,CAAA,cAAW;wBAAE,WAAU;;;;;;gBACtD;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,oBAAM,6LAAC,uKAAA,CAAA,kBAAe;wBAAC,MAAM,2KAAA,CAAA,SAAM;wBAAE,WAAU;;;;;;gBACjD;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,oBAAM,6LAAC,uKAAA,CAAA,kBAAe;wBAAC,MAAM,2KAAA,CAAA,SAAM;wBAAE,WAAU;;;;;;gBACjD;aACD;QACH;KACD;IAED,gDAAgD;IAChD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,6JAAA,CAAA,UAAK,CAAC,SAAS;6BAAC;YACd,aAAa;QACf;4BAAG,EAAE;IAEL,qBACE,6LAAC;QACC,WAAW,CAAC,iIAAiI,EAC3I,cAAc,SAAS,OACxB,kBAAkB,EACjB,aAAa,aAAkB,eAAe,SAAS,eAAe,CAAC,SAAS,CAAC,QAAQ,CAAC,kBACtF,kBACA,qBACJ;;0BAEF,6LAAC;gBAAI,WAAU;;oBACZ,CAAC,6BACA,6LAAC;wBAAK,WAAU;kCAAsD;;;;;;kCAExE,6LAAC;wBACC,MAAK;wBACL,WAAW,CAAC,kFAAkF,EAC5F,cAAc,YAAY,IAC1B;wBACF,SAAS,IAAM,eAAe,CAAC;;0CAE/B,6LAAC,uKAAA,CAAA,kBAAe;gCACd,MAAM,2KAAA,CAAA,SAAM;gCACZ,WAAU;;;;;;0CAEZ,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;0BAI9B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACZ,WAAW,GAAG,CAAC,CAAC,wBACf,6LAAC;4BAAwB,WAAU;;gCAChC,CAAC,6BACA,6LAAC;oCACC,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,cAAc,QAAQ,KAAK;;wCAEzC,QAAQ,KAAK;sDACd,6LAAC,uKAAA,CAAA,kBAAe;4CACd,MAAM,2KAAA,CAAA,iBAAc;4CACpB,WAAW,CAAC,6BAA6B,EACvC,iBAAiB,QAAQ,CAAC,QAAQ,KAAK,IAAI,cAAc,IACzD;;;;;;;;;;;;gCAKP,CAAC,eAAe,iBAAiB,QAAQ,CAAC,QAAQ,KAAK,CAAC,mBACvD,6LAAC;oCAAG,WAAU;8CACX,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,qBAClB,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAW,CAAC,2DAA2D,EACrE,aAAa,KAAK,IAAI,GAClB,oEACA,4EACL,CAAC,EAAE,cAAc,mBAAmB,IAAI;;kEAEzC,6LAAC;wDAAK,WAAU;kEAAQ,KAAK,IAAI;;;;;;oDAChC,CAAC,6BAAe,6LAAC;kEAAM,KAAK,IAAI;;;;;;;;;;;;2CAV5B,KAAK,IAAI;;;;;;;;;;;2BApBhB,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;AA0CnC;GAlKwB;;QACL,qIAAA,CAAA,cAAW;;;KADN", "debugId": null}}, {"offset": {"line": 623, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/components/layout/Layout.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect } from 'react';\nimport Header from './Header';\nimport Sidebar from './Sidebar';\n\nexport default function Layout({ children }: { children: React.ReactNode }) {\n  // Close sidebar when clicking outside on mobile\n  useEffect(() => {\n    if (typeof window === 'undefined') return;\n\n    const handleClickOutside = (event: MouseEvent) => {\n      const sidebar = document.querySelector('aside');\n      const menuButton = document.querySelector('button[aria-label=\"Toggle menu\"]');\n\n      if (\n        sidebar &&\n        !sidebar.contains(event.target as Node) &&\n        menuButton &&\n        !menuButton.contains(event.target as Node) &&\n        document.documentElement.classList.contains('sidebar-open')\n      ) {\n        document.documentElement.classList.remove('sidebar-open');\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, []);\n\n  return (\n    <div className=\"flex min-h-screen flex-col\">\n      <Header />\n      <div className=\"flex flex-1\">\n        <Sidebar />\n        <main className=\"flex-1 transition-all duration-200 md:pl-64\">\n          <div className=\"container mx-auto px-4 py-8 md:px-6 lg:px-8\">\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS,OAAO,EAAE,QAAQ,EAAiC;;IACxE,gDAAgD;IAChD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,uCAAmC;;YAAM;YAEzC,MAAM;uDAAqB,CAAC;oBAC1B,MAAM,UAAU,SAAS,aAAa,CAAC;oBACvC,MAAM,aAAa,SAAS,aAAa,CAAC;oBAE1C,IACE,WACA,CAAC,QAAQ,QAAQ,CAAC,MAAM,MAAM,KAC9B,cACA,CAAC,WAAW,QAAQ,CAAC,MAAM,MAAM,KACjC,SAAS,eAAe,CAAC,SAAS,CAAC,QAAQ,CAAC,iBAC5C;wBACA,SAAS,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC;oBAC5C;gBACF;;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;oCAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;2BAAG,EAAE;IAEL,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,yIAAA,CAAA,UAAM;;;;;0BACP,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,0IAAA,CAAA,UAAO;;;;;kCACR,6LAAC;wBAAK,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMb;GAvCwB;KAAA", "debugId": null}}]}