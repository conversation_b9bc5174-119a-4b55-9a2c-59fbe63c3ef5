{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/lib/agents.ts"], "sourcesContent": ["import { Agent, Message, Session } from '@/types/agent';\n\n// Mock data for agents\nconst agents: Agent[] = [\n  {\n    id: '1',\n    name: 'Data Analyst',\n    description: 'Analyze data sets and generate insights with natural language queries.',\n    longDescription: 'The Data Analyst agent helps you explore and understand your data through natural language. Ask questions about your data, request visualizations, or get statistical summaries without writing complex queries or code. This agent can work with various data formats including CSV, Excel, SQL databases, and more.',\n    category: 'Analytics',\n    capabilities: [\n      'Natural language data queries',\n      'Data visualization generation',\n      'Statistical analysis',\n      'Anomaly detection',\n      'Trend identification',\n      'Report generation'\n    ],\n    usageCount: 1245,\n    isNew: false,\n    isFeatured: true,\n    createdAt: '2023-10-15',\n    updatedAt: '2024-03-01',\n    version: '2.3.0',\n    creator: 'Data Science Team',\n    avatarUrl: '/agents/data-analyst.svg',\n    tags: ['data', 'analytics', 'visualization', 'statistics'],\n    relatedAgentIds: ['5', '7', '8']\n  },\n  {\n    id: '2',\n    name: 'Code Assistant',\n    description: 'Help with coding tasks, debugging, and code reviews across multiple languages.',\n    longDescription: 'The Code Assistant agent is your programming partner. It can help write, debug, and optimize code in multiple languages including Python, JavaScript, Java, C++, and more. Ask for code examples, get help with debugging, or request code reviews. The agent can also explain complex code and suggest improvements.',\n    category: 'Development',\n    capabilities: [\n      'Code generation',\n      'Debugging assistance',\n      'Code optimization',\n      'Code explanation',\n      'Multiple language support',\n      'Best practices recommendations'\n    ],\n    usageCount: 3421,\n    isNew: false,\n    isFeatured: true,\n    createdAt: '2023-08-10',\n    updatedAt: '2024-02-15',\n    version: '3.1.0',\n    creator: 'Engineering Team',\n    avatarUrl: '/agents/code-assistant.svg',\n    tags: ['coding', 'programming', 'development', 'debugging'],\n    relatedAgentIds: ['9', '10']\n  },\n  {\n    id: '3',\n    name: 'Research Companion',\n    description: 'Find, summarize, and organize research papers and articles.',\n    longDescription: 'The Research Companion agent helps you stay on top of the latest research in your field. It can find relevant papers, summarize key findings, and help you organize your research materials. Ask for papers on specific topics, get summaries of complex articles, or request literature reviews.',\n    category: 'Research',\n    capabilities: [\n      'Research paper search',\n      'Article summarization',\n      'Literature review assistance',\n      'Citation generation',\n      'Research organization',\n      'Key findings extraction'\n    ],\n    usageCount: 876,\n    isNew: true,\n    isFeatured: true,\n    createdAt: '2024-01-20',\n    updatedAt: '2024-03-10',\n    version: '1.2.0',\n    creator: 'Research & Development',\n    avatarUrl: '/agents/research-companion.svg',\n    tags: ['research', 'papers', 'academic', 'literature'],\n    relatedAgentIds: ['11', '5']\n  },\n  {\n    id: '4',\n    name: 'Meeting Summarizer',\n    description: 'Generate concise summaries and action items from meeting transcripts.',\n    longDescription: 'The Meeting Summarizer agent helps you capture the important points from your meetings. Upload a transcript or recording, and the agent will identify key discussion points, decisions made, and action items assigned. It can also generate meeting minutes in various formats.',\n    category: 'Productivity',\n    capabilities: [\n      'Meeting transcript analysis',\n      'Key point extraction',\n      'Action item identification',\n      'Decision tracking',\n      'Meeting minutes generation',\n      'Follow-up reminder creation'\n    ],\n    usageCount: 2134,\n    isNew: false,\n    isFeatured: true,\n    createdAt: '2023-11-05',\n    updatedAt: '2024-02-28',\n    version: '2.0.1',\n    creator: 'Productivity Team',\n    avatarUrl: '/agents/meeting-summarizer.svg',\n    tags: ['meetings', 'productivity', 'transcription', 'summaries'],\n    relatedAgentIds: ['5', '6', '12']\n  },\n  {\n    id: '5',\n    name: 'Document Analyzer',\n    description: 'Extract key information from documents and generate summaries.',\n    longDescription: 'The Document Analyzer agent helps you process and understand documents quickly. Upload any document (PDF, Word, etc.), and the agent will extract key information, generate summaries, and answer questions about the content. It can handle contracts, reports, articles, and more.',\n    category: 'Productivity',\n    capabilities: [\n      'Document parsing',\n      'Key information extraction',\n      'Summary generation',\n      'Question answering',\n      'Multiple format support',\n      'Entity recognition'\n    ],\n    usageCount: 567,\n    isNew: false,\n    isFeatured: false,\n    createdAt: '2023-09-12',\n    updatedAt: '2024-01-30',\n    version: '1.5.2',\n    creator: 'Content Team',\n    avatarUrl: '/agents/document-analyzer.svg',\n    tags: ['documents', 'analysis', 'extraction', 'summaries'],\n    relatedAgentIds: ['1', '3', '4']\n  },\n  {\n    id: '6',\n    name: 'Presentation Creator',\n    description: 'Generate professional presentations from outlines or topics.',\n    longDescription: 'The Presentation Creator agent helps you build compelling presentations quickly. Provide a topic or outline, and the agent will generate slide content, suggest visuals, and help you structure your presentation for maximum impact. It can create presentations for various purposes including business, education, and sales.',\n    category: 'Productivity',\n    capabilities: [\n      'Slide content generation',\n      'Presentation structure suggestions',\n      'Visual element recommendations',\n      'Talking points creation',\n      'Multiple template support',\n      'Export to PowerPoint/Google Slides'\n    ],\n    usageCount: 321,\n    isNew: true,\n    isFeatured: false,\n    createdAt: '2024-02-01',\n    updatedAt: '2024-03-15',\n    version: '1.0.0',\n    creator: 'Marketing Team',\n    avatarUrl: '/agents/presentation-creator.svg',\n    tags: ['presentations', 'slides', 'design', 'content'],\n    relatedAgentIds: ['4', '13']\n  },\n  {\n    id: '7',\n    name: 'Data Visualization Expert',\n    description: 'Create beautiful and informative data visualizations from your datasets.',\n    longDescription: 'The Data Visualization Expert agent helps you transform raw data into compelling visual stories. Upload your data and describe what you want to highlight, and the agent will generate appropriate charts, graphs, and interactive visualizations that make your data insights clear and impactful.',\n    category: 'Analytics',\n    capabilities: [\n      'Chart and graph generation',\n      'Interactive visualization creation',\n      'Color palette optimization',\n      'Data storytelling assistance',\n      'Multiple export formats',\n      'Accessibility considerations'\n    ],\n    usageCount: 892,\n    isNew: false,\n    isFeatured: false,\n    createdAt: '2023-11-18',\n    updatedAt: '2024-02-10',\n    version: '1.8.0',\n    creator: 'Data Science Team',\n    avatarUrl: '/agents/data-analyst.svg',\n    tags: ['visualization', 'charts', 'graphs', 'data'],\n    relatedAgentIds: ['1', '8']\n  },\n  {\n    id: '8',\n    name: 'Predictive Analytics Agent',\n    description: 'Forecast trends and make predictions based on historical data.',\n    longDescription: 'The Predictive Analytics Agent uses machine learning algorithms to analyze your historical data and generate forecasts and predictions. It can identify patterns, trends, and anomalies to help you make data-driven decisions about future outcomes.',\n    category: 'Analytics',\n    capabilities: [\n      'Time series forecasting',\n      'Trend analysis',\n      'Anomaly detection',\n      'Predictive modeling',\n      'Scenario planning',\n      'Confidence interval calculation'\n    ],\n    usageCount: 754,\n    isNew: true,\n    isFeatured: false,\n    createdAt: '2024-01-05',\n    updatedAt: '2024-03-20',\n    version: '1.2.1',\n    creator: 'Data Science Team',\n    avatarUrl: '/agents/data-analyst.svg',\n    tags: ['predictions', 'forecasting', 'analytics', 'trends'],\n    relatedAgentIds: ['1', '7']\n  },\n  {\n    id: '9',\n    name: 'API Documentation Generator',\n    description: 'Automatically generate comprehensive API documentation from your code.',\n    longDescription: 'The API Documentation Generator analyzes your API code and generates clear, comprehensive documentation. It can create reference docs, guides, examples, and interactive API explorers to help developers understand and use your APIs effectively.',\n    category: 'Development',\n    capabilities: [\n      'API reference generation',\n      'Code example creation',\n      'Interactive API explorer',\n      'Multiple format support',\n      'Versioning assistance',\n      'Consistency checking'\n    ],\n    usageCount: 623,\n    isNew: false,\n    isFeatured: false,\n    createdAt: '2023-10-08',\n    updatedAt: '2024-02-12',\n    version: '2.1.0',\n    creator: 'Engineering Team',\n    avatarUrl: '/agents/code-assistant.svg',\n    tags: ['documentation', 'API', 'development', 'reference'],\n    relatedAgentIds: ['2', '10']\n  },\n  {\n    id: '10',\n    name: 'Code Reviewer',\n    description: 'Get detailed code reviews with suggestions for improvements and best practices.',\n    longDescription: 'The Code Reviewer agent analyzes your code for bugs, security issues, performance problems, and adherence to best practices. It provides detailed feedback with specific suggestions for improvements and explanations of potential issues.',\n    category: 'Development',\n    capabilities: [\n      'Bug detection',\n      'Security vulnerability scanning',\n      'Performance optimization',\n      'Best practice enforcement',\n      'Code style consistency',\n      'Refactoring suggestions'\n    ],\n    usageCount: 1876,\n    isNew: false,\n    isFeatured: false,\n    createdAt: '2023-09-20',\n    updatedAt: '2024-03-05',\n    version: '2.4.0',\n    creator: 'Engineering Team',\n    avatarUrl: '/agents/code-assistant.svg',\n    tags: ['code review', 'quality', 'security', 'best practices'],\n    relatedAgentIds: ['2', '9']\n  },\n  {\n    id: '11',\n    name: 'Literature Review Assistant',\n    description: 'Compile comprehensive literature reviews on any research topic.',\n    longDescription: 'The Literature Review Assistant helps researchers compile thorough literature reviews by finding relevant papers, organizing them by themes, identifying key findings, and highlighting research gaps. It can save weeks of manual research work.',\n    category: 'Research',\n    capabilities: [\n      'Research paper discovery',\n      'Thematic organization',\n      'Gap analysis',\n      'Citation management',\n      'Summary generation',\n      'Trend identification'\n    ],\n    usageCount: 542,\n    isNew: true,\n    isFeatured: false,\n    createdAt: '2024-02-15',\n    updatedAt: '2024-03-18',\n    version: '1.0.2',\n    creator: 'Research & Development',\n    avatarUrl: '/agents/research-companion.svg',\n    tags: ['literature review', 'research', 'academic', 'papers'],\n    relatedAgentIds: ['3']\n  },\n  {\n    id: '12',\n    name: 'Project Manager Assistant',\n    description: 'Track projects, manage tasks, and coordinate team activities efficiently.',\n    longDescription: 'The Project Manager Assistant helps you plan, track, and manage projects from start to finish. It can create project timelines, assign and track tasks, identify risks, generate status reports, and facilitate team communication to keep your projects on track.',\n    category: 'Productivity',\n    capabilities: [\n      'Project planning',\n      'Task management',\n      'Timeline creation',\n      'Risk assessment',\n      'Status reporting',\n      'Resource allocation'\n    ],\n    usageCount: 1432,\n    isNew: false,\n    isFeatured: false,\n    createdAt: '2023-08-25',\n    updatedAt: '2024-02-20',\n    version: '2.2.0',\n    creator: 'Productivity Team',\n    avatarUrl: '/agents/meeting-summarizer.svg',\n    tags: ['project management', 'tasks', 'planning', 'coordination'],\n    relatedAgentIds: ['4', '13']\n  },\n  {\n    id: '13',\n    name: 'Content Creator',\n    description: 'Generate high-quality content for blogs, social media, and marketing materials.',\n    longDescription: 'The Content Creator agent helps you produce engaging content for various platforms. Provide a topic and target audience, and it will generate blog posts, social media updates, marketing copy, and more, tailored to your brand voice and content strategy.',\n    category: 'Marketing',\n    capabilities: [\n      'Blog post generation',\n      'Social media content creation',\n      'Marketing copy writing',\n      'SEO optimization',\n      'Brand voice consistency',\n      'Content strategy alignment'\n    ],\n    usageCount: 2187,\n    isNew: false,\n    isFeatured: false,\n    createdAt: '2023-07-12',\n    updatedAt: '2024-03-08',\n    version: '3.0.1',\n    creator: 'Marketing Team',\n    avatarUrl: '/agents/presentation-creator.svg',\n    tags: ['content', 'writing', 'marketing', 'social media'],\n    relatedAgentIds: ['6', '12']\n  }\n];\n\n// Mock data for sessions\nconst sessions: Session[] = [\n  {\n    id: 'session1',\n    agentId: '2',\n    title: 'JavaScript Debugging Help',\n    messages: [\n      {\n        id: 'msg1',\n        content: 'Hello! How can I help you with coding today?',\n        role: 'assistant',\n        timestamp: Date.now() - 3600000\n      },\n      {\n        id: 'msg2',\n        content: 'I have a bug in my JavaScript code. The event listener is not working.',\n        role: 'user',\n        timestamp: Date.now() - 3500000\n      },\n      {\n        id: 'msg3',\n        content: 'Let\\'s take a look. Can you share the code that\\'s not working?',\n        role: 'assistant',\n        timestamp: Date.now() - 3400000\n      }\n    ],\n    createdAt: Date.now() - 3600000,\n    updatedAt: Date.now() - 3400000,\n    isSaved: true\n  },\n  {\n    id: 'session2',\n    agentId: '1',\n    title: 'Sales Data Analysis',\n    messages: [\n      {\n        id: 'msg1',\n        content: 'Welcome to Data Analyst. What data would you like to analyze today?',\n        role: 'assistant',\n        timestamp: Date.now() - 86400000\n      },\n      {\n        id: 'msg2',\n        content: 'I need to analyze our Q1 sales data to find trends.',\n        role: 'user',\n        timestamp: Date.now() - 86300000\n      },\n      {\n        id: 'msg3',\n        content: 'I can help with that. Do you have the sales data file you can upload?',\n        role: 'assistant',\n        timestamp: Date.now() - 86200000\n      }\n    ],\n    createdAt: Date.now() - 86400000,\n    updatedAt: Date.now() - 86200000,\n    isSaved: true\n  }\n];\n\n// Function to get all agents\nexport function getAllAgents(): Agent[] {\n  return agents;\n}\n\n// Function to get an agent by ID\nexport function getAgentById(id: string): Agent | undefined {\n  return agents.find(agent => agent.id === id);\n}\n\n// Function to get featured agents\nexport function getFeaturedAgents(): Agent[] {\n  return agents.filter(agent => agent.isFeatured);\n}\n\n// Function to get new agents\nexport function getNewAgents(): Agent[] {\n  return agents.filter(agent => agent.isNew);\n}\n\n// Function to get agents by category\nexport function getAgentsByCategory(category: string): Agent[] {\n  return agents.filter(agent => agent.category === category);\n}\n\n// Function to get agents by tag\nexport function getAgentsByTag(tag: string): Agent[] {\n  return agents.filter(agent => agent.tags?.includes(tag));\n}\n\n// Function to get all unique categories\nexport function getAllCategories(): string[] {\n  const categories = new Set<string>();\n  agents.forEach(agent => categories.add(agent.category));\n  return Array.from(categories).sort();\n}\n\n// Function to get all unique tags\nexport function getAllTags(): string[] {\n  const tags = new Set<string>();\n  agents.forEach(agent => {\n    agent.tags?.forEach(tag => tags.add(tag));\n  });\n  return Array.from(tags).sort();\n}\n\n// Function to get related agents\nexport function getRelatedAgents(agentId: string): Agent[] {\n  const agent = getAgentById(agentId);\n  if (!agent || !agent.relatedAgentIds || agent.relatedAgentIds.length === 0) {\n    return [];\n  }\n\n  return agent.relatedAgentIds\n    .map(id => getAgentById(id))\n    .filter((agent): agent is Agent => agent !== undefined);\n}\n\n// Function to get agents by IDs\nexport function getAgentsByIds(ids: string[]): Agent[] {\n  return ids\n    .map(id => getAgentById(id))\n    .filter((agent): agent is Agent => agent !== undefined);\n}\n\n// Function to search agents\nexport function searchAgents(query: string): Agent[] {\n  if (!query) return agents;\n\n  const lowercaseQuery = query.toLowerCase();\n  return agents.filter(agent =>\n    agent.name.toLowerCase().includes(lowercaseQuery) ||\n    agent.description.toLowerCase().includes(lowercaseQuery) ||\n    agent.category.toLowerCase().includes(lowercaseQuery) ||\n    agent.tags?.some(tag => tag.toLowerCase().includes(lowercaseQuery)) ||\n    agent.capabilities.some(capability => capability.toLowerCase().includes(lowercaseQuery))\n  );\n}\n\n// Function to get sessions for an agent\nexport function getSessionsForAgent(agentId: string): Session[] {\n  return sessions.filter(session => session.agentId === agentId);\n}\n\n// Function to get a session by ID\nexport function getSessionById(sessionId: string): Session | undefined {\n  return sessions.find(session => session.id === sessionId);\n}\n\n// Function to create a new message\nexport function createMessage(content: string, role: 'user' | 'assistant' | 'system'): Message {\n  return {\n    id: `msg_${Date.now()}`,\n    content,\n    role,\n    timestamp: Date.now()\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAEA,uBAAuB;AACvB,MAAM,SAAkB;IACtB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAQ;YAAa;YAAiB;SAAa;QAC1D,iBAAiB;YAAC;YAAK;YAAK;SAAI;IAClC;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAU;YAAe;YAAe;SAAY;QAC3D,iBAAiB;YAAC;YAAK;SAAK;IAC9B;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAY;YAAU;YAAY;SAAa;QACtD,iBAAiB;YAAC;YAAM;SAAI;IAC9B;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAY;YAAgB;YAAiB;SAAY;QAChE,iBAAiB;YAAC;YAAK;YAAK;SAAK;IACnC;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAa;YAAY;YAAc;SAAY;QAC1D,iBAAiB;YAAC;YAAK;YAAK;SAAI;IAClC;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAiB;YAAU;YAAU;SAAU;QACtD,iBAAiB;YAAC;YAAK;SAAK;IAC9B;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAiB;YAAU;YAAU;SAAO;QACnD,iBAAiB;YAAC;YAAK;SAAI;IAC7B;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAe;YAAe;YAAa;SAAS;QAC3D,iBAAiB;YAAC;YAAK;SAAI;IAC7B;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAiB;YAAO;YAAe;SAAY;QAC1D,iBAAiB;YAAC;YAAK;SAAK;IAC9B;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAe;YAAW;YAAY;SAAiB;QAC9D,iBAAiB;YAAC;YAAK;SAAI;IAC7B;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAqB;YAAY;YAAY;SAAS;QAC7D,iBAAiB;YAAC;SAAI;IACxB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAsB;YAAS;YAAY;SAAe;QACjE,iBAAiB;YAAC;YAAK;SAAK;IAC9B;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAW;YAAW;YAAa;SAAe;QACzD,iBAAiB;YAAC;YAAK;SAAK;IAC9B;CACD;AAED,yBAAyB;AACzB,MAAM,WAAsB;IAC1B;QACE,IAAI;QACJ,SAAS;QACT,OAAO;QACP,UAAU;YACR;gBACE,IAAI;gBACJ,SAAS;gBACT,MAAM;gBACN,WAAW,KAAK,GAAG,KAAK;YAC1B;YACA;gBACE,IAAI;gBACJ,SAAS;gBACT,MAAM;gBACN,WAAW,KAAK,GAAG,KAAK;YAC1B;YACA;gBACE,IAAI;gBACJ,SAAS;gBACT,MAAM;gBACN,WAAW,KAAK,GAAG,KAAK;YAC1B;SACD;QACD,WAAW,KAAK,GAAG,KAAK;QACxB,WAAW,KAAK,GAAG,KAAK;QACxB,SAAS;IACX;IACA;QACE,IAAI;QACJ,SAAS;QACT,OAAO;QACP,UAAU;YACR;gBACE,IAAI;gBACJ,SAAS;gBACT,MAAM;gBACN,WAAW,KAAK,GAAG,KAAK;YAC1B;YACA;gBACE,IAAI;gBACJ,SAAS;gBACT,MAAM;gBACN,WAAW,KAAK,GAAG,KAAK;YAC1B;YACA;gBACE,IAAI;gBACJ,SAAS;gBACT,MAAM;gBACN,WAAW,KAAK,GAAG,KAAK;YAC1B;SACD;QACD,WAAW,KAAK,GAAG,KAAK;QACxB,WAAW,KAAK,GAAG,KAAK;QACxB,SAAS;IACX;CACD;AAGM,SAAS;IACd,OAAO;AACT;AAGO,SAAS,aAAa,EAAU;IACrC,OAAO,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;AAC3C;AAGO,SAAS;IACd,OAAO,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,UAAU;AAChD;AAGO,SAAS;IACd,OAAO,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,KAAK;AAC3C;AAGO,SAAS,oBAAoB,QAAgB;IAClD,OAAO,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,QAAQ,KAAK;AACnD;AAGO,SAAS,eAAe,GAAW;IACxC,OAAO,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,IAAI,EAAE,SAAS;AACrD;AAGO,SAAS;IACd,MAAM,aAAa,IAAI;IACvB,OAAO,OAAO,CAAC,CAAA,QAAS,WAAW,GAAG,CAAC,MAAM,QAAQ;IACrD,OAAO,MAAM,IAAI,CAAC,YAAY,IAAI;AACpC;AAGO,SAAS;IACd,MAAM,OAAO,IAAI;IACjB,OAAO,OAAO,CAAC,CAAA;QACb,MAAM,IAAI,EAAE,QAAQ,CAAA,MAAO,KAAK,GAAG,CAAC;IACtC;IACA,OAAO,MAAM,IAAI,CAAC,MAAM,IAAI;AAC9B;AAGO,SAAS,iBAAiB,OAAe;IAC9C,MAAM,QAAQ,aAAa;IAC3B,IAAI,CAAC,SAAS,CAAC,MAAM,eAAe,IAAI,MAAM,eAAe,CAAC,MAAM,KAAK,GAAG;QAC1E,OAAO,EAAE;IACX;IAEA,OAAO,MAAM,eAAe,CACzB,GAAG,CAAC,CAAA,KAAM,aAAa,KACvB,MAAM,CAAC,CAAC,QAA0B,UAAU;AACjD;AAGO,SAAS,eAAe,GAAa;IAC1C,OAAO,IACJ,GAAG,CAAC,CAAA,KAAM,aAAa,KACvB,MAAM,CAAC,CAAC,QAA0B,UAAU;AACjD;AAGO,SAAS,aAAa,KAAa;IACxC,IAAI,CAAC,OAAO,OAAO;IAEnB,MAAM,iBAAiB,MAAM,WAAW;IACxC,OAAO,OAAO,MAAM,CAAC,CAAA,QACnB,MAAM,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,mBAClC,MAAM,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,mBACzC,MAAM,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,mBACtC,MAAM,IAAI,EAAE,KAAK,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC,oBACnD,MAAM,YAAY,CAAC,IAAI,CAAC,CAAA,aAAc,WAAW,WAAW,GAAG,QAAQ,CAAC;AAE5E;AAGO,SAAS,oBAAoB,OAAe;IACjD,OAAO,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,OAAO,KAAK;AACxD;AAGO,SAAS,eAAe,SAAiB;IAC9C,OAAO,SAAS,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;AACjD;AAGO,SAAS,cAAc,OAAe,EAAE,IAAqC;IAClF,OAAO;QACL,IAAI,CAAC,IAAI,EAAE,KAAK,GAAG,IAAI;QACvB;QACA;QACA,WAAW,KAAK,GAAG;IACrB;AACF", "debugId": null}}, {"offset": {"line": 584, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/lib/events.ts"], "sourcesContent": ["'use client';\n\n// Custom event types\nexport const EVENTS = {\n  FAVORITES_UPDATED: 'favorites-updated',\n  RECENTLY_VIEWED_UPDATED: 'recently-viewed-updated',\n};\n\n// Function to dispatch a custom event\nexport function dispatchCustomEvent(eventName: string, detail?: any) {\n  if (typeof window === 'undefined') return;\n  \n  const event = new CustomEvent(eventName, { detail });\n  window.dispatchEvent(event);\n}\n\n// Function to add an event listener\nexport function addCustomEventListener(eventName: string, callback: (event: CustomEvent) => void) {\n  if (typeof window === 'undefined') return () => {};\n  \n  const eventListener = (e: Event) => callback(e as CustomEvent);\n  window.addEventListener(eventName, eventListener);\n  \n  // Return a function to remove the event listener\n  return () => {\n    window.removeEventListener(eventName, eventListener);\n  };\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAGO,MAAM,SAAS;IACpB,mBAAmB;IACnB,yBAAyB;AAC3B;AAGO,SAAS,oBAAoB,SAAiB,EAAE,MAAY;IACjE,uCAAmC;;IAAM;IAEzC,MAAM,QAAQ,IAAI,YAAY,WAAW;QAAE;IAAO;IAClD,OAAO,aAAa,CAAC;AACvB;AAGO,SAAS,uBAAuB,SAAiB,EAAE,QAAsC;IAC9F,uCAAmC;;IAAe;IAElD,MAAM,gBAAgB,CAAC,IAAa,SAAS;IAC7C,OAAO,gBAAgB,CAAC,WAAW;IAEnC,iDAAiD;IACjD,OAAO;QACL,OAAO,mBAAmB,CAAC,WAAW;IACxC;AACF", "debugId": null}}, {"offset": {"line": 623, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/lib/userPreferences.ts"], "sourcesContent": ["'use client';\n\nimport { UserPreferences } from '@/types/auth';\nimport { dispatchCustomEvent, EVENTS } from './events';\n\n// Default user preferences\nconst defaultPreferences: UserPreferences = {\n  favoriteAgentIds: [],\n  recentlyViewedAgentIds: [],\n  darkMode: false,\n  defaultView: 'grid',\n  pageSize: 12,\n};\n\n// Get user preferences from localStorage\nexport function getUserPreferences(userId?: string): UserPreferences {\n  if (typeof window === 'undefined') {\n    return defaultPreferences;\n  }\n\n  // If userId is provided, use it to get user-specific preferences\n  const storageKey = userId ? `userPreferences_${userId}` : 'userPreferences';\n  const storedPreferences = localStorage.getItem(storageKey);\n  if (!storedPreferences) {\n    return defaultPreferences;\n  }\n\n  try {\n    return JSON.parse(storedPreferences) as UserPreferences;\n  } catch (error) {\n    console.error('Error parsing user preferences:', error);\n    return defaultPreferences;\n  }\n}\n\n// Save user preferences to localStorage\nexport function saveUserPreferences(preferences: UserPreferences, userId?: string): void {\n  if (typeof window === 'undefined') {\n    return;\n  }\n\n  // If userId is provided, use it to save user-specific preferences\n  const storageKey = userId ? `userPreferences_${userId}` : 'userPreferences';\n  localStorage.setItem(storageKey, JSON.stringify(preferences));\n}\n\n// Add an agent to favorites\nexport function toggleFavoriteAgent(agentId: string, userId?: string): boolean {\n  const preferences = getUserPreferences(userId);\n  const isFavorite = preferences.favoriteAgentIds.includes(agentId);\n\n  if (isFavorite) {\n    preferences.favoriteAgentIds = preferences.favoriteAgentIds.filter(id => id !== agentId);\n  } else {\n    preferences.favoriteAgentIds.push(agentId);\n  }\n\n  saveUserPreferences(preferences, userId);\n\n  // Dispatch event to notify other components\n  dispatchCustomEvent(EVENTS.FAVORITES_UPDATED, {\n    agentId,\n    isFavorite: !isFavorite,\n    userId\n  });\n\n  return !isFavorite; // Return the new favorite status\n}\n\n// Check if an agent is favorited\nexport function isAgentFavorited(agentId: string, userId?: string): boolean {\n  const preferences = getUserPreferences(userId);\n  return preferences.favoriteAgentIds.includes(agentId);\n}\n\n// Add an agent to recently viewed\nexport function addToRecentlyViewed(agentId: string, userId?: string): void {\n  const preferences = getUserPreferences(userId);\n\n  // Remove if already exists to avoid duplicates\n  preferences.recentlyViewedAgentIds = preferences.recentlyViewedAgentIds.filter(id => id !== agentId);\n\n  // Add to the beginning of the array\n  preferences.recentlyViewedAgentIds.unshift(agentId);\n\n  // Keep only the 10 most recent\n  if (preferences.recentlyViewedAgentIds.length > 10) {\n    preferences.recentlyViewedAgentIds = preferences.recentlyViewedAgentIds.slice(0, 10);\n  }\n\n  saveUserPreferences(preferences, userId);\n\n  // Dispatch event to notify other components\n  dispatchCustomEvent(EVENTS.RECENTLY_VIEWED_UPDATED, {\n    agentId,\n    recentlyViewedIds: preferences.recentlyViewedAgentIds,\n    userId\n  });\n}\n\n// Get recently viewed agents\nexport function getRecentlyViewedAgentIds(userId?: string): string[] {\n  const preferences = getUserPreferences(userId);\n  return preferences.recentlyViewedAgentIds;\n}\n\n// Update page size preference\nexport function updatePageSize(pageSize: number, userId?: string): void {\n  const preferences = getUserPreferences(userId);\n  preferences.pageSize = pageSize;\n  saveUserPreferences(preferences, userId);\n}\n\n// Get page size preference\nexport function getPageSize(userId?: string): number {\n  const preferences = getUserPreferences(userId);\n  return preferences.pageSize;\n}\n\n// Update default view preference\nexport function updateDefaultView(view: 'grid' | 'list', userId?: string): void {\n  const preferences = getUserPreferences(userId);\n  preferences.defaultView = view;\n  saveUserPreferences(preferences, userId);\n}\n\n// Get default view preference\nexport function getDefaultView(userId?: string): 'grid' | 'list' {\n  const preferences = getUserPreferences(userId);\n  return preferences.defaultView;\n}\n\n// Toggle dark mode\nexport function toggleDarkMode(userId?: string): boolean {\n  const preferences = getUserPreferences(userId);\n  preferences.darkMode = !preferences.darkMode;\n  saveUserPreferences(preferences, userId);\n  return preferences.darkMode;\n}\n\n// Get dark mode preference\nexport function getDarkMode(userId?: string): boolean {\n  const preferences = getUserPreferences(userId);\n  return preferences.darkMode;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAGA;AAHA;;AAKA,2BAA2B;AAC3B,MAAM,qBAAsC;IAC1C,kBAAkB,EAAE;IACpB,wBAAwB,EAAE;IAC1B,UAAU;IACV,aAAa;IACb,UAAU;AACZ;AAGO,SAAS,mBAAmB,MAAe;IAChD,uCAAmC;;IAEnC;IAEA,iEAAiE;IACjE,MAAM,aAAa,SAAS,CAAC,gBAAgB,EAAE,QAAQ,GAAG;IAC1D,MAAM,oBAAoB,aAAa,OAAO,CAAC;IAC/C,IAAI,CAAC,mBAAmB;QACtB,OAAO;IACT;IAEA,IAAI;QACF,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO;IACT;AACF;AAGO,SAAS,oBAAoB,WAA4B,EAAE,MAAe;IAC/E,uCAAmC;;IAEnC;IAEA,kEAAkE;IAClE,MAAM,aAAa,SAAS,CAAC,gBAAgB,EAAE,QAAQ,GAAG;IAC1D,aAAa,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC;AAClD;AAGO,SAAS,oBAAoB,OAAe,EAAE,MAAe;IAClE,MAAM,cAAc,mBAAmB;IACvC,MAAM,aAAa,YAAY,gBAAgB,CAAC,QAAQ,CAAC;IAEzD,IAAI,YAAY;QACd,YAAY,gBAAgB,GAAG,YAAY,gBAAgB,CAAC,MAAM,CAAC,CAAA,KAAM,OAAO;IAClF,OAAO;QACL,YAAY,gBAAgB,CAAC,IAAI,CAAC;IACpC;IAEA,oBAAoB,aAAa;IAEjC,4CAA4C;IAC5C,CAAA,GAAA,uHAAA,CAAA,sBAAmB,AAAD,EAAE,uHAAA,CAAA,SAAM,CAAC,iBAAiB,EAAE;QAC5C;QACA,YAAY,CAAC;QACb;IACF;IAEA,OAAO,CAAC,YAAY,iCAAiC;AACvD;AAGO,SAAS,iBAAiB,OAAe,EAAE,MAAe;IAC/D,MAAM,cAAc,mBAAmB;IACvC,OAAO,YAAY,gBAAgB,CAAC,QAAQ,CAAC;AAC/C;AAGO,SAAS,oBAAoB,OAAe,EAAE,MAAe;IAClE,MAAM,cAAc,mBAAmB;IAEvC,+CAA+C;IAC/C,YAAY,sBAAsB,GAAG,YAAY,sBAAsB,CAAC,MAAM,CAAC,CAAA,KAAM,OAAO;IAE5F,oCAAoC;IACpC,YAAY,sBAAsB,CAAC,OAAO,CAAC;IAE3C,+BAA+B;IAC/B,IAAI,YAAY,sBAAsB,CAAC,MAAM,GAAG,IAAI;QAClD,YAAY,sBAAsB,GAAG,YAAY,sBAAsB,CAAC,KAAK,CAAC,GAAG;IACnF;IAEA,oBAAoB,aAAa;IAEjC,4CAA4C;IAC5C,CAAA,GAAA,uHAAA,CAAA,sBAAmB,AAAD,EAAE,uHAAA,CAAA,SAAM,CAAC,uBAAuB,EAAE;QAClD;QACA,mBAAmB,YAAY,sBAAsB;QACrD;IACF;AACF;AAGO,SAAS,0BAA0B,MAAe;IACvD,MAAM,cAAc,mBAAmB;IACvC,OAAO,YAAY,sBAAsB;AAC3C;AAGO,SAAS,eAAe,QAAgB,EAAE,MAAe;IAC9D,MAAM,cAAc,mBAAmB;IACvC,YAAY,QAAQ,GAAG;IACvB,oBAAoB,aAAa;AACnC;AAGO,SAAS,YAAY,MAAe;IACzC,MAAM,cAAc,mBAAmB;IACvC,OAAO,YAAY,QAAQ;AAC7B;AAGO,SAAS,kBAAkB,IAAqB,EAAE,MAAe;IACtE,MAAM,cAAc,mBAAmB;IACvC,YAAY,WAAW,GAAG;IAC1B,oBAAoB,aAAa;AACnC;AAGO,SAAS,eAAe,MAAe;IAC5C,MAAM,cAAc,mBAAmB;IACvC,OAAO,YAAY,WAAW;AAChC;AAGO,SAAS,eAAe,MAAe;IAC5C,MAAM,cAAc,mBAAmB;IACvC,YAAY,QAAQ,GAAG,CAAC,YAAY,QAAQ;IAC5C,oBAAoB,aAAa;IACjC,OAAO,YAAY,QAAQ;AAC7B;AAGO,SAAS,YAAY,MAAe;IACzC,MAAM,cAAc,mBAAmB;IACvC,OAAO,YAAY,QAAQ;AAC7B", "debugId": null}}, {"offset": {"line": 753, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/app/browse/components/AgentGrid.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { Agent } from '@/types/agent';\nimport { isAgentFavorited, toggleFavoriteAgent, addToRecentlyViewed } from '@/lib/userPreferences';\nimport { addCustomEventListener, EVENTS } from '@/lib/events';\n\ninterface AgentGridProps {\n  agents: Agent[];\n}\n\nexport default function AgentGrid({ agents }: AgentGridProps) {\n  const [favoriteAgents, setFavoriteAgents] = useState<Record<string, boolean>>({});\n\n  // Load favorite status for each agent\n  useEffect(() => {\n    if (typeof window === 'undefined') return;\n\n    const loadFavorites = () => {\n      const favorites: Record<string, boolean> = {};\n      agents.forEach(agent => {\n        favorites[agent.id] = isAgentFavorited(agent.id);\n      });\n      setFavoriteAgents(favorites);\n    };\n\n    loadFavorites();\n\n    // Listen for favorites updates\n    const removeListener = addCustomEventListener(EVENTS.FAVORITES_UPDATED, (event) => {\n      const { agentId, isFavorite } = event.detail;\n      setFavoriteAgents(prev => ({\n        ...prev,\n        [agentId]: isFavorite\n      }));\n    });\n\n    return () => {\n      removeListener();\n    };\n  }, [agents]);\n\n  // Handle favorite toggle\n  const handleFavoriteToggle = (agentId: string, e: React.MouseEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n\n    const newStatus = toggleFavoriteAgent(agentId);\n    setFavoriteAgents(prev => ({\n      ...prev,\n      [agentId]: newStatus\n    }));\n  };\n\n  // Handle agent click to track recently viewed\n  const handleAgentClick = (agentId: string) => {\n    addToRecentlyViewed(agentId);\n  };\n  if (agents.length === 0) {\n    return (\n      <div className=\"flex h-64 flex-col items-center justify-center rounded-lg border border-gray-200 bg-white p-6 text-center dark:border-gray-800 dark:bg-gray-900\">\n        <svg\n          xmlns=\"http://www.w3.org/2000/svg\"\n          width=\"24\"\n          height=\"24\"\n          viewBox=\"0 0 24 24\"\n          fill=\"none\"\n          stroke=\"currentColor\"\n          strokeWidth=\"2\"\n          strokeLinecap=\"round\"\n          strokeLinejoin=\"round\"\n          className=\"mb-4 h-12 w-12 text-gray-400\"\n        >\n          <circle cx=\"12\" cy=\"12\" r=\"10\" />\n          <line x1=\"12\" x2=\"12\" y1=\"8\" y2=\"12\" />\n          <line x1=\"12\" x2=\"12.01\" y1=\"16\" y2=\"16\" />\n        </svg>\n        <h3 className=\"mb-2 text-lg font-semibold text-gray-900 dark:text-white\">No agents found</h3>\n        <p className=\"text-gray-600 dark:text-gray-400\">\n          Try adjusting your search or filter criteria to find what you're looking for.\n        </p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4\">\n      {agents.map((agent) => (\n        <div\n          key={agent.id}\n          className=\"flex flex-col rounded-lg border border-gray-200 bg-white p-6 shadow-sm transition-shadow hover:shadow-md dark:border-gray-800 dark:bg-gray-900 h-full min-h-[280px]\"\n        >\n          <div className=\"mb-4 flex items-center justify-between\">\n            <span className=\"rounded-full bg-gray-100 px-3 py-1 text-xs font-medium text-gray-800 dark:bg-gray-800 dark:text-gray-200\">\n              {agent.category}\n            </span>\n            <div className=\"flex items-center space-x-2\">\n              {agent.isNew && (\n                <span className=\"rounded-full bg-green-100 px-3 py-1 text-xs font-medium text-green-800 dark:bg-green-900/30 dark:text-green-400\">\n                  New\n                </span>\n              )}\n              <button\n                onClick={(e) => handleFavoriteToggle(agent.id, e)}\n                className=\"rounded-full p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-600 dark:hover:bg-gray-800 dark:hover:text-gray-300\"\n                aria-label={favoriteAgents[agent.id] ? 'Remove from favorites' : 'Add to favorites'}\n              >\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  width=\"24\"\n                  height=\"24\"\n                  viewBox=\"0 0 24 24\"\n                  fill={favoriteAgents[agent.id] ? 'currentColor' : 'none'}\n                  stroke=\"currentColor\"\n                  strokeWidth=\"2\"\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  className={`h-5 w-5 ${favoriteAgents[agent.id] ? 'text-yellow-400' : ''}`}\n                >\n                  <path d=\"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z\" />\n                </svg>\n              </button>\n            </div>\n          </div>\n\n          <div className=\"h-[60px] mb-4\">\n            <div className=\"flex items-start\">\n              {agent.avatarUrl && (\n                <div className=\"mr-3 flex-shrink-0 h-10 w-10 min-w-[40px] overflow-hidden rounded-full bg-gray-100 dark:bg-gray-800\">\n                  <Image\n                    src={agent.avatarUrl}\n                    alt={`${agent.name} icon`}\n                    width={40}\n                    height={40}\n                    className=\"h-full w-full object-cover\"\n                  />\n                </div>\n              )}\n              <h3 className=\"text-xl font-bold text-gray-900 dark:text-white self-start pt-1 line-clamp-2\">{agent.name}</h3>\n            </div>\n          </div>\n\n          <div className=\"h-[72px] mb-4\">\n            <p className=\"text-sm text-gray-600 dark:text-gray-400 line-clamp-3\">{agent.description}</p>\n          </div>\n\n          <div className=\"mt-auto pt-4 flex items-center justify-between\">\n            <div className=\"flex flex-col\">\n              <span className=\"text-xs text-gray-500 dark:text-gray-500\">\n                {agent.usageCount.toLocaleString()} uses\n              </span>\n              <span className=\"text-xs text-gray-500 dark:text-gray-500\">\n                Updated {new Date(agent.updatedAt).toLocaleDateString()}\n              </span>\n            </div>\n            <Link\n              href={`/agent/${agent.id}`}\n              className=\"rounded-md bg-blue-600 px-3 py-1.5 text-sm font-medium text-white hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800\"\n              onClick={() => handleAgentClick(agent.id)}\n            >\n              View Agent\n            </Link>\n          </div>\n        </div>\n      ))}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;;;AAPA;;;;;;AAae,SAAS,UAAU,EAAE,MAAM,EAAkB;;IAC1D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B,CAAC;IAE/E,sCAAsC;IACtC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,uCAAmC;;YAAM;YAEzC,MAAM;qDAAgB;oBACpB,MAAM,YAAqC,CAAC;oBAC5C,OAAO,OAAO;6DAAC,CAAA;4BACb,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,CAAA,GAAA,gIAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM,EAAE;wBACjD;;oBACA,kBAAkB;gBACpB;;YAEA;YAEA,+BAA+B;YAC/B,MAAM,iBAAiB,CAAA,GAAA,uHAAA,CAAA,yBAAsB,AAAD,EAAE,uHAAA,CAAA,SAAM,CAAC,iBAAiB;sDAAE,CAAC;oBACvE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,MAAM,MAAM;oBAC5C;8DAAkB,CAAA,OAAQ,CAAC;gCACzB,GAAG,IAAI;gCACP,CAAC,QAAQ,EAAE;4BACb,CAAC;;gBACH;;YAEA;uCAAO;oBACL;gBACF;;QACF;8BAAG;QAAC;KAAO;IAEX,yBAAyB;IACzB,MAAM,uBAAuB,CAAC,SAAiB;QAC7C,EAAE,cAAc;QAChB,EAAE,eAAe;QAEjB,MAAM,YAAY,CAAA,GAAA,gIAAA,CAAA,sBAAmB,AAAD,EAAE;QACtC,kBAAkB,CAAA,OAAQ,CAAC;gBACzB,GAAG,IAAI;gBACP,CAAC,QAAQ,EAAE;YACb,CAAC;IACH;IAEA,8CAA8C;IAC9C,MAAM,mBAAmB,CAAC;QACxB,CAAA,GAAA,gIAAA,CAAA,sBAAmB,AAAD,EAAE;IACtB;IACA,IAAI,OAAO,MAAM,KAAK,GAAG;QACvB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBACC,OAAM;oBACN,OAAM;oBACN,QAAO;oBACP,SAAQ;oBACR,MAAK;oBACL,QAAO;oBACP,aAAY;oBACZ,eAAc;oBACd,gBAAe;oBACf,WAAU;;sCAEV,6LAAC;4BAAO,IAAG;4BAAK,IAAG;4BAAK,GAAE;;;;;;sCAC1B,6LAAC;4BAAK,IAAG;4BAAK,IAAG;4BAAK,IAAG;4BAAI,IAAG;;;;;;sCAChC,6LAAC;4BAAK,IAAG;4BAAK,IAAG;4BAAQ,IAAG;4BAAK,IAAG;;;;;;;;;;;;8BAEtC,6LAAC;oBAAG,WAAU;8BAA2D;;;;;;8BACzE,6LAAC;oBAAE,WAAU;8BAAmC;;;;;;;;;;;;IAKtD;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACZ,OAAO,GAAG,CAAC,CAAC,sBACX,6LAAC;gBAEC,WAAU;;kCAEV,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CACb,MAAM,QAAQ;;;;;;0CAEjB,6LAAC;gCAAI,WAAU;;oCACZ,MAAM,KAAK,kBACV,6LAAC;wCAAK,WAAU;kDAAkH;;;;;;kDAIpI,6LAAC;wCACC,SAAS,CAAC,IAAM,qBAAqB,MAAM,EAAE,EAAE;wCAC/C,WAAU;wCACV,cAAY,cAAc,CAAC,MAAM,EAAE,CAAC,GAAG,0BAA0B;kDAEjE,cAAA,6LAAC;4CACC,OAAM;4CACN,OAAM;4CACN,QAAO;4CACP,SAAQ;4CACR,MAAM,cAAc,CAAC,MAAM,EAAE,CAAC,GAAG,iBAAiB;4CAClD,QAAO;4CACP,aAAY;4CACZ,eAAc;4CACd,gBAAe;4CACf,WAAW,CAAC,QAAQ,EAAE,cAAc,CAAC,MAAM,EAAE,CAAC,GAAG,oBAAoB,IAAI;sDAEzE,cAAA,6LAAC;gDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMhB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;gCACZ,MAAM,SAAS,kBACd,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;wCACJ,KAAK,MAAM,SAAS;wCACpB,KAAK,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC;wCACzB,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;;;;;;8CAIhB,6LAAC;oCAAG,WAAU;8CAAgF,MAAM,IAAI;;;;;;;;;;;;;;;;;kCAI5G,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;sCAAyD,MAAM,WAAW;;;;;;;;;;;kCAGzF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;;4CACb,MAAM,UAAU,CAAC,cAAc;4CAAG;;;;;;;kDAErC,6LAAC;wCAAK,WAAU;;4CAA2C;4CAChD,IAAI,KAAK,MAAM,SAAS,EAAE,kBAAkB;;;;;;;;;;;;;0CAGzD,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAM,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gCAC1B,WAAU;gCACV,SAAS,IAAM,iBAAiB,MAAM,EAAE;0CACzC;;;;;;;;;;;;;eAtEE,MAAM,EAAE;;;;;;;;;;AA8EvB;GA5JwB;KAAA", "debugId": null}}, {"offset": {"line": 1103, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/app/browse/components/AgentList.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { Agent } from '@/types/agent';\nimport { isAgentFavorited, toggleFavoriteAgent, addToRecentlyViewed } from '@/lib/userPreferences';\nimport { addCustomEventListener, EVENTS } from '@/lib/events';\n\ninterface AgentListProps {\n  agents: Agent[];\n}\n\nexport default function AgentList({ agents }: AgentListProps) {\n  const [favoriteAgents, setFavoriteAgents] = useState<Record<string, boolean>>({});\n\n  // Load favorite status for each agent\n  useEffect(() => {\n    if (typeof window === 'undefined') return;\n\n    const loadFavorites = () => {\n      const favorites: Record<string, boolean> = {};\n      agents.forEach(agent => {\n        favorites[agent.id] = isAgentFavorited(agent.id);\n      });\n      setFavoriteAgents(favorites);\n    };\n\n    loadFavorites();\n\n    // Listen for favorites updates\n    const removeListener = addCustomEventListener(EVENTS.FAVORITES_UPDATED, (event) => {\n      const { agentId, isFavorite } = event.detail;\n      setFavoriteAgents(prev => ({\n        ...prev,\n        [agentId]: isFavorite\n      }));\n    });\n\n    return () => {\n      removeListener();\n    };\n  }, [agents]);\n\n  // Handle favorite toggle\n  const handleFavoriteToggle = (agentId: string, e: React.MouseEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n\n    const newStatus = toggleFavoriteAgent(agentId);\n    setFavoriteAgents(prev => ({\n      ...prev,\n      [agentId]: newStatus\n    }));\n  };\n\n  // Handle agent click to track recently viewed\n  const handleAgentClick = (agentId: string) => {\n    addToRecentlyViewed(agentId);\n  };\n  if (agents.length === 0) {\n    return (\n      <div className=\"flex h-64 flex-col items-center justify-center rounded-lg border border-gray-200 bg-white p-6 text-center dark:border-gray-800 dark:bg-gray-900\">\n        <svg\n          xmlns=\"http://www.w3.org/2000/svg\"\n          width=\"24\"\n          height=\"24\"\n          viewBox=\"0 0 24 24\"\n          fill=\"none\"\n          stroke=\"currentColor\"\n          strokeWidth=\"2\"\n          strokeLinecap=\"round\"\n          strokeLinejoin=\"round\"\n          className=\"mb-4 h-12 w-12 text-gray-400\"\n        >\n          <circle cx=\"12\" cy=\"12\" r=\"10\" />\n          <line x1=\"12\" x2=\"12\" y1=\"8\" y2=\"12\" />\n          <line x1=\"12\" x2=\"12.01\" y1=\"16\" y2=\"16\" />\n        </svg>\n        <h3 className=\"mb-2 text-lg font-semibold text-gray-900 dark:text-white\">No agents found</h3>\n        <p className=\"text-gray-600 dark:text-gray-400\">\n          Try adjusting your search or filter criteria to find what you're looking for.\n        </p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"overflow-hidden rounded-lg border border-gray-200 dark:border-gray-800\">\n      <table className=\"min-w-full divide-y divide-gray-200 dark:divide-gray-800\">\n        <thead className=\"bg-gray-50 dark:bg-gray-800\">\n          <tr>\n            <th\n              scope=\"col\"\n              className=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400\"\n            >\n              Agent\n            </th>\n            <th\n              scope=\"col\"\n              className=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400\"\n            >\n              Category\n            </th>\n            <th\n              scope=\"col\"\n              className=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400\"\n            >\n              Usage\n            </th>\n            <th\n              scope=\"col\"\n              className=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400\"\n            >\n              Last Updated\n            </th>\n            <th scope=\"col\" className=\"relative px-6 py-3\">\n              <span className=\"sr-only\">View</span>\n            </th>\n          </tr>\n        </thead>\n        <tbody className=\"divide-y divide-gray-200 bg-white dark:divide-gray-800 dark:bg-gray-900\">\n          {agents.map((agent) => (\n            <tr key={agent.id} className=\"hover:bg-gray-50 dark:hover:bg-gray-800\">\n              <td className=\"whitespace-nowrap px-6 py-4\">\n                <div className=\"flex items-start\">\n                  {agent.avatarUrl && (\n                    <div className=\"mr-3 h-10 w-10 min-w-[40px] flex-shrink-0 overflow-hidden rounded-full bg-gray-100 dark:bg-gray-800\">\n                      <Image\n                        src={agent.avatarUrl}\n                        alt={`${agent.name} icon`}\n                        width={40}\n                        height={40}\n                        className=\"h-full w-full object-cover\"\n                      />\n                    </div>\n                  )}\n                  <div className=\"flex-1\">\n                    <div className=\"flex items-center\">\n                      <div className=\"font-medium text-gray-900 dark:text-white self-center\">{agent.name}</div>\n                      <div className=\"ml-2 flex items-center space-x-2\">\n                        {agent.isNew && (\n                          <span className=\"rounded-full bg-green-100 px-2 py-0.5 text-xs font-medium text-green-800 dark:bg-green-900/30 dark:text-green-400\">\n                            New\n                          </span>\n                        )}\n                        <button\n                          onClick={(e) => handleFavoriteToggle(agent.id, e)}\n                          className=\"rounded-full p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-600 dark:hover:bg-gray-800 dark:hover:text-gray-300\"\n                          aria-label={favoriteAgents[agent.id] ? 'Remove from favorites' : 'Add to favorites'}\n                        >\n                          <svg\n                            xmlns=\"http://www.w3.org/2000/svg\"\n                            width=\"24\"\n                            height=\"24\"\n                            viewBox=\"0 0 24 24\"\n                            fill={favoriteAgents[agent.id] ? 'currentColor' : 'none'}\n                            stroke=\"currentColor\"\n                            strokeWidth=\"2\"\n                            strokeLinecap=\"round\"\n                            strokeLinejoin=\"round\"\n                            className={`h-4 w-4 ${favoriteAgents[agent.id] ? 'text-yellow-400' : ''}`}\n                          >\n                            <path d=\"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z\" />\n                          </svg>\n                        </button>\n                      </div>\n                    </div>\n                    <div className=\"text-sm text-gray-500 dark:text-gray-400 line-clamp-2 max-w-md\">{agent.description}</div>\n                  </div>\n                </div>\n              </td>\n              <td className=\"whitespace-nowrap px-6 py-4\">\n                <span className=\"rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800 dark:bg-gray-800 dark:text-gray-200\">\n                  {agent.category}\n                </span>\n              </td>\n              <td className=\"whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400\">\n                {agent.usageCount.toLocaleString()}\n              </td>\n              <td className=\"whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400\">\n                {new Date(agent.updatedAt).toLocaleDateString()}\n              </td>\n              <td className=\"whitespace-nowrap px-6 py-4 text-right text-sm font-medium\">\n                <Link\n                  href={`/agent/${agent.id}`}\n                  className=\"rounded-md bg-blue-600 px-3 py-1.5 text-sm font-medium text-white hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800\"\n                  onClick={() => handleAgentClick(agent.id)}\n                >\n                  View Agent\n                </Link>\n              </td>\n            </tr>\n          ))}\n        </tbody>\n      </table>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;;;AAPA;;;;;;AAae,SAAS,UAAU,EAAE,MAAM,EAAkB;;IAC1D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B,CAAC;IAE/E,sCAAsC;IACtC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,uCAAmC;;YAAM;YAEzC,MAAM;qDAAgB;oBACpB,MAAM,YAAqC,CAAC;oBAC5C,OAAO,OAAO;6DAAC,CAAA;4BACb,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,CAAA,GAAA,gIAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM,EAAE;wBACjD;;oBACA,kBAAkB;gBACpB;;YAEA;YAEA,+BAA+B;YAC/B,MAAM,iBAAiB,CAAA,GAAA,uHAAA,CAAA,yBAAsB,AAAD,EAAE,uHAAA,CAAA,SAAM,CAAC,iBAAiB;sDAAE,CAAC;oBACvE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,MAAM,MAAM;oBAC5C;8DAAkB,CAAA,OAAQ,CAAC;gCACzB,GAAG,IAAI;gCACP,CAAC,QAAQ,EAAE;4BACb,CAAC;;gBACH;;YAEA;uCAAO;oBACL;gBACF;;QACF;8BAAG;QAAC;KAAO;IAEX,yBAAyB;IACzB,MAAM,uBAAuB,CAAC,SAAiB;QAC7C,EAAE,cAAc;QAChB,EAAE,eAAe;QAEjB,MAAM,YAAY,CAAA,GAAA,gIAAA,CAAA,sBAAmB,AAAD,EAAE;QACtC,kBAAkB,CAAA,OAAQ,CAAC;gBACzB,GAAG,IAAI;gBACP,CAAC,QAAQ,EAAE;YACb,CAAC;IACH;IAEA,8CAA8C;IAC9C,MAAM,mBAAmB,CAAC;QACxB,CAAA,GAAA,gIAAA,CAAA,sBAAmB,AAAD,EAAE;IACtB;IACA,IAAI,OAAO,MAAM,KAAK,GAAG;QACvB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBACC,OAAM;oBACN,OAAM;oBACN,QAAO;oBACP,SAAQ;oBACR,MAAK;oBACL,QAAO;oBACP,aAAY;oBACZ,eAAc;oBACd,gBAAe;oBACf,WAAU;;sCAEV,6LAAC;4BAAO,IAAG;4BAAK,IAAG;4BAAK,GAAE;;;;;;sCAC1B,6LAAC;4BAAK,IAAG;4BAAK,IAAG;4BAAK,IAAG;4BAAI,IAAG;;;;;;sCAChC,6LAAC;4BAAK,IAAG;4BAAK,IAAG;4BAAQ,IAAG;4BAAK,IAAG;;;;;;;;;;;;8BAEtC,6LAAC;oBAAG,WAAU;8BAA2D;;;;;;8BACzE,6LAAC;oBAAE,WAAU;8BAAmC;;;;;;;;;;;;IAKtD;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAM,WAAU;;8BACf,6LAAC;oBAAM,WAAU;8BACf,cAAA,6LAAC;;0CACC,6LAAC;gCACC,OAAM;gCACN,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCACC,OAAM;gCACN,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCACC,OAAM;gCACN,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCACC,OAAM;gCACN,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCAAG,OAAM;gCAAM,WAAU;0CACxB,cAAA,6LAAC;oCAAK,WAAU;8CAAU;;;;;;;;;;;;;;;;;;;;;;8BAIhC,6LAAC;oBAAM,WAAU;8BACd,OAAO,GAAG,CAAC,CAAC,sBACX,6LAAC;4BAAkB,WAAU;;8CAC3B,6LAAC;oCAAG,WAAU;8CACZ,cAAA,6LAAC;wCAAI,WAAU;;4CACZ,MAAM,SAAS,kBACd,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oDACJ,KAAK,MAAM,SAAS;oDACpB,KAAK,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC;oDACzB,OAAO;oDACP,QAAQ;oDACR,WAAU;;;;;;;;;;;0DAIhB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAyD,MAAM,IAAI;;;;;;0EAClF,6LAAC;gEAAI,WAAU;;oEACZ,MAAM,KAAK,kBACV,6LAAC;wEAAK,WAAU;kFAAoH;;;;;;kFAItI,6LAAC;wEACC,SAAS,CAAC,IAAM,qBAAqB,MAAM,EAAE,EAAE;wEAC/C,WAAU;wEACV,cAAY,cAAc,CAAC,MAAM,EAAE,CAAC,GAAG,0BAA0B;kFAEjE,cAAA,6LAAC;4EACC,OAAM;4EACN,OAAM;4EACN,QAAO;4EACP,SAAQ;4EACR,MAAM,cAAc,CAAC,MAAM,EAAE,CAAC,GAAG,iBAAiB;4EAClD,QAAO;4EACP,aAAY;4EACZ,eAAc;4EACd,gBAAe;4EACf,WAAW,CAAC,QAAQ,EAAE,cAAc,CAAC,MAAM,EAAE,CAAC,GAAG,oBAAoB,IAAI;sFAEzE,cAAA,6LAAC;gFAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;kEAKhB,6LAAC;wDAAI,WAAU;kEAAkE,MAAM,WAAW;;;;;;;;;;;;;;;;;;;;;;;8CAIxG,6LAAC;oCAAG,WAAU;8CACZ,cAAA,6LAAC;wCAAK,WAAU;kDACb,MAAM,QAAQ;;;;;;;;;;;8CAGnB,6LAAC;oCAAG,WAAU;8CACX,MAAM,UAAU,CAAC,cAAc;;;;;;8CAElC,6LAAC;oCAAG,WAAU;8CACX,IAAI,KAAK,MAAM,SAAS,EAAE,kBAAkB;;;;;;8CAE/C,6LAAC;oCAAG,WAAU;8CACZ,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAM,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;wCAC1B,WAAU;wCACV,SAAS,IAAM,iBAAiB,MAAM,EAAE;kDACzC;;;;;;;;;;;;2BAjEI,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;AA2E7B;GAzLwB;KAAA", "debugId": null}}, {"offset": {"line": 1528, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/app/browse/components/Pagination.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { getPageSize, updatePageSize } from '@/lib/userPreferences';\n\ninterface PaginationProps {\n  totalItems: number;\n  currentPage: number;\n  onPageChange: (page: number) => void;\n  onPageSizeChange: (pageSize: number) => void;\n}\n\nexport default function Pagination({\n  totalItems,\n  currentPage,\n  onPageChange,\n  onPageSizeChange,\n}: PaginationProps) {\n  const [pageSize, setPageSize] = useState(12);\n  const totalPages = Math.ceil(totalItems / pageSize);\n\n  // Load page size from user preferences on initial render only\n  useEffect(() => {\n    if (typeof window !== 'undefined') {\n      const savedPageSize = getPageSize();\n      setPageSize(savedPageSize);\n      // Only update if different from current to avoid loops\n      if (savedPageSize !== pageSize) {\n        onPageSizeChange(savedPageSize);\n      }\n    }\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n\n  // Handle page size change\n  const handlePageSizeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\n    const newPageSize = parseInt(e.target.value, 10);\n    setPageSize(newPageSize);\n    updatePageSize(newPageSize);\n    onPageSizeChange(newPageSize);\n\n    // If current page would be out of bounds with new page size, adjust it\n    const newTotalPages = Math.ceil(totalItems / newPageSize);\n    if (currentPage > newTotalPages) {\n      onPageChange(newTotalPages);\n    }\n  };\n\n  // Generate page numbers to display\n  const getPageNumbers = () => {\n    const pageNumbers = [];\n    const maxPagesToShow = 5;\n\n    if (totalPages <= maxPagesToShow) {\n      // Show all pages if there are few\n      for (let i = 1; i <= totalPages; i++) {\n        pageNumbers.push(i);\n      }\n    } else {\n      // Always show first page\n      pageNumbers.push(1);\n\n      // Calculate start and end of page range around current page\n      let start = Math.max(2, currentPage - 1);\n      let end = Math.min(totalPages - 1, currentPage + 1);\n\n      // Adjust if at the beginning\n      if (currentPage <= 2) {\n        end = Math.min(totalPages - 1, 4);\n      }\n\n      // Adjust if at the end\n      if (currentPage >= totalPages - 1) {\n        start = Math.max(2, totalPages - 3);\n      }\n\n      // Add ellipsis if needed\n      if (start > 2) {\n        pageNumbers.push('...');\n      }\n\n      // Add page numbers\n      for (let i = start; i <= end; i++) {\n        pageNumbers.push(i);\n      }\n\n      // Add ellipsis if needed\n      if (end < totalPages - 1) {\n        pageNumbers.push('...');\n      }\n\n      // Always show last page\n      pageNumbers.push(totalPages);\n    }\n\n    return pageNumbers;\n  };\n\n  // Don't show pagination if there's only one page or no items\n  if (totalPages <= 1 || totalItems === 0) {\n    return null;\n  }\n\n  return (\n    <div className=\"mt-8 flex flex-col items-center justify-between gap-4 sm:flex-row\">\n      <div className=\"flex items-center\">\n        <label htmlFor=\"pageSize\" className=\"mr-2 text-sm text-gray-600 dark:text-gray-400\">\n          Show:\n        </label>\n        <select\n          id=\"pageSize\"\n          value={pageSize}\n          onChange={handlePageSizeChange}\n          className=\"rounded-md border border-gray-300 bg-white px-2 py-1 text-sm text-gray-700 focus:border-blue-500 focus:outline-none focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200\"\n        >\n          <option value=\"6\">6</option>\n          <option value=\"12\">12</option>\n          <option value=\"24\">24</option>\n          <option value=\"48\">48</option>\n        </select>\n        <span className=\"ml-2 text-sm text-gray-600 dark:text-gray-400\">per page</span>\n      </div>\n\n      <div className=\"flex items-center\">\n        <span className=\"mr-4 text-sm text-gray-600 dark:text-gray-400\">\n          Showing {Math.min((currentPage - 1) * pageSize + 1, totalItems)} - {Math.min(currentPage * pageSize, totalItems)} of {totalItems}\n        </span>\n\n        <nav className=\"flex\">\n          <button\n            onClick={() => onPageChange(currentPage - 1)}\n            disabled={currentPage === 1}\n            className=\"mr-1 rounded-md border border-gray-300 bg-white px-3 py-1 text-sm text-gray-700 hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700\"\n            aria-label=\"Previous page\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              width=\"24\"\n              height=\"24\"\n              viewBox=\"0 0 24 24\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              strokeWidth=\"2\"\n              strokeLinecap=\"round\"\n              strokeLinejoin=\"round\"\n              className=\"h-4 w-4\"\n            >\n              <path d=\"m15 18-6-6 6-6\" />\n            </svg>\n          </button>\n\n          {getPageNumbers().map((page, index) => (\n            <button\n              key={index}\n              onClick={() => typeof page === 'number' && onPageChange(page)}\n              disabled={page === '...' || page === currentPage}\n              className={`mx-1 rounded-md px-3 py-1 text-sm ${\n                page === currentPage\n                  ? 'bg-blue-600 text-white dark:bg-blue-700'\n                  : page === '...'\n                  ? 'cursor-default border border-transparent text-gray-500 dark:text-gray-400'\n                  : 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700'\n              }`}\n            >\n              {page}\n            </button>\n          ))}\n\n          <button\n            onClick={() => onPageChange(currentPage + 1)}\n            disabled={currentPage === totalPages}\n            className=\"ml-1 rounded-md border border-gray-300 bg-white px-3 py-1 text-sm text-gray-700 hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700\"\n            aria-label=\"Next page\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              width=\"24\"\n              height=\"24\"\n              viewBox=\"0 0 24 24\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              strokeWidth=\"2\"\n              strokeLinecap=\"round\"\n              strokeLinejoin=\"round\"\n              className=\"h-4 w-4\"\n            >\n              <path d=\"m9 18 6-6-6-6\" />\n            </svg>\n          </button>\n        </nav>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAYe,SAAS,WAAW,EACjC,UAAU,EACV,WAAW,EACX,YAAY,EACZ,gBAAgB,EACA;;IAChB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,aAAa,KAAK,IAAI,CAAC,aAAa;IAE1C,8DAA8D;IAC9D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,wCAAmC;gBACjC,MAAM,gBAAgB,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;gBAChC,YAAY;gBACZ,uDAAuD;gBACvD,IAAI,kBAAkB,UAAU;oBAC9B,iBAAiB;gBACnB;YACF;QACF,uDAAuD;QACvD;+BAAG,EAAE;IAEL,0BAA0B;IAC1B,MAAM,uBAAuB,CAAC;QAC5B,MAAM,cAAc,SAAS,EAAE,MAAM,CAAC,KAAK,EAAE;QAC7C,YAAY;QACZ,CAAA,GAAA,gIAAA,CAAA,iBAAc,AAAD,EAAE;QACf,iBAAiB;QAEjB,uEAAuE;QACvE,MAAM,gBAAgB,KAAK,IAAI,CAAC,aAAa;QAC7C,IAAI,cAAc,eAAe;YAC/B,aAAa;QACf;IACF;IAEA,mCAAmC;IACnC,MAAM,iBAAiB;QACrB,MAAM,cAAc,EAAE;QACtB,MAAM,iBAAiB;QAEvB,IAAI,cAAc,gBAAgB;YAChC,kCAAkC;YAClC,IAAK,IAAI,IAAI,GAAG,KAAK,YAAY,IAAK;gBACpC,YAAY,IAAI,CAAC;YACnB;QACF,OAAO;YACL,yBAAyB;YACzB,YAAY,IAAI,CAAC;YAEjB,4DAA4D;YAC5D,IAAI,QAAQ,KAAK,GAAG,CAAC,GAAG,cAAc;YACtC,IAAI,MAAM,KAAK,GAAG,CAAC,aAAa,GAAG,cAAc;YAEjD,6BAA6B;YAC7B,IAAI,eAAe,GAAG;gBACpB,MAAM,KAAK,GAAG,CAAC,aAAa,GAAG;YACjC;YAEA,uBAAuB;YACvB,IAAI,eAAe,aAAa,GAAG;gBACjC,QAAQ,KAAK,GAAG,CAAC,GAAG,aAAa;YACnC;YAEA,yBAAyB;YACzB,IAAI,QAAQ,GAAG;gBACb,YAAY,IAAI,CAAC;YACnB;YAEA,mBAAmB;YACnB,IAAK,IAAI,IAAI,OAAO,KAAK,KAAK,IAAK;gBACjC,YAAY,IAAI,CAAC;YACnB;YAEA,yBAAyB;YACzB,IAAI,MAAM,aAAa,GAAG;gBACxB,YAAY,IAAI,CAAC;YACnB;YAEA,wBAAwB;YACxB,YAAY,IAAI,CAAC;QACnB;QAEA,OAAO;IACT;IAEA,6DAA6D;IAC7D,IAAI,cAAc,KAAK,eAAe,GAAG;QACvC,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAM,SAAQ;wBAAW,WAAU;kCAAgD;;;;;;kCAGpF,6LAAC;wBACC,IAAG;wBACH,OAAO;wBACP,UAAU;wBACV,WAAU;;0CAEV,6LAAC;gCAAO,OAAM;0CAAI;;;;;;0CAClB,6LAAC;gCAAO,OAAM;0CAAK;;;;;;0CACnB,6LAAC;gCAAO,OAAM;0CAAK;;;;;;0CACnB,6LAAC;gCAAO,OAAM;0CAAK;;;;;;;;;;;;kCAErB,6LAAC;wBAAK,WAAU;kCAAgD;;;;;;;;;;;;0BAGlE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAK,WAAU;;4BAAgD;4BACrD,KAAK,GAAG,CAAC,CAAC,cAAc,CAAC,IAAI,WAAW,GAAG;4BAAY;4BAAI,KAAK,GAAG,CAAC,cAAc,UAAU;4BAAY;4BAAK;;;;;;;kCAGxH,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,aAAa,cAAc;gCAC1C,UAAU,gBAAgB;gCAC1B,WAAU;gCACV,cAAW;0CAEX,cAAA,6LAAC;oCACC,OAAM;oCACN,OAAM;oCACN,QAAO;oCACP,SAAQ;oCACR,MAAK;oCACL,QAAO;oCACP,aAAY;oCACZ,eAAc;oCACd,gBAAe;oCACf,WAAU;8CAEV,cAAA,6LAAC;wCAAK,GAAE;;;;;;;;;;;;;;;;4BAIX,iBAAiB,GAAG,CAAC,CAAC,MAAM,sBAC3B,6LAAC;oCAEC,SAAS,IAAM,OAAO,SAAS,YAAY,aAAa;oCACxD,UAAU,SAAS,SAAS,SAAS;oCACrC,WAAW,CAAC,kCAAkC,EAC5C,SAAS,cACL,4CACA,SAAS,QACT,8EACA,kJACJ;8CAED;mCAXI;;;;;0CAeT,6LAAC;gCACC,SAAS,IAAM,aAAa,cAAc;gCAC1C,UAAU,gBAAgB;gCAC1B,WAAU;gCACV,cAAW;0CAEX,cAAA,6LAAC;oCACC,OAAM;oCACN,OAAM;oCACN,QAAO;oCACP,SAAQ;oCACR,MAAK;oCACL,QAAO;oCACP,aAAY;oCACZ,eAAc;oCACd,gBAAe;oCACf,WAAU;8CAEV,cAAA,6LAAC;wCAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtB;GArLwB;KAAA", "debugId": null}}, {"offset": {"line": 1815, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/app/favorites/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useMemo } from 'react';\nimport { getAllAgents } from '@/lib/agents';\nimport { Agent } from '@/types/agent';\nimport { getUserPreferences, isAgentFavorited } from '@/lib/userPreferences';\nimport { addCustomEventListener, EVENTS } from '@/lib/events';\nimport AgentGrid from '../browse/components/AgentGrid';\nimport AgentList from '../browse/components/AgentList';\nimport Pagination from '../browse/components/Pagination';\nimport Link from 'next/link';\n\nexport default function FavoritesPage() {\n  const allAgents = useMemo(() => getAllAgents(), []);\n  const [favoriteAgents, setFavoriteAgents] = useState<Agent[]>([]);\n  const [displayedAgents, setDisplayedAgents] = useState<Agent[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n\n  // View state\n  const [view, setView] = useState<'grid' | 'list'>('grid');\n  const [sortBy, setSortBy] = useState('recent');\n\n  // Pagination states\n  const [currentPage, setCurrentPage] = useState(1);\n  const [pageSize, setPageSize] = useState(12);\n\n  // Load favorite agents\n  useEffect(() => {\n    if (typeof window === 'undefined') return;\n\n    const loadFavorites = () => {\n      const preferences = getUserPreferences();\n      const favoriteIds = preferences.favoriteAgentIds;\n\n      // Filter agents to only include favorites\n      const favorites = allAgents.filter(agent =>\n        favoriteIds.includes(agent.id)\n      );\n\n      setFavoriteAgents(favorites);\n      setIsLoading(false);\n    };\n\n    loadFavorites();\n\n    // Listen for favorites updates\n    const removeFavoritesListener = addCustomEventListener(EVENTS.FAVORITES_UPDATED, () => {\n      loadFavorites();\n    });\n\n    // Set up event listener for storage changes (in case favorites are updated in another tab)\n    const handleStorageChange = (e: StorageEvent) => {\n      if (e.key === 'userPreferences') {\n        loadFavorites();\n      }\n    };\n\n    window.addEventListener('storage', handleStorageChange);\n    return () => {\n      window.removeEventListener('storage', handleStorageChange);\n      removeFavoritesListener();\n    };\n  }, [allAgents]);\n\n  // Apply sorting\n  useEffect(() => {\n    let sorted = [...favoriteAgents];\n\n    switch (sortBy) {\n      case 'recent':\n        // For \"recent\", we'll use the order in the favorites array\n        // which is already sorted by most recently added\n        break;\n      case 'name':\n        sorted.sort((a, b) => a.name.localeCompare(b.name));\n        break;\n      case 'popular':\n        sorted.sort((a, b) => b.usageCount - a.usageCount);\n        break;\n      case 'category':\n        sorted.sort((a, b) => a.category.localeCompare(b.category));\n        break;\n      default:\n        break;\n    }\n\n    setFavoriteAgents(sorted);\n  }, [sortBy]);\n\n  // Apply pagination\n  useEffect(() => {\n    // Ensure current page is valid\n    const maxPage = Math.max(1, Math.ceil(favoriteAgents.length / pageSize));\n    const validCurrentPage = Math.min(currentPage, maxPage);\n\n    if (validCurrentPage !== currentPage) {\n      setCurrentPage(validCurrentPage);\n      return;\n    }\n\n    const startIndex = (validCurrentPage - 1) * pageSize;\n    const endIndex = startIndex + pageSize;\n    setDisplayedAgents(favoriteAgents.slice(startIndex, endIndex));\n  }, [favoriteAgents, currentPage, pageSize]);\n\n  // Handle page change\n  const handlePageChange = (page: number) => {\n    setCurrentPage(page);\n    window.scrollTo({ top: 0, behavior: 'smooth' });\n  };\n\n  // Handle page size change\n  const handlePageSizeChange = (size: number) => {\n    setPageSize(size);\n    setCurrentPage(1); // Reset to first page when page size changes\n  };\n\n  // Handle view change\n  const handleViewChange = (newView: 'grid' | 'list') => {\n    setView(newView);\n  };\n\n  // Handle sort change\n  const handleSortChange = (newSortBy: string) => {\n    setSortBy(newSortBy);\n  };\n\n  // Render loading state\n  if (isLoading) {\n    return (\n      <div className=\"container mx-auto px-4 py-8\">\n        <h1 className=\"mb-6 text-2xl font-bold text-gray-900 dark:text-white md:text-3xl\">Favorite Agents</h1>\n        <div className=\"flex h-64 items-center justify-center\">\n          <div className=\"h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-blue-600\"></div>\n        </div>\n      </div>\n    );\n  }\n\n  // Render empty state\n  if (favoriteAgents.length === 0) {\n    return (\n      <div className=\"container mx-auto px-4 py-8\">\n        <h1 className=\"mb-6 text-2xl font-bold text-gray-900 dark:text-white md:text-3xl\">Favorite Agents</h1>\n        <div className=\"flex h-64 flex-col items-center justify-center rounded-lg border border-gray-200 bg-white p-6 text-center dark:border-gray-800 dark:bg-gray-900\">\n          <svg\n            xmlns=\"http://www.w3.org/2000/svg\"\n            width=\"24\"\n            height=\"24\"\n            viewBox=\"0 0 24 24\"\n            fill=\"none\"\n            stroke=\"currentColor\"\n            strokeWidth=\"2\"\n            strokeLinecap=\"round\"\n            strokeLinejoin=\"round\"\n            className=\"mb-4 h-12 w-12 text-gray-400\"\n          >\n            <path d=\"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z\" />\n          </svg>\n          <h3 className=\"mb-2 text-lg font-semibold text-gray-900 dark:text-white\">No favorite agents yet</h3>\n          <p className=\"mb-6 text-gray-600 dark:text-gray-400\">\n            Start adding agents to your favorites to see them here.\n          </p>\n          <Link\n            href=\"/browse\"\n            className=\"rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800\"\n          >\n            Browse Agents\n          </Link>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"container mx-auto px-4 py-8\">\n      <div className=\"mb-8 flex flex-col justify-between gap-4 sm:flex-row sm:items-center\">\n        <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white md:text-3xl\">Favorite Agents</h1>\n\n        <div className=\"flex flex-wrap items-center gap-2 sm:gap-4\">\n          {/* Sort options */}\n          <div className=\"relative\">\n            <select\n              value={sortBy}\n              onChange={(e) => handleSortChange(e.target.value)}\n              className=\"rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700\"\n            >\n              <option value=\"recent\">Recently Added</option>\n              <option value=\"name\">Name (A-Z)</option>\n              <option value=\"popular\">Most Popular</option>\n              <option value=\"category\">Category</option>\n            </select>\n          </div>\n\n          {/* View toggle */}\n          <div className=\"flex rounded-md border border-gray-300 dark:border-gray-600\">\n            <button\n              onClick={() => handleViewChange('grid')}\n              className={`flex items-center justify-center rounded-l-md px-3 py-2 ${\n                view === 'grid'\n                  ? 'bg-gray-100 text-gray-900 dark:bg-gray-700 dark:text-white'\n                  : 'bg-white text-gray-700 hover:bg-gray-50 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700'\n              }`}\n              aria-label=\"Grid view\"\n            >\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                width=\"24\"\n                height=\"24\"\n                viewBox=\"0 0 24 24\"\n                fill=\"none\"\n                stroke=\"currentColor\"\n                strokeWidth=\"2\"\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                className=\"h-4 w-4\"\n              >\n                <rect width=\"7\" height=\"7\" x=\"3\" y=\"3\" rx=\"1\" />\n                <rect width=\"7\" height=\"7\" x=\"14\" y=\"3\" rx=\"1\" />\n                <rect width=\"7\" height=\"7\" x=\"14\" y=\"14\" rx=\"1\" />\n                <rect width=\"7\" height=\"7\" x=\"3\" y=\"14\" rx=\"1\" />\n              </svg>\n            </button>\n            <button\n              onClick={() => handleViewChange('list')}\n              className={`flex items-center justify-center rounded-r-md px-3 py-2 ${\n                view === 'list'\n                  ? 'bg-gray-100 text-gray-900 dark:bg-gray-700 dark:text-white'\n                  : 'bg-white text-gray-700 hover:bg-gray-50 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700'\n              }`}\n              aria-label=\"List view\"\n            >\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                width=\"24\"\n                height=\"24\"\n                viewBox=\"0 0 24 24\"\n                fill=\"none\"\n                stroke=\"currentColor\"\n                strokeWidth=\"2\"\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                className=\"h-4 w-4\"\n              >\n                <line x1=\"8\" x2=\"21\" y1=\"6\" y2=\"6\" />\n                <line x1=\"8\" x2=\"21\" y1=\"12\" y2=\"12\" />\n                <line x1=\"8\" x2=\"21\" y1=\"18\" y2=\"18\" />\n                <line x1=\"3\" x2=\"3.01\" y1=\"6\" y2=\"6\" />\n                <line x1=\"3\" x2=\"3.01\" y1=\"12\" y2=\"12\" />\n                <line x1=\"3\" x2=\"3.01\" y1=\"18\" y2=\"18\" />\n              </svg>\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {view === 'grid' ? (\n        <AgentGrid agents={displayedAgents} />\n      ) : (\n        <AgentList agents={displayedAgents} />\n      )}\n\n      {favoriteAgents.length > 0 && (\n        <Pagination\n          totalItems={favoriteAgents.length}\n          currentPage={currentPage}\n          onPageChange={handlePageChange}\n          onPageSizeChange={handlePageSizeChange}\n        />\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAVA;;;;;;;;;AAYe,SAAS;;IACtB,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;4CAAE,IAAM,CAAA,GAAA,uHAAA,CAAA,eAAY,AAAD;2CAAK,EAAE;IAClD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAClE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,aAAa;IACb,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IAClD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,oBAAoB;IACpB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,uBAAuB;IACvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,uCAAmC;;YAAM;YAEzC,MAAM;yDAAgB;oBACpB,MAAM,cAAc,CAAA,GAAA,gIAAA,CAAA,qBAAkB,AAAD;oBACrC,MAAM,cAAc,YAAY,gBAAgB;oBAEhD,0CAA0C;oBAC1C,MAAM,YAAY,UAAU,MAAM;2EAAC,CAAA,QACjC,YAAY,QAAQ,CAAC,MAAM,EAAE;;oBAG/B,kBAAkB;oBAClB,aAAa;gBACf;;YAEA;YAEA,+BAA+B;YAC/B,MAAM,0BAA0B,CAAA,GAAA,uHAAA,CAAA,yBAAsB,AAAD,EAAE,uHAAA,CAAA,SAAM,CAAC,iBAAiB;mEAAE;oBAC/E;gBACF;;YAEA,2FAA2F;YAC3F,MAAM;+DAAsB,CAAC;oBAC3B,IAAI,EAAE,GAAG,KAAK,mBAAmB;wBAC/B;oBACF;gBACF;;YAEA,OAAO,gBAAgB,CAAC,WAAW;YACnC;2CAAO;oBACL,OAAO,mBAAmB,CAAC,WAAW;oBACtC;gBACF;;QACF;kCAAG;QAAC;KAAU;IAEd,gBAAgB;IAChB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,SAAS;mBAAI;aAAe;YAEhC,OAAQ;gBACN,KAAK;oBAGH;gBACF,KAAK;oBACH,OAAO,IAAI;mDAAC,CAAC,GAAG,IAAM,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;;oBACjD;gBACF,KAAK;oBACH,OAAO,IAAI;mDAAC,CAAC,GAAG,IAAM,EAAE,UAAU,GAAG,EAAE,UAAU;;oBACjD;gBACF,KAAK;oBACH,OAAO,IAAI;mDAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,CAAC,aAAa,CAAC,EAAE,QAAQ;;oBACzD;gBACF;oBACE;YACJ;YAEA,kBAAkB;QACpB;kCAAG;QAAC;KAAO;IAEX,mBAAmB;IACnB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,+BAA+B;YAC/B,MAAM,UAAU,KAAK,GAAG,CAAC,GAAG,KAAK,IAAI,CAAC,eAAe,MAAM,GAAG;YAC9D,MAAM,mBAAmB,KAAK,GAAG,CAAC,aAAa;YAE/C,IAAI,qBAAqB,aAAa;gBACpC,eAAe;gBACf;YACF;YAEA,MAAM,aAAa,CAAC,mBAAmB,CAAC,IAAI;YAC5C,MAAM,WAAW,aAAa;YAC9B,mBAAmB,eAAe,KAAK,CAAC,YAAY;QACtD;kCAAG;QAAC;QAAgB;QAAa;KAAS;IAE1C,qBAAqB;IACrB,MAAM,mBAAmB,CAAC;QACxB,eAAe;QACf,OAAO,QAAQ,CAAC;YAAE,KAAK;YAAG,UAAU;QAAS;IAC/C;IAEA,0BAA0B;IAC1B,MAAM,uBAAuB,CAAC;QAC5B,YAAY;QACZ,eAAe,IAAI,6CAA6C;IAClE;IAEA,qBAAqB;IACrB,MAAM,mBAAmB,CAAC;QACxB,QAAQ;IACV;IAEA,qBAAqB;IACrB,MAAM,mBAAmB,CAAC;QACxB,UAAU;IACZ;IAEA,uBAAuB;IACvB,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAAoE;;;;;;8BAClF,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;IAIvB;IAEA,qBAAqB;IACrB,IAAI,eAAe,MAAM,KAAK,GAAG;QAC/B,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAAoE;;;;;;8BAClF,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,OAAM;4BACN,OAAM;4BACN,QAAO;4BACP,SAAQ;4BACR,MAAK;4BACL,QAAO;4BACP,aAAY;4BACZ,eAAc;4BACd,gBAAe;4BACf,WAAU;sCAEV,cAAA,6LAAC;gCAAK,GAAE;;;;;;;;;;;sCAEV,6LAAC;4BAAG,WAAU;sCAA2D;;;;;;sCACzE,6LAAC;4BAAE,WAAU;sCAAwC;;;;;;sCAGrD,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA+D;;;;;;kCAE7E,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;oCAChD,WAAU;;sDAEV,6LAAC;4CAAO,OAAM;sDAAS;;;;;;sDACvB,6LAAC;4CAAO,OAAM;sDAAO;;;;;;sDACrB,6LAAC;4CAAO,OAAM;sDAAU;;;;;;sDACxB,6LAAC;4CAAO,OAAM;sDAAW;;;;;;;;;;;;;;;;;0CAK7B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,iBAAiB;wCAChC,WAAW,CAAC,wDAAwD,EAClE,SAAS,SACL,+DACA,sGACJ;wCACF,cAAW;kDAEX,cAAA,6LAAC;4CACC,OAAM;4CACN,OAAM;4CACN,QAAO;4CACP,SAAQ;4CACR,MAAK;4CACL,QAAO;4CACP,aAAY;4CACZ,eAAc;4CACd,gBAAe;4CACf,WAAU;;8DAEV,6LAAC;oDAAK,OAAM;oDAAI,QAAO;oDAAI,GAAE;oDAAI,GAAE;oDAAI,IAAG;;;;;;8DAC1C,6LAAC;oDAAK,OAAM;oDAAI,QAAO;oDAAI,GAAE;oDAAK,GAAE;oDAAI,IAAG;;;;;;8DAC3C,6LAAC;oDAAK,OAAM;oDAAI,QAAO;oDAAI,GAAE;oDAAK,GAAE;oDAAK,IAAG;;;;;;8DAC5C,6LAAC;oDAAK,OAAM;oDAAI,QAAO;oDAAI,GAAE;oDAAI,GAAE;oDAAK,IAAG;;;;;;;;;;;;;;;;;kDAG/C,6LAAC;wCACC,SAAS,IAAM,iBAAiB;wCAChC,WAAW,CAAC,wDAAwD,EAClE,SAAS,SACL,+DACA,sGACJ;wCACF,cAAW;kDAEX,cAAA,6LAAC;4CACC,OAAM;4CACN,OAAM;4CACN,QAAO;4CACP,SAAQ;4CACR,MAAK;4CACL,QAAO;4CACP,aAAY;4CACZ,eAAc;4CACd,gBAAe;4CACf,WAAU;;8DAEV,6LAAC;oDAAK,IAAG;oDAAI,IAAG;oDAAK,IAAG;oDAAI,IAAG;;;;;;8DAC/B,6LAAC;oDAAK,IAAG;oDAAI,IAAG;oDAAK,IAAG;oDAAK,IAAG;;;;;;8DAChC,6LAAC;oDAAK,IAAG;oDAAI,IAAG;oDAAK,IAAG;oDAAK,IAAG;;;;;;8DAChC,6LAAC;oDAAK,IAAG;oDAAI,IAAG;oDAAO,IAAG;oDAAI,IAAG;;;;;;8DACjC,6LAAC;oDAAK,IAAG;oDAAI,IAAG;oDAAO,IAAG;oDAAK,IAAG;;;;;;8DAClC,6LAAC;oDAAK,IAAG;oDAAI,IAAG;oDAAO,IAAG;oDAAK,IAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAO3C,SAAS,uBACR,6LAAC,mJAAA,CAAA,UAAS;gBAAC,QAAQ;;;;;qCAEnB,6LAAC,mJAAA,CAAA,UAAS;gBAAC,QAAQ;;;;;;YAGpB,eAAe,MAAM,GAAG,mBACvB,6LAAC,oJAAA,CAAA,UAAU;gBACT,YAAY,eAAe,MAAM;gBACjC,aAAa;gBACb,cAAc;gBACd,kBAAkB;;;;;;;;;;;;AAK5B;GApQwB;KAAA", "debugId": null}}]}