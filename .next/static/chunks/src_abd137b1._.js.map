{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/lib/agents.ts"], "sourcesContent": ["import { Agent, Message, Session } from '@/types/agent';\n\n// Mock data for agents\nconst agents: Agent[] = [\n  {\n    id: '1',\n    name: 'Data Analyst',\n    description: 'Analyze data sets and generate insights with natural language queries.',\n    longDescription: 'The Data Analyst agent helps you explore and understand your data through natural language. Ask questions about your data, request visualizations, or get statistical summaries without writing complex queries or code. This agent can work with various data formats including CSV, Excel, SQL databases, and more.',\n    category: 'Analytics',\n    capabilities: [\n      'Natural language data queries',\n      'Data visualization generation',\n      'Statistical analysis',\n      'Anomaly detection',\n      'Trend identification',\n      'Report generation'\n    ],\n    usageCount: 1245,\n    isNew: false,\n    isFeatured: true,\n    createdAt: '2023-10-15',\n    updatedAt: '2024-03-01',\n    version: '2.3.0',\n    creator: 'Data Science Team',\n    avatarUrl: '/agents/data-analyst.svg',\n    tags: ['data', 'analytics', 'visualization', 'statistics'],\n    relatedAgentIds: ['5', '7', '8']\n  },\n  {\n    id: '2',\n    name: 'Code Assistant',\n    description: 'Help with coding tasks, debugging, and code reviews across multiple languages.',\n    longDescription: 'The Code Assistant agent is your programming partner. It can help write, debug, and optimize code in multiple languages including Python, JavaScript, Java, C++, and more. Ask for code examples, get help with debugging, or request code reviews. The agent can also explain complex code and suggest improvements.',\n    category: 'Development',\n    capabilities: [\n      'Code generation',\n      'Debugging assistance',\n      'Code optimization',\n      'Code explanation',\n      'Multiple language support',\n      'Best practices recommendations'\n    ],\n    usageCount: 3421,\n    isNew: false,\n    isFeatured: true,\n    createdAt: '2023-08-10',\n    updatedAt: '2024-02-15',\n    version: '3.1.0',\n    creator: 'Engineering Team',\n    avatarUrl: '/agents/code-assistant.svg',\n    tags: ['coding', 'programming', 'development', 'debugging'],\n    relatedAgentIds: ['9', '10']\n  },\n  {\n    id: '3',\n    name: 'Research Companion',\n    description: 'Find, summarize, and organize research papers and articles.',\n    longDescription: 'The Research Companion agent helps you stay on top of the latest research in your field. It can find relevant papers, summarize key findings, and help you organize your research materials. Ask for papers on specific topics, get summaries of complex articles, or request literature reviews.',\n    category: 'Research',\n    capabilities: [\n      'Research paper search',\n      'Article summarization',\n      'Literature review assistance',\n      'Citation generation',\n      'Research organization',\n      'Key findings extraction'\n    ],\n    usageCount: 876,\n    isNew: true,\n    isFeatured: true,\n    createdAt: '2024-01-20',\n    updatedAt: '2024-03-10',\n    version: '1.2.0',\n    creator: 'Research & Development',\n    avatarUrl: '/agents/research-companion.svg',\n    tags: ['research', 'papers', 'academic', 'literature'],\n    relatedAgentIds: ['11', '5']\n  },\n  {\n    id: '4',\n    name: 'Meeting Summarizer',\n    description: 'Generate concise summaries and action items from meeting transcripts.',\n    longDescription: 'The Meeting Summarizer agent helps you capture the important points from your meetings. Upload a transcript or recording, and the agent will identify key discussion points, decisions made, and action items assigned. It can also generate meeting minutes in various formats.',\n    category: 'Productivity',\n    capabilities: [\n      'Meeting transcript analysis',\n      'Key point extraction',\n      'Action item identification',\n      'Decision tracking',\n      'Meeting minutes generation',\n      'Follow-up reminder creation'\n    ],\n    usageCount: 2134,\n    isNew: false,\n    isFeatured: true,\n    createdAt: '2023-11-05',\n    updatedAt: '2024-02-28',\n    version: '2.0.1',\n    creator: 'Productivity Team',\n    avatarUrl: '/agents/meeting-summarizer.svg',\n    tags: ['meetings', 'productivity', 'transcription', 'summaries'],\n    relatedAgentIds: ['5', '6', '12']\n  },\n  {\n    id: '5',\n    name: 'Document Analyzer',\n    description: 'Extract key information from documents and generate summaries.',\n    longDescription: 'The Document Analyzer agent helps you process and understand documents quickly. Upload any document (PDF, Word, etc.), and the agent will extract key information, generate summaries, and answer questions about the content. It can handle contracts, reports, articles, and more.',\n    category: 'Productivity',\n    capabilities: [\n      'Document parsing',\n      'Key information extraction',\n      'Summary generation',\n      'Question answering',\n      'Multiple format support',\n      'Entity recognition'\n    ],\n    usageCount: 567,\n    isNew: false,\n    isFeatured: false,\n    createdAt: '2023-09-12',\n    updatedAt: '2024-01-30',\n    version: '1.5.2',\n    creator: 'Content Team',\n    avatarUrl: '/agents/document-analyzer.svg',\n    tags: ['documents', 'analysis', 'extraction', 'summaries'],\n    relatedAgentIds: ['1', '3', '4']\n  },\n  {\n    id: '6',\n    name: 'Presentation Creator',\n    description: 'Generate professional presentations from outlines or topics.',\n    longDescription: 'The Presentation Creator agent helps you build compelling presentations quickly. Provide a topic or outline, and the agent will generate slide content, suggest visuals, and help you structure your presentation for maximum impact. It can create presentations for various purposes including business, education, and sales.',\n    category: 'Productivity',\n    capabilities: [\n      'Slide content generation',\n      'Presentation structure suggestions',\n      'Visual element recommendations',\n      'Talking points creation',\n      'Multiple template support',\n      'Export to PowerPoint/Google Slides'\n    ],\n    usageCount: 321,\n    isNew: true,\n    isFeatured: false,\n    createdAt: '2024-02-01',\n    updatedAt: '2024-03-15',\n    version: '1.0.0',\n    creator: 'Marketing Team',\n    avatarUrl: '/agents/presentation-creator.svg',\n    tags: ['presentations', 'slides', 'design', 'content'],\n    relatedAgentIds: ['4', '13']\n  },\n  {\n    id: '7',\n    name: 'Data Visualization Expert',\n    description: 'Create beautiful and informative data visualizations from your datasets.',\n    longDescription: 'The Data Visualization Expert agent helps you transform raw data into compelling visual stories. Upload your data and describe what you want to highlight, and the agent will generate appropriate charts, graphs, and interactive visualizations that make your data insights clear and impactful.',\n    category: 'Analytics',\n    capabilities: [\n      'Chart and graph generation',\n      'Interactive visualization creation',\n      'Color palette optimization',\n      'Data storytelling assistance',\n      'Multiple export formats',\n      'Accessibility considerations'\n    ],\n    usageCount: 892,\n    isNew: false,\n    isFeatured: false,\n    createdAt: '2023-11-18',\n    updatedAt: '2024-02-10',\n    version: '1.8.0',\n    creator: 'Data Science Team',\n    avatarUrl: '/agents/data-analyst.svg',\n    tags: ['visualization', 'charts', 'graphs', 'data'],\n    relatedAgentIds: ['1', '8']\n  },\n  {\n    id: '8',\n    name: 'Predictive Analytics Agent',\n    description: 'Forecast trends and make predictions based on historical data.',\n    longDescription: 'The Predictive Analytics Agent uses machine learning algorithms to analyze your historical data and generate forecasts and predictions. It can identify patterns, trends, and anomalies to help you make data-driven decisions about future outcomes.',\n    category: 'Analytics',\n    capabilities: [\n      'Time series forecasting',\n      'Trend analysis',\n      'Anomaly detection',\n      'Predictive modeling',\n      'Scenario planning',\n      'Confidence interval calculation'\n    ],\n    usageCount: 754,\n    isNew: true,\n    isFeatured: false,\n    createdAt: '2024-01-05',\n    updatedAt: '2024-03-20',\n    version: '1.2.1',\n    creator: 'Data Science Team',\n    avatarUrl: '/agents/data-analyst.svg',\n    tags: ['predictions', 'forecasting', 'analytics', 'trends'],\n    relatedAgentIds: ['1', '7']\n  },\n  {\n    id: '9',\n    name: 'API Documentation Generator',\n    description: 'Automatically generate comprehensive API documentation from your code.',\n    longDescription: 'The API Documentation Generator analyzes your API code and generates clear, comprehensive documentation. It can create reference docs, guides, examples, and interactive API explorers to help developers understand and use your APIs effectively.',\n    category: 'Development',\n    capabilities: [\n      'API reference generation',\n      'Code example creation',\n      'Interactive API explorer',\n      'Multiple format support',\n      'Versioning assistance',\n      'Consistency checking'\n    ],\n    usageCount: 623,\n    isNew: false,\n    isFeatured: false,\n    createdAt: '2023-10-08',\n    updatedAt: '2024-02-12',\n    version: '2.1.0',\n    creator: 'Engineering Team',\n    avatarUrl: '/agents/code-assistant.svg',\n    tags: ['documentation', 'API', 'development', 'reference'],\n    relatedAgentIds: ['2', '10']\n  },\n  {\n    id: '10',\n    name: 'Code Reviewer',\n    description: 'Get detailed code reviews with suggestions for improvements and best practices.',\n    longDescription: 'The Code Reviewer agent analyzes your code for bugs, security issues, performance problems, and adherence to best practices. It provides detailed feedback with specific suggestions for improvements and explanations of potential issues.',\n    category: 'Development',\n    capabilities: [\n      'Bug detection',\n      'Security vulnerability scanning',\n      'Performance optimization',\n      'Best practice enforcement',\n      'Code style consistency',\n      'Refactoring suggestions'\n    ],\n    usageCount: 1876,\n    isNew: false,\n    isFeatured: false,\n    createdAt: '2023-09-20',\n    updatedAt: '2024-03-05',\n    version: '2.4.0',\n    creator: 'Engineering Team',\n    avatarUrl: '/agents/code-assistant.svg',\n    tags: ['code review', 'quality', 'security', 'best practices'],\n    relatedAgentIds: ['2', '9']\n  },\n  {\n    id: '11',\n    name: 'Literature Review Assistant',\n    description: 'Compile comprehensive literature reviews on any research topic.',\n    longDescription: 'The Literature Review Assistant helps researchers compile thorough literature reviews by finding relevant papers, organizing them by themes, identifying key findings, and highlighting research gaps. It can save weeks of manual research work.',\n    category: 'Research',\n    capabilities: [\n      'Research paper discovery',\n      'Thematic organization',\n      'Gap analysis',\n      'Citation management',\n      'Summary generation',\n      'Trend identification'\n    ],\n    usageCount: 542,\n    isNew: true,\n    isFeatured: false,\n    createdAt: '2024-02-15',\n    updatedAt: '2024-03-18',\n    version: '1.0.2',\n    creator: 'Research & Development',\n    avatarUrl: '/agents/research-companion.svg',\n    tags: ['literature review', 'research', 'academic', 'papers'],\n    relatedAgentIds: ['3']\n  },\n  {\n    id: '12',\n    name: 'Project Manager Assistant',\n    description: 'Track projects, manage tasks, and coordinate team activities efficiently.',\n    longDescription: 'The Project Manager Assistant helps you plan, track, and manage projects from start to finish. It can create project timelines, assign and track tasks, identify risks, generate status reports, and facilitate team communication to keep your projects on track.',\n    category: 'Productivity',\n    capabilities: [\n      'Project planning',\n      'Task management',\n      'Timeline creation',\n      'Risk assessment',\n      'Status reporting',\n      'Resource allocation'\n    ],\n    usageCount: 1432,\n    isNew: false,\n    isFeatured: false,\n    createdAt: '2023-08-25',\n    updatedAt: '2024-02-20',\n    version: '2.2.0',\n    creator: 'Productivity Team',\n    avatarUrl: '/agents/meeting-summarizer.svg',\n    tags: ['project management', 'tasks', 'planning', 'coordination'],\n    relatedAgentIds: ['4', '13']\n  },\n  {\n    id: '13',\n    name: 'Content Creator',\n    description: 'Generate high-quality content for blogs, social media, and marketing materials.',\n    longDescription: 'The Content Creator agent helps you produce engaging content for various platforms. Provide a topic and target audience, and it will generate blog posts, social media updates, marketing copy, and more, tailored to your brand voice and content strategy.',\n    category: 'Marketing',\n    capabilities: [\n      'Blog post generation',\n      'Social media content creation',\n      'Marketing copy writing',\n      'SEO optimization',\n      'Brand voice consistency',\n      'Content strategy alignment'\n    ],\n    usageCount: 2187,\n    isNew: false,\n    isFeatured: false,\n    createdAt: '2023-07-12',\n    updatedAt: '2024-03-08',\n    version: '3.0.1',\n    creator: 'Marketing Team',\n    avatarUrl: '/agents/presentation-creator.svg',\n    tags: ['content', 'writing', 'marketing', 'social media'],\n    relatedAgentIds: ['6', '12']\n  }\n];\n\n// Mock data for sessions\nconst sessions: Session[] = [\n  {\n    id: 'session1',\n    agentId: '2',\n    title: 'JavaScript Debugging Help',\n    messages: [\n      {\n        id: 'msg1',\n        content: 'Hello! How can I help you with coding today?',\n        role: 'assistant',\n        timestamp: Date.now() - 3600000\n      },\n      {\n        id: 'msg2',\n        content: 'I have a bug in my JavaScript code. The event listener is not working.',\n        role: 'user',\n        timestamp: Date.now() - 3500000\n      },\n      {\n        id: 'msg3',\n        content: 'Let\\'s take a look. Can you share the code that\\'s not working?',\n        role: 'assistant',\n        timestamp: Date.now() - 3400000\n      }\n    ],\n    createdAt: Date.now() - 3600000,\n    updatedAt: Date.now() - 3400000,\n    isSaved: true\n  },\n  {\n    id: 'session2',\n    agentId: '1',\n    title: 'Sales Data Analysis',\n    messages: [\n      {\n        id: 'msg1',\n        content: 'Welcome to Data Analyst. What data would you like to analyze today?',\n        role: 'assistant',\n        timestamp: Date.now() - 86400000\n      },\n      {\n        id: 'msg2',\n        content: 'I need to analyze our Q1 sales data to find trends.',\n        role: 'user',\n        timestamp: Date.now() - 86300000\n      },\n      {\n        id: 'msg3',\n        content: 'I can help with that. Do you have the sales data file you can upload?',\n        role: 'assistant',\n        timestamp: Date.now() - 86200000\n      }\n    ],\n    createdAt: Date.now() - 86400000,\n    updatedAt: Date.now() - 86200000,\n    isSaved: true\n  }\n];\n\n// Function to get all agents\nexport function getAllAgents(): Agent[] {\n  return agents;\n}\n\n// Function to get an agent by ID\nexport function getAgentById(id: string): Agent | undefined {\n  return agents.find(agent => agent.id === id);\n}\n\n// Function to get featured agents\nexport function getFeaturedAgents(): Agent[] {\n  return agents.filter(agent => agent.isFeatured);\n}\n\n// Function to get new agents\nexport function getNewAgents(): Agent[] {\n  return agents.filter(agent => agent.isNew);\n}\n\n// Function to get agents by category\nexport function getAgentsByCategory(category: string): Agent[] {\n  return agents.filter(agent => agent.category === category);\n}\n\n// Function to get agents by tag\nexport function getAgentsByTag(tag: string): Agent[] {\n  return agents.filter(agent => agent.tags?.includes(tag));\n}\n\n// Function to get all unique categories\nexport function getAllCategories(): string[] {\n  const categories = new Set<string>();\n  agents.forEach(agent => categories.add(agent.category));\n  return Array.from(categories).sort();\n}\n\n// Function to get all unique tags\nexport function getAllTags(): string[] {\n  const tags = new Set<string>();\n  agents.forEach(agent => {\n    agent.tags?.forEach(tag => tags.add(tag));\n  });\n  return Array.from(tags).sort();\n}\n\n// Function to get related agents\nexport function getRelatedAgents(agentId: string): Agent[] {\n  const agent = getAgentById(agentId);\n  if (!agent || !agent.relatedAgentIds || agent.relatedAgentIds.length === 0) {\n    return [];\n  }\n\n  return agent.relatedAgentIds\n    .map(id => getAgentById(id))\n    .filter((agent): agent is Agent => agent !== undefined);\n}\n\n// Function to get agents by IDs\nexport function getAgentsByIds(ids: string[]): Agent[] {\n  return ids\n    .map(id => getAgentById(id))\n    .filter((agent): agent is Agent => agent !== undefined);\n}\n\n// Function to search agents\nexport function searchAgents(query: string): Agent[] {\n  if (!query) return agents;\n\n  const lowercaseQuery = query.toLowerCase();\n  return agents.filter(agent =>\n    agent.name.toLowerCase().includes(lowercaseQuery) ||\n    agent.description.toLowerCase().includes(lowercaseQuery) ||\n    agent.category.toLowerCase().includes(lowercaseQuery) ||\n    agent.tags?.some(tag => tag.toLowerCase().includes(lowercaseQuery)) ||\n    agent.capabilities.some(capability => capability.toLowerCase().includes(lowercaseQuery))\n  );\n}\n\n// Function to get sessions for an agent\nexport function getSessionsForAgent(agentId: string): Session[] {\n  return sessions.filter(session => session.agentId === agentId);\n}\n\n// Function to get a session by ID\nexport function getSessionById(sessionId: string): Session | undefined {\n  return sessions.find(session => session.id === sessionId);\n}\n\n// Function to create a new message\nexport function createMessage(content: string, role: 'user' | 'assistant' | 'system'): Message {\n  return {\n    id: `msg_${Date.now()}`,\n    content,\n    role,\n    timestamp: Date.now()\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAEA,uBAAuB;AACvB,MAAM,SAAkB;IACtB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAQ;YAAa;YAAiB;SAAa;QAC1D,iBAAiB;YAAC;YAAK;YAAK;SAAI;IAClC;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAU;YAAe;YAAe;SAAY;QAC3D,iBAAiB;YAAC;YAAK;SAAK;IAC9B;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAY;YAAU;YAAY;SAAa;QACtD,iBAAiB;YAAC;YAAM;SAAI;IAC9B;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAY;YAAgB;YAAiB;SAAY;QAChE,iBAAiB;YAAC;YAAK;YAAK;SAAK;IACnC;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAa;YAAY;YAAc;SAAY;QAC1D,iBAAiB;YAAC;YAAK;YAAK;SAAI;IAClC;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAiB;YAAU;YAAU;SAAU;QACtD,iBAAiB;YAAC;YAAK;SAAK;IAC9B;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAiB;YAAU;YAAU;SAAO;QACnD,iBAAiB;YAAC;YAAK;SAAI;IAC7B;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAe;YAAe;YAAa;SAAS;QAC3D,iBAAiB;YAAC;YAAK;SAAI;IAC7B;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAiB;YAAO;YAAe;SAAY;QAC1D,iBAAiB;YAAC;YAAK;SAAK;IAC9B;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAe;YAAW;YAAY;SAAiB;QAC9D,iBAAiB;YAAC;YAAK;SAAI;IAC7B;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAqB;YAAY;YAAY;SAAS;QAC7D,iBAAiB;YAAC;SAAI;IACxB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAsB;YAAS;YAAY;SAAe;QACjE,iBAAiB;YAAC;YAAK;SAAK;IAC9B;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAW;YAAW;YAAa;SAAe;QACzD,iBAAiB;YAAC;YAAK;SAAK;IAC9B;CACD;AAED,yBAAyB;AACzB,MAAM,WAAsB;IAC1B;QACE,IAAI;QACJ,SAAS;QACT,OAAO;QACP,UAAU;YACR;gBACE,IAAI;gBACJ,SAAS;gBACT,MAAM;gBACN,WAAW,KAAK,GAAG,KAAK;YAC1B;YACA;gBACE,IAAI;gBACJ,SAAS;gBACT,MAAM;gBACN,WAAW,KAAK,GAAG,KAAK;YAC1B;YACA;gBACE,IAAI;gBACJ,SAAS;gBACT,MAAM;gBACN,WAAW,KAAK,GAAG,KAAK;YAC1B;SACD;QACD,WAAW,KAAK,GAAG,KAAK;QACxB,WAAW,KAAK,GAAG,KAAK;QACxB,SAAS;IACX;IACA;QACE,IAAI;QACJ,SAAS;QACT,OAAO;QACP,UAAU;YACR;gBACE,IAAI;gBACJ,SAAS;gBACT,MAAM;gBACN,WAAW,KAAK,GAAG,KAAK;YAC1B;YACA;gBACE,IAAI;gBACJ,SAAS;gBACT,MAAM;gBACN,WAAW,KAAK,GAAG,KAAK;YAC1B;YACA;gBACE,IAAI;gBACJ,SAAS;gBACT,MAAM;gBACN,WAAW,KAAK,GAAG,KAAK;YAC1B;SACD;QACD,WAAW,KAAK,GAAG,KAAK;QACxB,WAAW,KAAK,GAAG,KAAK;QACxB,SAAS;IACX;CACD;AAGM,SAAS;IACd,OAAO;AACT;AAGO,SAAS,aAAa,EAAU;IACrC,OAAO,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;AAC3C;AAGO,SAAS;IACd,OAAO,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,UAAU;AAChD;AAGO,SAAS;IACd,OAAO,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,KAAK;AAC3C;AAGO,SAAS,oBAAoB,QAAgB;IAClD,OAAO,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,QAAQ,KAAK;AACnD;AAGO,SAAS,eAAe,GAAW;IACxC,OAAO,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,IAAI,EAAE,SAAS;AACrD;AAGO,SAAS;IACd,MAAM,aAAa,IAAI;IACvB,OAAO,OAAO,CAAC,CAAA,QAAS,WAAW,GAAG,CAAC,MAAM,QAAQ;IACrD,OAAO,MAAM,IAAI,CAAC,YAAY,IAAI;AACpC;AAGO,SAAS;IACd,MAAM,OAAO,IAAI;IACjB,OAAO,OAAO,CAAC,CAAA;QACb,MAAM,IAAI,EAAE,QAAQ,CAAA,MAAO,KAAK,GAAG,CAAC;IACtC;IACA,OAAO,MAAM,IAAI,CAAC,MAAM,IAAI;AAC9B;AAGO,SAAS,iBAAiB,OAAe;IAC9C,MAAM,QAAQ,aAAa;IAC3B,IAAI,CAAC,SAAS,CAAC,MAAM,eAAe,IAAI,MAAM,eAAe,CAAC,MAAM,KAAK,GAAG;QAC1E,OAAO,EAAE;IACX;IAEA,OAAO,MAAM,eAAe,CACzB,GAAG,CAAC,CAAA,KAAM,aAAa,KACvB,MAAM,CAAC,CAAC,QAA0B,UAAU;AACjD;AAGO,SAAS,eAAe,GAAa;IAC1C,OAAO,IACJ,GAAG,CAAC,CAAA,KAAM,aAAa,KACvB,MAAM,CAAC,CAAC,QAA0B,UAAU;AACjD;AAGO,SAAS,aAAa,KAAa;IACxC,IAAI,CAAC,OAAO,OAAO;IAEnB,MAAM,iBAAiB,MAAM,WAAW;IACxC,OAAO,OAAO,MAAM,CAAC,CAAA,QACnB,MAAM,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,mBAClC,MAAM,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,mBACzC,MAAM,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,mBACtC,MAAM,IAAI,EAAE,KAAK,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC,oBACnD,MAAM,YAAY,CAAC,IAAI,CAAC,CAAA,aAAc,WAAW,WAAW,GAAG,QAAQ,CAAC;AAE5E;AAGO,SAAS,oBAAoB,OAAe;IACjD,OAAO,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,OAAO,KAAK;AACxD;AAGO,SAAS,eAAe,SAAiB;IAC9C,OAAO,SAAS,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;AACjD;AAGO,SAAS,cAAc,OAAe,EAAE,IAAqC;IAClF,OAAO;QACL,IAAI,CAAC,IAAI,EAAE,KAAK,GAAG,IAAI;QACvB;QACA;QACA,WAAW,KAAK,GAAG;IACrB;AACF", "debugId": null}}, {"offset": {"line": 584, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/agent/%5Bid%5D/components/AgentHeader.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { Agent } from '@/types/agent';\n\ninterface AgentHeaderProps {\n  agent: Agent;\n}\n\nexport default function AgentHeader({ agent }: AgentHeaderProps) {\n  const [isFavorite, setIsFavorite] = useState(false);\n\n  return (\n    <div className=\"mb-8 border-b border-gray-200 pb-6 dark:border-gray-800\">\n      <div className=\"mb-4 flex items-center justify-between\">\n        <Link\n          href=\"/\"\n          className=\"flex items-center text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300\"\n        >\n          <svg\n            xmlns=\"http://www.w3.org/2000/svg\"\n            width=\"24\"\n            height=\"24\"\n            viewBox=\"0 0 24 24\"\n            fill=\"none\"\n            stroke=\"currentColor\"\n            strokeWidth=\"2\"\n            strokeLinecap=\"round\"\n            strokeLinejoin=\"round\"\n            className=\"mr-2 h-4 w-4\"\n          >\n            <path d=\"m15 18-6-6 6-6\" />\n          </svg>\n          Back to Home\n        </Link>\n        <div className=\"flex items-center space-x-2\">\n          <button\n            onClick={() => setIsFavorite(!isFavorite)}\n            className=\"rounded-full p-2 text-gray-500 hover:bg-gray-100 hover:text-gray-700 dark:text-gray-400 dark:hover:bg-gray-800 dark:hover:text-gray-300\"\n            aria-label={isFavorite ? \"Remove from favorites\" : \"Add to favorites\"}\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              width=\"24\"\n              height=\"24\"\n              viewBox=\"0 0 24 24\"\n              fill={isFavorite ? \"currentColor\" : \"none\"}\n              stroke=\"currentColor\"\n              strokeWidth=\"2\"\n              strokeLinecap=\"round\"\n              strokeLinejoin=\"round\"\n              className=\"h-5 w-5\"\n            >\n              <path d=\"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z\" />\n            </svg>\n          </button>\n          <button\n            className=\"rounded-full p-2 text-gray-500 hover:bg-gray-100 hover:text-gray-700 dark:text-gray-400 dark:hover:bg-gray-800 dark:hover:text-gray-300\"\n            aria-label=\"Share agent\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              width=\"24\"\n              height=\"24\"\n              viewBox=\"0 0 24 24\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              strokeWidth=\"2\"\n              strokeLinecap=\"round\"\n              strokeLinejoin=\"round\"\n              className=\"h-5 w-5\"\n            >\n              <path d=\"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8\" />\n              <polyline points=\"16 6 12 2 8 6\" />\n              <line x1=\"12\" x2=\"12\" y1=\"2\" y2=\"15\" />\n            </svg>\n          </button>\n        </div>\n      </div>\n      <div className=\"flex items-start justify-between\">\n        <div>\n          <div className=\"flex items-center\">\n            <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white md:text-3xl\">{agent.name}</h1>\n            {agent.isNew && (\n              <span className=\"ml-3 rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800 dark:bg-green-900/30 dark:text-green-400\">\n                New\n              </span>\n            )}\n          </div>\n          <div className=\"mt-2 flex items-center text-sm text-gray-500 dark:text-gray-400\">\n            <span className=\"mr-2 rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800 dark:bg-gray-800 dark:text-gray-200\">\n              {agent.category}\n            </span>\n            <span className=\"mr-2\">•</span>\n            <span>Version {agent.version}</span>\n            <span className=\"mx-2\">•</span>\n            <span>Updated {new Date(agent.updatedAt).toLocaleDateString()}</span>\n            <span className=\"mx-2\">•</span>\n            <span>{agent.usageCount.toLocaleString()} uses</span>\n          </div>\n        </div>\n        <button className=\"rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800\">\n          New Chat\n        </button>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAUe,SAAS,YAAY,EAAE,KAAK,EAAoB;;IAC7D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;;0CAEV,6LAAC;gCACC,OAAM;gCACN,OAAM;gCACN,QAAO;gCACP,SAAQ;gCACR,MAAK;gCACL,QAAO;gCACP,aAAY;gCACZ,eAAc;gCACd,gBAAe;gCACf,WAAU;0CAEV,cAAA,6LAAC;oCAAK,GAAE;;;;;;;;;;;4BACJ;;;;;;;kCAGR,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,cAAc,CAAC;gCAC9B,WAAU;gCACV,cAAY,aAAa,0BAA0B;0CAEnD,cAAA,6LAAC;oCACC,OAAM;oCACN,OAAM;oCACN,QAAO;oCACP,SAAQ;oCACR,MAAM,aAAa,iBAAiB;oCACpC,QAAO;oCACP,aAAY;oCACZ,eAAc;oCACd,gBAAe;oCACf,WAAU;8CAEV,cAAA,6LAAC;wCAAK,GAAE;;;;;;;;;;;;;;;;0CAGZ,6LAAC;gCACC,WAAU;gCACV,cAAW;0CAEX,cAAA,6LAAC;oCACC,OAAM;oCACN,OAAM;oCACN,QAAO;oCACP,SAAQ;oCACR,MAAK;oCACL,QAAO;oCACP,aAAY;oCACZ,eAAc;oCACd,gBAAe;oCACf,WAAU;;sDAEV,6LAAC;4CAAK,GAAE;;;;;;sDACR,6LAAC;4CAAS,QAAO;;;;;;sDACjB,6LAAC;4CAAK,IAAG;4CAAK,IAAG;4CAAK,IAAG;4CAAI,IAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAKxC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAgE,MAAM,IAAI;;;;;;oCACvF,MAAM,KAAK,kBACV,6LAAC;wCAAK,WAAU;kDAA2H;;;;;;;;;;;;0CAK/I,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDACb,MAAM,QAAQ;;;;;;kDAEjB,6LAAC;wCAAK,WAAU;kDAAO;;;;;;kDACvB,6LAAC;;4CAAK;4CAAS,MAAM,OAAO;;;;;;;kDAC5B,6LAAC;wCAAK,WAAU;kDAAO;;;;;;kDACvB,6LAAC;;4CAAK;4CAAS,IAAI,KAAK,MAAM,SAAS,EAAE,kBAAkB;;;;;;;kDAC3D,6LAAC;wCAAK,WAAU;kDAAO;;;;;;kDACvB,6LAAC;;4CAAM,MAAM,UAAU,CAAC,cAAc;4CAAG;;;;;;;;;;;;;;;;;;;kCAG7C,6LAAC;wBAAO,WAAU;kCAA4H;;;;;;;;;;;;;;;;;;AAMtJ;GAlGwB;KAAA", "debugId": null}}, {"offset": {"line": 876, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/agent/%5Bid%5D/components/AgentInfo.tsx"], "sourcesContent": ["import { Agent } from '@/types/agent';\n\ninterface AgentInfoProps {\n  agent: Agent;\n}\n\nexport default function AgentInfo({ agent }: AgentInfoProps) {\n  return (\n    <div className=\"mb-8 space-y-6\">\n      {/* About section - now full width */}\n      <div className=\"rounded-lg border border-gray-200 bg-white p-6 dark:border-gray-800 dark:bg-gray-900\">\n        <h2 className=\"mb-4 text-xl font-bold text-gray-900 dark:text-white\">About</h2>\n        <p className=\"mb-4 text-gray-700 dark:text-gray-300\">\n          {agent.longDescription || agent.description}\n        </p>\n        <h3 className=\"mb-2 mt-6 text-lg font-semibold text-gray-900 dark:text-white\">Capabilities</h3>\n        <ul className=\"list-inside list-disc space-y-1 text-gray-700 dark:text-gray-300\">\n          {agent.capabilities.map((capability, index) => (\n            <li key={index}>{capability}</li>\n          ))}\n        </ul>\n      </div>\n\n      {/* Details section - now full width */}\n      <div className=\"rounded-lg border border-gray-200 bg-white p-6 dark:border-gray-800 dark:bg-gray-900\">\n        <h2 className=\"mb-4 text-xl font-bold text-gray-900 dark:text-white\">Details</h2>\n        <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3\">\n          <div>\n            <h3 className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Created by</h3>\n            <p className=\"text-gray-900 dark:text-white\">{agent.creator || 'Internal Team'}</p>\n          </div>\n          <div>\n            <h3 className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Created on</h3>\n            <p className=\"text-gray-900 dark:text-white\">{new Date(agent.createdAt).toLocaleDateString()}</p>\n          </div>\n          <div>\n            <h3 className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Last updated</h3>\n            <p className=\"text-gray-900 dark:text-white\">{new Date(agent.updatedAt).toLocaleDateString()}</p>\n          </div>\n          <div>\n            <h3 className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Version</h3>\n            <p className=\"text-gray-900 dark:text-white\">{agent.version}</p>\n          </div>\n          <div>\n            <h3 className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Category</h3>\n            <p className=\"text-gray-900 dark:text-white\">{agent.category}</p>\n          </div>\n          <div>\n            <h3 className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Usage</h3>\n            <p className=\"text-gray-900 dark:text-white\">{agent.usageCount.toLocaleString()} times</p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAMe,SAAS,UAAU,EAAE,KAAK,EAAkB;IACzD,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAuD;;;;;;kCACrE,6LAAC;wBAAE,WAAU;kCACV,MAAM,eAAe,IAAI,MAAM,WAAW;;;;;;kCAE7C,6LAAC;wBAAG,WAAU;kCAAgE;;;;;;kCAC9E,6LAAC;wBAAG,WAAU;kCACX,MAAM,YAAY,CAAC,GAAG,CAAC,CAAC,YAAY,sBACnC,6LAAC;0CAAgB;+BAAR;;;;;;;;;;;;;;;;0BAMf,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAuD;;;;;;kCACrE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAuD;;;;;;kDACrE,6LAAC;wCAAE,WAAU;kDAAiC,MAAM,OAAO,IAAI;;;;;;;;;;;;0CAEjE,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAuD;;;;;;kDACrE,6LAAC;wCAAE,WAAU;kDAAiC,IAAI,KAAK,MAAM,SAAS,EAAE,kBAAkB;;;;;;;;;;;;0CAE5F,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAuD;;;;;;kDACrE,6LAAC;wCAAE,WAAU;kDAAiC,IAAI,KAAK,MAAM,SAAS,EAAE,kBAAkB;;;;;;;;;;;;0CAE5F,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAuD;;;;;;kDACrE,6LAAC;wCAAE,WAAU;kDAAiC,MAAM,OAAO;;;;;;;;;;;;0CAE7D,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAuD;;;;;;kDACrE,6LAAC;wCAAE,WAAU;kDAAiC,MAAM,QAAQ;;;;;;;;;;;;0CAE9D,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAuD;;;;;;kDACrE,6LAAC;wCAAE,WAAU;;4CAAiC,MAAM,UAAU,CAAC,cAAc;4CAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM5F;KAjDwB", "debugId": null}}, {"offset": {"line": 1124, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/agent/%5Bid%5D/components/ChatInterface.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef, useEffect } from 'react';\nimport { Message } from '@/types/agent';\nimport { createMessage } from '@/lib/agents';\n\ninterface ChatInterfaceProps {\n  agentName: string;\n  initialMessages?: Message[];\n}\n\nexport default function ChatInterface({ agentName, initialMessages = [] }: ChatInterfaceProps) {\n  const [messages, setMessages] = useState<Message[]>(initialMessages.length > 0 \n    ? initialMessages \n    : [createMessage(`Hello! I'm ${agentName}. How can I help you today?`, 'assistant')]);\n  \n  const [input, setInput] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n\n  // Scroll to bottom of messages\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  // Handle sending a message\n  const handleSendMessage = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!input.trim()) return;\n    \n    // Add user message\n    const userMessage = createMessage(input, 'user');\n    setMessages(prev => [...prev, userMessage]);\n    setInput('');\n    setIsLoading(true);\n    \n    // Simulate agent response after a delay\n    setTimeout(() => {\n      const responseMessage = createMessage(\n        `This is a simulated response from ${agentName}. In a real application, this would be generated by the AI agent based on your message: \"${input}\"`,\n        'assistant'\n      );\n      setMessages(prev => [...prev, responseMessage]);\n      setIsLoading(false);\n    }, 1500);\n  };\n\n  return (\n    <div className=\"flex h-[600px] flex-col rounded-lg border border-gray-200 bg-white dark:border-gray-800 dark:bg-gray-900\">\n      {/* Chat header */}\n      <div className=\"border-b border-gray-200 p-4 dark:border-gray-800\">\n        <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white\">Chat with {agentName}</h2>\n      </div>\n      \n      {/* Messages area */}\n      <div className=\"flex-1 overflow-y-auto p-4\">\n        <div className=\"space-y-4\">\n          {messages.map((message) => (\n            <div\n              key={message.id}\n              className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}\n            >\n              <div\n                className={`max-w-[80%] rounded-lg px-4 py-2 ${\n                  message.role === 'user'\n                    ? 'bg-blue-600 text-white'\n                    : 'bg-gray-100 text-gray-900 dark:bg-gray-800 dark:text-white'\n                }`}\n              >\n                <p className=\"whitespace-pre-wrap\">{message.content}</p>\n                <div\n                  className={`mt-1 text-xs ${\n                    message.role === 'user' ? 'text-blue-200' : 'text-gray-500 dark:text-gray-400'\n                  }`}\n                >\n                  {new Date(message.timestamp).toLocaleTimeString()}\n                </div>\n              </div>\n            </div>\n          ))}\n          {isLoading && (\n            <div className=\"flex justify-start\">\n              <div className=\"max-w-[80%] rounded-lg bg-gray-100 px-4 py-2 dark:bg-gray-800\">\n                <div className=\"flex space-x-2\">\n                  <div className=\"h-2 w-2 animate-bounce rounded-full bg-gray-500 dark:bg-gray-400\"></div>\n                  <div className=\"h-2 w-2 animate-bounce rounded-full bg-gray-500 dark:bg-gray-400\" style={{ animationDelay: '0.2s' }}></div>\n                  <div className=\"h-2 w-2 animate-bounce rounded-full bg-gray-500 dark:bg-gray-400\" style={{ animationDelay: '0.4s' }}></div>\n                </div>\n              </div>\n            </div>\n          )}\n          <div ref={messagesEndRef} />\n        </div>\n      </div>\n      \n      {/* Input area */}\n      <div className=\"border-t border-gray-200 p-4 dark:border-gray-800\">\n        <form onSubmit={handleSendMessage} className=\"flex space-x-2\">\n          <input\n            type=\"text\"\n            value={input}\n            onChange={(e) => setInput(e.target.value)}\n            placeholder=\"Type your message...\"\n            className=\"flex-1 rounded-md border border-gray-300 px-4 py-2 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white dark:focus:border-blue-600 dark:focus:ring-blue-600\"\n            disabled={isLoading}\n          />\n          <button\n            type=\"submit\"\n            disabled={isLoading || !input.trim()}\n            className=\"rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700 disabled:cursor-not-allowed disabled:opacity-50 dark:bg-blue-700 dark:hover:bg-blue-800\"\n          >\n            Send\n          </button>\n        </form>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;;;AAJA;;;AAWe,SAAS,cAAc,EAAE,SAAS,EAAE,kBAAkB,EAAE,EAAsB;;IAC3F,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,gBAAgB,MAAM,GAAG,IACzE,kBACA;QAAC,CAAA,GAAA,uHAAA,CAAA,gBAAa,AAAD,EAAE,CAAC,WAAW,EAAE,UAAU,2BAA2B,CAAC,EAAE;KAAa;IAEtF,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE9C,+BAA+B;IAC/B,MAAM,iBAAiB;QACrB,eAAe,OAAO,EAAE,eAAe;YAAE,UAAU;QAAS;IAC9D;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR;QACF;kCAAG;QAAC;KAAS;IAEb,2BAA2B;IAC3B,MAAM,oBAAoB,OAAO;QAC/B,EAAE,cAAc;QAEhB,IAAI,CAAC,MAAM,IAAI,IAAI;QAEnB,mBAAmB;QACnB,MAAM,cAAc,CAAA,GAAA,uHAAA,CAAA,gBAAa,AAAD,EAAE,OAAO;QACzC,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QAC1C,SAAS;QACT,aAAa;QAEb,wCAAwC;QACxC,WAAW;YACT,MAAM,kBAAkB,CAAA,GAAA,uHAAA,CAAA,gBAAa,AAAD,EAClC,CAAC,kCAAkC,EAAE,UAAU,yFAAyF,EAAE,MAAM,CAAC,CAAC,EAClJ;YAEF,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAgB;YAC9C,aAAa;QACf,GAAG;IACL;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAG,WAAU;;wBAAsD;wBAAW;;;;;;;;;;;;0BAIjF,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;wBACZ,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;gCAEC,WAAW,CAAC,KAAK,EAAE,QAAQ,IAAI,KAAK,SAAS,gBAAgB,iBAAiB;0CAE9E,cAAA,6LAAC;oCACC,WAAW,CAAC,iCAAiC,EAC3C,QAAQ,IAAI,KAAK,SACb,2BACA,8DACJ;;sDAEF,6LAAC;4CAAE,WAAU;sDAAuB,QAAQ,OAAO;;;;;;sDACnD,6LAAC;4CACC,WAAW,CAAC,aAAa,EACvB,QAAQ,IAAI,KAAK,SAAS,kBAAkB,oCAC5C;sDAED,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB;;;;;;;;;;;;+BAhB9C,QAAQ,EAAE;;;;;wBAqBlB,2BACC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;4CAAmE,OAAO;gDAAE,gBAAgB;4CAAO;;;;;;sDAClH,6LAAC;4CAAI,WAAU;4CAAmE,OAAO;gDAAE,gBAAgB;4CAAO;;;;;;;;;;;;;;;;;;;;;;sCAK1H,6LAAC;4BAAI,KAAK;;;;;;;;;;;;;;;;;0BAKd,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAK,UAAU;oBAAmB,WAAU;;sCAC3C,6LAAC;4BACC,MAAK;4BACL,OAAO;4BACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4BACxC,aAAY;4BACZ,WAAU;4BACV,UAAU;;;;;;sCAEZ,6LAAC;4BACC,MAAK;4BACL,UAAU,aAAa,CAAC,MAAM,IAAI;4BAClC,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAOX;GA/GwB;KAAA", "debugId": null}}, {"offset": {"line": 1363, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/agent/%5Bid%5D/components/SessionHistory.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Session } from '@/types/agent';\n\ninterface SessionHistoryProps {\n  sessions: Session[];\n  onSelectSession: (session: Session) => void;\n}\n\nexport default function SessionHistory({ sessions, onSelectSession }: SessionHistoryProps) {\n  const [isExpanded, setIsExpanded] = useState(true);\n\n  // Format timestamp to readable date\n  const formatDate = (timestamp: number) => {\n    const date = new Date(timestamp);\n    const today = new Date();\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n    \n    if (date.toDateString() === today.toDateString()) {\n      return `Today at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;\n    } else if (date.toDateString() === yesterday.toDateString()) {\n      return `Yesterday at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;\n    } else {\n      return date.toLocaleDateString([], { month: 'short', day: 'numeric' }) + \n             ` at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;\n    }\n  };\n\n  if (sessions.length === 0) {\n    return (\n      <div className=\"mb-6 rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900\">\n        <div className=\"flex items-center justify-between\">\n          <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white\">Session History</h2>\n          <button\n            onClick={() => setIsExpanded(!isExpanded)}\n            className=\"rounded p-1 text-gray-500 hover:bg-gray-100 hover:text-gray-700 dark:text-gray-400 dark:hover:bg-gray-800 dark:hover:text-gray-300\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              width=\"24\"\n              height=\"24\"\n              viewBox=\"0 0 24 24\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              strokeWidth=\"2\"\n              strokeLinecap=\"round\"\n              strokeLinejoin=\"round\"\n              className={`h-5 w-5 transition-transform ${isExpanded ? 'rotate-180' : ''}`}\n            >\n              <path d=\"m18 15-6-6-6 6\" />\n            </svg>\n          </button>\n        </div>\n        {isExpanded && (\n          <div className=\"mt-4 text-center text-gray-500 dark:text-gray-400\">\n            <p>No previous sessions found.</p>\n            <p className=\"mt-2 text-sm\">Start a new chat to begin.</p>\n          </div>\n        )}\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"mb-6 rounded-lg border border-gray-200 bg-white dark:border-gray-800 dark:bg-gray-900\">\n      <div className=\"flex items-center justify-between border-b border-gray-200 p-4 dark:border-gray-800\">\n        <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white\">Session History</h2>\n        <button\n          onClick={() => setIsExpanded(!isExpanded)}\n          className=\"rounded p-1 text-gray-500 hover:bg-gray-100 hover:text-gray-700 dark:text-gray-400 dark:hover:bg-gray-800 dark:hover:text-gray-300\"\n        >\n          <svg\n            xmlns=\"http://www.w3.org/2000/svg\"\n            width=\"24\"\n            height=\"24\"\n            viewBox=\"0 0 24 24\"\n            fill=\"none\"\n            stroke=\"currentColor\"\n            strokeWidth=\"2\"\n            strokeLinecap=\"round\"\n            strokeLinejoin=\"round\"\n            className={`h-5 w-5 transition-transform ${isExpanded ? 'rotate-180' : ''}`}\n          >\n            <path d=\"m18 15-6-6-6 6\" />\n          </svg>\n        </button>\n      </div>\n      \n      {isExpanded && (\n        <div className=\"max-h-80 overflow-y-auto p-4\">\n          <div className=\"space-y-2\">\n            {sessions.map((session) => (\n              <button\n                key={session.id}\n                onClick={() => onSelectSession(session)}\n                className=\"w-full rounded-md border border-gray-200 bg-white p-3 text-left hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:hover:bg-gray-700\"\n              >\n                <div className=\"flex items-center justify-between\">\n                  <h3 className=\"font-medium text-gray-900 dark:text-white\">{session.title}</h3>\n                  {session.isSaved && (\n                    <span className=\"rounded-full bg-blue-100 px-2 py-0.5 text-xs font-medium text-blue-800 dark:bg-blue-900/30 dark:text-blue-400\">\n                      Saved\n                    </span>\n                  )}\n                </div>\n                <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\n                  {formatDate(session.updatedAt)}\n                </p>\n                <p className=\"mt-2 line-clamp-2 text-sm text-gray-600 dark:text-gray-300\">\n                  {session.messages[session.messages.length - 1]?.content || 'No messages'}\n                </p>\n              </button>\n            ))}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAUe,SAAS,eAAe,EAAE,QAAQ,EAAE,eAAe,EAAuB;;IACvF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,oCAAoC;IACpC,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,QAAQ,IAAI;QAClB,MAAM,YAAY,IAAI,KAAK;QAC3B,UAAU,OAAO,CAAC,UAAU,OAAO,KAAK;QAExC,IAAI,KAAK,YAAY,OAAO,MAAM,YAAY,IAAI;YAChD,OAAO,CAAC,SAAS,EAAE,KAAK,kBAAkB,CAAC,EAAE,EAAE;gBAAE,MAAM;gBAAW,QAAQ;YAAU,IAAI;QAC1F,OAAO,IAAI,KAAK,YAAY,OAAO,UAAU,YAAY,IAAI;YAC3D,OAAO,CAAC,aAAa,EAAE,KAAK,kBAAkB,CAAC,EAAE,EAAE;gBAAE,MAAM;gBAAW,QAAQ;YAAU,IAAI;QAC9F,OAAO;YACL,OAAO,KAAK,kBAAkB,CAAC,EAAE,EAAE;gBAAE,OAAO;gBAAS,KAAK;YAAU,KAC7D,CAAC,IAAI,EAAE,KAAK,kBAAkB,CAAC,EAAE,EAAE;gBAAE,MAAM;gBAAW,QAAQ;YAAU,IAAI;QACrF;IACF;IAEA,IAAI,SAAS,MAAM,KAAK,GAAG;QACzB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsD;;;;;;sCACpE,6LAAC;4BACC,SAAS,IAAM,cAAc,CAAC;4BAC9B,WAAU;sCAEV,cAAA,6LAAC;gCACC,OAAM;gCACN,OAAM;gCACN,QAAO;gCACP,SAAQ;gCACR,MAAK;gCACL,QAAO;gCACP,aAAY;gCACZ,eAAc;gCACd,gBAAe;gCACf,WAAW,CAAC,6BAA6B,EAAE,aAAa,eAAe,IAAI;0CAE3E,cAAA,6LAAC;oCAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;gBAIb,4BACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;sCAAE;;;;;;sCACH,6LAAC;4BAAE,WAAU;sCAAe;;;;;;;;;;;;;;;;;;IAKtC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAsD;;;;;;kCACpE,6LAAC;wBACC,SAAS,IAAM,cAAc,CAAC;wBAC9B,WAAU;kCAEV,cAAA,6LAAC;4BACC,OAAM;4BACN,OAAM;4BACN,QAAO;4BACP,SAAQ;4BACR,MAAK;4BACL,QAAO;4BACP,aAAY;4BACZ,eAAc;4BACd,gBAAe;4BACf,WAAW,CAAC,6BAA6B,EAAE,aAAa,eAAe,IAAI;sCAE3E,cAAA,6LAAC;gCAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;YAKb,4BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;4BAEC,SAAS,IAAM,gBAAgB;4BAC/B,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA6C,QAAQ,KAAK;;;;;;wCACvE,QAAQ,OAAO,kBACd,6LAAC;4CAAK,WAAU;sDAAgH;;;;;;;;;;;;8CAKpI,6LAAC;oCAAE,WAAU;8CACV,WAAW,QAAQ,SAAS;;;;;;8CAE/B,6LAAC;oCAAE,WAAU;8CACV,QAAQ,QAAQ,CAAC,QAAQ,QAAQ,CAAC,MAAM,GAAG,EAAE,EAAE,WAAW;;;;;;;2BAhBxD,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;AAyB/B;GA9GwB;KAAA", "debugId": null}}, {"offset": {"line": 1620, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/agent/%5Bid%5D/components/SessionControls.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\ninterface SessionControlsProps {\n  onSaveSession: () => void;\n  onClearChat: () => void;\n  onExportChat: () => void;\n}\n\nexport default function SessionControls({ onSaveSession, onClearChat, onExportChat }: SessionControlsProps) {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  return (\n    <div className=\"mb-6 flex items-center justify-between rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900\">\n      <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white\">Current Session</h2>\n      \n      <div className=\"relative\">\n        <button\n          onClick={() => setIsMenuOpen(!isMenuOpen)}\n          className=\"rounded-md border border-gray-300 bg-white px-3 py-1.5 text-sm font-medium text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700\"\n        >\n          Actions\n          <svg\n            xmlns=\"http://www.w3.org/2000/svg\"\n            width=\"24\"\n            height=\"24\"\n            viewBox=\"0 0 24 24\"\n            fill=\"none\"\n            stroke=\"currentColor\"\n            strokeWidth=\"2\"\n            strokeLinecap=\"round\"\n            strokeLinejoin=\"round\"\n            className=\"ml-1 inline-block h-4 w-4\"\n          >\n            <path d=\"m6 9 6 6 6-6\" />\n          </svg>\n        </button>\n        \n        {isMenuOpen && (\n          <div className=\"absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-gray-800 dark:ring-gray-700\">\n            <div className=\"py-1\">\n              <button\n                onClick={() => {\n                  onSaveSession();\n                  setIsMenuOpen(false);\n                }}\n                className=\"block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700\"\n              >\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  width=\"24\"\n                  height=\"24\"\n                  viewBox=\"0 0 24 24\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  strokeWidth=\"2\"\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  className=\"mr-2 inline-block h-4 w-4\"\n                >\n                  <path d=\"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z\" />\n                  <polyline points=\"17 21 17 13 7 13 7 21\" />\n                  <polyline points=\"7 3 7 8 15 8\" />\n                </svg>\n                Save Session\n              </button>\n              <button\n                onClick={() => {\n                  onExportChat();\n                  setIsMenuOpen(false);\n                }}\n                className=\"block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700\"\n              >\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  width=\"24\"\n                  height=\"24\"\n                  viewBox=\"0 0 24 24\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  strokeWidth=\"2\"\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  className=\"mr-2 inline-block h-4 w-4\"\n                >\n                  <path d=\"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4\" />\n                  <polyline points=\"7 10 12 15 17 10\" />\n                  <line x1=\"12\" x2=\"12\" y1=\"15\" y2=\"3\" />\n                </svg>\n                Export Chat\n              </button>\n              <button\n                onClick={() => {\n                  onClearChat();\n                  setIsMenuOpen(false);\n                }}\n                className=\"block w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-gray-100 dark:text-red-400 dark:hover:bg-gray-700\"\n              >\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  width=\"24\"\n                  height=\"24\"\n                  viewBox=\"0 0 24 24\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  strokeWidth=\"2\"\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  className=\"mr-2 inline-block h-4 w-4\"\n                >\n                  <path d=\"M3 6h18\" />\n                  <path d=\"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6\" />\n                  <path d=\"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2\" />\n                  <line x1=\"10\" x2=\"10\" y1=\"11\" y2=\"17\" />\n                  <line x1=\"14\" x2=\"14\" y1=\"11\" y2=\"17\" />\n                </svg>\n                Clear Chat\n              </button>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAUe,SAAS,gBAAgB,EAAE,aAAa,EAAE,WAAW,EAAE,YAAY,EAAwB;;IACxG,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAAsD;;;;;;0BAEpE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS,IAAM,cAAc,CAAC;wBAC9B,WAAU;;4BACX;0CAEC,6LAAC;gCACC,OAAM;gCACN,OAAM;gCACN,QAAO;gCACP,SAAQ;gCACR,MAAK;gCACL,QAAO;gCACP,aAAY;gCACZ,eAAc;gCACd,gBAAe;gCACf,WAAU;0CAEV,cAAA,6LAAC;oCAAK,GAAE;;;;;;;;;;;;;;;;;oBAIX,4BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;wCACP;wCACA,cAAc;oCAChB;oCACA,WAAU;;sDAEV,6LAAC;4CACC,OAAM;4CACN,OAAM;4CACN,QAAO;4CACP,SAAQ;4CACR,MAAK;4CACL,QAAO;4CACP,aAAY;4CACZ,eAAc;4CACd,gBAAe;4CACf,WAAU;;8DAEV,6LAAC;oDAAK,GAAE;;;;;;8DACR,6LAAC;oDAAS,QAAO;;;;;;8DACjB,6LAAC;oDAAS,QAAO;;;;;;;;;;;;wCACb;;;;;;;8CAGR,6LAAC;oCACC,SAAS;wCACP;wCACA,cAAc;oCAChB;oCACA,WAAU;;sDAEV,6LAAC;4CACC,OAAM;4CACN,OAAM;4CACN,QAAO;4CACP,SAAQ;4CACR,MAAK;4CACL,QAAO;4CACP,aAAY;4CACZ,eAAc;4CACd,gBAAe;4CACf,WAAU;;8DAEV,6LAAC;oDAAK,GAAE;;;;;;8DACR,6LAAC;oDAAS,QAAO;;;;;;8DACjB,6LAAC;oDAAK,IAAG;oDAAK,IAAG;oDAAK,IAAG;oDAAK,IAAG;;;;;;;;;;;;wCAC7B;;;;;;;8CAGR,6LAAC;oCACC,SAAS;wCACP;wCACA,cAAc;oCAChB;oCACA,WAAU;;sDAEV,6LAAC;4CACC,OAAM;4CACN,OAAM;4CACN,QAAO;4CACP,SAAQ;4CACR,MAAK;4CACL,QAAO;4CACP,aAAY;4CACZ,eAAc;4CACd,gBAAe;4CACf,WAAU;;8DAEV,6LAAC;oDAAK,GAAE;;;;;;8DACR,6LAAC;oDAAK,GAAE;;;;;;8DACR,6LAAC;oDAAK,GAAE;;;;;;8DACR,6LAAC;oDAAK,IAAG;oDAAK,IAAG;oDAAK,IAAG;oDAAK,IAAG;;;;;;8DACjC,6LAAC;oDAAK,IAAG;oDAAK,IAAG;oDAAK,IAAG;oDAAK,IAAG;;;;;;;;;;;;wCAC7B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStB;GAnHwB;KAAA", "debugId": null}}, {"offset": {"line": 1904, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/lib/events.ts"], "sourcesContent": ["'use client';\n\n// Custom event types\nexport const EVENTS = {\n  FAVORITES_UPDATED: 'favorites-updated',\n  RECENTLY_VIEWED_UPDATED: 'recently-viewed-updated',\n};\n\n// Function to dispatch a custom event\nexport function dispatchCustomEvent(eventName: string, detail?: any) {\n  if (typeof window === 'undefined') return;\n  \n  const event = new CustomEvent(eventName, { detail });\n  window.dispatchEvent(event);\n}\n\n// Function to add an event listener\nexport function addCustomEventListener(eventName: string, callback: (event: CustomEvent) => void) {\n  if (typeof window === 'undefined') return () => {};\n  \n  const eventListener = (e: Event) => callback(e as CustomEvent);\n  window.addEventListener(eventName, eventListener);\n  \n  // Return a function to remove the event listener\n  return () => {\n    window.removeEventListener(eventName, eventListener);\n  };\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAGO,MAAM,SAAS;IACpB,mBAAmB;IACnB,yBAAyB;AAC3B;AAGO,SAAS,oBAAoB,SAAiB,EAAE,MAAY;IACjE,uCAAmC;;IAAM;IAEzC,MAAM,QAAQ,IAAI,YAAY,WAAW;QAAE;IAAO;IAClD,OAAO,aAAa,CAAC;AACvB;AAGO,SAAS,uBAAuB,SAAiB,EAAE,QAAsC;IAC9F,uCAAmC;;IAAe;IAElD,MAAM,gBAAgB,CAAC,IAAa,SAAS;IAC7C,OAAO,gBAAgB,CAAC,WAAW;IAEnC,iDAAiD;IACjD,OAAO;QACL,OAAO,mBAAmB,CAAC,WAAW;IACxC;AACF", "debugId": null}}, {"offset": {"line": 1943, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/lib/userPreferences.ts"], "sourcesContent": ["'use client';\n\nimport { UserPreferences } from '@/types/auth';\nimport { dispatchCustomEvent, EVENTS } from './events';\n\n// Default user preferences\nconst defaultPreferences: UserPreferences = {\n  favoriteAgentIds: [],\n  recentlyViewedAgentIds: [],\n  darkMode: false,\n  defaultView: 'grid',\n  pageSize: 12,\n};\n\n// Get user preferences from localStorage\nexport function getUserPreferences(userId?: string): UserPreferences {\n  if (typeof window === 'undefined') {\n    return defaultPreferences;\n  }\n\n  // If userId is provided, use it to get user-specific preferences\n  const storageKey = userId ? `userPreferences_${userId}` : 'userPreferences';\n  const storedPreferences = localStorage.getItem(storageKey);\n  if (!storedPreferences) {\n    return defaultPreferences;\n  }\n\n  try {\n    return JSON.parse(storedPreferences) as UserPreferences;\n  } catch (error) {\n    console.error('Error parsing user preferences:', error);\n    return defaultPreferences;\n  }\n}\n\n// Save user preferences to localStorage\nexport function saveUserPreferences(preferences: UserPreferences, userId?: string): void {\n  if (typeof window === 'undefined') {\n    return;\n  }\n\n  // If userId is provided, use it to save user-specific preferences\n  const storageKey = userId ? `userPreferences_${userId}` : 'userPreferences';\n  localStorage.setItem(storageKey, JSON.stringify(preferences));\n}\n\n// Add an agent to favorites\nexport function toggleFavoriteAgent(agentId: string, userId?: string): boolean {\n  const preferences = getUserPreferences(userId);\n  const isFavorite = preferences.favoriteAgentIds.includes(agentId);\n\n  if (isFavorite) {\n    preferences.favoriteAgentIds = preferences.favoriteAgentIds.filter(id => id !== agentId);\n  } else {\n    preferences.favoriteAgentIds.push(agentId);\n  }\n\n  saveUserPreferences(preferences, userId);\n\n  // Dispatch event to notify other components\n  dispatchCustomEvent(EVENTS.FAVORITES_UPDATED, {\n    agentId,\n    isFavorite: !isFavorite,\n    userId\n  });\n\n  return !isFavorite; // Return the new favorite status\n}\n\n// Check if an agent is favorited\nexport function isAgentFavorited(agentId: string, userId?: string): boolean {\n  const preferences = getUserPreferences(userId);\n  return preferences.favoriteAgentIds.includes(agentId);\n}\n\n// Add an agent to recently viewed\nexport function addToRecentlyViewed(agentId: string, userId?: string): void {\n  const preferences = getUserPreferences(userId);\n\n  // Remove if already exists to avoid duplicates\n  preferences.recentlyViewedAgentIds = preferences.recentlyViewedAgentIds.filter(id => id !== agentId);\n\n  // Add to the beginning of the array\n  preferences.recentlyViewedAgentIds.unshift(agentId);\n\n  // Keep only the 10 most recent\n  if (preferences.recentlyViewedAgentIds.length > 10) {\n    preferences.recentlyViewedAgentIds = preferences.recentlyViewedAgentIds.slice(0, 10);\n  }\n\n  saveUserPreferences(preferences, userId);\n\n  // Dispatch event to notify other components\n  dispatchCustomEvent(EVENTS.RECENTLY_VIEWED_UPDATED, {\n    agentId,\n    recentlyViewedIds: preferences.recentlyViewedAgentIds,\n    userId\n  });\n}\n\n// Get recently viewed agents\nexport function getRecentlyViewedAgentIds(userId?: string): string[] {\n  const preferences = getUserPreferences(userId);\n  return preferences.recentlyViewedAgentIds;\n}\n\n// Update page size preference\nexport function updatePageSize(pageSize: number, userId?: string): void {\n  const preferences = getUserPreferences(userId);\n  preferences.pageSize = pageSize;\n  saveUserPreferences(preferences, userId);\n}\n\n// Get page size preference\nexport function getPageSize(userId?: string): number {\n  const preferences = getUserPreferences(userId);\n  return preferences.pageSize;\n}\n\n// Update default view preference\nexport function updateDefaultView(view: 'grid' | 'list', userId?: string): void {\n  const preferences = getUserPreferences(userId);\n  preferences.defaultView = view;\n  saveUserPreferences(preferences, userId);\n}\n\n// Get default view preference\nexport function getDefaultView(userId?: string): 'grid' | 'list' {\n  const preferences = getUserPreferences(userId);\n  return preferences.defaultView;\n}\n\n// Toggle dark mode\nexport function toggleDarkMode(userId?: string): boolean {\n  const preferences = getUserPreferences(userId);\n  preferences.darkMode = !preferences.darkMode;\n  saveUserPreferences(preferences, userId);\n  return preferences.darkMode;\n}\n\n// Get dark mode preference\nexport function getDarkMode(userId?: string): boolean {\n  const preferences = getUserPreferences(userId);\n  return preferences.darkMode;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAGA;AAHA;;AAKA,2BAA2B;AAC3B,MAAM,qBAAsC;IAC1C,kBAAkB,EAAE;IACpB,wBAAwB,EAAE;IAC1B,UAAU;IACV,aAAa;IACb,UAAU;AACZ;AAGO,SAAS,mBAAmB,MAAe;IAChD,uCAAmC;;IAEnC;IAEA,iEAAiE;IACjE,MAAM,aAAa,SAAS,CAAC,gBAAgB,EAAE,QAAQ,GAAG;IAC1D,MAAM,oBAAoB,aAAa,OAAO,CAAC;IAC/C,IAAI,CAAC,mBAAmB;QACtB,OAAO;IACT;IAEA,IAAI;QACF,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO;IACT;AACF;AAGO,SAAS,oBAAoB,WAA4B,EAAE,MAAe;IAC/E,uCAAmC;;IAEnC;IAEA,kEAAkE;IAClE,MAAM,aAAa,SAAS,CAAC,gBAAgB,EAAE,QAAQ,GAAG;IAC1D,aAAa,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC;AAClD;AAGO,SAAS,oBAAoB,OAAe,EAAE,MAAe;IAClE,MAAM,cAAc,mBAAmB;IACvC,MAAM,aAAa,YAAY,gBAAgB,CAAC,QAAQ,CAAC;IAEzD,IAAI,YAAY;QACd,YAAY,gBAAgB,GAAG,YAAY,gBAAgB,CAAC,MAAM,CAAC,CAAA,KAAM,OAAO;IAClF,OAAO;QACL,YAAY,gBAAgB,CAAC,IAAI,CAAC;IACpC;IAEA,oBAAoB,aAAa;IAEjC,4CAA4C;IAC5C,CAAA,GAAA,uHAAA,CAAA,sBAAmB,AAAD,EAAE,uHAAA,CAAA,SAAM,CAAC,iBAAiB,EAAE;QAC5C;QACA,YAAY,CAAC;QACb;IACF;IAEA,OAAO,CAAC,YAAY,iCAAiC;AACvD;AAGO,SAAS,iBAAiB,OAAe,EAAE,MAAe;IAC/D,MAAM,cAAc,mBAAmB;IACvC,OAAO,YAAY,gBAAgB,CAAC,QAAQ,CAAC;AAC/C;AAGO,SAAS,oBAAoB,OAAe,EAAE,MAAe;IAClE,MAAM,cAAc,mBAAmB;IAEvC,+CAA+C;IAC/C,YAAY,sBAAsB,GAAG,YAAY,sBAAsB,CAAC,MAAM,CAAC,CAAA,KAAM,OAAO;IAE5F,oCAAoC;IACpC,YAAY,sBAAsB,CAAC,OAAO,CAAC;IAE3C,+BAA+B;IAC/B,IAAI,YAAY,sBAAsB,CAAC,MAAM,GAAG,IAAI;QAClD,YAAY,sBAAsB,GAAG,YAAY,sBAAsB,CAAC,KAAK,CAAC,GAAG;IACnF;IAEA,oBAAoB,aAAa;IAEjC,4CAA4C;IAC5C,CAAA,GAAA,uHAAA,CAAA,sBAAmB,AAAD,EAAE,uHAAA,CAAA,SAAM,CAAC,uBAAuB,EAAE;QAClD;QACA,mBAAmB,YAAY,sBAAsB;QACrD;IACF;AACF;AAGO,SAAS,0BAA0B,MAAe;IACvD,MAAM,cAAc,mBAAmB;IACvC,OAAO,YAAY,sBAAsB;AAC3C;AAGO,SAAS,eAAe,QAAgB,EAAE,MAAe;IAC9D,MAAM,cAAc,mBAAmB;IACvC,YAAY,QAAQ,GAAG;IACvB,oBAAoB,aAAa;AACnC;AAGO,SAAS,YAAY,MAAe;IACzC,MAAM,cAAc,mBAAmB;IACvC,OAAO,YAAY,QAAQ;AAC7B;AAGO,SAAS,kBAAkB,IAAqB,EAAE,MAAe;IACtE,MAAM,cAAc,mBAAmB;IACvC,YAAY,WAAW,GAAG;IAC1B,oBAAoB,aAAa;AACnC;AAGO,SAAS,eAAe,MAAe;IAC5C,MAAM,cAAc,mBAAmB;IACvC,OAAO,YAAY,WAAW;AAChC;AAGO,SAAS,eAAe,MAAe;IAC5C,MAAM,cAAc,mBAAmB;IACvC,YAAY,QAAQ,GAAG,CAAC,YAAY,QAAQ;IAC5C,oBAAoB,aAAa;IACjC,OAAO,YAAY,QAAQ;AAC7B;AAGO,SAAS,YAAY,MAAe;IACzC,MAAM,cAAc,mBAAmB;IACvC,OAAO,YAAY,QAAQ;AAC7B", "debugId": null}}, {"offset": {"line": 2073, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/agent/%5Bid%5D/components/RelatedAgents.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { Agent } from '@/types/agent';\nimport { getRelatedAgents } from '@/lib/agents';\nimport { addToRecentlyViewed } from '@/lib/userPreferences';\n\ninterface RelatedAgentsProps {\n  agentId: string;\n}\n\nexport default function RelatedAgents({ agentId }: RelatedAgentsProps) {\n  const [relatedAgents, setRelatedAgents] = useState<Agent[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n\n  useEffect(() => {\n    // Get related agents\n    const agents = getRelatedAgents(agentId);\n    setRelatedAgents(agents);\n    setIsLoading(false);\n  }, [agentId]);\n\n  // Handle agent click to track recently viewed\n  const handleAgentClick = (id: string) => {\n    addToRecentlyViewed(id);\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"rounded-lg border border-gray-200 bg-white p-6 dark:border-gray-800 dark:bg-gray-900\">\n        <h2 className=\"mb-4 text-xl font-bold text-gray-900 dark:text-white\">Related Agents</h2>\n        <div className=\"flex justify-center py-4\">\n          <div className=\"h-6 w-6 animate-spin rounded-full border-b-2 border-t-2 border-blue-600\"></div>\n        </div>\n      </div>\n    );\n  }\n\n  if (relatedAgents.length === 0) {\n    return null; // Don't show the section if there are no related agents\n  }\n\n  return (\n    <div className=\"rounded-lg border border-gray-200 bg-white p-6 dark:border-gray-800 dark:bg-gray-900\">\n      <h2 className=\"mb-4 text-xl font-bold text-gray-900 dark:text-white\">Related Agents</h2>\n      <div className=\"space-y-4\">\n        {relatedAgents.map((agent) => (\n          <Link\n            key={agent.id}\n            href={`/agent/${agent.id}`}\n            className=\"flex items-center rounded-lg border border-gray-200 p-3 transition-colors hover:bg-gray-50 dark:border-gray-700 dark:hover:bg-gray-800\"\n            onClick={() => handleAgentClick(agent.id)}\n          >\n            {agent.avatarUrl && (\n              <div className=\"mr-3 h-10 w-10 flex-shrink-0 overflow-hidden rounded-full bg-gray-100 dark:bg-gray-800\">\n                <Image\n                  src={agent.avatarUrl}\n                  alt={`${agent.name} icon`}\n                  width={40}\n                  height={40}\n                  className=\"h-full w-full object-cover\"\n                />\n              </div>\n            )}\n            <div className=\"flex-1\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <h3 className=\"font-medium text-gray-900 dark:text-white\">{agent.name}</h3>\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400\">{agent.category}</p>\n                </div>\n                {agent.isNew && (\n                  <span className=\"rounded-full bg-green-100 px-2 py-0.5 text-xs font-medium text-green-800 dark:bg-green-900/30 dark:text-green-400\">\n                    New\n                  </span>\n                )}\n              </div>\n            </div>\n          </Link>\n        ))}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;;;AAPA;;;;;;AAae,SAAS,cAAc,EAAE,OAAO,EAAsB;;IACnE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAC9D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,qBAAqB;YACrB,MAAM,SAAS,CAAA,GAAA,uHAAA,CAAA,mBAAgB,AAAD,EAAE;YAChC,iBAAiB;YACjB,aAAa;QACf;kCAAG;QAAC;KAAQ;IAEZ,8CAA8C;IAC9C,MAAM,mBAAmB,CAAC;QACxB,CAAA,GAAA,gIAAA,CAAA,sBAAmB,AAAD,EAAE;IACtB;IAEA,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAAuD;;;;;;8BACrE,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;IAIvB;IAEA,IAAI,cAAc,MAAM,KAAK,GAAG;QAC9B,OAAO,MAAM,wDAAwD;IACvE;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAAuD;;;;;;0BACrE,6LAAC;gBAAI,WAAU;0BACZ,cAAc,GAAG,CAAC,CAAC,sBAClB,6LAAC,+JAAA,CAAA,UAAI;wBAEH,MAAM,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;wBAC1B,WAAU;wBACV,SAAS,IAAM,iBAAiB,MAAM,EAAE;;4BAEvC,MAAM,SAAS,kBACd,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oCACJ,KAAK,MAAM,SAAS;oCACpB,KAAK,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC;oCACzB,OAAO;oCACP,QAAQ;oCACR,WAAU;;;;;;;;;;;0CAIhB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA6C,MAAM,IAAI;;;;;;8DACrE,6LAAC;oDAAE,WAAU;8DAA4C,MAAM,QAAQ;;;;;;;;;;;;wCAExE,MAAM,KAAK,kBACV,6LAAC;4CAAK,WAAU;sDAAoH;;;;;;;;;;;;;;;;;;uBAvBrI,MAAM,EAAE;;;;;;;;;;;;;;;;AAkCzB;GAvEwB;KAAA", "debugId": null}}, {"offset": {"line": 2259, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/agent/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useParams } from 'next/navigation';\nimport { getAgentById, getSessionsForAgent } from '@/lib/agents';\nimport { Session, Message } from '@/types/agent';\nimport AgentHeader from './components/AgentHeader';\nimport AgentInfo from './components/AgentInfo';\nimport ChatInterface from './components/ChatInterface';\nimport SessionHistory from './components/SessionHistory';\nimport SessionControls from './components/SessionControls';\nimport RelatedAgents from './components/RelatedAgents';\nimport { createMessage, getRelatedAgents } from '@/lib/agents';\nimport { addToRecentlyViewed } from '@/lib/userPreferences';\n\nexport default function AgentPage() {\n  const params = useParams();\n  const agentId = params.id as string;\n\n  const [agent, setAgent] = useState(getAgentById(agentId));\n  const [sessions, setSessions] = useState(getSessionsForAgent(agentId));\n  const [currentSession, setCurrentSession] = useState<Session | null>(null);\n  const [messages, setMessages] = useState<Message[]>([]);\n\n  useEffect(() => {\n    // Initialize with a welcome message if no session is selected\n    if (!currentSession) {\n      setMessages([\n        createMessage(`Hello! I'm ${agent?.name}. How can I help you today?`, 'assistant')\n      ]);\n    }\n\n    // Track this agent as recently viewed\n    if (agent) {\n      addToRecentlyViewed(agentId);\n    }\n  }, [agent, currentSession, agentId]);\n\n  // Handle selecting a session from history\n  const handleSelectSession = (session: Session) => {\n    setCurrentSession(session);\n    setMessages(session.messages);\n  };\n\n  // Handle saving the current session\n  const handleSaveSession = () => {\n    if (messages.length <= 1) {\n      alert('Cannot save an empty session. Please start a conversation first.');\n      return;\n    }\n\n    const userMessages = messages.filter(m => m.role === 'user');\n    if (userMessages.length === 0) {\n      alert('Cannot save a session without any user messages.');\n      return;\n    }\n\n    const sessionTitle = userMessages[0].content.substring(0, 30) + (userMessages[0].content.length > 30 ? '...' : '');\n\n    const newSession: Session = {\n      id: `session_${Date.now()}`,\n      agentId,\n      title: sessionTitle,\n      messages: [...messages],\n      createdAt: messages[0].timestamp,\n      updatedAt: Date.now(),\n      isSaved: true\n    };\n\n    setSessions(prev => [newSession, ...prev]);\n    setCurrentSession(newSession);\n    alert('Session saved successfully!');\n  };\n\n  // Handle clearing the chat\n  const handleClearChat = () => {\n    if (confirm('Are you sure you want to clear the current chat? This cannot be undone.')) {\n      setMessages([\n        createMessage(`Hello! I'm ${agent?.name}. How can I help you today?`, 'assistant')\n      ]);\n      setCurrentSession(null);\n    }\n  };\n\n  // Handle exporting the chat\n  const handleExportChat = () => {\n    if (messages.length <= 1) {\n      alert('There is nothing to export. Please start a conversation first.');\n      return;\n    }\n\n    const chatContent = messages.map(msg => {\n      const role = msg.role === 'assistant' ? agent?.name || 'Assistant' : 'You';\n      const time = new Date(msg.timestamp).toLocaleString();\n      return `${role} (${time}):\\n${msg.content}\\n`;\n    }).join('\\n');\n\n    const blob = new Blob([chatContent], { type: 'text/plain' });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `Chat with ${agent?.name} - ${new Date().toLocaleDateString()}.txt`;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  };\n\n  if (!agent) {\n    return (\n      <div className=\"flex min-h-screen items-center justify-center\">\n        <div className=\"text-center\">\n          <h1 className=\"mb-4 text-2xl font-bold text-gray-900 dark:text-white\">Agent Not Found</h1>\n          <p className=\"mb-6 text-gray-600 dark:text-gray-400\">\n            The agent you're looking for doesn't exist or has been removed.\n          </p>\n          <a\n            href=\"/\"\n            className=\"rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800\"\n          >\n            Return to Home\n          </a>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"container mx-auto px-4 py-8\">\n      <AgentHeader agent={agent} />\n\n      <div className=\"grid grid-cols-1 gap-8 lg:grid-cols-3\">\n        <div className=\"lg:col-span-2\">\n          <SessionControls\n            onSaveSession={handleSaveSession}\n            onClearChat={handleClearChat}\n            onExportChat={handleExportChat}\n          />\n          <ChatInterface\n            agentName={agent.name}\n            initialMessages={messages}\n          />\n        </div>\n\n        <div>\n          <SessionHistory\n            sessions={sessions}\n            onSelectSession={handleSelectSession}\n          />\n          <AgentInfo agent={agent} />\n          <div className=\"mt-6\">\n            <RelatedAgents agentId={agentId} />\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;;;AAbA;;;;;;;;;;;;AAee,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,UAAU,OAAO,EAAE;IAEzB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,eAAY,AAAD,EAAE;IAChD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,uHAAA,CAAA,sBAAmB,AAAD,EAAE;IAC7D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACrE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IAEtD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,8DAA8D;YAC9D,IAAI,CAAC,gBAAgB;gBACnB,YAAY;oBACV,CAAA,GAAA,uHAAA,CAAA,gBAAa,AAAD,EAAE,CAAC,WAAW,EAAE,OAAO,KAAK,2BAA2B,CAAC,EAAE;iBACvE;YACH;YAEA,sCAAsC;YACtC,IAAI,OAAO;gBACT,CAAA,GAAA,gIAAA,CAAA,sBAAmB,AAAD,EAAE;YACtB;QACF;8BAAG;QAAC;QAAO;QAAgB;KAAQ;IAEnC,0CAA0C;IAC1C,MAAM,sBAAsB,CAAC;QAC3B,kBAAkB;QAClB,YAAY,QAAQ,QAAQ;IAC9B;IAEA,oCAAoC;IACpC,MAAM,oBAAoB;QACxB,IAAI,SAAS,MAAM,IAAI,GAAG;YACxB,MAAM;YACN;QACF;QAEA,MAAM,eAAe,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;QACrD,IAAI,aAAa,MAAM,KAAK,GAAG;YAC7B,MAAM;YACN;QACF;QAEA,MAAM,eAAe,YAAY,CAAC,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC,OAAO,CAAC,MAAM,GAAG,KAAK,QAAQ,EAAE;QAEjH,MAAM,aAAsB;YAC1B,IAAI,CAAC,QAAQ,EAAE,KAAK,GAAG,IAAI;YAC3B;YACA,OAAO;YACP,UAAU;mBAAI;aAAS;YACvB,WAAW,QAAQ,CAAC,EAAE,CAAC,SAAS;YAChC,WAAW,KAAK,GAAG;YACnB,SAAS;QACX;QAEA,YAAY,CAAA,OAAQ;gBAAC;mBAAe;aAAK;QACzC,kBAAkB;QAClB,MAAM;IACR;IAEA,2BAA2B;IAC3B,MAAM,kBAAkB;QACtB,IAAI,QAAQ,4EAA4E;YACtF,YAAY;gBACV,CAAA,GAAA,uHAAA,CAAA,gBAAa,AAAD,EAAE,CAAC,WAAW,EAAE,OAAO,KAAK,2BAA2B,CAAC,EAAE;aACvE;YACD,kBAAkB;QACpB;IACF;IAEA,4BAA4B;IAC5B,MAAM,mBAAmB;QACvB,IAAI,SAAS,MAAM,IAAI,GAAG;YACxB,MAAM;YACN;QACF;QAEA,MAAM,cAAc,SAAS,GAAG,CAAC,CAAA;YAC/B,MAAM,OAAO,IAAI,IAAI,KAAK,cAAc,OAAO,QAAQ,cAAc;YACrE,MAAM,OAAO,IAAI,KAAK,IAAI,SAAS,EAAE,cAAc;YACnD,OAAO,GAAG,KAAK,EAAE,EAAE,KAAK,IAAI,EAAE,IAAI,OAAO,CAAC,EAAE,CAAC;QAC/C,GAAG,IAAI,CAAC;QAER,MAAM,OAAO,IAAI,KAAK;YAAC;SAAY,EAAE;YAAE,MAAM;QAAa;QAC1D,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG,CAAC,UAAU,EAAE,OAAO,KAAK,GAAG,EAAE,IAAI,OAAO,kBAAkB,GAAG,IAAI,CAAC;QAChF,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,EAAE,KAAK;QACP,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,IAAI,eAAe,CAAC;IACtB;IAEA,IAAI,CAAC,OAAO;QACV,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwD;;;;;;kCACtE,6LAAC;wBAAE,WAAU;kCAAwC;;;;;;kCAGrD,6LAAC;wBACC,MAAK;wBACL,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,8JAAA,CAAA,UAAW;gBAAC,OAAO;;;;;;0BAEpB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,kKAAA,CAAA,UAAe;gCACd,eAAe;gCACf,aAAa;gCACb,cAAc;;;;;;0CAEhB,6LAAC,gKAAA,CAAA,UAAa;gCACZ,WAAW,MAAM,IAAI;gCACrB,iBAAiB;;;;;;;;;;;;kCAIrB,6LAAC;;0CACC,6LAAC,iKAAA,CAAA,UAAc;gCACb,UAAU;gCACV,iBAAiB;;;;;;0CAEnB,6LAAC,4JAAA,CAAA,UAAS;gCAAC,OAAO;;;;;;0CAClB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,gKAAA,CAAA,UAAa;oCAAC,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMpC;GA9IwB;;QACP,qIAAA,CAAA,YAAS;;;KADF", "debugId": null}}]}