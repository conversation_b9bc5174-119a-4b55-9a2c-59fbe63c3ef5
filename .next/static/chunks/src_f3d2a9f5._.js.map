{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/data/resources.ts"], "sourcesContent": ["import { Resource, ResourceCategoryInfo } from '@/types/resources';\n\n// Resource categories information\nexport const resourceCategories: ResourceCategoryInfo[] = [\n  {\n    id: 'productivity',\n    name: 'Productivity Tools',\n    description: 'Tools to enhance personal and team productivity, including task management, note-taking, and time tracking.',\n    icon: 'bolt'\n  },\n  {\n    id: 'project-management',\n    name: 'Project Management',\n    description: 'Software and platforms for planning, executing, and monitoring projects of all sizes.',\n    icon: 'tasks'\n  },\n  {\n    id: 'design',\n    name: 'Design Tools',\n    description: 'Resources for UI/UX design, graphic design, prototyping, and visual collaboration.',\n    icon: 'palette'\n  },\n  {\n    id: 'development',\n    name: 'Development Tools',\n    description: 'Tools and services for software development, coding, testing, and deployment.',\n    icon: 'code'\n  },\n  {\n    id: 'research',\n    name: 'Research Tools',\n    description: 'Resources for market research, user research, academic research, and data collection.',\n    icon: 'magnifying-glass-chart'\n  },\n  {\n    id: 'analytics',\n    name: 'Analytics & Data',\n    description: 'Tools for data analysis, visualization, business intelligence, and reporting.',\n    icon: 'chart-line'\n  },\n  {\n    id: 'communication',\n    name: 'Communication',\n    description: 'Platforms for team communication, client meetings, presentations, and email management.',\n    icon: 'comments'\n  },\n  {\n    id: 'collaboration',\n    name: 'Collaboration',\n    description: 'Tools that facilitate teamwork, knowledge sharing, and cross-functional collaboration.',\n    icon: 'users-gear'\n  }\n];\n\n// Sample resources data\nexport const resources: Resource[] = [\n  // Productivity Tools\n  {\n    id: 'notion-ai',\n    name: 'Notion AI',\n    description: 'AI-powered writing assistant integrated into Notion that helps draft, edit, summarize, and brainstorm content directly in your workspace.',\n    url: 'https://www.notion.so/product/ai',\n    category: 'productivity',\n    tags: ['writing-assistant', 'content-generation', 'summarization'],\n    pricing: 'paid',\n    logoUrl: 'https://upload.wikimedia.org/wikipedia/commons/4/45/Notion_app_logo.png'\n  },\n  {\n    id: 'mem',\n    name: 'Mem',\n    description: 'AI-powered workspace that automatically organizes your notes and knowledge with powerful search and retrieval capabilities.',\n    url: 'https://mem.ai',\n    category: 'productivity',\n    tags: ['note-taking', 'knowledge-management', 'ai-organization'],\n    pricing: 'freemium',\n    logoUrl: 'https://mem.ai/assets/favicons/favicon.svg'\n  },\n  {\n    id: 'otter',\n    name: 'Otter.ai',\n    description: 'AI meeting assistant that records, transcribes, and summarizes meetings in real-time with speaker identification.',\n    url: 'https://otter.ai',\n    category: 'productivity',\n    tags: ['transcription', 'meeting-assistant', 'note-taking'],\n    pricing: 'freemium',\n    logoUrl: 'https://assets-global.website-files.com/618e9316785b3582a5178502/6230b90e3dceec1c2208f309_favicon-256x256.png'\n  },\n  {\n    id: 'reclaim',\n    name: 'Reclaim.ai',\n    description: 'AI calendar assistant that automatically schedules your tasks, habits, and meetings to optimize your time and protect your calendar.',\n    url: 'https://reclaim.ai',\n    category: 'productivity',\n    tags: ['calendar-management', 'time-blocking', 'scheduling'],\n    pricing: 'freemium',\n    logoUrl: 'https://reclaim.ai/favicon/favicon-32x32.png'\n  },\n  {\n    id: 'timetask',\n    name: 'TimeTask AI',\n    description: 'AI-powered time tracking tool that automatically categorizes your work and provides insights to improve productivity.',\n    url: 'https://timetask.ai',\n    category: 'productivity',\n    tags: ['time-tracking', 'productivity-analytics', 'work-insights'],\n    pricing: 'freemium',\n    logoUrl: 'https://timetask.ai/favicon.ico'\n  },\n\n  // Project Management\n  {\n    id: 'asana-ai',\n    name: 'Asana AI',\n    description: 'AI assistant integrated into Asana that helps teams plan projects, write task descriptions, summarize discussions, and automate workflows.',\n    url: 'https://asana.com/ai',\n    category: 'project-management',\n    tags: ['task-management', 'ai-assistant', 'workflow-automation'],\n    pricing: 'paid',\n    logoUrl: 'https://upload.wikimedia.org/wikipedia/commons/3/3b/Asana_logo.svg'\n  },\n  {\n    id: 'clickup-ai',\n    name: 'ClickUp AI',\n    description: 'AI-powered project management assistant that writes, edits, summarizes, and generates content directly within your project management workflow.',\n    url: 'https://clickup.com/ai',\n    category: 'project-management',\n    tags: ['content-generation', 'task-automation', 'summarization'],\n    pricing: 'paid',\n    logoUrl: 'https://clickup.com/landing/images/clickup-logo.svg'\n  },\n  {\n    id: 'motion',\n    name: 'Motion',\n    description: 'AI-powered project management tool that automatically schedules and prioritizes tasks based on deadlines, priorities, and team capacity.',\n    url: 'https://www.usemotion.com',\n    category: 'project-management',\n    tags: ['ai-scheduling', 'task-prioritization', 'time-management'],\n    pricing: 'paid',\n    logoUrl: 'https://www.usemotion.com/favicon.png'\n  },\n  {\n    id: 'forecast',\n    name: 'Forecast',\n    description: 'AI-native platform for project and resource management that predicts project delivery dates, resource needs, and budget requirements.',\n    url: 'https://www.forecast.app',\n    category: 'project-management',\n    tags: ['resource-planning', 'project-forecasting', 'budget-management'],\n    pricing: 'paid',\n    logoUrl: 'https://www.forecast.app/hubfs/favicon-1.png'\n  },\n  {\n    id: 'teamwork-ai',\n    name: 'Teamwork AI',\n    description: 'AI-powered project management platform that automates routine tasks, provides insights, and helps teams deliver projects more efficiently.',\n    url: 'https://www.teamwork.com/ai-project-management-software',\n    category: 'project-management',\n    tags: ['task-automation', 'project-insights', 'team-collaboration'],\n    pricing: 'paid',\n    logoUrl: 'https://www.teamwork.com/app/themes/teamwork-theme/dist/images/favicon/apple-touch-icon.png'\n  },\n\n  // Design Tools\n  {\n    id: 'midjourney',\n    name: 'Midjourney',\n    description: 'AI image generation tool that creates stunning visuals from text descriptions, ideal for concept art, illustrations, and design inspiration.',\n    url: 'https://www.midjourney.com',\n    category: 'design',\n    tags: ['ai-image-generation', 'concept-art', 'visual-design'],\n    pricing: 'paid',\n    logoUrl: 'https://upload.wikimedia.org/wikipedia/commons/e/e6/Midjourney_Emblem.png'\n  },\n  {\n    id: 'figma-ai',\n    name: 'Figma AI',\n    description: 'AI-powered design features in Figma that help generate and edit designs, create variations, and improve design workflows.',\n    url: 'https://www.figma.com/ai',\n    category: 'design',\n    tags: ['ui-design', 'design-generation', 'workflow-automation'],\n    pricing: 'paid',\n    logoUrl: 'https://upload.wikimedia.org/wikipedia/commons/3/33/Figma-logo.svg'\n  },\n  {\n    id: 'gamma',\n    name: 'Gamma',\n    description: 'AI-powered presentation platform that transforms simple text prompts into beautiful, presentation-ready slides and documents.',\n    url: 'https://gamma.app',\n    category: 'design',\n    tags: ['presentations', 'content-generation', 'slide-design'],\n    pricing: 'freemium',\n    logoUrl: 'https://assets-global.website-files.com/6127a84dfe068e153ef20572/62b1399ae4703b5c4f195f80_Favicon.png'\n  },\n  {\n    id: 'canva-ai',\n    name: 'Canva AI',\n    description: 'Suite of AI tools in Canva that help generate designs, text, images, and edit photos to create professional-looking content quickly.',\n    url: 'https://www.canva.com/ai-image-generator/',\n    category: 'design',\n    tags: ['graphic-design', 'image-generation', 'text-to-image'],\n    pricing: 'freemium',\n    logoUrl: 'https://upload.wikimedia.org/wikipedia/commons/0/08/Canva_icon_2021.svg'\n  },\n  {\n    id: 'runway',\n    name: 'Runway',\n    description: 'AI-powered creative suite for generating and editing videos, images, and 3D content with text prompts and intuitive controls.',\n    url: 'https://runwayml.com',\n    category: 'design',\n    tags: ['video-generation', 'image-editing', 'creative-tools'],\n    pricing: 'freemium',\n    logoUrl: 'https://runwayml.com/favicon.svg'\n  },\n\n  // Development Tools\n  {\n    id: 'github-copilot',\n    name: 'GitHub Copilot',\n    description: 'AI pair programmer that suggests code completions and entire functions in real-time, directly in your editor.',\n    url: 'https://github.com/features/copilot',\n    category: 'development',\n    tags: ['code-completion', 'pair-programming', 'ai-coding'],\n    pricing: 'paid',\n    logoUrl: 'https://github.githubassets.com/assets/GitHub-Mark-ea2971cee799.png'\n  },\n  {\n    id: 'augment-agent',\n    name: 'Augment Agent',\n    description: 'AI coding assistant that helps developers understand, modify, and debug code with natural language, providing context-aware suggestions.',\n    url: 'https://www.augmentcode.com/',\n    category: 'development',\n    tags: ['code-understanding', 'debugging', 'ai-assistant'],\n    pricing: 'paid',\n    logoUrl: 'https://www.augmentcode.com/favicon.ico'\n  },\n  {\n    id: 'tabnine',\n    name: 'Tabnine',\n    description: 'AI code completion tool that predicts and suggests code based on context and patterns, supporting multiple programming languages and IDEs.',\n    url: 'https://www.tabnine.com',\n    category: 'development',\n    tags: ['code-completion', 'ai-assistant', 'multi-language'],\n    pricing: 'freemium',\n    logoUrl: 'https://www.tabnine.com/favicon.ico'\n  },\n  {\n    id: 'codeium',\n    name: 'Codeium',\n    description: 'Free AI-powered code completion and chat tool that helps developers write code faster with context-aware suggestions.',\n    url: 'https://codeium.com',\n    category: 'development',\n    tags: ['code-completion', 'code-chat', 'free'],\n    pricing: 'freemium',\n    logoUrl: 'https://codeium.com/favicon.ico'\n  },\n  {\n    id: 'warp',\n    name: 'Warp',\n    description: 'AI-powered terminal that makes the command line more productive with features like AI command search, suggestions, and explanations.',\n    url: 'https://www.warp.dev',\n    category: 'development',\n    tags: ['terminal', 'command-line', 'productivity'],\n    pricing: 'freemium',\n    logoUrl: 'https://assets.warp.dev/warp_logo.png'\n  },\n\n  // Research Tools\n  {\n    id: 'elicit',\n    name: 'Elicit',\n    description: 'AI research assistant that helps you find, summarize, and analyze academic papers, extracting key insights and generating literature reviews.',\n    url: 'https://elicit.org',\n    category: 'research',\n    tags: ['academic-research', 'paper-summaries', 'literature-review'],\n    pricing: 'freemium',\n    logoUrl: 'https://elicit.org/favicon.ico'\n  },\n  {\n    id: 'consensus',\n    name: 'Consensus',\n    description: 'AI-powered search engine for scientific research that provides summaries of papers and highlights key findings with citations.',\n    url: 'https://consensus.app',\n    category: 'research',\n    tags: ['scientific-search', 'paper-summaries', 'evidence-based'],\n    pricing: 'freemium',\n    logoUrl: 'https://consensus.app/favicon.ico'\n  },\n  {\n    id: 'perplexity',\n    name: 'Perplexity AI',\n    description: 'AI-powered answer engine that provides comprehensive, cited responses to complex research questions with sources and references.',\n    url: 'https://www.perplexity.ai',\n    category: 'research',\n    tags: ['answer-engine', 'information-search', 'cited-responses'],\n    pricing: 'freemium',\n    logoUrl: 'https://www.perplexity.ai/favicon.ico'\n  },\n  {\n    id: 'scholarai',\n    name: 'ScholarAI',\n    description: 'AI research assistant that helps academics search, summarize, and analyze scientific papers, generating insights and connections.',\n    url: 'https://scholarai.io',\n    category: 'research',\n    tags: ['academic-research', 'paper-analysis', 'literature-review'],\n    pricing: 'freemium',\n    logoUrl: 'https://scholarai.io/favicon.ico'\n  },\n  {\n    id: 'researchrabbit',\n    name: 'ResearchRabbit',\n    description: 'AI-powered literature discovery tool that helps researchers find relevant papers and visualize connections between publications.',\n    url: 'https://www.researchrabbit.ai',\n    category: 'research',\n    tags: ['literature-discovery', 'citation-network', 'research-mapping'],\n    pricing: 'freemium',\n    logoUrl: 'https://www.researchrabbit.ai/favicon.png'\n  },\n\n  // Analytics & Data\n  {\n    id: 'obviously-ai',\n    name: 'Obviously AI',\n    description: 'No-code AI platform that allows anyone to build and deploy machine learning models for prediction and analysis without coding.',\n    url: 'https://www.obviously.ai',\n    category: 'analytics',\n    tags: ['no-code-ai', 'predictive-analytics', 'machine-learning'],\n    pricing: 'paid',\n    logoUrl: 'https://www.obviously.ai/favicon.ico'\n  },\n  {\n    id: 'akkio',\n    name: 'Akkio',\n    description: 'AI platform that makes it easy for businesses to build, deploy, and use predictive models without data science expertise.',\n    url: 'https://www.akkio.com',\n    category: 'analytics',\n    tags: ['predictive-analytics', 'no-code', 'business-intelligence'],\n    pricing: 'paid',\n    logoUrl: 'https://www.akkio.com/favicon.ico'\n  },\n  {\n    id: 'deepnote',\n    name: 'Deepnote',\n    description: 'AI-enhanced collaborative data notebook that combines the best of notebooks, spreadsheets, and visual interfaces for data analysis.',\n    url: 'https://deepnote.com',\n    category: 'analytics',\n    tags: ['data-science', 'notebooks', 'collaboration'],\n    pricing: 'freemium',\n    logoUrl: 'https://deepnote.com/favicon.ico'\n  },\n  {\n    id: 'hex',\n    name: 'Hex',\n    description: 'Collaborative data platform with AI capabilities that helps teams explore, analyze, and share data insights through notebooks and apps.',\n    url: 'https://hex.tech',\n    category: 'analytics',\n    tags: ['data-science', 'collaboration', 'data-apps'],\n    pricing: 'freemium',\n    logoUrl: 'https://hex.tech/favicon.ico'\n  },\n  {\n    id: 'einblick',\n    name: 'Einblick',\n    description: 'AI-powered data science platform that accelerates analysis with collaborative canvas, automated insights, and predictive capabilities.',\n    url: 'https://www.einblick.ai',\n    category: 'analytics',\n    tags: ['data-science', 'visual-analytics', 'collaboration'],\n    pricing: 'freemium',\n    logoUrl: 'https://www.einblick.ai/favicon.ico'\n  },\n\n  // Communication\n  {\n    id: 'chatgpt',\n    name: 'ChatGPT',\n    description: 'AI assistant that can draft emails, summarize conversations, generate content, and help with various communication tasks.',\n    url: 'https://chat.openai.com',\n    category: 'communication',\n    tags: ['ai-assistant', 'content-generation', 'writing-help'],\n    pricing: 'freemium',\n    logoUrl: 'https://chat.openai.com/apple-touch-icon.png'\n  },\n  {\n    id: 'fireflies',\n    name: 'Fireflies.ai',\n    description: 'AI meeting assistant that records, transcribes, and analyzes voice conversations, creating searchable notes and action items.',\n    url: 'https://fireflies.ai',\n    category: 'communication',\n    tags: ['meeting-assistant', 'transcription', 'meeting-insights'],\n    pricing: 'freemium',\n    logoUrl: 'https://fireflies.ai/favicon.ico'\n  },\n  {\n    id: 'grammarly',\n    name: 'Grammarly',\n    description: 'AI writing assistant that helps improve grammar, clarity, engagement, and delivery in all types of written communication.',\n    url: 'https://www.grammarly.com',\n    category: 'communication',\n    tags: ['writing-assistant', 'grammar-checker', 'communication-improvement'],\n    pricing: 'freemium',\n    logoUrl: 'https://static.grammarly.com/assets/files/efe57d016d9efff36da7884c193b646b/favicon-32x32.png'\n  },\n  {\n    id: 'summary-ai',\n    name: 'Summary AI',\n    description: 'AI tool that automatically summarizes meetings, articles, and documents to extract key points and action items.',\n    url: 'https://www.summary.ai',\n    category: 'communication',\n    tags: ['summarization', 'meeting-notes', 'information-extraction'],\n    pricing: 'freemium',\n    logoUrl: 'https://www.summary.ai/favicon.ico'\n  },\n  {\n    id: 'descript',\n    name: 'Descript',\n    description: 'All-in-one audio and video editing platform with AI features like transcription, content editing, and voice generation.',\n    url: 'https://www.descript.com',\n    category: 'communication',\n    tags: ['video-editing', 'transcription', 'content-creation'],\n    pricing: 'freemium',\n    logoUrl: 'https://www.descript.com/favicon.ico'\n  },\n\n  // Collaboration\n  {\n    id: 'coda-ai',\n    name: 'Coda AI',\n    description: 'AI-powered collaborative document platform that combines docs, spreadsheets, and apps with AI assistance for content generation and analysis.',\n    url: 'https://coda.io/product/ai-alpha',\n    category: 'collaboration',\n    tags: ['documents', 'ai-writing', 'team-workspace'],\n    pricing: 'freemium',\n    logoUrl: 'https://cdn.coda.io/icons/png/color/coda-192.png'\n  },\n  {\n    id: 'tome',\n    name: 'Tome',\n    description: 'AI-powered storytelling format that helps teams create beautiful, interactive presentations and documents with generative AI.',\n    url: 'https://tome.app',\n    category: 'collaboration',\n    tags: ['presentations', 'storytelling', 'content-generation'],\n    pricing: 'freemium',\n    logoUrl: 'https://tome.app/favicon.ico'\n  },\n  {\n    id: 'mural-ai',\n    name: 'MURAL AI',\n    description: 'AI-enhanced digital workspace for visual collaboration that helps teams brainstorm, plan, and solve problems together.',\n    url: 'https://www.mural.co/ai-features',\n    category: 'collaboration',\n    tags: ['visual-collaboration', 'brainstorming', 'workshops'],\n    pricing: 'paid',\n    logoUrl: 'https://assets-global.website-files.com/62e11362da2667ac3d0e6ed5/62e3a2c2d605d15a6bffc4a2_favicon-32.png'\n  },\n  {\n    id: 'notion-ai-collab',\n    name: 'Notion AI for Teams',\n    description: 'AI-powered collaborative workspace that helps teams create, organize, and share knowledge with AI assistance for content and insights.',\n    url: 'https://www.notion.so/product/ai',\n    category: 'collaboration',\n    tags: ['team-workspace', 'knowledge-management', 'ai-writing'],\n    pricing: 'paid',\n    logoUrl: 'https://upload.wikimedia.org/wikipedia/commons/4/45/Notion_app_logo.png'\n  },\n  {\n    id: 'craft-ai',\n    name: 'Craft AI',\n    description: 'AI-powered document editor and knowledge base that helps teams create beautiful documents and organize information.',\n    url: 'https://www.craft.do/features/ai',\n    category: 'collaboration',\n    tags: ['documents', 'knowledge-base', 'content-creation'],\n    pricing: 'freemium',\n    logoUrl: 'https://www.craft.do/images/favicon.ico'\n  }\n];\n\n// Helper functions to filter resources by category\nexport const getResourcesByCategory = (category: string) => {\n  return resources.filter(resource => resource.category === category);\n};\n\n// Helper function to get a category by ID\nexport const getCategoryById = (id: string) => {\n  return resourceCategories.find(category => category.id === id);\n};\n"], "names": [], "mappings": ";;;;;;AAGO,MAAM,qBAA6C;IACxD;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;IACR;CACD;AAGM,MAAM,YAAwB;IACnC,qBAAqB;IACrB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAqB;YAAsB;SAAgB;QAClE,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAe;YAAwB;SAAkB;QAChE,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAiB;YAAqB;SAAc;QAC3D,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAuB;YAAiB;SAAa;QAC5D,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAiB;YAA0B;SAAgB;QAClE,SAAS;QACT,SAAS;IACX;IAEA,qBAAqB;IACrB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAmB;YAAgB;SAAsB;QAChE,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAsB;YAAmB;SAAgB;QAChE,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAiB;YAAuB;SAAkB;QACjE,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAqB;YAAuB;SAAoB;QACvE,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAmB;YAAoB;SAAqB;QACnE,SAAS;QACT,SAAS;IACX;IAEA,eAAe;IACf;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAuB;YAAe;SAAgB;QAC7D,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAa;YAAqB;SAAsB;QAC/D,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAiB;YAAsB;SAAe;QAC7D,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAkB;YAAoB;SAAgB;QAC7D,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAoB;YAAiB;SAAiB;QAC7D,SAAS;QACT,SAAS;IACX;IAEA,oBAAoB;IACpB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAmB;YAAoB;SAAY;QAC1D,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAsB;YAAa;SAAe;QACzD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAmB;YAAgB;SAAiB;QAC3D,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAmB;YAAa;SAAO;QAC9C,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAY;YAAgB;SAAe;QAClD,SAAS;QACT,SAAS;IACX;IAEA,iBAAiB;IACjB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAqB;YAAmB;SAAoB;QACnE,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAqB;YAAmB;SAAiB;QAChE,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAiB;YAAsB;SAAkB;QAChE,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAqB;YAAkB;SAAoB;QAClE,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAwB;YAAoB;SAAmB;QACtE,SAAS;QACT,SAAS;IACX;IAEA,mBAAmB;IACnB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAc;YAAwB;SAAmB;QAChE,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAwB;YAAW;SAAwB;QAClE,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAgB;YAAa;SAAgB;QACpD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAgB;YAAiB;SAAY;QACpD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAgB;YAAoB;SAAgB;QAC3D,SAAS;QACT,SAAS;IACX;IAEA,gBAAgB;IAChB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAgB;YAAsB;SAAe;QAC5D,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAqB;YAAiB;SAAmB;QAChE,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAqB;YAAmB;SAA4B;QAC3E,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAiB;YAAiB;SAAyB;QAClE,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAiB;YAAiB;SAAmB;QAC5D,SAAS;QACT,SAAS;IACX;IAEA,gBAAgB;IAChB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAa;YAAc;SAAiB;QACnD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAiB;YAAgB;SAAqB;QAC7D,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAwB;YAAiB;SAAY;QAC5D,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAkB;YAAwB;SAAa;QAC9D,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAa;YAAkB;SAAmB;QACzD,SAAS;QACT,SAAS;IACX;CACD;AAGM,MAAM,yBAAyB,CAAC;IACrC,OAAO,UAAU,MAAM,CAAC,CAAA,WAAY,SAAS,QAAQ,KAAK;AAC5D;AAGO,MAAM,kBAAkB,CAAC;IAC9B,OAAO,mBAAmB,IAAI,CAAC,CAAA,WAAY,SAAS,EAAE,KAAK;AAC7D", "debugId": null}}, {"offset": {"line": 648, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/learning/resources/page.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { \n  faBolt, \n  faTasks, \n  faPalette, \n  faCode, \n  faMagnifyingGlassChart, \n  faChartLine, \n  faComments, \n  faUsersGear,\n  faArrowRight\n} from '@fortawesome/free-solid-svg-icons';\nimport { resourceCategories } from '@/data/resources';\n\n// Map category IDs to FontAwesome icons\nconst categoryIcons: Record<string, any> = {\n  'productivity': faBolt,\n  'project-management': faTasks,\n  'design': faPalette,\n  'development': faCode,\n  'research': faMagnifyingGlass<PERSON>hart,\n  'analytics': faChartLine,\n  'communication': faComments,\n  'collaboration': faUsersGear\n};\n\nexport default function ResourcesPage() {\n  return (\n    <div className=\"space-y-8\">\n      <div className=\"rounded-lg bg-gradient-to-r from-teal-600 to-emerald-700 p-8 text-white shadow-lg\">\n        <h1 className=\"mb-4 text-3xl font-bold\">Resources Directory</h1>\n        <p className=\"mb-6 text-lg\">\n          Explore our curated collection of tools and services for consulting professionals. Find resources for productivity, project management, design, development, research, and more.\n        </p>\n      </div>\n\n      <div className=\"grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4\">\n        {resourceCategories.map((category) => (\n          <Link\n            key={category.id}\n            href={`/learning/resources/${category.id}`}\n            className=\"group flex flex-col rounded-lg border border-gray-200 bg-white p-6 shadow-sm transition hover:shadow-md dark:border-gray-800 dark:bg-gray-900\"\n          >\n            <div className=\"mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-teal-100 text-teal-600 dark:bg-teal-900/30 dark:text-teal-400\">\n              <FontAwesomeIcon icon={categoryIcons[category.id]} className=\"h-6 w-6\" />\n            </div>\n            <h3 className=\"mb-2 text-xl font-bold text-gray-900 group-hover:text-teal-600 dark:text-white dark:group-hover:text-teal-400\">\n              {category.name}\n            </h3>\n            <p className=\"mb-4 flex-1 text-gray-600 dark:text-gray-400\">{category.description}</p>\n            <div className=\"mt-auto flex items-center text-sm font-medium text-teal-600 dark:text-teal-400\">\n              Browse resources\n              <FontAwesomeIcon icon={faArrowRight} className=\"ml-1 h-3 w-3\" />\n            </div>\n          </Link>\n        ))}\n      </div>\n\n      <div className=\"rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-800 dark:bg-gray-900\">\n        <h2 className=\"mb-4 text-2xl font-bold\">About Our Resources Directory</h2>\n        <p className=\"mb-4 text-gray-600 dark:text-gray-400\">\n          This directory is regularly updated with the latest tools and services that can help consulting professionals work more efficiently and deliver better results for clients. Our team carefully evaluates each resource before adding it to the directory.\n        </p>\n        <p className=\"mb-4 text-gray-600 dark:text-gray-400\">\n          Each category includes a variety of tools with different pricing models, from free to enterprise-level solutions, allowing you to find options that fit your specific needs and budget.\n        </p>\n        <div className=\"mt-4 rounded-md bg-teal-50 p-4 dark:bg-teal-900/20\">\n          <h3 className=\"mb-2 font-semibold text-teal-800 dark:text-teal-300\">Have a suggestion?</h3>\n          <p className=\"text-teal-700 dark:text-teal-400\">\n            If you know of a great tool that should be included in our directory, please let us know. We're always looking to expand our collection with valuable resources.\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAWA;AAhBA;;;;;;AAkBA,wCAAwC;AACxC,MAAM,gBAAqC;IACzC,gBAAgB,2KAAA,CAAA,SAAM;IACtB,sBAAsB,2KAAA,CAAA,UAAO;IAC7B,UAAU,2KAAA,CAAA,YAAS;IACnB,eAAe,2KAAA,CAAA,SAAM;IACrB,YAAY,2KAAA,CAAA,yBAAsB;IAClC,aAAa,2KAAA,CAAA,cAAW;IACxB,iBAAiB,2KAAA,CAAA,aAAU;IAC3B,iBAAiB,2KAAA,CAAA,cAAW;AAC9B;AAEe,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,6LAAC;wBAAE,WAAU;kCAAe;;;;;;;;;;;;0BAK9B,6LAAC;gBAAI,WAAU;0BACZ,2HAAA,CAAA,qBAAkB,CAAC,GAAG,CAAC,CAAC,yBACvB,6LAAC,+JAAA,CAAA,UAAI;wBAEH,MAAM,CAAC,oBAAoB,EAAE,SAAS,EAAE,EAAE;wBAC1C,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,uKAAA,CAAA,kBAAe;oCAAC,MAAM,aAAa,CAAC,SAAS,EAAE,CAAC;oCAAE,WAAU;;;;;;;;;;;0CAE/D,6LAAC;gCAAG,WAAU;0CACX,SAAS,IAAI;;;;;;0CAEhB,6LAAC;gCAAE,WAAU;0CAAgD,SAAS,WAAW;;;;;;0CACjF,6LAAC;gCAAI,WAAU;;oCAAiF;kDAE9F,6LAAC,uKAAA,CAAA,kBAAe;wCAAC,MAAM,2KAAA,CAAA,eAAY;wCAAE,WAAU;;;;;;;;;;;;;uBAb5C,SAAS,EAAE;;;;;;;;;;0BAmBtB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,6LAAC;wBAAE,WAAU;kCAAwC;;;;;;kCAGrD,6LAAC;wBAAE,WAAU;kCAAwC;;;;;;kCAGrD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAsD;;;;;;0CACpE,6LAAC;gCAAE,WAAU;0CAAmC;;;;;;;;;;;;;;;;;;;;;;;;AAO1D;KAjDwB", "debugId": null}}]}