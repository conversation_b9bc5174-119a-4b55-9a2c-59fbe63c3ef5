{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/app/admin/page.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport {\n  faChartLine,\n  faUsers,\n  faRobot,\n  faGear,\n  faShieldAlt,\n  faBook,\n  faSitemap\n} from '@fortawesome/free-solid-svg-icons';\n\nexport default function AdminPage() {\n  return (\n    <div className=\"space-y-8\">\n      <div className=\"rounded-lg bg-gradient-to-r from-purple-600 to-indigo-700 p-8 text-white shadow-lg\">\n        <h1 className=\"mb-4 text-3xl font-bold\">Admin Dashboard</h1>\n        <p className=\"mb-6 text-lg\">\n          Manage your AI Hub platform, monitor usage, and analyze performance metrics.\n        </p>\n      </div>\n\n      <div className=\"grid gap-6 md:grid-cols-2 lg:grid-cols-3\">\n        <Link\n          href=\"/admin/analytics\"\n          className=\"group flex flex-col rounded-lg border border-gray-200 bg-white p-6 shadow-sm transition hover:shadow-md dark:border-gray-800 dark:bg-gray-900\"\n        >\n          <div className=\"mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-purple-100 text-purple-600 dark:bg-purple-900/30 dark:text-purple-400\">\n            <FontAwesomeIcon icon={faChartLine} className=\"h-6 w-6\" />\n          </div>\n          <h3 className=\"mb-2 text-xl font-bold text-gray-900 group-hover:text-purple-600 dark:text-white dark:group-hover:text-purple-400\">\n            Analytics\n          </h3>\n          <p className=\"mb-4 flex-1 text-gray-600 dark:text-gray-400\">\n            View detailed analytics on platform usage, agent interactions, and user behavior.\n          </p>\n        </Link>\n\n        <Link\n          href=\"/admin/users\"\n          className=\"group flex flex-col rounded-lg border border-gray-200 bg-white p-6 shadow-sm transition hover:shadow-md dark:border-gray-800 dark:bg-gray-900\"\n        >\n          <div className=\"mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400\">\n            <FontAwesomeIcon icon={faUsers} className=\"h-6 w-6\" />\n          </div>\n          <h3 className=\"mb-2 text-xl font-bold text-gray-900 group-hover:text-blue-600 dark:text-white dark:group-hover:text-blue-400\">\n            User Management\n          </h3>\n          <p className=\"mb-4 flex-1 text-gray-600 dark:text-gray-400\">\n            Manage users, roles, and permissions. View user activity and engagement metrics.\n          </p>\n        </Link>\n\n        <Link\n          href=\"/admin/agents\"\n          className=\"group flex flex-col rounded-lg border border-gray-200 bg-white p-6 shadow-sm transition hover:shadow-md dark:border-gray-800 dark:bg-gray-900\"\n        >\n          <div className=\"mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400\">\n            <FontAwesomeIcon icon={faRobot} className=\"h-6 w-6\" />\n          </div>\n          <h3 className=\"mb-2 text-xl font-bold text-gray-900 group-hover:text-green-600 dark:text-white dark:group-hover:text-green-400\">\n            Agent Management\n          </h3>\n          <p className=\"mb-4 flex-1 text-gray-600 dark:text-gray-400\">\n            Configure, deploy, and monitor AI agents. Analyze agent performance and usage.\n          </p>\n        </Link>\n\n        <Link\n          href=\"/admin/ai-org\"\n          className=\"group flex flex-col rounded-lg border border-gray-200 bg-white p-6 shadow-sm transition hover:shadow-md dark:border-gray-800 dark:bg-gray-900\"\n        >\n          <div className=\"mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-indigo-100 text-indigo-600 dark:bg-indigo-900/30 dark:text-indigo-400\">\n            <FontAwesomeIcon icon={faSitemap} className=\"h-6 w-6\" />\n          </div>\n          <h3 className=\"mb-2 text-xl font-bold text-gray-900 group-hover:text-indigo-600 dark:text-white dark:group-hover:text-indigo-400\">\n            AI Organization\n          </h3>\n          <p className=\"mb-4 flex-1 text-gray-600 dark:text-gray-400\">\n            View and manage the AI agent organization structure, roles, and implementation status.\n          </p>\n        </Link>\n\n        <Link\n          href=\"/admin/content\"\n          className=\"group flex flex-col rounded-lg border border-gray-200 bg-white p-6 shadow-sm transition hover:shadow-md dark:border-gray-800 dark:bg-gray-900\"\n        >\n          <div className=\"mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-amber-100 text-amber-600 dark:bg-amber-900/30 dark:text-amber-400\">\n            <FontAwesomeIcon icon={faBook} className=\"h-6 w-6\" />\n          </div>\n          <h3 className=\"mb-2 text-xl font-bold text-gray-900 group-hover:text-amber-600 dark:text-white dark:group-hover:text-amber-400\">\n            Content Management\n          </h3>\n          <p className=\"mb-4 flex-1 text-gray-600 dark:text-gray-400\">\n            Manage learning content, resources, and other platform content. Track content engagement.\n          </p>\n        </Link>\n\n        <Link\n          href=\"/admin/security\"\n          className=\"group flex flex-col rounded-lg border border-gray-200 bg-white p-6 shadow-sm transition hover:shadow-md dark:border-gray-800 dark:bg-gray-900\"\n        >\n          <div className=\"mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100 text-red-600 dark:bg-red-900/30 dark:text-red-400\">\n            <FontAwesomeIcon icon={faShieldAlt} className=\"h-6 w-6\" />\n          </div>\n          <h3 className=\"mb-2 text-xl font-bold text-gray-900 group-hover:text-red-600 dark:text-white dark:group-hover:text-red-400\">\n            Security & Compliance\n          </h3>\n          <p className=\"mb-4 flex-1 text-gray-600 dark:text-gray-400\">\n            Monitor sensitive data usage, manage compliance settings, and view security alerts.\n          </p>\n        </Link>\n\n        <Link\n          href=\"/admin/settings\"\n          className=\"group flex flex-col rounded-lg border border-gray-200 bg-white p-6 shadow-sm transition hover:shadow-md dark:border-gray-800 dark:bg-gray-900\"\n        >\n          <div className=\"mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400\">\n            <FontAwesomeIcon icon={faGear} className=\"h-6 w-6\" />\n          </div>\n          <h3 className=\"mb-2 text-xl font-bold text-gray-900 group-hover:text-gray-600 dark:text-white dark:group-hover:text-gray-400\">\n            Platform Settings\n          </h3>\n          <p className=\"mb-4 flex-1 text-gray-600 dark:text-gray-400\">\n            Configure global platform settings, integrations, and appearance options.\n          </p>\n        </Link>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AALA;;;;;AAee,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,6LAAC;wBAAE,WAAU;kCAAe;;;;;;;;;;;;0BAK9B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,uKAAA,CAAA,kBAAe;oCAAC,MAAM,2KAAA,CAAA,cAAW;oCAAE,WAAU;;;;;;;;;;;0CAEhD,6LAAC;gCAAG,WAAU;0CAAoH;;;;;;0CAGlI,6LAAC;gCAAE,WAAU;0CAA+C;;;;;;;;;;;;kCAK9D,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,uKAAA,CAAA,kBAAe;oCAAC,MAAM,2KAAA,CAAA,UAAO;oCAAE,WAAU;;;;;;;;;;;0CAE5C,6LAAC;gCAAG,WAAU;0CAAgH;;;;;;0CAG9H,6LAAC;gCAAE,WAAU;0CAA+C;;;;;;;;;;;;kCAK9D,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,uKAAA,CAAA,kBAAe;oCAAC,MAAM,2KAAA,CAAA,UAAO;oCAAE,WAAU;;;;;;;;;;;0CAE5C,6LAAC;gCAAG,WAAU;0CAAkH;;;;;;0CAGhI,6LAAC;gCAAE,WAAU;0CAA+C;;;;;;;;;;;;kCAK9D,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,uKAAA,CAAA,kBAAe;oCAAC,MAAM,2KAAA,CAAA,YAAS;oCAAE,WAAU;;;;;;;;;;;0CAE9C,6LAAC;gCAAG,WAAU;0CAAoH;;;;;;0CAGlI,6LAAC;gCAAE,WAAU;0CAA+C;;;;;;;;;;;;kCAK9D,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,uKAAA,CAAA,kBAAe;oCAAC,MAAM,2KAAA,CAAA,SAAM;oCAAE,WAAU;;;;;;;;;;;0CAE3C,6LAAC;gCAAG,WAAU;0CAAkH;;;;;;0CAGhI,6LAAC;gCAAE,WAAU;0CAA+C;;;;;;;;;;;;kCAK9D,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,uKAAA,CAAA,kBAAe;oCAAC,MAAM,2KAAA,CAAA,cAAW;oCAAE,WAAU;;;;;;;;;;;0CAEhD,6LAAC;gCAAG,WAAU;0CAA8G;;;;;;0CAG5H,6LAAC;gCAAE,WAAU;0CAA+C;;;;;;;;;;;;kCAK9D,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,uKAAA,CAAA,kBAAe;oCAAC,MAAM,2KAAA,CAAA,SAAM;oCAAE,WAAU;;;;;;;;;;;0CAE3C,6LAAC;gCAAG,WAAU;0CAAgH;;;;;;0CAG9H,6LAAC;gCAAE,WAAU;0CAA+C;;;;;;;;;;;;;;;;;;;;;;;;AAOtE;KAtHwB", "debugId": null}}]}