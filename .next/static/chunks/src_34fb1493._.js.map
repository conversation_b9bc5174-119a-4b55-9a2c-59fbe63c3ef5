{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/data/resources-original.ts"], "sourcesContent": ["import { Resource, ResourceCategoryInfo } from '@/types/resources';\n\n// Resource categories information\nexport const resourceCategories: ResourceCategoryInfo[] = [\n  {\n    id: 'productivity',\n    name: 'Productivity Tools',\n    description: 'Tools to enhance personal and team productivity, including task management, note-taking, and time tracking.',\n    icon: 'bolt'\n  },\n  {\n    id: 'project-management',\n    name: 'Project Management',\n    description: 'Software and platforms for planning, executing, and monitoring projects of all sizes.',\n    icon: 'tasks'\n  },\n  {\n    id: 'design',\n    name: 'Design Tools',\n    description: 'Resources for UI/UX design, graphic design, prototyping, and visual collaboration.',\n    icon: 'palette'\n  },\n  {\n    id: 'development',\n    name: 'Development Tools',\n    description: 'Tools and services for software development, coding, testing, and deployment.',\n    icon: 'code'\n  },\n  {\n    id: 'research',\n    name: 'Research Tools',\n    description: 'Resources for market research, user research, academic research, and data collection.',\n    icon: 'magnifying-glass-chart'\n  },\n  {\n    id: 'analytics',\n    name: 'Analytics & Data',\n    description: 'Tools for data analysis, visualization, business intelligence, and reporting.',\n    icon: 'chart-line'\n  },\n  {\n    id: 'communication',\n    name: 'Communication',\n    description: 'Platforms for team communication, client meetings, presentations, and email management.',\n    icon: 'comments'\n  },\n  {\n    id: 'collaboration',\n    name: 'Collaboration',\n    description: 'Tools that facilitate teamwork, knowledge sharing, and cross-functional collaboration.',\n    icon: 'users-gear'\n  }\n];\n\n// Sample resources data\nexport const resources: Resource[] = [\n  {\n    id: 'notion',\n    name: 'Notion',\n    description: 'All-in-one workspace for notes, tasks, wikis, and databases.',\n    url: 'https://www.notion.so',\n    category: 'productivity',\n    tags: ['note-taking', 'project-management', 'wiki'],\n    pricing: 'freemium',\n    logoUrl: 'https://www.notion.so/images/favicon.ico'\n  },\n  {\n    id: 'figma',\n    name: 'Figma',\n    description: 'Collaborative interface design tool for teams.',\n    url: 'https://www.figma.com',\n    category: 'design',\n    tags: ['ui-design', 'prototyping', 'collaboration'],\n    pricing: 'freemium',\n    logoUrl: 'https://static.figma.com/app/icon/1/favicon.png'\n  },\n  {\n    id: 'vscode',\n    name: 'Visual Studio Code',\n    description: 'Free, open-source code editor with powerful development features.',\n    url: 'https://code.visualstudio.com',\n    category: 'development',\n    tags: ['code-editor', 'debugging', 'extensions'],\n    pricing: 'free',\n    logoUrl: 'https://code.visualstudio.com/favicon.ico'\n  },\n  {\n    id: 'slack',\n    name: 'Slack',\n    description: 'Channel-based messaging platform for teams and workplaces.',\n    url: 'https://slack.com',\n    category: 'communication',\n    tags: ['messaging', 'team-communication', 'integrations'],\n    pricing: 'freemium',\n    logoUrl: 'https://a.slack-edge.com/80588/marketing/img/meta/favicon-32.png'\n  },\n  {\n    id: 'trello',\n    name: 'Trello',\n    description: 'Visual tool for organizing work with boards, lists, and cards.',\n    url: 'https://trello.com',\n    category: 'project-management',\n    tags: ['kanban', 'task-management', 'collaboration'],\n    pricing: 'freemium',\n    logoUrl: 'https://a.trellocdn.com/prgb/dist/images/ios/apple-touch-icon-152x152-precomposed.0307bc39ec6c9ff499c8.png'\n  },\n  {\n    id: 'google-analytics',\n    name: 'Google Analytics',\n    description: 'Web analytics service that tracks and reports website traffic.',\n    url: 'https://analytics.google.com',\n    category: 'analytics',\n    tags: ['web-analytics', 'reporting', 'user-behavior'],\n    pricing: 'freemium',\n    logoUrl: 'https://www.google.com/analytics/images/ga_icon_256.png'\n  },\n  {\n    id: 'miro',\n    name: 'Miro',\n    description: 'Online collaborative whiteboard platform for teams.',\n    url: 'https://miro.com',\n    category: 'collaboration',\n    tags: ['whiteboard', 'brainstorming', 'visual-collaboration'],\n    pricing: 'freemium',\n    logoUrl: 'https://miro.com/static/images/favicon/apple-touch-icon.png'\n  },\n  {\n    id: 'google-scholar',\n    name: 'Google Scholar',\n    description: 'Search engine for academic literature and research papers.',\n    url: 'https://scholar.google.com',\n    category: 'research',\n    tags: ['academic-research', 'citations', 'literature-search'],\n    pricing: 'free',\n    logoUrl: 'https://scholar.google.com/favicon.ico'\n  }\n];\n"], "names": [], "mappings": ";;;;AAGO,MAAM,qBAA6C;IACxD;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;IACR;CACD;AAGM,MAAM,YAAwB;IACnC;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAe;YAAsB;SAAO;QACnD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAa;YAAe;SAAgB;QACnD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAe;YAAa;SAAa;QAChD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAa;YAAsB;SAAe;QACzD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAU;YAAmB;SAAgB;QACpD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAiB;YAAa;SAAgB;QACrD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAc;YAAiB;SAAuB;QAC7D,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAqB;YAAa;SAAoB;QAC7D,SAAS;QACT,SAAS;IACX;CACD", "debugId": null}}, {"offset": {"line": 184, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/data/resources.ts"], "sourcesContent": ["import { Resource, ResourceCategoryInfo } from '@/types/resources';\nimport { resources as existingResources, resourceCategories } from './resources-original';\n\n// New resources from the AI Presentation Series Summary PDF\nconst newResources: Resource[] = [\n  // Productivity Tools\n  {\n    id: 'perplexity-ai',\n    name: 'Perplexity AI',\n    description: 'An AI-powered search engine with a chatbot interface that understands and responds to user queries using GPT-3.5.',\n    url: 'https://www.perplexity.ai/',\n    category: 'productivity',\n    tags: ['search-engine', 'ai-assistant', 'research'],\n    pricing: 'freemium',\n    logoUrl: 'https://www.perplexity.ai/favicon.ico'\n  },\n  {\n    id: 'claude-ai',\n    name: 'Claude <PERSON>',\n    description: 'Analyzes and suggests improvements for very long content, similar to ChatGPT but with enhanced capabilities for handling lengthy documents.',\n    url: 'https://claude.ai/',\n    category: 'productivity',\n    tags: ['ai-assistant', 'content-generation', 'document-analysis'],\n    pricing: 'freemium',\n    logoUrl: 'https://claude.ai/favicon.ico'\n  },\n  {\n    id: 'fathom-ai',\n    name: 'Fathom AI',\n    description: 'Zoom app that records, transcribes, and highlights key moments from calls, making meeting follow-up more efficient.',\n    url: 'https://fathom.video/',\n    category: 'productivity',\n    tags: ['meeting-assistant', 'transcription', 'video-conferencing'],\n    pricing: 'freemium',\n    logoUrl: 'https://fathom.video/favicon.ico'\n  },\n  {\n    id: 'plaud-ai',\n    name: 'Plaud.ai',\n    description: 'AI tool for note-taking and transcription that helps capture and organize information from meetings and conversations.',\n    url: 'https://www.plaud.ai/',\n    category: 'productivity',\n    tags: ['note-taking', 'transcription', 'meeting-assistant'],\n    pricing: 'freemium',\n    logoUrl: 'https://www.plaud.ai/favicon.ico'\n  },\n  {\n    id: 'whisper',\n    name: 'Whisper',\n    description: 'Converts audio to text and vice versa using AI, providing accurate transcription for various languages and accents.',\n    url: 'https://openai.com/index/whisper/',\n    category: 'productivity',\n    tags: ['transcription', 'audio-processing', 'speech-to-text'],\n    pricing: 'freemium',\n    logoUrl: 'https://openai.com/favicon.ico'\n  },\n  {\n    id: 'notebooklm',\n    name: 'NotebookLM',\n    description: 'Tool for data management and note-taking that uses AI to help organize and retrieve information efficiently.',\n    url: 'https://notebooklm.google.com',\n    category: 'productivity',\n    tags: ['note-taking', 'knowledge-management', 'ai-organization'],\n    pricing: 'free',\n    logoUrl: 'https://notebooklm.google.com/favicon.ico'\n  },\n  {\n    id: 'deepl',\n    name: 'DeepL',\n    description: 'Translation tool for efficient and accurate translations between multiple languages, powered by advanced AI.',\n    url: 'https://www.deepl.com',\n    category: 'productivity',\n    tags: ['translation', 'language-processing', 'communication'],\n    pricing: 'freemium',\n    logoUrl: 'https://www.deepl.com/favicon.ico'\n  },\n\n  // Design Tools\n  {\n    id: 'midjourney',\n    name: 'Midjourney',\n    description: 'Generates images from descriptive language, similar to DALL-E and Stable Diffusion, with a focus on artistic quality.',\n    url: 'https://www.midjourney.com/',\n    category: 'design',\n    tags: ['image-generation', 'ai-art', 'creative-tools'],\n    pricing: 'paid',\n    logoUrl: 'https://www.midjourney.com/favicon.ico'\n  },\n  {\n    id: 'bing-images',\n    name: 'Bing Image Creator',\n    description: 'Provides tools for creating images from text descriptions, potentially offering more features than Midjourney.',\n    url: 'https://www.bing.com/images/create',\n    category: 'design',\n    tags: ['image-generation', 'ai-art', 'creative-tools'],\n    pricing: 'free',\n    logoUrl: 'https://www.bing.com/favicon.ico'\n  },\n  {\n    id: 'meta-imagine',\n    name: 'Meta Imagine',\n    description: 'Uses models to generate images from textual prompts provided by the user, created by Meta (Facebook).',\n    url: 'https://imagine.meta.com',\n    category: 'design',\n    tags: ['image-generation', 'ai-art', 'creative-tools'],\n    pricing: 'free',\n    logoUrl: 'https://imagine.meta.com/favicon.ico'\n  },\n  {\n    id: 'designer-microsoft',\n    name: 'Microsoft Designer',\n    description: 'Microsoft\\'s answer to Canva, focusing on design solutions with AI-powered features for creating professional graphics.',\n    url: 'https://designer.microsoft.com/',\n    category: 'design',\n    tags: ['graphic-design', 'presentation', 'marketing-materials'],\n    pricing: 'freemium',\n    logoUrl: 'https://designer.microsoft.com/favicon.ico'\n  },\n  {\n    id: 'runway',\n    name: 'Runway',\n    description: 'A creative platform for video production and editing with AI-powered tools for visual effects and content creation.',\n    url: 'https://app.runwayml.com/',\n    category: 'design',\n    tags: ['video-editing', 'visual-effects', 'content-creation'],\n    pricing: 'freemium',\n    logoUrl: 'https://app.runwayml.com/favicon.ico'\n  },\n  {\n    id: 'clipdrop',\n    name: 'Clipdrop',\n    description: 'Developed by Stability AI, for various image and video editing tasks with AI-powered features.',\n    url: 'https://clipdrop.co/',\n    category: 'design',\n    tags: ['image-editing', 'video-editing', 'content-creation'],\n    pricing: 'freemium',\n    logoUrl: 'https://clipdrop.co/favicon.ico'\n  },\n  {\n    id: 'interior-ai',\n    name: 'Interior AI',\n    description: 'Interior design tool for generating and visualizing room layouts using AI to create realistic interior designs.',\n    url: 'https://interiorai.com/',\n    category: 'design',\n    tags: ['interior-design', 'visualization', 'architecture'],\n    pricing: 'freemium',\n    logoUrl: 'https://interiorai.com/favicon.ico'\n  },\n  {\n    id: 'meshy-ai',\n    name: 'Meshy.ai',\n    description: 'For 3D modeling, used in architecture and design to create and manipulate 3D objects with AI assistance.',\n    url: 'https://www.meshy.ai',\n    category: 'design',\n    tags: ['3d-modeling', 'architecture', 'design'],\n    pricing: 'freemium',\n    logoUrl: 'https://www.meshy.ai/favicon.ico'\n  },\n  {\n    id: 'mnml-ai',\n    name: 'MNML AI',\n    description: 'Architecture design assistant that helps create minimalist architectural designs with AI guidance.',\n    url: 'https://mnml.ai',\n    category: 'design',\n    tags: ['architecture', 'design', 'minimalism'],\n    pricing: 'freemium',\n    logoUrl: 'https://mnml.ai/favicon.ico'\n  },\n  {\n    id: 'ulama-tech',\n    name: 'Ulama.tech',\n    description: 'For architectural design, specifically for structure and planning with AI-powered tools.',\n    url: 'http://ulama.tech',\n    category: 'design',\n    tags: ['architecture', 'structural-design', 'planning'],\n    pricing: 'freemium',\n    logoUrl: 'http://ulama.tech/favicon.ico'\n  },\n  {\n    id: 'weshop-ai',\n    name: 'WeShop',\n    description: 'Produce high-quality product images inexpensively and quickly using AI-generated visuals.',\n    url: 'https://www.weshop.ai/',\n    category: 'design',\n    tags: ['product-photography', 'e-commerce', 'marketing'],\n    pricing: 'paid',\n    logoUrl: 'https://www.weshop.ai/favicon.ico'\n  },\n  {\n    id: 'botika',\n    name: 'Botika',\n    description: 'Helps fashion retailers save on photo costs and boost sales using AI-generated models for product visualization.',\n    url: 'https://botika.io/',\n    category: 'design',\n    tags: ['fashion', 'e-commerce', 'product-visualization'],\n    pricing: 'paid',\n    logoUrl: 'https://botika.io/favicon.ico'\n  },\n  {\n    id: 'flux-ai',\n    name: 'Flux AI',\n    description: 'Enables creative image generation and animation with AI-powered tools for designers and artists.',\n    url: 'https://flux-ai.io/',\n    category: 'design',\n    tags: ['image-generation', 'animation', 'creative-tools'],\n    pricing: 'freemium',\n    logoUrl: 'https://flux-ai.io/favicon.ico'\n  },\n\n  // Development Tools\n  {\n    id: '10web',\n    name: '10Web',\n    description: 'An AI-Powered WordPress Platform for website development and management with automated features.',\n    url: 'https://10web.io/',\n    category: 'development',\n    tags: ['wordpress', 'website-builder', 'automation'],\n    pricing: 'paid',\n    logoUrl: 'https://10web.io/favicon.ico'\n  },\n  {\n    id: 'framer',\n    name: 'Framer',\n    description: 'A tool for building interactive websites and web applications with a focus on design and user experience.',\n    url: 'https://framer.com/',\n    category: 'development',\n    tags: ['website-builder', 'prototyping', 'design'],\n    pricing: 'freemium',\n    logoUrl: 'https://framer.com/favicon.ico'\n  },\n  {\n    id: 'github-copilot',\n    name: 'GitHub Copilot',\n    description: 'AI pair programmer that assists in code completion and suggestions within code editors, powered by OpenAI Codex.',\n    url: 'https://github.com/features/copilot',\n    category: 'development',\n    tags: ['coding-assistant', 'pair-programming', 'code-completion'],\n    pricing: 'paid',\n    logoUrl: 'https://github.com/favicon.ico'\n  },\n  {\n    id: 'github-spark',\n    name: 'GitHub Spark',\n    description: 'AI tool for building web applications using natural language, aiming to lower the barrier to software development.',\n    url: 'https://github.com/features',\n    category: 'development',\n    tags: ['web-development', 'no-code', 'ai-coding'],\n    pricing: 'paid',\n    logoUrl: 'https://github.com/favicon.ico'\n  },\n  {\n    id: 'langchain',\n    name: 'LangChain',\n    description: 'Builds AI-powered applications by connecting large language models with external data sources and tools.',\n    url: 'https://www.langchain.com/',\n    category: 'development',\n    tags: ['llm-framework', 'ai-development', 'integration'],\n    pricing: 'free',\n    logoUrl: 'https://www.langchain.com/favicon.ico'\n  },\n\n  // Communication\n  {\n    id: 'heygen',\n    name: 'HeyGen',\n    description: 'Produces studio quality videos with AI-generated avatars and voices for professional communication.',\n    url: 'https://www.heygen.com',\n    category: 'communication',\n    tags: ['video-creation', 'avatars', 'presentation'],\n    pricing: 'paid',\n    logoUrl: 'https://www.heygen.com/favicon.ico'\n  },\n  {\n    id: 'beautiful-ai',\n    name: 'Beautiful.ai',\n    description: 'Presentation tool that simplifies the creation of professional presentations using AI to handle design elements.',\n    url: 'http://beautiful.ai',\n    category: 'communication',\n    tags: ['presentation', 'slides', 'design'],\n    pricing: 'freemium',\n    logoUrl: 'http://beautiful.ai/favicon.ico'\n  },\n  {\n    id: 'gamma-app',\n    name: 'Gamma.app',\n    description: 'Creates engaging presentations by transforming ideas into visually appealing slides with AI assistance.',\n    url: 'https://gamma.app/',\n    category: 'communication',\n    tags: ['presentation', 'slides', 'design'],\n    pricing: 'freemium',\n    logoUrl: 'https://gamma.app/favicon.ico'\n  },\n  {\n    id: 'decktopus',\n    name: 'Decktopus',\n    description: 'An AI-powered tool that assists in creating presentation starting points with professional templates and designs.',\n    url: 'https://decktopus.com',\n    category: 'communication',\n    tags: ['presentation', 'slides', 'design'],\n    pricing: 'freemium',\n    logoUrl: 'https://decktopus.com/favicon.ico'\n  },\n  {\n    id: 'opus',\n    name: 'Opus',\n    description: 'Transforms long videos into short clips with a single click using generative AI for more effective communication.',\n    url: 'https://www.opus.pro/',\n    category: 'communication',\n    tags: ['video-editing', 'content-creation', 'summarization'],\n    pricing: 'paid',\n    logoUrl: 'https://www.opus.pro/favicon.ico'\n  },\n  {\n    id: 'vapi',\n    name: 'VAPI',\n    description: 'Builds and optimizes voice agents for customer service and communication applications.',\n    url: 'https://vapi.ai/',\n    category: 'communication',\n    tags: ['voice-agents', 'customer-service', 'automation'],\n    pricing: 'paid',\n    logoUrl: 'https://vapi.ai/favicon.ico'\n  },\n  {\n    id: 'sora',\n    name: 'Sora',\n    description: 'Turn text instructions into detailed video scenes for communication and presentation purposes.',\n    url: 'https://openai.com/sora',\n    category: 'communication',\n    tags: ['video-generation', 'content-creation', 'presentation'],\n    pricing: 'paid',\n    logoUrl: 'https://openai.com/favicon.ico'\n  },\n\n  // Collaboration\n  {\n    id: 'rancelab',\n    name: 'RanceLab',\n    description: 'Integrates WhatsApp with other platforms for better team collaboration and customer communication.',\n    url: 'https://www.rancelab.com/',\n    category: 'collaboration',\n    tags: ['whatsapp-integration', 'communication', 'customer-service'],\n    pricing: 'paid',\n    logoUrl: 'https://www.rancelab.com/favicon.ico'\n  },\n  {\n    id: 'lawgeex',\n    name: 'LawGeex',\n    description: 'Legal automation platform that uses AI to review contracts and facilitate legal collaboration.',\n    url: 'https://www.lawgeex.com/',\n    category: 'collaboration',\n    tags: ['legal', 'contract-review', 'automation'],\n    pricing: 'paid',\n    logoUrl: 'https://www.lawgeex.com/favicon.ico'\n  },\n\n  // Analytics & Data\n  {\n    id: 'browse-ai',\n    name: 'BrowseAI',\n    description: 'Facilitates data extraction and monitoring from websites for easy data acquisition and analysis.',\n    url: 'https://www.browse.ai/',\n    category: 'analytics',\n    tags: ['data-extraction', 'web-scraping', 'monitoring'],\n    pricing: 'freemium',\n    logoUrl: 'https://www.browse.ai/favicon.ico'\n  },\n  {\n    id: 'relevance-ai',\n    name: 'Relevance AI',\n    description: 'Platform providing AI-driven insights and analytics to enhance business decision-making.',\n    url: 'https://relevanceai.com/',\n    category: 'analytics',\n    tags: ['data-analysis', 'insights', 'business-intelligence'],\n    pricing: 'freemium',\n    logoUrl: 'https://relevanceai.com/favicon.ico'\n  },\n\n  // Project Management\n  {\n    id: 'make-com',\n    name: 'Make.com',\n    description: 'Automation platform for streamlining workflows and processes using AI to connect apps and automate tasks.',\n    url: 'https://make.com',\n    category: 'project-management',\n    tags: ['automation', 'workflow', 'integration'],\n    pricing: 'freemium',\n    logoUrl: 'https://make.com/favicon.ico'\n  },\n  {\n    id: 'zapier-central',\n    name: 'Zapier Central',\n    description: 'Automating tasks and workflows using AI-powered integrations between different applications and services.',\n    url: 'https://zapier.com/central',\n    category: 'project-management',\n    tags: ['automation', 'workflow', 'integration'],\n    pricing: 'freemium',\n    logoUrl: 'https://zapier.com/favicon.ico'\n  },\n  {\n    id: 'agents-ai',\n    name: 'Agents.ai',\n    description: 'Professional network of AI agents for business automation and task management.',\n    url: 'https://agents.ai/',\n    category: 'project-management',\n    tags: ['automation', 'ai-agents', 'task-management'],\n    pricing: 'paid',\n    logoUrl: 'https://agents.ai/favicon.ico'\n  },\n  {\n    id: 'napkin-ai',\n    name: 'Napkin.ai',\n    description: 'Useful for generating content, proofreading, and ideation feedback for project planning and documentation.',\n    url: 'http://napkin.ai',\n    category: 'project-management',\n    tags: ['content-generation', 'ideation', 'documentation'],\n    pricing: 'freemium',\n    logoUrl: 'http://napkin.ai/favicon.ico'\n  }\n];\n\n// Combine existing resources with new resources, avoiding duplicates\nconst combinedResources: Resource[] = [\n  ...existingResources,\n  ...newResources.filter(newResource =>\n    !existingResources.some(existingResource =>\n      existingResource.id === newResource.id ||\n      existingResource.name.toLowerCase() === newResource.name.toLowerCase()\n    )\n  )\n];\n\nexport { resourceCategories, combinedResources as resources };\n\n// Helper functions\nexport function getResourcesByCategory(categoryId: string): Resource[] {\n  return combinedResources.filter(resource => resource.category === categoryId);\n}\n\nexport function getCategoryById(categoryId: string): ResourceCategoryInfo | undefined {\n  return resourceCategories.find(category => category.id === categoryId);\n}\n"], "names": [], "mappings": ";;;;;AACA;;AAEA,4DAA4D;AAC5D,MAAM,eAA2B;IAC/B,qBAAqB;IACrB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAiB;YAAgB;SAAW;QACnD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAgB;YAAsB;SAAoB;QACjE,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAqB;YAAiB;SAAqB;QAClE,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAe;YAAiB;SAAoB;QAC3D,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAiB;YAAoB;SAAiB;QAC7D,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAe;YAAwB;SAAkB;QAChE,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAe;YAAuB;SAAgB;QAC7D,SAAS;QACT,SAAS;IACX;IAEA,eAAe;IACf;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAoB;YAAU;SAAiB;QACtD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAoB;YAAU;SAAiB;QACtD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAoB;YAAU;SAAiB;QACtD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAkB;YAAgB;SAAsB;QAC/D,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAiB;YAAkB;SAAmB;QAC7D,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAiB;YAAiB;SAAmB;QAC5D,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAmB;YAAiB;SAAe;QAC1D,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAe;YAAgB;SAAS;QAC/C,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAgB;YAAU;SAAa;QAC9C,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAgB;YAAqB;SAAW;QACvD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAuB;YAAc;SAAY;QACxD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAW;YAAc;SAAwB;QACxD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAoB;YAAa;SAAiB;QACzD,SAAS;QACT,SAAS;IACX;IAEA,oBAAoB;IACpB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAa;YAAmB;SAAa;QACpD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAmB;YAAe;SAAS;QAClD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAoB;YAAoB;SAAkB;QACjE,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAmB;YAAW;SAAY;QACjD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAiB;YAAkB;SAAc;QACxD,SAAS;QACT,SAAS;IACX;IAEA,gBAAgB;IAChB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAkB;YAAW;SAAe;QACnD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAgB;YAAU;SAAS;QAC1C,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAgB;YAAU;SAAS;QAC1C,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAgB;YAAU;SAAS;QAC1C,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAiB;YAAoB;SAAgB;QAC5D,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAgB;YAAoB;SAAa;QACxD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAoB;YAAoB;SAAe;QAC9D,SAAS;QACT,SAAS;IACX;IAEA,gBAAgB;IAChB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAwB;YAAiB;SAAmB;QACnE,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAS;YAAmB;SAAa;QAChD,SAAS;QACT,SAAS;IACX;IAEA,mBAAmB;IACnB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAmB;YAAgB;SAAa;QACvD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAiB;YAAY;SAAwB;QAC5D,SAAS;QACT,SAAS;IACX;IAEA,qBAAqB;IACrB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAc;YAAY;SAAc;QAC/C,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAc;YAAY;SAAc;QAC/C,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAc;YAAa;SAAkB;QACpD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAsB;YAAY;SAAgB;QACzD,SAAS;QACT,SAAS;IACX;CACD;AAED,qEAAqE;AACrE,MAAM,oBAAgC;OACjC,uIAAA,CAAA,YAAiB;OACjB,aAAa,MAAM,CAAC,CAAA,cACrB,CAAC,uIAAA,CAAA,YAAiB,CAAC,IAAI,CAAC,CAAA,mBACtB,iBAAiB,EAAE,KAAK,YAAY,EAAE,IACtC,iBAAiB,IAAI,CAAC,WAAW,OAAO,YAAY,IAAI,CAAC,WAAW;CAGzE;;AAKM,SAAS,uBAAuB,UAAkB;IACvD,OAAO,kBAAkB,MAAM,CAAC,CAAA,WAAY,SAAS,QAAQ,KAAK;AACpE;AAEO,SAAS,gBAAgB,UAAkB;IAChD,OAAO,uIAAA,CAAA,qBAAkB,CAAC,IAAI,CAAC,CAAA,WAAY,SAAS,EAAE,KAAK;AAC7D", "debugId": null}}, {"offset": {"line": 791, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/learning/resources/page.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { \n  faBolt, \n  faTasks, \n  faPalette, \n  faCode, \n  faMagnifyingGlassChart, \n  faChartLine, \n  faComments, \n  faUsersGear,\n  faArrowRight\n} from '@fortawesome/free-solid-svg-icons';\nimport { resourceCategories } from '@/data/resources';\n\n// Map category IDs to FontAwesome icons\nconst categoryIcons: Record<string, any> = {\n  'productivity': faBolt,\n  'project-management': faTasks,\n  'design': faPalette,\n  'development': faCode,\n  'research': faMagnifyingGlass<PERSON>hart,\n  'analytics': faChartLine,\n  'communication': faComments,\n  'collaboration': faUsersGear\n};\n\nexport default function ResourcesPage() {\n  return (\n    <div className=\"space-y-8\">\n      <div className=\"rounded-lg bg-gradient-to-r from-teal-600 to-emerald-700 p-8 text-white shadow-lg\">\n        <h1 className=\"mb-4 text-3xl font-bold\">Resources Directory</h1>\n        <p className=\"mb-6 text-lg\">\n          Explore our curated collection of tools and services for consulting professionals. Find resources for productivity, project management, design, development, research, and more.\n        </p>\n      </div>\n\n      <div className=\"grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4\">\n        {resourceCategories.map((category) => (\n          <Link\n            key={category.id}\n            href={`/learning/resources/${category.id}`}\n            className=\"group flex flex-col rounded-lg border border-gray-200 bg-white p-6 shadow-sm transition hover:shadow-md dark:border-gray-800 dark:bg-gray-900\"\n          >\n            <div className=\"mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-teal-100 text-teal-600 dark:bg-teal-900/30 dark:text-teal-400\">\n              <FontAwesomeIcon icon={categoryIcons[category.id]} className=\"h-6 w-6\" />\n            </div>\n            <h3 className=\"mb-2 text-xl font-bold text-gray-900 group-hover:text-teal-600 dark:text-white dark:group-hover:text-teal-400\">\n              {category.name}\n            </h3>\n            <p className=\"mb-4 flex-1 text-gray-600 dark:text-gray-400\">{category.description}</p>\n            <div className=\"mt-auto flex items-center text-sm font-medium text-teal-600 dark:text-teal-400\">\n              Browse resources\n              <FontAwesomeIcon icon={faArrowRight} className=\"ml-1 h-3 w-3\" />\n            </div>\n          </Link>\n        ))}\n      </div>\n\n      <div className=\"rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-800 dark:bg-gray-900\">\n        <h2 className=\"mb-4 text-2xl font-bold\">About Our Resources Directory</h2>\n        <p className=\"mb-4 text-gray-600 dark:text-gray-400\">\n          This directory is regularly updated with the latest tools and services that can help consulting professionals work more efficiently and deliver better results for clients. Our team carefully evaluates each resource before adding it to the directory.\n        </p>\n        <p className=\"mb-4 text-gray-600 dark:text-gray-400\">\n          Each category includes a variety of tools with different pricing models, from free to enterprise-level solutions, allowing you to find options that fit your specific needs and budget.\n        </p>\n        <div className=\"mt-4 rounded-md bg-teal-50 p-4 dark:bg-teal-900/20\">\n          <h3 className=\"mb-2 font-semibold text-teal-800 dark:text-teal-300\">Have a suggestion?</h3>\n          <p className=\"text-teal-700 dark:text-teal-400\">\n            If you know of a great tool that should be included in our directory, please let us know. We're always looking to expand our collection with valuable resources.\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAWA;AAAA;AAhBA;;;;;;AAkBA,wCAAwC;AACxC,MAAM,gBAAqC;IACzC,gBAAgB,2KAAA,CAAA,SAAM;IACtB,sBAAsB,2KAAA,CAAA,UAAO;IAC7B,UAAU,2KAAA,CAAA,YAAS;IACnB,eAAe,2KAAA,CAAA,SAAM;IACrB,YAAY,2KAAA,CAAA,yBAAsB;IAClC,aAAa,2KAAA,CAAA,cAAW;IACxB,iBAAiB,2KAAA,CAAA,aAAU;IAC3B,iBAAiB,2KAAA,CAAA,cAAW;AAC9B;AAEe,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,6LAAC;wBAAE,WAAU;kCAAe;;;;;;;;;;;;0BAK9B,6LAAC;gBAAI,WAAU;0BACZ,uIAAA,CAAA,qBAAkB,CAAC,GAAG,CAAC,CAAC,yBACvB,6LAAC,+JAAA,CAAA,UAAI;wBAEH,MAAM,CAAC,oBAAoB,EAAE,SAAS,EAAE,EAAE;wBAC1C,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,uKAAA,CAAA,kBAAe;oCAAC,MAAM,aAAa,CAAC,SAAS,EAAE,CAAC;oCAAE,WAAU;;;;;;;;;;;0CAE/D,6LAAC;gCAAG,WAAU;0CACX,SAAS,IAAI;;;;;;0CAEhB,6LAAC;gCAAE,WAAU;0CAAgD,SAAS,WAAW;;;;;;0CACjF,6LAAC;gCAAI,WAAU;;oCAAiF;kDAE9F,6LAAC,uKAAA,CAAA,kBAAe;wCAAC,MAAM,2KAAA,CAAA,eAAY;wCAAE,WAAU;;;;;;;;;;;;;uBAb5C,SAAS,EAAE;;;;;;;;;;0BAmBtB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,6LAAC;wBAAE,WAAU;kCAAwC;;;;;;kCAGrD,6LAAC;wBAAE,WAAU;kCAAwC;;;;;;kCAGrD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAsD;;;;;;0CACpE,6LAAC;gCAAE,WAAU;0CAAmC;;;;;;;;;;;;;;;;;;;;;;;;AAO1D;KAjDwB", "debugId": null}}]}