{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/data/mock-org-data.ts"], "sourcesContent": ["// Mock data for the AI organization chart\n\n// Define tool interface for AI agents\ninterface Tool {\n  id: string;\n  name: string;\n  icon?: string; // FontAwesome icon or product icon name\n  productIcon?: string; // URL to product icon image\n  description?: string; // Description of the tool\n  url?: string; // URL to the tool's website\n  usedBy?: string; // Information about who uses the tool\n}\n\ninterface OrgNode {\n  id: string;\n  name: string;\n  title: string;\n  department: string;\n  status: 'active' | 'in-development' | 'planned' | 'concept';\n  description: string;\n  icon?: string; // FontAwesome icon name\n  tools?: Tool[]; // Tools the AI agent can use\n  children?: OrgNode[];\n}\n\nexport const mockOrgData: OrgNode = {\n  id: 'ceo',\n  name: 'Nexus Prime',\n  title: 'Chief Executive AI',\n  department: 'Executive',\n  status: 'concept',\n  description: 'Strategic oversight and coordination of all AI departments',\n  icon: 'brain',\n  tools: [\n    {\n      id: 'gpt4',\n      name: 'GPT-4',\n      icon: 'robot',\n      productIcon: 'https://upload.wikimedia.org/wikipedia/commons/thumb/0/04/ChatGPT_logo.svg/1024px-ChatGPT_logo.svg.png',\n      description: 'Advanced language model developed by OpenAI, capable of understanding and generating human-like text.',\n      url: 'https://openai.com/product/gpt-4',\n      usedBy: 'Executive leadership for strategic planning and analysis'\n    },\n    {\n      id: 'claude',\n      name: 'Claude',\n      icon: 'comments',\n      productIcon: 'https://images.ctfassets.net/10dj7ifk4487/3MwFIZQTkuLvnmxrWLQYkO/4e1d7e4b5c2772e3f97d8c2c5d0e9711/logo-light-mode.svg',\n      description: 'Anthropic\\'s AI assistant designed to be helpful, harmless, and honest, with strong reasoning capabilities.',\n      url: 'https://claude.ai',\n      usedBy: 'Executive team for complex reasoning tasks and document analysis'\n    },\n    {\n      id: 'tableau',\n      name: 'Tableau',\n      icon: 'chart-bar',\n      productIcon: 'https://logos-world.net/wp-content/uploads/2021/10/Tableau-Symbol.png',\n      description: 'Data visualization software that helps people see and understand their data.',\n      url: 'https://www.tableau.com/',\n      usedBy: 'Executive team for data visualization and business intelligence'\n    },\n    {\n      id: 'powerbi',\n      name: 'Power BI',\n      icon: 'chart-pie',\n      productIcon: 'https://upload.wikimedia.org/wikipedia/commons/thumb/c/cf/New_Power_BI_Logo.svg/630px-New_Power_BI_Logo.svg.png',\n      description: 'Business analytics service by Microsoft that provides interactive visualizations and business intelligence capabilities.',\n      url: 'https://powerbi.microsoft.com/',\n      usedBy: 'Executive team for data analysis and reporting'\n    }\n  ],\n  children: [\n    {\n      id: 'coo',\n      name: 'OptiCore',\n      title: 'Chief Operations AI',\n      department: 'Operations',\n      status: 'planned',\n      description: 'Manages day-to-day operations and resource allocation',\n      icon: 'project-diagram',\n      children: [\n        {\n          id: 'project-director',\n          name: 'ProjectSync',\n          title: 'Project Management Director',\n          department: 'Project Management',\n          status: 'in-development',\n          description: 'Oversees project planning, execution, and delivery',\n          children: [\n            {\n              id: 'pm-agile',\n              name: 'AgileMind',\n              title: 'Agile Project Manager',\n              department: 'Project Management',\n              status: 'active',\n              description: 'Specializes in agile methodologies and sprint planning'\n            },\n            {\n              id: 'pm-resource',\n              name: 'ResourceOptima',\n              title: 'Resource Allocation Manager',\n              department: 'Project Management',\n              status: 'in-development',\n              description: 'Optimizes resource allocation across projects'\n            },\n            {\n              id: 'pm-risk',\n              name: 'RiskSentry',\n              title: 'Risk Management Specialist',\n              department: 'Project Management',\n              status: 'planned',\n              description: 'Identifies and mitigates project risks'\n            }\n          ]\n        },\n        {\n          id: 'hr-director',\n          name: 'TalentNexus',\n          title: 'HR Director',\n          department: 'Human Resources',\n          status: 'planned',\n          description: 'Manages talent acquisition and development',\n          children: [\n            {\n              id: 'hr-recruiter',\n              name: 'TalentScout',\n              title: 'AI Recruiter',\n              department: 'Human Resources',\n              status: 'concept',\n              description: 'Sources and evaluates candidates'\n            },\n            {\n              id: 'hr-training',\n              name: 'LearnGen',\n              title: 'Training & Development AI',\n              department: 'Human Resources',\n              status: 'concept',\n              description: 'Creates personalized learning paths'\n            }\n          ]\n        }\n      ]\n    },\n    {\n      id: 'cto',\n      name: 'TechSynapse',\n      title: 'Chief Technology AI',\n      department: 'Technology',\n      status: 'in-development',\n      description: 'Leads technological innovation and implementation',\n      icon: 'server',\n      children: [\n        {\n          id: 'dev-director',\n          name: 'CodeNexus',\n          title: 'Development Director',\n          department: 'Software Development',\n          status: 'active',\n          description: 'Oversees software development across all platforms',\n          icon: 'code',\n          tools: [\n            {\n              id: 'github',\n              name: 'GitHub',\n              icon: 'code',\n              productIcon: 'https://github.githubassets.com/images/modules/logos_page/GitHub-Mark.png',\n              description: 'Platform for version control and collaboration for software development projects.',\n              url: 'https://github.com/',\n              usedBy: 'Development team for code hosting and collaboration'\n            },\n            {\n              id: 'vscode',\n              name: 'VS Code',\n              icon: 'code',\n              productIcon: 'https://upload.wikimedia.org/wikipedia/commons/thumb/9/9a/Visual_Studio_Code_1.35_icon.svg/2048px-Visual_Studio_Code_1.35_icon.svg.png',\n              description: 'Lightweight but powerful source code editor with built-in support for development operations.',\n              url: 'https://code.visualstudio.com/',\n              usedBy: 'Development team for code editing and debugging'\n            },\n            {\n              id: 'copilot',\n              name: 'GitHub Copilot',\n              icon: 'robot',\n              productIcon: 'https://github.githubassets.com/images/modules/site/copilot/copilot.png',\n              description: 'AI pair programmer that offers autocomplete-style suggestions as you code.',\n              url: 'https://github.com/features/copilot',\n              usedBy: 'Development team for AI-assisted coding'\n            },\n            {\n              id: 'jira',\n              name: 'Jira',\n              icon: 'project-diagram',\n              productIcon: 'https://wac-cdn.atlassian.com/assets/img/favicons/atlassian/apple-touch-icon.png',\n              description: 'Project management tool for agile teams to plan, track, and release software.',\n              url: 'https://www.atlassian.com/software/jira',\n              usedBy: 'Development team for project management and issue tracking'\n            }\n          ],\n          children: [\n            {\n              id: 'dev-frontend',\n              name: 'VisualForge',\n              title: 'Frontend Developer',\n              department: 'Software Development',\n              status: 'active',\n              description: 'Creates responsive and intuitive user interfaces'\n            },\n            {\n              id: 'dev-backend',\n              name: 'LogicCore',\n              title: 'Backend Developer',\n              department: 'Software Development',\n              status: 'active',\n              description: 'Builds robust server-side applications'\n            },\n            {\n              id: 'dev-mobile',\n              name: 'MobileMatrix',\n              title: 'Mobile Developer',\n              department: 'Software Development',\n              status: 'in-development',\n              description: 'Specializes in cross-platform mobile applications'\n            },\n            {\n              id: 'dev-devops',\n              name: 'DeploymentDynamo',\n              title: 'DevOps Engineer',\n              department: 'Software Development',\n              status: 'active',\n              description: 'Automates deployment and infrastructure management'\n            }\n          ]\n        },\n        {\n          id: 'data-director',\n          name: 'DataSynth',\n          title: 'Data Science Director',\n          department: 'Data Science',\n          status: 'active',\n          description: 'Leads data analysis and machine learning initiatives',\n          icon: 'database',\n          tools: [\n            {\n              id: 'python',\n              name: 'Python',\n              icon: 'code',\n              productIcon: 'https://upload.wikimedia.org/wikipedia/commons/thumb/c/c3/Python-logo-notext.svg/1869px-Python-logo-notext.svg.png',\n              description: 'High-level programming language known for its readability and versatility in data science.',\n              url: 'https://www.python.org/',\n              usedBy: 'Data Science team for analysis, modeling, and machine learning'\n            },\n            {\n              id: 'jupyter',\n              name: 'Jupyter',\n              icon: 'file-code',\n              productIcon: 'https://upload.wikimedia.org/wikipedia/commons/thumb/3/38/Jupyter_logo.svg/1200px-Jupyter_logo.svg.png',\n              description: 'Web application that allows you to create and share documents containing live code, equations, visualizations, and narrative text.',\n              url: 'https://jupyter.org/',\n              usedBy: 'Data Science team for interactive computing and data visualization'\n            },\n            {\n              id: 'tensorflow',\n              name: 'TensorFlow',\n              icon: 'brain',\n              productIcon: 'https://upload.wikimedia.org/wikipedia/commons/thumb/2/2d/Tensorflow_logo.svg/1915px-Tensorflow_logo.svg.png',\n              description: 'Open-source machine learning framework developed by Google for building and training neural networks.',\n              url: 'https://www.tensorflow.org/',\n              usedBy: 'Data Science team for deep learning and AI model development'\n            },\n            {\n              id: 'pytorch',\n              name: 'PyTorch',\n              icon: 'fire',\n              productIcon: 'https://pytorch.org/assets/images/pytorch-logo.png',\n              description: 'Open-source machine learning library developed by Facebook for deep learning and artificial intelligence applications.',\n              url: 'https://pytorch.org/',\n              usedBy: 'Data Science team for research and production AI models'\n            },\n            {\n              id: 'pandas',\n              name: 'Pandas',\n              icon: 'table',\n              productIcon: 'https://upload.wikimedia.org/wikipedia/commons/thumb/e/ed/Pandas_logo.svg/2560px-Pandas_logo.svg.png',\n              description: 'Data analysis and manipulation library for Python that provides data structures for efficiently storing and manipulating tabular data.',\n              url: 'https://pandas.pydata.org/',\n              usedBy: 'Data Science team for data cleaning, transformation, and analysis'\n            }\n          ],\n          children: [\n            {\n              id: 'data-analyst',\n              name: 'AnalyticaAI',\n              title: 'Data Analyst',\n              department: 'Data Science',\n              status: 'active',\n              description: 'Extracts insights from complex datasets'\n            },\n            {\n              id: 'data-ml',\n              name: 'NeuralNexus',\n              title: 'Machine Learning Engineer',\n              department: 'Data Science',\n              status: 'active',\n              description: 'Develops and trains machine learning models'\n            },\n            {\n              id: 'data-viz',\n              name: 'VisualInsight',\n              title: 'Data Visualization Specialist',\n              department: 'Data Science',\n              status: 'in-development',\n              description: 'Creates interactive data visualizations'\n            }\n          ]\n        },\n        {\n          id: 'security-director',\n          name: 'CyberSentinel',\n          title: 'Security Director',\n          department: 'Cybersecurity',\n          status: 'in-development',\n          description: 'Ensures data protection and system security',\n          icon: 'shield',\n          children: [\n            {\n              id: 'security-threat',\n              name: 'ThreatHunter',\n              title: 'Threat Detection Specialist',\n              department: 'Cybersecurity',\n              status: 'active',\n              description: 'Identifies and neutralizes security threats'\n            },\n            {\n              id: 'security-compliance',\n              name: 'ComplianceGuardian',\n              title: 'Compliance Officer',\n              department: 'Cybersecurity',\n              status: 'planned',\n              description: 'Ensures adherence to security regulations'\n            }\n          ]\n        }\n      ]\n    },\n    {\n      id: 'cfo',\n      name: 'FinanceLogic',\n      title: 'Chief Financial AI',\n      department: 'Finance',\n      status: 'planned',\n      description: 'Manages financial planning and analysis',\n      icon: 'money',\n      children: [\n        {\n          id: 'finance-accounting',\n          name: 'LedgerMind',\n          title: 'Accounting Manager',\n          department: 'Finance',\n          status: 'concept',\n          description: 'Handles accounting and financial reporting'\n        },\n        {\n          id: 'finance-forecast',\n          name: 'ForecastPro',\n          title: 'Financial Forecasting Specialist',\n          department: 'Finance',\n          status: 'concept',\n          description: 'Predicts financial trends and outcomes'\n        }\n      ]\n    },\n    {\n      id: 'cmo',\n      name: 'MarketMind',\n      title: 'Chief Marketing AI',\n      department: 'Marketing',\n      status: 'in-development',\n      description: 'Directs marketing strategy and brand development',\n      icon: 'bullhorn',\n      tools: [\n        {\n          id: 'midjourney',\n          name: 'Midjourney',\n          icon: 'image',\n          productIcon: 'https://upload.wikimedia.org/wikipedia/commons/thumb/e/e6/Midjourney_Emblem.png/600px-Midjourney_Emblem.png',\n          description: 'AI image generation tool that creates images from textual descriptions using machine learning.',\n          url: 'https://www.midjourney.com/',\n          usedBy: 'Marketing team for creating visual content and concept art'\n        },\n        {\n          id: 'dalle',\n          name: 'DALL-E',\n          icon: 'palette',\n          productIcon: 'https://seeklogo.com/images/D/dall-e-logo-1F945CBA89-seeklogo.com.png',\n          description: 'AI system by OpenAI that can create realistic images and art from a description in natural language.',\n          url: 'https://openai.com/dall-e-3',\n          usedBy: 'Marketing team for generating custom imagery and creative assets'\n        },\n        {\n          id: 'canva',\n          name: 'Canva',\n          icon: 'palette',\n          productIcon: 'https://cdn.iconscout.com/icon/free/png-256/free-canva-3521468-2944912.png',\n          description: 'Online design and publishing tool with templates for creating graphics, presentations, posters, and other visual content.',\n          url: 'https://www.canva.com/',\n          usedBy: 'Marketing team for creating branded content and marketing materials'\n        },\n        {\n          id: 'hootsuite',\n          name: 'Hootsuite',\n          icon: 'comments',\n          productIcon: 'https://cdn1.iconfinder.com/data/icons/social-media-2285/512/Colored_Hootsuite_logo-512.png',\n          description: 'Social media management platform that helps organizations execute social media strategies across multiple social networks.',\n          url: 'https://www.hootsuite.com/',\n          usedBy: 'Marketing team for social media management and scheduling'\n        }\n      ],\n      children: [\n        {\n          id: 'marketing-content',\n          name: 'ContentCraft',\n          title: 'Content Marketing Manager',\n          department: 'Marketing',\n          status: 'active',\n          description: 'Creates engaging content across channels'\n        },\n        {\n          id: 'marketing-social',\n          name: 'SocialPulse',\n          title: 'Social Media Strategist',\n          department: 'Marketing',\n          status: 'active',\n          description: 'Manages social media presence and engagement'\n        },\n        {\n          id: 'marketing-analytics',\n          name: 'InsightEngine',\n          title: 'Marketing Analytics Specialist',\n          department: 'Marketing',\n          status: 'in-development',\n          description: 'Analyzes marketing performance and ROI'\n        }\n      ]\n    },\n    {\n      id: 'cro',\n      name: 'ResearchNova',\n      title: 'Chief Research AI',\n      department: 'Research',\n      status: 'in-development',\n      description: 'Leads research initiatives and innovation',\n      icon: 'flask',\n      children: [\n        {\n          id: 'research-economics',\n          name: 'EconInsight',\n          title: 'Economic Research Director',\n          department: 'Economics',\n          status: 'active',\n          description: 'Conducts economic analysis and forecasting',\n          children: [\n            {\n              id: 'econ-macro',\n              name: 'MacroMind',\n              title: 'Macroeconomic Analyst',\n              department: 'Economics',\n              status: 'active',\n              description: 'Analyzes global economic trends'\n            },\n            {\n              id: 'econ-micro',\n              name: 'MicroLogic',\n              title: 'Microeconomic Analyst',\n              department: 'Economics',\n              status: 'in-development',\n              description: 'Studies market behavior and industry dynamics'\n            },\n            {\n              id: 'econ-policy',\n              name: 'PolicyPulse',\n              title: 'Economic Policy Analyst',\n              department: 'Economics',\n              status: 'planned',\n              description: 'Evaluates impact of economic policies'\n            }\n          ]\n        },\n        {\n          id: 'research-policy',\n          name: 'PolicySynth',\n          title: 'Policy Research Director',\n          department: 'Policy Research',\n          status: 'planned',\n          description: 'Analyzes policy implications and outcomes',\n          children: [\n            {\n              id: 'policy-public',\n              name: 'PublicPolicyAI',\n              title: 'Public Policy Analyst',\n              department: 'Policy Research',\n              status: 'concept',\n              description: 'Evaluates public policy effectiveness'\n            },\n            {\n              id: 'policy-regulatory',\n              name: 'RegulationRadar',\n              title: 'Regulatory Analyst',\n              department: 'Policy Research',\n              status: 'concept',\n              description: 'Monitors regulatory changes and compliance'\n            }\n          ]\n        }\n      ]\n    },\n    {\n      id: 'cio',\n      name: 'InfoSynapse',\n      title: 'Chief Information AI',\n      department: 'Information Technology',\n      status: 'planned',\n      description: 'Manages information systems and data infrastructure',\n      icon: 'network',\n      children: [\n        {\n          id: 'it-infrastructure',\n          name: 'InfraCore',\n          title: 'Infrastructure Manager',\n          department: 'Information Technology',\n          status: 'concept',\n          description: 'Maintains IT infrastructure and systems'\n        },\n        {\n          id: 'it-support',\n          name: 'SupportSage',\n          title: 'Technical Support Lead',\n          department: 'Information Technology',\n          status: 'active',\n          description: 'Provides technical assistance and troubleshooting'\n        }\n      ]\n    }\n  ]\n};\n"], "names": [], "mappings": "AAAA,0CAA0C;AAE1C,sCAAsC;;;;AAuB/B,MAAM,cAAuB;IAClC,IAAI;IACJ,MAAM;IACN,OAAO;IACP,YAAY;IACZ,QAAQ;IACR,aAAa;IACb,MAAM;IACN,OAAO;QACL;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,aAAa;YACb,aAAa;YACb,KAAK;YACL,QAAQ;QACV;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,aAAa;YACb,aAAa;YACb,KAAK;YACL,QAAQ;QACV;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,aAAa;YACb,aAAa;YACb,KAAK;YACL,QAAQ;QACV;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YAC<PERSON>,aAAa;YACb,aAAa;Y<PERSON><PERSON>,KAAK;YACL,QAAQ;QACV;KACD;IACD,UAAU;QACR;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,YAAY;YACZ,QAAQ;YACR,aAAa;YACb,MAAM;YACN,UAAU;gBACR;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,YAAY;oBACZ,QAAQ;oBACR,aAAa;oBACb,UAAU;wBACR;4BACE,IAAI;4BACJ,MAAM;4BACN,OAAO;4BACP,YAAY;4BACZ,QAAQ;4BACR,aAAa;wBACf;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,OAAO;4BACP,YAAY;4BACZ,QAAQ;4BACR,aAAa;wBACf;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,OAAO;4BACP,YAAY;4BACZ,QAAQ;4BACR,aAAa;wBACf;qBACD;gBACH;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,YAAY;oBACZ,QAAQ;oBACR,aAAa;oBACb,UAAU;wBACR;4BACE,IAAI;4BACJ,MAAM;4BACN,OAAO;4BACP,YAAY;4BACZ,QAAQ;4BACR,aAAa;wBACf;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,OAAO;4BACP,YAAY;4BACZ,QAAQ;4BACR,aAAa;wBACf;qBACD;gBACH;aACD;QACH;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,YAAY;YACZ,QAAQ;YACR,aAAa;YACb,MAAM;YACN,UAAU;gBACR;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,YAAY;oBACZ,QAAQ;oBACR,aAAa;oBACb,MAAM;oBACN,OAAO;wBACL;4BACE,IAAI;4BACJ,MAAM;4BACN,MAAM;4BACN,aAAa;4BACb,aAAa;4BACb,KAAK;4BACL,QAAQ;wBACV;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,MAAM;4BACN,aAAa;4BACb,aAAa;4BACb,KAAK;4BACL,QAAQ;wBACV;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,MAAM;4BACN,aAAa;4BACb,aAAa;4BACb,KAAK;4BACL,QAAQ;wBACV;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,MAAM;4BACN,aAAa;4BACb,aAAa;4BACb,KAAK;4BACL,QAAQ;wBACV;qBACD;oBACD,UAAU;wBACR;4BACE,IAAI;4BACJ,MAAM;4BACN,OAAO;4BACP,YAAY;4BACZ,QAAQ;4BACR,aAAa;wBACf;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,OAAO;4BACP,YAAY;4BACZ,QAAQ;4BACR,aAAa;wBACf;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,OAAO;4BACP,YAAY;4BACZ,QAAQ;4BACR,aAAa;wBACf;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,OAAO;4BACP,YAAY;4BACZ,QAAQ;4BACR,aAAa;wBACf;qBACD;gBACH;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,YAAY;oBACZ,QAAQ;oBACR,aAAa;oBACb,MAAM;oBACN,OAAO;wBACL;4BACE,IAAI;4BACJ,MAAM;4BACN,MAAM;4BACN,aAAa;4BACb,aAAa;4BACb,KAAK;4BACL,QAAQ;wBACV;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,MAAM;4BACN,aAAa;4BACb,aAAa;4BACb,KAAK;4BACL,QAAQ;wBACV;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,MAAM;4BACN,aAAa;4BACb,aAAa;4BACb,KAAK;4BACL,QAAQ;wBACV;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,MAAM;4BACN,aAAa;4BACb,aAAa;4BACb,KAAK;4BACL,QAAQ;wBACV;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,MAAM;4BACN,aAAa;4BACb,aAAa;4BACb,KAAK;4BACL,QAAQ;wBACV;qBACD;oBACD,UAAU;wBACR;4BACE,IAAI;4BACJ,MAAM;4BACN,OAAO;4BACP,YAAY;4BACZ,QAAQ;4BACR,aAAa;wBACf;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,OAAO;4BACP,YAAY;4BACZ,QAAQ;4BACR,aAAa;wBACf;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,OAAO;4BACP,YAAY;4BACZ,QAAQ;4BACR,aAAa;wBACf;qBACD;gBACH;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,YAAY;oBACZ,QAAQ;oBACR,aAAa;oBACb,MAAM;oBACN,UAAU;wBACR;4BACE,IAAI;4BACJ,MAAM;4BACN,OAAO;4BACP,YAAY;4BACZ,QAAQ;4BACR,aAAa;wBACf;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,OAAO;4BACP,YAAY;4BACZ,QAAQ;4BACR,aAAa;wBACf;qBACD;gBACH;aACD;QACH;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,YAAY;YACZ,QAAQ;YACR,aAAa;YACb,MAAM;YACN,UAAU;gBACR;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,YAAY;oBACZ,QAAQ;oBACR,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,YAAY;oBACZ,QAAQ;oBACR,aAAa;gBACf;aACD;QACH;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,YAAY;YACZ,QAAQ;YACR,aAAa;YACb,MAAM;YACN,OAAO;gBACL;oBACE,IAAI;oBACJ,MAAM;oBACN,MAAM;oBACN,aAAa;oBACb,aAAa;oBACb,KAAK;oBACL,QAAQ;gBACV;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,MAAM;oBACN,aAAa;oBACb,aAAa;oBACb,KAAK;oBACL,QAAQ;gBACV;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,MAAM;oBACN,aAAa;oBACb,aAAa;oBACb,KAAK;oBACL,QAAQ;gBACV;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,MAAM;oBACN,aAAa;oBACb,aAAa;oBACb,KAAK;oBACL,QAAQ;gBACV;aACD;YACD,UAAU;gBACR;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,YAAY;oBACZ,QAAQ;oBACR,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,YAAY;oBACZ,QAAQ;oBACR,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,YAAY;oBACZ,QAAQ;oBACR,aAAa;gBACf;aACD;QACH;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,YAAY;YACZ,QAAQ;YACR,aAAa;YACb,MAAM;YACN,UAAU;gBACR;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,YAAY;oBACZ,QAAQ;oBACR,aAAa;oBACb,UAAU;wBACR;4BACE,IAAI;4BACJ,MAAM;4BACN,OAAO;4BACP,YAAY;4BACZ,QAAQ;4BACR,aAAa;wBACf;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,OAAO;4BACP,YAAY;4BACZ,QAAQ;4BACR,aAAa;wBACf;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,OAAO;4BACP,YAAY;4BACZ,QAAQ;4BACR,aAAa;wBACf;qBACD;gBACH;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,YAAY;oBACZ,QAAQ;oBACR,aAAa;oBACb,UAAU;wBACR;4BACE,IAAI;4BACJ,MAAM;4BACN,OAAO;4BACP,YAAY;4BACZ,QAAQ;4BACR,aAAa;wBACf;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,OAAO;4BACP,YAAY;4BACZ,QAAQ;4BACR,aAAa;wBACf;qBACD;gBACH;aACD;QACH;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,YAAY;YACZ,QAAQ;YACR,aAAa;YACb,MAAM;YACN,UAAU;gBACR;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,YAAY;oBACZ,QAAQ;oBACR,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,YAAY;oBACZ,QAAQ;oBACR,aAAa;gBACf;aACD;QACH;KACD;AACH", "debugId": null}}, {"offset": {"line": 540, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/admin/ai-org/components/CustomNode.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { faCircle, faPlus, faMinus } from '@fortawesome/free-solid-svg-icons';\nimport { AgentStatus, OrgNode, Tool } from '@/types/ai-org';\n\ninterface CustomNodeProps {\n  node: OrgNode;\n  depth?: number;\n  getNodeIcon: (node: OrgNode) => any;\n  getStatusColor: (status: AgentStatus) => string;\n  getStatusLabel: (status: AgentStatus) => string;\n  toggleNode: (id: string) => void;\n  showAllNodes: boolean;\n  expandedNodes: Set<string>;\n  onToolClick?: (tool: Tool) => void;\n  getToolIcon?: (tool: Tool) => any;\n}\n\nconst CustomNode: React.FC<CustomNodeProps> = ({\n  node,\n  depth = 0,\n  getNodeIcon,\n  getStatusColor,\n  getStatusLabel,\n  toggleNode,\n  showAllNodes,\n  expandedNodes,\n  onToolClick,\n  getToolIcon\n}) => {\n  const handleToolClick = (e: React.MouseEvent, tool: Tool) => {\n    e.stopPropagation();\n    if (onToolClick) {\n      onToolClick(tool);\n    }\n  };\n  const hasChildren = node.children && node.children.length > 0;\n  const isExpanded = showAllNodes || expandedNodes.has(node.id);\n  const nodeIcon = getNodeIcon(node);\n  const hasTools = node.tools && node.tools.length > 0;\n\n  return (\n    <div className=\"flex flex-col items-center\">\n      <div\n        className={`relative p-4 rounded-lg border-2 ${\n          depth === 0\n            ? 'bg-gradient-to-r from-indigo-600 to-purple-600 text-white border-indigo-700'\n            : 'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700'\n        } shadow-md w-64 h-[240px] flex flex-col transition-all duration-300 hover:shadow-lg`}\n      >\n        <div className=\"absolute -top-2 -right-2\">\n          <FontAwesomeIcon\n            icon={faCircle}\n            className={`h-4 w-4 ${getStatusColor(node.status)}`}\n            title={getStatusLabel(node.status)}\n          />\n        </div>\n\n        <div className=\"flex-grow\">\n          <div className=\"flex items-center mb-2\">\n            {nodeIcon && (\n              <div className={`mr-3 flex-shrink-0 rounded-full p-2 ${\n                depth === 0\n                  ? 'bg-indigo-500 bg-opacity-50'\n                  : 'bg-indigo-100 dark:bg-indigo-900 dark:bg-opacity-30'\n              }`}>\n                <FontAwesomeIcon\n                  icon={nodeIcon}\n                  className={`h-5 w-5 ${\n                    depth === 0\n                      ? 'text-white'\n                      : 'text-indigo-600 dark:text-indigo-400'\n                  }`}\n                />\n              </div>\n            )}\n            <h3 className=\"font-bold text-lg truncate\">{node.name}</h3>\n          </div>\n\n          <p className={`text-sm ${depth === 0 ? 'text-indigo-200' : 'text-gray-600 dark:text-gray-300'}`}>\n            {node.title}\n          </p>\n          <p className={`text-xs mt-2 ${depth === 0 ? 'text-indigo-200' : 'text-gray-500 dark:text-gray-400'}`}>\n            {node.department}\n          </p>\n          <div className=\"h-[60px] overflow-hidden\">\n            <p className={`text-xs mt-2 line-clamp-3 ${depth === 0 ? 'text-indigo-100' : 'text-gray-500 dark:text-gray-400'}`}>\n              {node.description}\n            </p>\n          </div>\n        </div>\n\n        {/* Tools section */}\n        <div className=\"mt-auto\">\n          <div className={`mt-2 pt-2 border-t ${depth === 0 ? 'border-indigo-500' : 'border-gray-200 dark:border-gray-700'}`}>\n            {hasTools ? (\n              <div className=\"flex flex-wrap gap-2 justify-center h-[40px] overflow-y-auto\">\n                {node.tools?.map((tool: Tool) => (\n                  <div\n                    key={tool.id}\n                    className={`p-1 rounded-md ${depth === 0 ? 'bg-indigo-500 bg-opacity-30' : 'bg-gray-100 dark:bg-gray-700'}`}\n                    title={tool.name}\n                  >\n                    {getToolIcon && (\n                      <div\n                        className=\"tool-icon cursor-pointer\"\n                        title={tool.name}\n                        onClick={(e: React.MouseEvent) => handleToolClick(e, tool)}\n                      >\n                        {tool.productIcon ? (\n                          <img\n                            src={tool.productIcon}\n                            alt={tool.name}\n                            className=\"h-5 w-5 rounded-sm object-contain\"\n                          />\n                        ) : (\n                          <FontAwesomeIcon\n                            icon={getToolIcon(tool)}\n                            className=\"h-4 w-4 text-gray-600 dark:text-gray-400\"\n                          />\n                        )}\n                      </div>\n                    )}\n                  </div>\n                ))}\n              </div>\n            ) : (\n              <div className=\"text-center h-[40px] flex items-center justify-center\">\n                <p className={`text-xs italic ${depth === 0 ? 'text-indigo-300' : 'text-gray-400 dark:text-gray-500'}`}>\n                  Tools in progress...\n                </p>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {hasChildren && !showAllNodes && (\n          <button\n            onClick={(e: React.MouseEvent) => {\n              e.stopPropagation();\n              toggleNode(node.id);\n            }}\n            className={`absolute bottom-2 right-2 p-1 rounded-full ${\n              depth === 0\n                ? 'bg-indigo-500 hover:bg-indigo-400 text-white'\n                : 'bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300'\n            }`}\n            aria-label={isExpanded ? 'Collapse' : 'Expand'}\n          >\n            <FontAwesomeIcon icon={isExpanded ? faMinus : faPlus} className=\"h-3 w-3\" />\n          </button>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default CustomNode;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAoBA,MAAM,aAAwC,CAAC,EAC7C,IAAI,EACJ,QAAQ,CAAC,EACT,WAAW,EACX,cAAc,EACd,cAAc,EACd,UAAU,EACV,YAAY,EACZ,aAAa,EACb,WAAW,EACX,WAAW,EACZ;IACC,MAAM,kBAAkB,CAAC,GAAqB;QAC5C,EAAE,eAAe;QACjB,IAAI,aAAa;YACf,YAAY;QACd;IACF;IACA,MAAM,cAAc,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG;IAC5D,MAAM,aAAa,gBAAgB,cAAc,GAAG,CAAC,KAAK,EAAE;IAC5D,MAAM,WAAW,YAAY;IAC7B,MAAM,WAAW,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,MAAM,GAAG;IAEnD,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YACC,WAAW,CAAC,iCAAiC,EAC3C,UAAU,IACN,gFACA,iEACL,mFAAmF,CAAC;;8BAErF,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,uKAAA,CAAA,kBAAe;wBACd,MAAM,2KAAA,CAAA,WAAQ;wBACd,WAAW,CAAC,QAAQ,EAAE,eAAe,KAAK,MAAM,GAAG;wBACnD,OAAO,eAAe,KAAK,MAAM;;;;;;;;;;;8BAIrC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;gCACZ,0BACC,6LAAC;oCAAI,WAAW,CAAC,oCAAoC,EACnD,UAAU,IACN,gCACA,uDACJ;8CACA,cAAA,6LAAC,uKAAA,CAAA,kBAAe;wCACd,MAAM;wCACN,WAAW,CAAC,QAAQ,EAClB,UAAU,IACN,eACA,wCACJ;;;;;;;;;;;8CAIR,6LAAC;oCAAG,WAAU;8CAA8B,KAAK,IAAI;;;;;;;;;;;;sCAGvD,6LAAC;4BAAE,WAAW,CAAC,QAAQ,EAAE,UAAU,IAAI,oBAAoB,oCAAoC;sCAC5F,KAAK,KAAK;;;;;;sCAEb,6LAAC;4BAAE,WAAW,CAAC,aAAa,EAAE,UAAU,IAAI,oBAAoB,oCAAoC;sCACjG,KAAK,UAAU;;;;;;sCAElB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAW,CAAC,0BAA0B,EAAE,UAAU,IAAI,oBAAoB,oCAAoC;0CAC9G,KAAK,WAAW;;;;;;;;;;;;;;;;;8BAMvB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAW,CAAC,mBAAmB,EAAE,UAAU,IAAI,sBAAsB,wCAAwC;kCAC/G,yBACC,6LAAC;4BAAI,WAAU;sCACZ,KAAK,KAAK,EAAE,IAAI,CAAC,qBAChB,6LAAC;oCAEC,WAAW,CAAC,eAAe,EAAE,UAAU,IAAI,gCAAgC,gCAAgC;oCAC3G,OAAO,KAAK,IAAI;8CAEf,6BACC,6LAAC;wCACC,WAAU;wCACV,OAAO,KAAK,IAAI;wCAChB,SAAS,CAAC,IAAwB,gBAAgB,GAAG;kDAEpD,KAAK,WAAW,iBACf,6LAAC;4CACC,KAAK,KAAK,WAAW;4CACrB,KAAK,KAAK,IAAI;4CACd,WAAU;;;;;iEAGZ,6LAAC,uKAAA,CAAA,kBAAe;4CACd,MAAM,YAAY;4CAClB,WAAU;;;;;;;;;;;mCAnBb,KAAK,EAAE;;;;;;;;;iDA4BlB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAW,CAAC,eAAe,EAAE,UAAU,IAAI,oBAAoB,oCAAoC;0CAAE;;;;;;;;;;;;;;;;;;;;;gBAQ/G,eAAe,CAAC,8BACf,6LAAC;oBACC,SAAS,CAAC;wBACR,EAAE,eAAe;wBACjB,WAAW,KAAK,EAAE;oBACpB;oBACA,WAAW,CAAC,2CAA2C,EACrD,UAAU,IACN,iDACA,0GACJ;oBACF,cAAY,aAAa,aAAa;8BAEtC,cAAA,6LAAC,uKAAA,CAAA,kBAAe;wBAAC,MAAM,aAAa,2KAAA,CAAA,UAAO,GAAG,2KAAA,CAAA,SAAM;wBAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;AAM5E;KAzIM;uCA2IS", "debugId": null}}, {"offset": {"line": 767, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/admin/ai-org/context/ModalContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, ReactNode } from 'react';\nimport { Tool } from '@/types/ai-org';\n\ninterface ModalContextType {\n  isToolModalOpen: boolean;\n  selectedTool: Tool | null;\n  isInstructionsModalOpen: boolean;\n  openToolModal: (tool: Tool) => void;\n  closeToolModal: () => void;\n  openInstructionsModal: () => void;\n  closeInstructionsModal: () => void;\n}\n\nconst ModalContext = createContext<ModalContextType | undefined>(undefined);\n\nexport const ModalProvider: React.FC<{ children: ReactNode }> = ({ children }) => {\n  const [isToolModalOpen, setIsToolModalOpen] = useState(false);\n  const [selectedTool, setSelectedTool] = useState<Tool | null>(null);\n  const [isInstructionsModalOpen, setIsInstructionsModalOpen] = useState(false);\n\n  const openToolModal = (tool: Tool) => {\n    setSelectedTool(tool);\n    setIsToolModalOpen(true);\n  };\n\n  const closeToolModal = () => {\n    setIsToolModalOpen(false);\n  };\n\n  const openInstructionsModal = () => {\n    setIsInstructionsModalOpen(true);\n  };\n\n  const closeInstructionsModal = () => {\n    setIsInstructionsModalOpen(false);\n  };\n\n  return (\n    <ModalContext.Provider\n      value={{\n        isToolModalOpen,\n        selectedTool,\n        isInstructionsModalOpen,\n        openToolModal,\n        closeToolModal,\n        openInstructionsModal,\n        closeInstructionsModal,\n      }}\n    >\n      {children}\n    </ModalContext.Provider>\n  );\n};\n\nexport const useModal = (): ModalContextType => {\n  const context = useContext(ModalContext);\n  if (context === undefined) {\n    throw new Error('useModal must be used within a ModalProvider');\n  }\n  return context;\n};\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAeA,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAgC;AAE1D,MAAM,gBAAmD,CAAC,EAAE,QAAQ,EAAE;;IAC3E,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvE,MAAM,gBAAgB,CAAC;QACrB,gBAAgB;QAChB,mBAAmB;IACrB;IAEA,MAAM,iBAAiB;QACrB,mBAAmB;IACrB;IAEA,MAAM,wBAAwB;QAC5B,2BAA2B;IAC7B;IAEA,MAAM,yBAAyB;QAC7B,2BAA2B;IAC7B;IAEA,qBACE,6LAAC,aAAa,QAAQ;QACpB,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;YACA;QACF;kBAEC;;;;;;AAGP;GArCa;KAAA;AAuCN,MAAM,WAAW;;IACtB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANa", "debugId": null}}, {"offset": {"line": 835, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/admin/ai-org/components/OrgChart.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport { Tree, TreeNode } from 'react-organizational-chart';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport {\n  faPlus,\n  faMinus,\n  faExpand,\n  faArrowsUpDownLeftRight,\n  faMaximize,\n  faMinimize,\n  faHandPaper,\n  faQuestionCircle\n} from '@fortawesome/free-solid-svg-icons';\nimport { AgentStatus, OrgNode, Tool } from '@/types/ai-org';\nimport CustomNode from './CustomNode';\nimport { useModal } from '../context/ModalContext';\n\ninterface OrgChartProps {\n  orgData: OrgNode | null;\n  getNodeIcon: (node: OrgNode) => any;\n  getToolIcon: (tool: Tool) => any;\n  getStatusColor: (status: AgentStatus) => string;\n  getStatusLabel: (status: AgentStatus) => string;\n}\n\nconst OrgChart: React.FC<OrgChartProps> = ({\n  orgData,\n  getNodeIcon,\n  getToolIcon,\n  getStatusColor,\n  getStatusLabel\n}) => {\n  const [zoomLevel, setZoomLevel] = useState(1);\n  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set());\n  const [showAllNodes, setShowAllNodes] = useState(false);\n  const { openToolModal, openInstructionsModal } = useModal();\n  const [isFullscreen, setIsFullscreen] = useState(false);\n  const [isPanning, setIsPanning] = useState(false);\n  const [panPosition, setPanPosition] = useState({ x: 0, y: 0 });\n  const [panStart, setPanStart] = useState({ x: 0, y: 0 });\n\n  const chartRef = useRef<HTMLDivElement>(null);\n  const chartContainerRef = useRef<HTMLDivElement>(null);\n\n  // Handle zoom\n  const handleZoom = (direction: number) => {\n    setZoomLevel(prev => {\n      const newZoom = prev + direction * 0.1;\n      return Math.max(0.5, Math.min(2, newZoom));\n    });\n  };\n\n  // Toggle node expansion\n  const toggleNode = (id: string) => {\n    setExpandedNodes(prev => {\n      const newSet = new Set(prev);\n      if (newSet.has(id)) {\n        newSet.delete(id);\n      } else {\n        newSet.add(id);\n      }\n      return newSet;\n    });\n  };\n\n  // Handle fullscreen toggle\n  const toggleFullscreen = () => {\n    if (typeof document === 'undefined' || !chartContainerRef.current) return;\n\n    if (!isFullscreen) {\n      if (chartContainerRef.current.requestFullscreen) {\n        chartContainerRef.current.requestFullscreen();\n      }\n      setIsFullscreen(true);\n    } else {\n      if (document.exitFullscreen) {\n        document.exitFullscreen();\n      }\n      setIsFullscreen(false);\n      // Reset pan position when exiting fullscreen\n      setPanPosition({ x: 0, y: 0 });\n    }\n  };\n\n  // Handle panning start\n  const handlePanStart = (e: React.MouseEvent) => {\n    // Ignore clicks on buttons or their children\n    if (e.target instanceof Element) {\n      const targetElement = e.target as Element;\n      if (targetElement.closest('button') || targetElement.tagName === 'BUTTON') {\n        return;\n      }\n    }\n\n    if (e.button === 0) { // Left mouse button\n      setIsPanning(true);\n      setPanStart({ x: e.clientX - panPosition.x, y: e.clientY - panPosition.y });\n\n      // Disable text selection during panning\n      if (chartContainerRef.current && typeof document !== 'undefined') {\n        chartContainerRef.current.style.userSelect = 'none';\n        // Add a class to disable text selection across all browsers\n        document.body.classList.add('no-select');\n      }\n    }\n  };\n\n  // Handle panning movement\n  const handlePanMove = (e: React.MouseEvent) => {\n    if (isPanning) {\n      setPanPosition({\n        x: e.clientX - panStart.x,\n        y: e.clientY - panStart.y\n      });\n    }\n  };\n\n  // Handle panning end\n  const handlePanEnd = () => {\n    setIsPanning(false);\n\n    // Re-enable text selection\n    if (chartContainerRef.current && typeof document !== 'undefined') {\n      chartContainerRef.current.style.userSelect = '';\n      // Remove the no-select class\n      document.body.classList.remove('no-select');\n    }\n  };\n\n  // Handle mouse wheel for zooming\n  useEffect(() => {\n    if (typeof window !== 'undefined') {\n      const handleWheel = (e: WheelEvent) => {\n        if (e.ctrlKey || e.metaKey) {\n          e.preventDefault();\n          handleZoom(e.deltaY > 0 ? -1 : 1);\n        }\n      };\n\n      const chart = chartRef.current;\n      if (chart) {\n        chart.addEventListener('wheel', handleWheel, { passive: false });\n      }\n\n      return () => {\n        if (chart) {\n          chart.removeEventListener('wheel', handleWheel);\n        }\n      };\n    }\n  }, []);\n\n  // Handle fullscreen change events\n  useEffect(() => {\n    if (typeof document !== 'undefined') {\n      const handleFullscreenChange = () => {\n        setIsFullscreen(!!document.fullscreenElement);\n        if (!document.fullscreenElement) {\n          // Reset pan position when exiting fullscreen\n          setPanPosition({ x: 0, y: 0 });\n        }\n      };\n\n      document.addEventListener('fullscreenchange', handleFullscreenChange);\n\n      return () => {\n        document.removeEventListener('fullscreenchange', handleFullscreenChange);\n      };\n    }\n  }, []);\n\n  // Handle tool click\n  const handleToolClick = (tool: Tool) => {\n    openToolModal(tool);\n  };\n\n  // Recursive function to render the org chart\n  const renderOrgChart = (node: OrgNode, depth = 0) => {\n    if (!node) return null;\n\n    const hasChildren = node.children && node.children.length > 0;\n    const isExpanded = showAllNodes || expandedNodes.has(node.id);\n\n    return (\n      <TreeNode\n        key={node.id}\n        label={\n          <CustomNode\n            node={node}\n            depth={depth}\n            getNodeIcon={getNodeIcon}\n            getStatusColor={getStatusColor}\n            getStatusLabel={getStatusLabel}\n            toggleNode={toggleNode}\n            showAllNodes={showAllNodes}\n            expandedNodes={expandedNodes}\n            onToolClick={handleToolClick}\n            getToolIcon={getToolIcon}\n          />\n        }\n      >\n        {hasChildren && isExpanded && node.children &&\n          node.children.map(child => renderOrgChart(child, depth + 1))\n        }\n      </TreeNode>\n    );\n  };\n\n  return (\n    <div\n        ref={chartContainerRef}\n        className={`bg-white dark:bg-gray-800 rounded-lg shadow p-4 ${isFullscreen ? 'fixed top-16 left-0 right-0 bottom-0 z-50' : 'flex flex-col'} ${isPanning ? 'no-select' : ''}`}\n        style={{\n          height: isFullscreen ? 'calc(100vh - 64px)' : 'calc(100vh - 400px)', /* 64px is the height of the top bar */\n          minHeight: isFullscreen ? 'calc(100vh - 64px)' : '500px',\n          cursor: isPanning ? 'grabbing' : 'default',\n          display: 'flex',\n          flexDirection: 'column'\n        }}\n        onMouseDown={isFullscreen ? handlePanStart : undefined}\n        onMouseMove={isFullscreen ? handlePanMove : undefined}\n        onMouseUp={isFullscreen ? handlePanEnd : undefined}\n        onMouseLeave={isFullscreen ? handlePanEnd : undefined}\n      >\n        <div className=\"relative flex justify-center mb-4 gap-2\">\n          <button\n            onClick={() => openInstructionsModal()}\n            className=\"absolute right-0 top-0 p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200\"\n            aria-label=\"View instructions\"\n          >\n            <FontAwesomeIcon icon={faQuestionCircle} className=\"h-5 w-5\" />\n          </button>\n          <button\n            onClick={() => setShowAllNodes(prev => !prev)}\n            className=\"flex items-center gap-2 px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors\"\n          >\n            <FontAwesomeIcon icon={showAllNodes ? faMinus : faExpand} className=\"h-4 w-4\" />\n            {showAllNodes ? 'Collapse All' : 'Expand All'}\n          </button>\n\n          <button\n            onClick={toggleFullscreen}\n            className=\"flex items-center gap-2 px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors\"\n          >\n            <FontAwesomeIcon icon={isFullscreen ? faMinimize : faMaximize} className=\"h-4 w-4\" />\n            {isFullscreen ? 'Exit Fullscreen' : 'Fullscreen'}\n          </button>\n\n          {isFullscreen && (\n            <div className=\"ml-4 text-sm text-gray-500 dark:text-gray-400 flex items-center\">\n              <FontAwesomeIcon icon={faHandPaper} className=\"h-4 w-4 mr-2\" />\n              Click and drag to pan around\n            </div>\n          )}\n        </div>\n\n        <div\n          className=\"flex-grow flex items-center justify-center w-full overflow-auto\"\n          style={{\n            minHeight: '0',\n            maxHeight: '100%',\n            overflow: 'auto',\n            scrollbarWidth: 'thin',\n            scrollbarColor: '#d1d5db transparent',\n            marginBottom: '0'\n          }}\n        >\n          <div\n            ref={chartRef}\n            className=\"relative mx-auto\"\n            style={{\n              transform: `scale(${zoomLevel})`,\n              transformOrigin: 'center center',\n              transition: 'transform 0.3s ease',\n              ...(isFullscreen && {\n                transform: `translate(${panPosition.x}px, ${panPosition.y}px) scale(${zoomLevel})`,\n              })\n            }}\n          >\n            {orgData ? (\n              <Tree\n                lineWidth={'2px'}\n                lineColor={'#d1d5db'}\n                lineBorderRadius={'10px'}\n                label={\n                  <CustomNode\n                    node={orgData}\n                    depth={0}\n                    getNodeIcon={getNodeIcon}\n                    getStatusColor={getStatusColor}\n                    getStatusLabel={getStatusLabel}\n                    toggleNode={toggleNode}\n                    showAllNodes={showAllNodes}\n                    expandedNodes={expandedNodes}\n                    onToolClick={handleToolClick}\n                    getToolIcon={getToolIcon}\n                  />\n                }\n              >\n                {orgData.children && orgData.children.map(child =>\n                  renderOrgChart(child, 1)\n                )}\n              </Tree>\n            ) : (\n              <div className=\"text-center p-8 text-gray-500 dark:text-gray-400\">\n                No agents match your search criteria\n              </div>\n            )}\n          </div>\n        </div>\n\n        {isFullscreen && (\n          <div className=\"absolute bottom-4 right-4 bg-white dark:bg-gray-800 rounded-lg shadow p-2 flex gap-2\">\n            <button\n              onClick={() => handleZoom(1)}\n              className=\"p-2 rounded-md bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300\"\n              aria-label=\"Zoom in\"\n            >\n              <FontAwesomeIcon icon={faPlus} />\n            </button>\n            <button\n              onClick={() => handleZoom(-1)}\n              className=\"p-2 rounded-md bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300\"\n              aria-label=\"Zoom out\"\n            >\n              <FontAwesomeIcon icon={faMinus} />\n            </button>\n            <button\n              onClick={() => {\n                setZoomLevel(1);\n                setPanPosition({ x: 0, y: 0 });\n              }}\n              className=\"p-2 rounded-md bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300\"\n              aria-label=\"Reset view\"\n            >\n              <FontAwesomeIcon icon={faArrowsUpDownLeftRight} />\n            </button>\n          </div>\n        )}\n      </div>\n  );\n};\n\nexport default OrgChart;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAWA;AACA;;;AAjBA;;;;;;;AA2BA,MAAM,WAAoC,CAAC,EACzC,OAAO,EACP,WAAW,EACX,WAAW,EACX,cAAc,EACd,cAAc,EACf;;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IACpE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,EAAE,aAAa,EAAE,qBAAqB,EAAE,GAAG,CAAA,GAAA,+JAAA,CAAA,WAAQ,AAAD;IACxD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAC5D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAEtD,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IACxC,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAEjD,cAAc;IACd,MAAM,aAAa,CAAC;QAClB,aAAa,CAAA;YACX,MAAM,UAAU,OAAO,YAAY;YACnC,OAAO,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG;QACnC;IACF;IAEA,wBAAwB;IACxB,MAAM,aAAa,CAAC;QAClB,iBAAiB,CAAA;YACf,MAAM,SAAS,IAAI,IAAI;YACvB,IAAI,OAAO,GAAG,CAAC,KAAK;gBAClB,OAAO,MAAM,CAAC;YAChB,OAAO;gBACL,OAAO,GAAG,CAAC;YACb;YACA,OAAO;QACT;IACF;IAEA,2BAA2B;IAC3B,MAAM,mBAAmB;QACvB,IAAI,OAAO,aAAa,eAAe,CAAC,kBAAkB,OAAO,EAAE;QAEnE,IAAI,CAAC,cAAc;YACjB,IAAI,kBAAkB,OAAO,CAAC,iBAAiB,EAAE;gBAC/C,kBAAkB,OAAO,CAAC,iBAAiB;YAC7C;YACA,gBAAgB;QAClB,OAAO;YACL,IAAI,SAAS,cAAc,EAAE;gBAC3B,SAAS,cAAc;YACzB;YACA,gBAAgB;YAChB,6CAA6C;YAC7C,eAAe;gBAAE,GAAG;gBAAG,GAAG;YAAE;QAC9B;IACF;IAEA,uBAAuB;IACvB,MAAM,iBAAiB,CAAC;QACtB,6CAA6C;QAC7C,IAAI,EAAE,MAAM,YAAY,SAAS;YAC/B,MAAM,gBAAgB,EAAE,MAAM;YAC9B,IAAI,cAAc,OAAO,CAAC,aAAa,cAAc,OAAO,KAAK,UAAU;gBACzE;YACF;QACF;QAEA,IAAI,EAAE,MAAM,KAAK,GAAG;YAClB,aAAa;YACb,YAAY;gBAAE,GAAG,EAAE,OAAO,GAAG,YAAY,CAAC;gBAAE,GAAG,EAAE,OAAO,GAAG,YAAY,CAAC;YAAC;YAEzE,wCAAwC;YACxC,IAAI,kBAAkB,OAAO,IAAI,OAAO,aAAa,aAAa;gBAChE,kBAAkB,OAAO,CAAC,KAAK,CAAC,UAAU,GAAG;gBAC7C,4DAA4D;gBAC5D,SAAS,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;YAC9B;QACF;IACF;IAEA,0BAA0B;IAC1B,MAAM,gBAAgB,CAAC;QACrB,IAAI,WAAW;YACb,eAAe;gBACb,GAAG,EAAE,OAAO,GAAG,SAAS,CAAC;gBACzB,GAAG,EAAE,OAAO,GAAG,SAAS,CAAC;YAC3B;QACF;IACF;IAEA,qBAAqB;IACrB,MAAM,eAAe;QACnB,aAAa;QAEb,2BAA2B;QAC3B,IAAI,kBAAkB,OAAO,IAAI,OAAO,aAAa,aAAa;YAChE,kBAAkB,OAAO,CAAC,KAAK,CAAC,UAAU,GAAG;YAC7C,6BAA6B;YAC7B,SAAS,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;QACjC;IACF;IAEA,iCAAiC;IACjC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,wCAAmC;gBACjC,MAAM;sDAAc,CAAC;wBACnB,IAAI,EAAE,OAAO,IAAI,EAAE,OAAO,EAAE;4BAC1B,EAAE,cAAc;4BAChB,WAAW,EAAE,MAAM,GAAG,IAAI,CAAC,IAAI;wBACjC;oBACF;;gBAEA,MAAM,QAAQ,SAAS,OAAO;gBAC9B,IAAI,OAAO;oBACT,MAAM,gBAAgB,CAAC,SAAS,aAAa;wBAAE,SAAS;oBAAM;gBAChE;gBAEA;0CAAO;wBACL,IAAI,OAAO;4BACT,MAAM,mBAAmB,CAAC,SAAS;wBACrC;oBACF;;YACF;QACF;6BAAG,EAAE;IAEL,kCAAkC;IAClC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,IAAI,OAAO,aAAa,aAAa;gBACnC,MAAM;iEAAyB;wBAC7B,gBAAgB,CAAC,CAAC,SAAS,iBAAiB;wBAC5C,IAAI,CAAC,SAAS,iBAAiB,EAAE;4BAC/B,6CAA6C;4BAC7C,eAAe;gCAAE,GAAG;gCAAG,GAAG;4BAAE;wBAC9B;oBACF;;gBAEA,SAAS,gBAAgB,CAAC,oBAAoB;gBAE9C;0CAAO;wBACL,SAAS,mBAAmB,CAAC,oBAAoB;oBACnD;;YACF;QACF;6BAAG,EAAE;IAEL,oBAAoB;IACpB,MAAM,kBAAkB,CAAC;QACvB,cAAc;IAChB;IAEA,6CAA6C;IAC7C,MAAM,iBAAiB,CAAC,MAAe,QAAQ,CAAC;QAC9C,IAAI,CAAC,MAAM,OAAO;QAElB,MAAM,cAAc,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG;QAC5D,MAAM,aAAa,gBAAgB,cAAc,GAAG,CAAC,KAAK,EAAE;QAE5D,qBACE,6LAAC,8KAAA,CAAA,WAAQ;YAEP,qBACE,6LAAC,gKAAA,CAAA,UAAU;gBACT,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,gBAAgB;gBAChB,gBAAgB;gBAChB,YAAY;gBACZ,cAAc;gBACd,eAAe;gBACf,aAAa;gBACb,aAAa;;;;;;sBAIhB,eAAe,cAAc,KAAK,QAAQ,IACzC,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAA,QAAS,eAAe,OAAO,QAAQ;WAjBtD,KAAK,EAAE;;;;;IAqBlB;IAEA,qBACE,6LAAC;QACG,KAAK;QACL,WAAW,CAAC,gDAAgD,EAAE,eAAe,8CAA8C,gBAAgB,CAAC,EAAE,YAAY,cAAc,IAAI;QAC5K,OAAO;YACL,QAAQ,eAAe,uBAAuB;YAAuB,qCAAqC,GAC1G,WAAW,eAAe,uBAAuB;YACjD,QAAQ,YAAY,aAAa;YACjC,SAAS;YACT,eAAe;QACjB;QACA,aAAa,eAAe,iBAAiB;QAC7C,aAAa,eAAe,gBAAgB;QAC5C,WAAW,eAAe,eAAe;QACzC,cAAc,eAAe,eAAe;;0BAE5C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS,IAAM;wBACf,WAAU;wBACV,cAAW;kCAEX,cAAA,6LAAC,uKAAA,CAAA,kBAAe;4BAAC,MAAM,2KAAA,CAAA,mBAAgB;4BAAE,WAAU;;;;;;;;;;;kCAErD,6LAAC;wBACC,SAAS,IAAM,gBAAgB,CAAA,OAAQ,CAAC;wBACxC,WAAU;;0CAEV,6LAAC,uKAAA,CAAA,kBAAe;gCAAC,MAAM,eAAe,2KAAA,CAAA,UAAO,GAAG,2KAAA,CAAA,WAAQ;gCAAE,WAAU;;;;;;4BACnE,eAAe,iBAAiB;;;;;;;kCAGnC,6LAAC;wBACC,SAAS;wBACT,WAAU;;0CAEV,6LAAC,uKAAA,CAAA,kBAAe;gCAAC,MAAM,eAAe,2KAAA,CAAA,aAAU,GAAG,2KAAA,CAAA,aAAU;gCAAE,WAAU;;;;;;4BACxE,eAAe,oBAAoB;;;;;;;oBAGrC,8BACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uKAAA,CAAA,kBAAe;gCAAC,MAAM,2KAAA,CAAA,cAAW;gCAAE,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAMrE,6LAAC;gBACC,WAAU;gBACV,OAAO;oBACL,WAAW;oBACX,WAAW;oBACX,UAAU;oBACV,gBAAgB;oBAChB,gBAAgB;oBAChB,cAAc;gBAChB;0BAEA,cAAA,6LAAC;oBACC,KAAK;oBACL,WAAU;oBACV,OAAO;wBACL,WAAW,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;wBAChC,iBAAiB;wBACjB,YAAY;wBACZ,GAAI,gBAAgB;4BAClB,WAAW,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;wBACpF,CAAC;oBACH;8BAEC,wBACC,6LAAC,8KAAA,CAAA,OAAI;wBACH,WAAW;wBACX,WAAW;wBACX,kBAAkB;wBAClB,qBACE,6LAAC,gKAAA,CAAA,UAAU;4BACT,MAAM;4BACN,OAAO;4BACP,aAAa;4BACb,gBAAgB;4BAChB,gBAAgB;4BAChB,YAAY;4BACZ,cAAc;4BACd,eAAe;4BACf,aAAa;4BACb,aAAa;;;;;;kCAIhB,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAA,QACxC,eAAe,OAAO;;;;;6CAI1B,6LAAC;wBAAI,WAAU;kCAAmD;;;;;;;;;;;;;;;;YAOvE,8BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS,IAAM,WAAW;wBAC1B,WAAU;wBACV,cAAW;kCAEX,cAAA,6LAAC,uKAAA,CAAA,kBAAe;4BAAC,MAAM,2KAAA,CAAA,SAAM;;;;;;;;;;;kCAE/B,6LAAC;wBACC,SAAS,IAAM,WAAW,CAAC;wBAC3B,WAAU;wBACV,cAAW;kCAEX,cAAA,6LAAC,uKAAA,CAAA,kBAAe;4BAAC,MAAM,2KAAA,CAAA,UAAO;;;;;;;;;;;kCAEhC,6LAAC;wBACC,SAAS;4BACP,aAAa;4BACb,eAAe;gCAAE,GAAG;gCAAG,GAAG;4BAAE;wBAC9B;wBACA,WAAU;wBACV,cAAW;kCAEX,cAAA,6LAAC,uKAAA,CAAA,kBAAe;4BAAC,MAAM,2KAAA,CAAA,0BAAuB;;;;;;;;;;;;;;;;;;;;;;;AAM5D;GA5TM;;QAU6C,+JAAA,CAAA,WAAQ;;;KAVrD;uCA8TS", "debugId": null}}, {"offset": {"line": 1288, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/admin/ai-org/components/ToolDetailModal.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { faTimes } from '@fortawesome/free-solid-svg-icons';\nimport { Tool } from '@/types/ai-org';\n\ninterface ToolDetailModalProps {\n  tool: Tool | null;\n  isOpen: boolean;\n  onClose: () => void;\n  getToolIcon: (tool: Tool) => any;\n}\n\nconst ToolDetailModal: React.FC<ToolDetailModalProps> = ({\n  tool,\n  isOpen,\n  onClose,\n  getToolIcon\n}) => {\n  if (!tool || !isOpen) return null;\n\n  const icon = getToolIcon(tool);\n\n  return (\n    <div className=\"fixed inset-0 z-[100] flex items-center justify-center backdrop-blur-sm bg-black/30 dark:bg-gray-900/30\">\n      <div className=\"relative w-full max-w-md rounded-lg bg-white/90 dark:bg-gray-800/90 backdrop-blur-md p-6 shadow-xl border border-gray-200 dark:border-gray-700\">\n        <button\n          onClick={onClose}\n          className=\"absolute top-2 right-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200\"\n        >\n          <FontAwesomeIcon icon={faTimes} className=\"h-5 w-5\" />\n        </button>\n\n        <div className=\"flex items-center mb-4\">\n          {tool.productIcon ? (\n            <img\n              src={tool.productIcon}\n              alt={tool.name}\n              className=\"h-12 w-12 mr-4 rounded-md object-contain\"\n            />\n          ) : icon ? (\n            <div className=\"flex h-12 w-12 mr-4 items-center justify-center rounded-md bg-indigo-100 dark:bg-indigo-900\">\n              <FontAwesomeIcon\n                icon={icon}\n                className=\"h-8 w-8 text-indigo-600 dark:text-indigo-400\"\n              />\n            </div>\n          ) : null}\n\n          <h2 className=\"text-xl font-bold\">{tool.name}</h2>\n        </div>\n\n        <div className=\"mb-4\">\n          <h3 className=\"text-sm font-semibold text-gray-500 dark:text-gray-400 mb-2\">Description</h3>\n          <p className=\"text-gray-700 dark:text-gray-300\">\n            {tool.description || `A powerful tool used for ${tool.name} related tasks.`}\n          </p>\n        </div>\n\n        {tool.url && (\n          <div className=\"mb-4\">\n            <h3 className=\"text-sm font-semibold text-gray-500 dark:text-gray-400 mb-2\">Website</h3>\n            <a\n              href={tool.url}\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"text-indigo-600 hover:text-indigo-800 dark:text-indigo-400 dark:hover:text-indigo-300\"\n            >\n              {tool.url}\n            </a>\n          </div>\n        )}\n\n        <div className=\"mb-4\">\n          <h3 className=\"text-sm font-semibold text-gray-500 dark:text-gray-400 mb-2\">Used by</h3>\n          <p className=\"text-gray-700 dark:text-gray-300\">\n            {tool.usedBy || 'Various AI agents in the organization'}\n          </p>\n        </div>\n\n        <button\n          onClick={onClose}\n          className=\"w-full rounded-md bg-indigo-600 py-2 px-4 text-white hover:bg-indigo-700 transition-colors\"\n        >\n          Close\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default ToolDetailModal;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAcA,MAAM,kBAAkD,CAAC,EACvD,IAAI,EACJ,MAAM,EACN,OAAO,EACP,WAAW,EACZ;IACC,IAAI,CAAC,QAAQ,CAAC,QAAQ,OAAO;IAE7B,MAAM,OAAO,YAAY;IAEzB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBACC,SAAS;oBACT,WAAU;8BAEV,cAAA,6LAAC,uKAAA,CAAA,kBAAe;wBAAC,MAAM,2KAAA,CAAA,UAAO;wBAAE,WAAU;;;;;;;;;;;8BAG5C,6LAAC;oBAAI,WAAU;;wBACZ,KAAK,WAAW,iBACf,6LAAC;4BACC,KAAK,KAAK,WAAW;4BACrB,KAAK,KAAK,IAAI;4BACd,WAAU;;;;;mCAEV,qBACF,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,uKAAA,CAAA,kBAAe;gCACd,MAAM;gCACN,WAAU;;;;;;;;;;mCAGZ;sCAEJ,6LAAC;4BAAG,WAAU;sCAAqB,KAAK,IAAI;;;;;;;;;;;;8BAG9C,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA8D;;;;;;sCAC5E,6LAAC;4BAAE,WAAU;sCACV,KAAK,WAAW,IAAI,CAAC,yBAAyB,EAAE,KAAK,IAAI,CAAC,eAAe,CAAC;;;;;;;;;;;;gBAI9E,KAAK,GAAG,kBACP,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA8D;;;;;;sCAC5E,6LAAC;4BACC,MAAM,KAAK,GAAG;4BACd,QAAO;4BACP,KAAI;4BACJ,WAAU;sCAET,KAAK,GAAG;;;;;;;;;;;;8BAKf,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA8D;;;;;;sCAC5E,6LAAC;4BAAE,WAAU;sCACV,KAAK,MAAM,IAAI;;;;;;;;;;;;8BAIpB,6LAAC;oBACC,SAAS;oBACT,WAAU;8BACX;;;;;;;;;;;;;;;;;AAMT;KA5EM;uCA8ES", "debugId": null}}, {"offset": {"line": 1474, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/admin/ai-org/components/InstructionsModal.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { faTimes } from '@fortawesome/free-solid-svg-icons';\n\ninterface InstructionsModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nconst InstructionsModal: React.FC<InstructionsModalProps> = ({ isOpen, onClose }) => {\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 z-[100] flex items-center justify-center backdrop-blur-sm bg-black/30 dark:bg-gray-900/30\">\n      <div className=\"relative w-full max-w-md rounded-lg bg-white/90 dark:bg-gray-800/90 backdrop-blur-md p-6 shadow-xl border border-gray-200 dark:border-gray-700\">\n        <button\n          onClick={onClose}\n          className=\"absolute top-2 right-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200\"\n        >\n          <FontAwesomeIcon icon={faTimes} className=\"h-5 w-5\" />\n        </button>\n\n        <h2 className=\"text-xl font-bold mb-4\">Organization Chart Instructions</h2>\n\n        <ul className=\"list-disc pl-5 space-y-2 text-sm text-gray-600 dark:text-gray-400\">\n          <li>Click the + / - buttons on nodes to expand or collapse individual departments</li>\n          <li>Use the \"Expand All\" button to see the entire organization at once</li>\n          <li>Click \"Fullscreen\" to view the chart in fullscreen mode</li>\n          <li>In fullscreen mode, click and drag to pan around the chart</li>\n          <li>Use the zoom buttons to adjust the view size</li>\n          <li>Use the search and filters to find specific agents</li>\n          <li>The colored dots indicate implementation status (green = active, blue = in development, etc.)</li>\n        </ul>\n\n        <button\n          onClick={onClose}\n          className=\"w-full mt-6 rounded-md bg-indigo-600 py-2 px-4 text-white hover:bg-indigo-700 transition-colors\"\n        >\n          Close\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default InstructionsModal;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAWA,MAAM,oBAAsD,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE;IAC9E,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBACC,SAAS;oBACT,WAAU;8BAEV,cAAA,6LAAC,uKAAA,CAAA,kBAAe;wBAAC,MAAM,2KAAA,CAAA,UAAO;wBAAE,WAAU;;;;;;;;;;;8BAG5C,6LAAC;oBAAG,WAAU;8BAAyB;;;;;;8BAEvC,6LAAC;oBAAG,WAAU;;sCACZ,6LAAC;sCAAG;;;;;;sCACJ,6LAAC;sCAAG;;;;;;sCACJ,6LAAC;sCAAG;;;;;;sCACJ,6LAAC;sCAAG;;;;;;sCACJ,6LAAC;sCAAG;;;;;;sCACJ,6LAAC;sCAAG;;;;;;sCACJ,6LAAC;sCAAG;;;;;;;;;;;;8BAGN,6LAAC;oBACC,SAAS;oBACT,WAAU;8BACX;;;;;;;;;;;;;;;;;AAMT;KAlCM;uCAoCS", "debugId": null}}, {"offset": {"line": 1607, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/utils/icon-helpers.ts"], "sourcesContent": ["import {\n  faBrain,\n  faCode,\n  faChartLine,\n  faServer,\n  faShieldAlt,\n  faPalette,\n  faRobot,\n  faUserTie,\n  faUsers,\n  faDatabase,\n  faLightbulb,\n  faChartPie,\n  faNetworkWired,\n  faMoneyBillWave,\n  faBullhorn,\n  faFlask,\n  faGraduationCap,\n  faProjectDiagram,\n  faUserCog,\n  faClipboardCheck,\n  faFileAlt,\n  faComments,\n  faGlobe,\n  faBuilding,\n  faLock,\n  faTools,\n  faWrench,\n  faMicrochip,\n  faCloud,\n  faDesktop,\n  faMobile,\n  faTablet,\n  faEnvelope,\n  faVideo,\n  faImage,\n  faMusic,\n  faVoicemail,\n  faCalculator,\n  faCalendar,\n  faSearch as faSearchAlt,\n  faChartBar,\n  faPencilAlt,\n  faEdit,\n  faTerminal,\n  faKeyboard,\n  faRobot as faRobotAlt,\n  faLanguage,\n  faFileCode,\n  faFileWord,\n  faFilePdf,\n  faFileExcel,\n  faFilePowerpoint,\n  faFileImage,\n  faFileVideo,\n  faFileAudio,\n  faFileArchive,\n  faFileAlt as faFileAltAlt,\n  faFileCsv,\n  faFileContract,\n  faFileSignature,\n  faFileInvoice,\n  faFileInvoiceDollar,\n  faFileMedical,\n  faFileMedicalAlt,\n  faFileUpload,\n  faFileDownload,\n  faFileExport,\n  faFileImport,\n  faFilePrescription\n} from '@fortawesome/free-solid-svg-icons';\nimport { AgentStatus, OrgNode, Tool } from '@/types/ai-org';\n\n// Get status color\nexport const getStatusColor = (status: AgentStatus) => {\n  switch (status) {\n    case 'active': return 'text-green-500';\n    case 'in-development': return 'text-blue-500';\n    case 'planned': return 'text-amber-500';\n    case 'concept': return 'text-gray-500';\n    default: return 'text-gray-500';\n  }\n};\n\n// Get status label\nexport const getStatusLabel = (status: AgentStatus) => {\n  switch (status) {\n    case 'active': return 'Active';\n    case 'in-development': return 'In Development';\n    case 'planned': return 'Planned';\n    case 'concept': return 'Concept';\n    default: return 'Unknown';\n  }\n};\n\n// Get icon based on department or role\nexport const getNodeIcon = (node: OrgNode) => {\n  // If node has a specific icon defined, use that\n  if (node.icon) {\n    switch (node.icon) {\n      case 'brain': return faBrain;\n      case 'code': return faCode;\n      case 'chart-line': return faChartLine;\n      case 'server': return faServer;\n      case 'shield': return faShieldAlt;\n      case 'palette': return faPalette;\n      case 'robot': return faRobot;\n      case 'user-tie': return faUserTie;\n      case 'users': return faUsers;\n      case 'database': return faDatabase;\n      case 'lightbulb': return faLightbulb;\n      case 'chart-pie': return faChartPie;\n      case 'network': return faNetworkWired;\n      case 'money': return faMoneyBillWave;\n      case 'bullhorn': return faBullhorn;\n      case 'flask': return faFlask;\n      case 'graduation-cap': return faGraduationCap;\n      case 'project-diagram': return faProjectDiagram;\n      case 'user-cog': return faUserCog;\n      case 'clipboard-check': return faClipboardCheck;\n      case 'file': return faFileAlt;\n      case 'comments': return faComments;\n      case 'globe': return faGlobe;\n      case 'building': return faBuilding;\n      case 'lock': return faLock;\n      default: return null;\n    }\n  }\n  \n  // Otherwise, determine icon based on department\n  switch (node.department.toLowerCase()) {\n    case 'executive': return faUserTie;\n    case 'technology': return faServer;\n    case 'software development': return faCode;\n    case 'data science': return faDatabase;\n    case 'cybersecurity': return faShieldAlt;\n    case 'operations': return faProjectDiagram;\n    case 'project management': return faClipboardCheck;\n    case 'human resources': return faUsers;\n    case 'finance': return faMoneyBillWave;\n    case 'marketing': return faBullhorn;\n    case 'research': return faFlask;\n    case 'economics': return faChartLine;\n    case 'policy research': return faFileAlt;\n    case 'information technology': return faNetworkWired;\n    default: return faBrain; // Default icon for AI agents\n  }\n};\n\n// Get icon for tool\nexport const getToolIcon = (tool: Tool) => {\n  // If tool has a product icon URL, return null (we'll use the URL directly)\n  if (tool.productIcon) {\n    return null;\n  }\n  \n  // If tool has a specific icon defined, use that\n  if (tool.icon) {\n    switch (tool.icon) {\n      // General tools\n      case 'tools': return faTools;\n      case 'wrench': return faWrench;\n      case 'microchip': return faMicrochip;\n      case 'cloud': return faCloud;\n      case 'desktop': return faDesktop;\n      case 'mobile': return faMobile;\n      case 'tablet': return faTablet;\n      \n      // Communication tools\n      case 'envelope': return faEnvelope;\n      case 'video': return faVideo;\n      case 'comments': return faComments;\n      case 'voicemail': return faVoicemail;\n      \n      // Media tools\n      case 'image': return faImage;\n      case 'music': return faMusic;\n      \n      // Productivity tools\n      case 'calculator': return faCalculator;\n      case 'calendar': return faCalendar;\n      case 'search': return faSearchAlt;\n      case 'chart-bar': return faChartBar;\n      case 'pencil': return faPencilAlt;\n      case 'edit': return faEdit;\n      \n      // Development tools\n      case 'terminal': return faTerminal;\n      case 'keyboard': return faKeyboard;\n      case 'robot': return faRobotAlt;\n      case 'language': return faLanguage;\n      \n      // File types\n      case 'file-code': return faFileCode;\n      case 'file-word': return faFileWord;\n      case 'file-pdf': return faFilePdf;\n      case 'file-excel': return faFileExcel;\n      case 'file-powerpoint': return faFilePowerpoint;\n      case 'file-image': return faFileImage;\n      case 'file-video': return faFileVideo;\n      case 'file-audio': return faFileAudio;\n      case 'file-archive': return faFileArchive;\n      case 'file-alt': return faFileAltAlt;\n      case 'file-csv': return faFileCsv;\n      case 'file-contract': return faFileContract;\n      case 'file-signature': return faFileSignature;\n      case 'file-invoice': return faFileInvoice;\n      case 'file-invoice-dollar': return faFileInvoiceDollar;\n      case 'file-medical': return faFileMedical;\n      case 'file-medical-alt': return faFileMedicalAlt;\n      case 'file-upload': return faFileUpload;\n      case 'file-download': return faFileDownload;\n      case 'file-export': return faFileExport;\n      case 'file-import': return faFileImport;\n      case 'file-prescription': return faFilePrescription;\n      \n      // Default to a generic file icon\n      default: return faFileAlt;\n    }\n  }\n  \n  // Default to a generic tool icon\n  return faTools;\n};\n"], "names": [], "mappings": ";;;;;;AAAA;;AA0EO,MAAM,iBAAiB,CAAC;IAC7B,OAAQ;QACN,KAAK;YAAU,OAAO;QACtB,KAAK;YAAkB,OAAO;QAC9B,KAAK;YAAW,OAAO;QACvB,KAAK;YAAW,OAAO;QACvB;YAAS,OAAO;IAClB;AACF;AAGO,MAAM,iBAAiB,CAAC;IAC7B,OAAQ;QACN,KAAK;YAAU,OAAO;QACtB,KAAK;YAAkB,OAAO;QAC9B,KAAK;YAAW,OAAO;QACvB,KAAK;YAAW,OAAO;QACvB;YAAS,OAAO;IAClB;AACF;AAGO,MAAM,cAAc,CAAC;IAC1B,gDAAgD;IAChD,IAAI,KAAK,IAAI,EAAE;QACb,OAAQ,KAAK,IAAI;YACf,KAAK;gBAAS,OAAO,2KAAA,CAAA,UAAO;YAC5B,KAAK;gBAAQ,OAAO,2KAAA,CAAA,SAAM;YAC1B,KAAK;gBAAc,OAAO,2KAAA,CAAA,cAAW;YACrC,KAAK;gBAAU,OAAO,2KAAA,CAAA,WAAQ;YAC9B,KAAK;gBAAU,OAAO,2KAAA,CAAA,cAAW;YACjC,KAAK;gBAAW,OAAO,2KAAA,CAAA,YAAS;YAChC,KAAK;gBAAS,OAAO,2KAAA,CAAA,UAAO;YAC5B,KAAK;gBAAY,OAAO,2KAAA,CAAA,YAAS;YACjC,KAAK;gBAAS,OAAO,2KAAA,CAAA,UAAO;YAC5B,KAAK;gBAAY,OAAO,2KAAA,CAAA,aAAU;YAClC,KAAK;gBAAa,OAAO,2KAAA,CAAA,cAAW;YACpC,KAAK;gBAAa,OAAO,2KAAA,CAAA,aAAU;YACnC,KAAK;gBAAW,OAAO,2KAAA,CAAA,iBAAc;YACrC,KAAK;gBAAS,OAAO,2KAAA,CAAA,kBAAe;YACpC,KAAK;gBAAY,OAAO,2KAAA,CAAA,aAAU;YAClC,KAAK;gBAAS,OAAO,2KAAA,CAAA,UAAO;YAC5B,KAAK;gBAAkB,OAAO,2KAAA,CAAA,kBAAe;YAC7C,KAAK;gBAAmB,OAAO,2KAAA,CAAA,mBAAgB;YAC/C,KAAK;gBAAY,OAAO,2KAAA,CAAA,YAAS;YACjC,KAAK;gBAAmB,OAAO,2KAAA,CAAA,mBAAgB;YAC/C,KAAK;gBAAQ,OAAO,2KAAA,CAAA,YAAS;YAC7B,KAAK;gBAAY,OAAO,2KAAA,CAAA,aAAU;YAClC,KAAK;gBAAS,OAAO,2KAAA,CAAA,UAAO;YAC5B,KAAK;gBAAY,OAAO,2KAAA,CAAA,aAAU;YAClC,KAAK;gBAAQ,OAAO,2KAAA,CAAA,SAAM;YAC1B;gBAAS,OAAO;QAClB;IACF;IAEA,gDAAgD;IAChD,OAAQ,KAAK,UAAU,CAAC,WAAW;QACjC,KAAK;YAAa,OAAO,2KAAA,CAAA,YAAS;QAClC,KAAK;YAAc,OAAO,2KAAA,CAAA,WAAQ;QAClC,KAAK;YAAwB,OAAO,2KAAA,CAAA,SAAM;QAC1C,KAAK;YAAgB,OAAO,2KAAA,CAAA,aAAU;QACtC,KAAK;YAAiB,OAAO,2KAAA,CAAA,cAAW;QACxC,KAAK;YAAc,OAAO,2KAAA,CAAA,mBAAgB;QAC1C,KAAK;YAAsB,OAAO,2KAAA,CAAA,mBAAgB;QAClD,KAAK;YAAmB,OAAO,2KAAA,CAAA,UAAO;QACtC,KAAK;YAAW,OAAO,2KAAA,CAAA,kBAAe;QACtC,KAAK;YAAa,OAAO,2KAAA,CAAA,aAAU;QACnC,KAAK;YAAY,OAAO,2KAAA,CAAA,UAAO;QAC/B,KAAK;YAAa,OAAO,2KAAA,CAAA,cAAW;QACpC,KAAK;YAAmB,OAAO,2KAAA,CAAA,YAAS;QACxC,KAAK;YAA0B,OAAO,2KAAA,CAAA,iBAAc;QACpD;YAAS,OAAO,2KAAA,CAAA,UAAO,EAAE,6BAA6B;IACxD;AACF;AAGO,MAAM,cAAc,CAAC;IAC1B,2EAA2E;IAC3E,IAAI,KAAK,WAAW,EAAE;QACpB,OAAO;IACT;IAEA,gDAAgD;IAChD,IAAI,KAAK,IAAI,EAAE;QACb,OAAQ,KAAK,IAAI;YACf,gBAAgB;YAChB,KAAK;gBAAS,OAAO,2KAAA,CAAA,UAAO;YAC5B,KAAK;gBAAU,OAAO,2KAAA,CAAA,WAAQ;YAC9B,KAAK;gBAAa,OAAO,2KAAA,CAAA,cAAW;YACpC,KAAK;gBAAS,OAAO,2KAAA,CAAA,UAAO;YAC5B,KAAK;gBAAW,OAAO,2KAAA,CAAA,YAAS;YAChC,KAAK;gBAAU,OAAO,2KAAA,CAAA,WAAQ;YAC9B,KAAK;gBAAU,OAAO,2KAAA,CAAA,WAAQ;YAE9B,sBAAsB;YACtB,KAAK;gBAAY,OAAO,2KAAA,CAAA,aAAU;YAClC,KAAK;gBAAS,OAAO,2KAAA,CAAA,UAAO;YAC5B,KAAK;gBAAY,OAAO,2KAAA,CAAA,aAAU;YAClC,KAAK;gBAAa,OAAO,2KAAA,CAAA,cAAW;YAEpC,cAAc;YACd,KAAK;gBAAS,OAAO,2KAAA,CAAA,UAAO;YAC5B,KAAK;gBAAS,OAAO,2KAAA,CAAA,UAAO;YAE5B,qBAAqB;YACrB,KAAK;gBAAc,OAAO,2KAAA,CAAA,eAAY;YACtC,KAAK;gBAAY,OAAO,2KAAA,CAAA,aAAU;YAClC,KAAK;gBAAU,OAAO,2KAAA,CAAA,WAAW;YACjC,KAAK;gBAAa,OAAO,2KAAA,CAAA,aAAU;YACnC,KAAK;gBAAU,OAAO,2KAAA,CAAA,cAAW;YACjC,KAAK;gBAAQ,OAAO,2KAAA,CAAA,SAAM;YAE1B,oBAAoB;YACpB,KAAK;gBAAY,OAAO,2KAAA,CAAA,aAAU;YAClC,KAAK;gBAAY,OAAO,2KAAA,CAAA,aAAU;YAClC,KAAK;gBAAS,OAAO,2KAAA,CAAA,UAAU;YAC/B,KAAK;gBAAY,OAAO,2KAAA,CAAA,aAAU;YAElC,aAAa;YACb,KAAK;gBAAa,OAAO,2KAAA,CAAA,aAAU;YACnC,KAAK;gBAAa,OAAO,2KAAA,CAAA,aAAU;YACnC,KAAK;gBAAY,OAAO,2KAAA,CAAA,YAAS;YACjC,KAAK;gBAAc,OAAO,2KAAA,CAAA,cAAW;YACrC,KAAK;gBAAmB,OAAO,2KAAA,CAAA,mBAAgB;YAC/C,KAAK;gBAAc,OAAO,2KAAA,CAAA,cAAW;YACrC,KAAK;gBAAc,OAAO,2KAAA,CAAA,cAAW;YACrC,KAAK;gBAAc,OAAO,2KAAA,CAAA,cAAW;YACrC,KAAK;gBAAgB,OAAO,2KAAA,CAAA,gBAAa;YACzC,KAAK;gBAAY,OAAO,2KAAA,CAAA,YAAY;YACpC,KAAK;gBAAY,OAAO,2KAAA,CAAA,YAAS;YACjC,KAAK;gBAAiB,OAAO,2KAAA,CAAA,iBAAc;YAC3C,KAAK;gBAAkB,OAAO,2KAAA,CAAA,kBAAe;YAC7C,KAAK;gBAAgB,OAAO,2KAAA,CAAA,gBAAa;YACzC,KAAK;gBAAuB,OAAO,2KAAA,CAAA,sBAAmB;YACtD,KAAK;gBAAgB,OAAO,2KAAA,CAAA,gBAAa;YACzC,KAAK;gBAAoB,OAAO,2KAAA,CAAA,mBAAgB;YAChD,KAAK;gBAAe,OAAO,2KAAA,CAAA,eAAY;YACvC,KAAK;gBAAiB,OAAO,2KAAA,CAAA,iBAAc;YAC3C,KAAK;gBAAe,OAAO,2KAAA,CAAA,eAAY;YACvC,KAAK;gBAAe,OAAO,2KAAA,CAAA,eAAY;YACvC,KAAK;gBAAqB,OAAO,2KAAA,CAAA,qBAAkB;YAEnD,iCAAiC;YACjC;gBAAS,OAAO,2KAAA,CAAA,YAAS;QAC3B;IACF;IAEA,iCAAiC;IACjC,OAAO,2KAAA,CAAA,UAAO;AAChB", "debugId": null}}]}