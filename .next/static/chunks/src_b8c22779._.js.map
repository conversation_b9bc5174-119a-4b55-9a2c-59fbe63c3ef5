{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/data/mock-users.ts"], "sourcesContent": ["import { User, UserRole, UserStatus } from '@/types/user';\n\n// Helper function to generate default permissions based on role\nconst getDefaultPermissions = (role: UserRole) => {\n  switch (role) {\n    case 'admin':\n      return {\n        canCreateAgents: true,\n        canEditAgents: true,\n        canDeleteAgents: true,\n        canManageUsers: true,\n        canAccessAdmin: true,\n        canAccessAnalytics: true,\n        canManageContent: true,\n        canApproveContent: true\n      };\n    case 'manager':\n      return {\n        canCreateAgents: true,\n        canEditAgents: true,\n        canDeleteAgents: false,\n        canManageUsers: false,\n        canAccessAdmin: true,\n        canAccessAnalytics: true,\n        canManageContent: true,\n        canApproveContent: true\n      };\n    case 'user':\n      return {\n        canCreateAgents: false,\n        canEditAgents: false,\n        canDeleteAgents: false,\n        canManageUsers: false,\n        canAccessAdmin: false,\n        canAccessAnalytics: false,\n        canManageContent: false,\n        canApproveContent: false\n      };\n    case 'guest':\n      return {\n        canCreateAgents: false,\n        canEditAgents: false,\n        canDeleteAgents: false,\n        canManageUsers: false,\n        canAccessAdmin: false,\n        canAccessAnalytics: false,\n        canManageContent: false,\n        canApproveContent: false\n      };\n  }\n};\n\n// Mock users data\nexport const mockUsers: User[] = [\n  {\n    id: 'user-001',\n    email: '<EMAIL>',\n    username: 'admin',\n    role: 'admin',\n    status: 'active',\n    permissions: getDefaultPermissions('admin'),\n    profile: {\n      firstName: 'Admin',\n      lastName: 'User',\n      jobTitle: 'System Administrator',\n      department: 'IT',\n      location: 'New York',\n      bio: 'System administrator with full access to all platform features.',\n      avatarUrl: 'https://randomuser.me/api/portraits/men/1.jpg',\n      phoneNumber: '+****************'\n    },\n    settings: {\n      theme: 'system',\n      emailNotifications: true,\n      twoFactorEnabled: true,\n      sidebarCollapsed: false,\n      language: 'en-US'\n    },\n    activity: {\n      lastLogin: new Date('2023-05-15T08:30:00'),\n      lastActive: new Date('2023-05-15T16:45:00'),\n      totalLogins: 247,\n      totalSessions: 312,\n      averageSessionDuration: 3600, // 1 hour\n      favoriteAgents: ['1', '3', '5'],\n      favoriteSection: 'admin'\n    },\n    createdAt: new Date('2022-01-01T00:00:00'),\n    updatedAt: new Date('2023-04-15T14:30:00'),\n    lastPasswordChange: new Date('2023-03-01T00:00:00')\n  },\n  {\n    id: 'user-002',\n    email: '<EMAIL>',\n    username: 'manager',\n    role: 'manager',\n    status: 'active',\n    permissions: getDefaultPermissions('manager'),\n    profile: {\n      firstName: 'Sarah',\n      lastName: 'Johnson',\n      jobTitle: 'Project Manager',\n      department: 'Operations',\n      location: 'Chicago',\n      bio: 'Project manager overseeing AI implementation projects.',\n      avatarUrl: 'https://randomuser.me/api/portraits/women/2.jpg',\n      phoneNumber: '+****************'\n    },\n    settings: {\n      theme: 'light',\n      emailNotifications: true,\n      twoFactorEnabled: false,\n      sidebarCollapsed: true,\n      language: 'en-US'\n    },\n    activity: {\n      lastLogin: new Date('2023-05-14T09:15:00'),\n      lastActive: new Date('2023-05-15T17:30:00'),\n      totalLogins: 183,\n      totalSessions: 245,\n      averageSessionDuration: 2700, // 45 minutes\n      favoriteAgents: ['2', '4'],\n      favoriteSection: 'browse'\n    },\n    createdAt: new Date('2022-02-15T00:00:00'),\n    updatedAt: new Date('2023-04-10T11:20:00'),\n    lastPasswordChange: new Date('2023-02-15T00:00:00')\n  },\n  {\n    id: 'user-003',\n    email: '<EMAIL>',\n    username: 'johnsmith',\n    role: 'user',\n    status: 'active',\n    permissions: getDefaultPermissions('user'),\n    profile: {\n      firstName: 'John',\n      lastName: 'Smith',\n      jobTitle: 'Data Analyst',\n      department: 'Analytics',\n      location: 'Boston',\n      bio: 'Data analyst using AI tools to enhance data processing workflows.',\n      avatarUrl: 'https://randomuser.me/api/portraits/men/3.jpg'\n    },\n    settings: {\n      theme: 'dark',\n      emailNotifications: false,\n      twoFactorEnabled: false,\n      sidebarCollapsed: false,\n      language: 'en-US'\n    },\n    activity: {\n      lastLogin: new Date('2023-05-15T10:00:00'),\n      lastActive: new Date('2023-05-15T15:30:00'),\n      totalLogins: 97,\n      totalSessions: 142,\n      averageSessionDuration: 1800, // 30 minutes\n      favoriteAgents: ['1', '3'],\n      favoriteSection: 'learning'\n    },\n    createdAt: new Date('2022-03-10T00:00:00'),\n    updatedAt: new Date('2023-03-25T09:45:00'),\n    lastPasswordChange: new Date('2023-01-20T00:00:00')\n  },\n  {\n    id: 'user-004',\n    email: '<EMAIL>',\n    username: 'emilyd',\n    role: 'user',\n    status: 'active',\n    permissions: getDefaultPermissions('user'),\n    profile: {\n      firstName: 'Emily',\n      lastName: 'Davis',\n      jobTitle: 'Content Strategist',\n      department: 'Marketing',\n      location: 'San Francisco',\n      bio: 'Content strategist exploring AI tools for content creation and optimization.',\n      avatarUrl: 'https://randomuser.me/api/portraits/women/4.jpg',\n      phoneNumber: '+****************'\n    },\n    settings: {\n      theme: 'light',\n      emailNotifications: true,\n      twoFactorEnabled: false,\n      sidebarCollapsed: true,\n      language: 'en-US'\n    },\n    activity: {\n      lastLogin: new Date('2023-05-14T14:20:00'),\n      lastActive: new Date('2023-05-15T11:45:00'),\n      totalLogins: 76,\n      totalSessions: 104,\n      averageSessionDuration: 2100, // 35 minutes\n      favoriteAgents: ['4', '5'],\n      favoriteSection: 'learning_resources'\n    },\n    createdAt: new Date('2022-04-05T00:00:00'),\n    updatedAt: new Date('2023-03-15T16:30:00'),\n    lastPasswordChange: new Date('2023-01-10T00:00:00')\n  },\n  {\n    id: 'user-005',\n    email: '<EMAIL>',\n    username: 'michaelb',\n    role: 'user',\n    status: 'inactive',\n    permissions: getDefaultPermissions('user'),\n    profile: {\n      firstName: 'Michael',\n      lastName: 'Brown',\n      jobTitle: 'Research Scientist',\n      department: 'R&D',\n      location: 'Seattle',\n      bio: 'Research scientist working on AI applications in healthcare.',\n      avatarUrl: 'https://randomuser.me/api/portraits/men/5.jpg'\n    },\n    settings: {\n      theme: 'system',\n      emailNotifications: false,\n      twoFactorEnabled: false,\n      sidebarCollapsed: false,\n      language: 'en-US'\n    },\n    activity: {\n      lastLogin: new Date('2023-04-20T09:30:00'),\n      lastActive: new Date('2023-04-20T14:15:00'),\n      totalLogins: 42,\n      totalSessions: 67,\n      averageSessionDuration: 3300, // 55 minutes\n      favoriteAgents: ['2'],\n      favoriteSection: 'agent_detail'\n    },\n    createdAt: new Date('2022-05-12T00:00:00'),\n    updatedAt: new Date('2023-04-20T14:15:00'),\n    lastPasswordChange: new Date('2022-12-05T00:00:00')\n  },\n  {\n    id: 'user-006',\n    email: '<EMAIL>',\n    username: 'jenniferw',\n    role: 'user',\n    status: 'pending',\n    permissions: getDefaultPermissions('user'),\n    profile: {\n      firstName: 'Jennifer',\n      lastName: 'Wilson',\n      jobTitle: 'UX Designer',\n      department: 'Design',\n      location: 'Austin',\n      avatarUrl: 'https://randomuser.me/api/portraits/women/6.jpg'\n    },\n    settings: {\n      theme: 'light',\n      emailNotifications: true,\n      twoFactorEnabled: false,\n      sidebarCollapsed: false,\n      language: 'en-US'\n    },\n    activity: {\n      lastLogin: new Date('2023-05-10T11:00:00'),\n      lastActive: new Date('2023-05-10T11:30:00'),\n      totalLogins: 2,\n      totalSessions: 2,\n      averageSessionDuration: 1800, // 30 minutes\n      favoriteAgents: [],\n      favoriteSection: 'home'\n    },\n    createdAt: new Date('2023-05-10T00:00:00'),\n    updatedAt: new Date('2023-05-10T11:30:00'),\n    lastPasswordChange: new Date('2023-05-10T00:00:00')\n  },\n  {\n    id: 'user-007',\n    email: '<EMAIL>',\n    username: 'robertt',\n    role: 'user',\n    status: 'suspended',\n    permissions: getDefaultPermissions('user'),\n    profile: {\n      firstName: 'Robert',\n      lastName: 'Taylor',\n      jobTitle: 'Sales Representative',\n      department: 'Sales',\n      location: 'Denver',\n      avatarUrl: 'https://randomuser.me/api/portraits/men/7.jpg',\n      phoneNumber: '+****************'\n    },\n    settings: {\n      theme: 'dark',\n      emailNotifications: false,\n      twoFactorEnabled: false,\n      sidebarCollapsed: true,\n      language: 'en-US'\n    },\n    activity: {\n      lastLogin: new Date('2023-03-15T13:45:00'),\n      lastActive: new Date('2023-03-15T15:30:00'),\n      totalLogins: 28,\n      totalSessions: 35,\n      averageSessionDuration: 1500, // 25 minutes\n      favoriteAgents: ['1'],\n      favoriteSection: 'browse'\n    },\n    createdAt: new Date('2022-06-20T00:00:00'),\n    updatedAt: new Date('2023-03-20T10:00:00'),\n    lastPasswordChange: new Date('2022-11-15T00:00:00')\n  },\n  {\n    id: 'user-008',\n    email: '<EMAIL>',\n    username: 'guest',\n    role: 'guest',\n    status: 'active',\n    permissions: getDefaultPermissions('guest'),\n    profile: {\n      firstName: 'Guest',\n      lastName: 'User',\n      jobTitle: 'External Consultant',\n      department: 'External',\n      location: 'Remote',\n      bio: 'Guest account with limited access for demonstration purposes.',\n      avatarUrl: 'https://randomuser.me/api/portraits/lego/1.jpg'\n    },\n    settings: {\n      theme: 'light',\n      emailNotifications: false,\n      twoFactorEnabled: false,\n      sidebarCollapsed: false,\n      language: 'en-US'\n    },\n    activity: {\n      lastLogin: new Date('2023-05-14T10:30:00'),\n      lastActive: new Date('2023-05-14T11:15:00'),\n      totalLogins: 5,\n      totalSessions: 5,\n      averageSessionDuration: 900, // 15 minutes\n      favoriteAgents: [],\n      favoriteSection: 'browse'\n    },\n    createdAt: new Date('2023-01-01T00:00:00'),\n    updatedAt: new Date('2023-05-14T11:15:00'),\n    lastPasswordChange: new Date('2023-01-01T00:00:00')\n  }\n];\n\n// Helper functions for user management\nexport const getUserById = (id: string): User | undefined => {\n  return mockUsers.find(user => user.id === id);\n};\n\nexport const getUsersByRole = (role: UserRole): User[] => {\n  return mockUsers.filter(user => user.role === role);\n};\n\nexport const getUsersByStatus = (status: UserStatus): User[] => {\n  return mockUsers.filter(user => user.status === status);\n};\n\nexport const getActiveUsers = (): User[] => {\n  return mockUsers.filter(user => user.status === 'active');\n};\n\nexport const searchUsers = (query: string): User[] => {\n  const lowercaseQuery = query.toLowerCase();\n  return mockUsers.filter(user => \n    user.username.toLowerCase().includes(lowercaseQuery) ||\n    user.email.toLowerCase().includes(lowercaseQuery) ||\n    user.profile.firstName.toLowerCase().includes(lowercaseQuery) ||\n    user.profile.lastName.toLowerCase().includes(lowercaseQuery) ||\n    (user.profile.jobTitle && user.profile.jobTitle.toLowerCase().includes(lowercaseQuery)) ||\n    (user.profile.department && user.profile.department.toLowerCase().includes(lowercaseQuery))\n  );\n};\n"], "names": [], "mappings": ";;;;;;;;AAEA,gEAAgE;AAChE,MAAM,wBAAwB,CAAC;IAC7B,OAAQ;QACN,KAAK;YACH,OAAO;gBACL,iBAAiB;gBACjB,eAAe;gBACf,iBAAiB;gBACjB,gBAAgB;gBAChB,gBAAgB;gBAChB,oBAAoB;gBACpB,kBAAkB;gBAClB,mBAAmB;YACrB;QACF,KAAK;YACH,OAAO;gBACL,iBAAiB;gBACjB,eAAe;gBACf,iBAAiB;gBACjB,gBAAgB;gBAChB,gBAAgB;gBAChB,oBAAoB;gBACpB,kBAAkB;gBAClB,mBAAmB;YACrB;QACF,KAAK;YACH,OAAO;gBACL,iBAAiB;gBACjB,eAAe;gBACf,iBAAiB;gBACjB,gBAAgB;gBAChB,gBAAgB;gBAChB,oBAAoB;gBACpB,kBAAkB;gBAClB,mBAAmB;YACrB;QACF,KAAK;YACH,OAAO;gBACL,iBAAiB;gBACjB,eAAe;gBACf,iBAAiB;gBACjB,gBAAgB;gBAChB,gBAAgB;gBAChB,oBAAoB;gBACpB,kBAAkB;gBAClB,mBAAmB;YACrB;IACJ;AACF;AAGO,MAAM,YAAoB;IAC/B;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,MAAM;QACN,QAAQ;QACR,aAAa,sBAAsB;QACnC,SAAS;YACP,WAAW;YACX,UAAU;YACV,UAAU;YACV,YAAY;YACZ,UAAU;YACV,KAAK;YACL,WAAW;YACX,aAAa;QACf;QACA,UAAU;YACR,OAAO;YACP,oBAAoB;YACpB,kBAAkB;YAClB,kBAAkB;YAClB,UAAU;QACZ;QACA,UAAU;YACR,WAAW,IAAI,KAAK;YACpB,YAAY,IAAI,KAAK;YACrB,aAAa;YACb,eAAe;YACf,wBAAwB;YACxB,gBAAgB;gBAAC;gBAAK;gBAAK;aAAI;YAC/B,iBAAiB;QACnB;QACA,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,oBAAoB,IAAI,KAAK;IAC/B;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,MAAM;QACN,QAAQ;QACR,aAAa,sBAAsB;QACnC,SAAS;YACP,WAAW;YACX,UAAU;YACV,UAAU;YACV,YAAY;YACZ,UAAU;YACV,KAAK;YACL,WAAW;YACX,aAAa;QACf;QACA,UAAU;YACR,OAAO;YACP,oBAAoB;YACpB,kBAAkB;YAClB,kBAAkB;YAClB,UAAU;QACZ;QACA,UAAU;YACR,WAAW,IAAI,KAAK;YACpB,YAAY,IAAI,KAAK;YACrB,aAAa;YACb,eAAe;YACf,wBAAwB;YACxB,gBAAgB;gBAAC;gBAAK;aAAI;YAC1B,iBAAiB;QACnB;QACA,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,oBAAoB,IAAI,KAAK;IAC/B;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,MAAM;QACN,QAAQ;QACR,aAAa,sBAAsB;QACnC,SAAS;YACP,WAAW;YACX,UAAU;YACV,UAAU;YACV,YAAY;YACZ,UAAU;YACV,KAAK;YACL,WAAW;QACb;QACA,UAAU;YACR,OAAO;YACP,oBAAoB;YACpB,kBAAkB;YAClB,kBAAkB;YAClB,UAAU;QACZ;QACA,UAAU;YACR,WAAW,IAAI,KAAK;YACpB,YAAY,IAAI,KAAK;YACrB,aAAa;YACb,eAAe;YACf,wBAAwB;YACxB,gBAAgB;gBAAC;gBAAK;aAAI;YAC1B,iBAAiB;QACnB;QACA,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,oBAAoB,IAAI,KAAK;IAC/B;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,MAAM;QACN,QAAQ;QACR,aAAa,sBAAsB;QACnC,SAAS;YACP,WAAW;YACX,UAAU;YACV,UAAU;YACV,YAAY;YACZ,UAAU;YACV,KAAK;YACL,WAAW;YACX,aAAa;QACf;QACA,UAAU;YACR,OAAO;YACP,oBAAoB;YACpB,kBAAkB;YAClB,kBAAkB;YAClB,UAAU;QACZ;QACA,UAAU;YACR,WAAW,IAAI,KAAK;YACpB,YAAY,IAAI,KAAK;YACrB,aAAa;YACb,eAAe;YACf,wBAAwB;YACxB,gBAAgB;gBAAC;gBAAK;aAAI;YAC1B,iBAAiB;QACnB;QACA,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,oBAAoB,IAAI,KAAK;IAC/B;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,MAAM;QACN,QAAQ;QACR,aAAa,sBAAsB;QACnC,SAAS;YACP,WAAW;YACX,UAAU;YACV,UAAU;YACV,YAAY;YACZ,UAAU;YACV,KAAK;YACL,WAAW;QACb;QACA,UAAU;YACR,OAAO;YACP,oBAAoB;YACpB,kBAAkB;YAClB,kBAAkB;YAClB,UAAU;QACZ;QACA,UAAU;YACR,WAAW,IAAI,KAAK;YACpB,YAAY,IAAI,KAAK;YACrB,aAAa;YACb,eAAe;YACf,wBAAwB;YACxB,gBAAgB;gBAAC;aAAI;YACrB,iBAAiB;QACnB;QACA,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,oBAAoB,IAAI,KAAK;IAC/B;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,MAAM;QACN,QAAQ;QACR,aAAa,sBAAsB;QACnC,SAAS;YACP,WAAW;YACX,UAAU;YACV,UAAU;YACV,YAAY;YACZ,UAAU;YACV,WAAW;QACb;QACA,UAAU;YACR,OAAO;YACP,oBAAoB;YACpB,kBAAkB;YAClB,kBAAkB;YAClB,UAAU;QACZ;QACA,UAAU;YACR,WAAW,IAAI,KAAK;YACpB,YAAY,IAAI,KAAK;YACrB,aAAa;YACb,eAAe;YACf,wBAAwB;YACxB,gBAAgB,EAAE;YAClB,iBAAiB;QACnB;QACA,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,oBAAoB,IAAI,KAAK;IAC/B;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,MAAM;QACN,QAAQ;QACR,aAAa,sBAAsB;QACnC,SAAS;YACP,WAAW;YACX,UAAU;YACV,UAAU;YACV,YAAY;YACZ,UAAU;YACV,WAAW;YACX,aAAa;QACf;QACA,UAAU;YACR,OAAO;YACP,oBAAoB;YACpB,kBAAkB;YAClB,kBAAkB;YAClB,UAAU;QACZ;QACA,UAAU;YACR,WAAW,IAAI,KAAK;YACpB,YAAY,IAAI,KAAK;YACrB,aAAa;YACb,eAAe;YACf,wBAAwB;YACxB,gBAAgB;gBAAC;aAAI;YACrB,iBAAiB;QACnB;QACA,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,oBAAoB,IAAI,KAAK;IAC/B;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,MAAM;QACN,QAAQ;QACR,aAAa,sBAAsB;QACnC,SAAS;YACP,WAAW;YACX,UAAU;YACV,UAAU;YACV,YAAY;YACZ,UAAU;YACV,KAAK;YACL,WAAW;QACb;QACA,UAAU;YACR,OAAO;YACP,oBAAoB;YACpB,kBAAkB;YAClB,kBAAkB;YAClB,UAAU;QACZ;QACA,UAAU;YACR,WAAW,IAAI,KAAK;YACpB,YAAY,IAAI,KAAK;YACrB,aAAa;YACb,eAAe;YACf,wBAAwB;YACxB,gBAAgB,EAAE;YAClB,iBAAiB;QACnB;QACA,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,oBAAoB,IAAI,KAAK;IAC/B;CACD;AAGM,MAAM,cAAc,CAAC;IAC1B,OAAO,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;AAC5C;AAEO,MAAM,iBAAiB,CAAC;IAC7B,OAAO,UAAU,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK;AAChD;AAEO,MAAM,mBAAmB,CAAC;IAC/B,OAAO,UAAU,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK;AAClD;AAEO,MAAM,iBAAiB;IAC5B,OAAO,UAAU,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK;AAClD;AAEO,MAAM,cAAc,CAAC;IAC1B,MAAM,iBAAiB,MAAM,WAAW;IACxC,OAAO,UAAU,MAAM,CAAC,CAAA,OACtB,KAAK,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,mBACrC,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,mBAClC,KAAK,OAAO,CAAC,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,mBAC9C,KAAK,OAAO,CAAC,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,mBAC5C,KAAK,OAAO,CAAC,QAAQ,IAAI,KAAK,OAAO,CAAC,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,mBACtE,KAAK,OAAO,CAAC,UAAU,IAAI,KAAK,OAAO,CAAC,UAAU,CAAC,WAAW,GAAG,QAAQ,CAAC;AAE/E", "debugId": null}}, {"offset": {"line": 398, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/admin/users/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport {\n  faUsers,\n  faUserPlus,\n  faSearch,\n  faArrowLeft,\n  faEdit,\n  faTrash,\n  faLock,\n  faUnlock,\n  faCheckCircle,\n  faTimesCircle,\n  faClock,\n  faExclamationTriangle\n} from '@fortawesome/free-solid-svg-icons';\nimport { mockUsers } from '@/data/mock-users';\nimport { User, UserRole, UserStatus } from '@/types/user';\n\nexport default function UsersPage() {\n  const [users, setUsers] = useState<User[]>([]);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [roleFilter, setRoleFilter] = useState<UserRole | 'all'>('all');\n  const [statusFilter, setStatusFilter] = useState<UserStatus | 'all'>('all');\n  const [sortBy, setSortBy] = useState<'name' | 'role' | 'status' | 'lastActive'>('lastActive');\n  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');\n\n  // Initialize users from mock data\n  useEffect(() => {\n    setUsers(mockUsers);\n  }, []);\n\n  // Filter and sort users\n  const filteredUsers = users\n    .filter(user => {\n      // Apply search query filter\n      if (searchQuery) {\n        const query = searchQuery.toLowerCase();\n        const fullName = `${user.profile.firstName} ${user.profile.lastName}`.toLowerCase();\n        if (!fullName.includes(query) &&\n            !user.email.toLowerCase().includes(query) &&\n            !user.username.toLowerCase().includes(query)) {\n          return false;\n        }\n      }\n\n      // Apply role filter\n      if (roleFilter !== 'all' && user.role !== roleFilter) {\n        return false;\n      }\n\n      // Apply status filter\n      if (statusFilter !== 'all' && user.status !== statusFilter) {\n        return false;\n      }\n\n      return true;\n    })\n    .sort((a, b) => {\n      // Apply sorting\n      if (sortBy === 'name') {\n        const nameA = `${a.profile.firstName} ${a.profile.lastName}`;\n        const nameB = `${b.profile.firstName} ${b.profile.lastName}`;\n        return sortDirection === 'asc'\n          ? nameA.localeCompare(nameB)\n          : nameB.localeCompare(nameA);\n      }\n\n      if (sortBy === 'role') {\n        return sortDirection === 'asc'\n          ? a.role.localeCompare(b.role)\n          : b.role.localeCompare(a.role);\n      }\n\n      if (sortBy === 'status') {\n        return sortDirection === 'asc'\n          ? a.status.localeCompare(b.status)\n          : b.status.localeCompare(a.status);\n      }\n\n      if (sortBy === 'lastActive') {\n        return sortDirection === 'asc'\n          ? a.activity.lastActive.getTime() - b.activity.lastActive.getTime()\n          : b.activity.lastActive.getTime() - a.activity.lastActive.getTime();\n      }\n\n      return 0;\n    });\n\n  // Handle search\n  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {\n    setSearchQuery(e.target.value);\n  };\n\n  // Handle sort\n  const handleSort = (column: 'name' | 'role' | 'status' | 'lastActive') => {\n    if (sortBy === column) {\n      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');\n    } else {\n      setSortBy(column);\n      setSortDirection('asc');\n    }\n  };\n\n  // Format date for display\n  const formatDate = (date: Date) => {\n    return new Date(date).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    });\n  };\n\n  // Get status badge\n  const getStatusBadge = (status: UserStatus) => {\n    switch (status) {\n      case 'active':\n        return (\n          <span className=\"inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800 dark:bg-green-900/30 dark:text-green-300\">\n            <FontAwesomeIcon icon={faCheckCircle} className=\"mr-1 h-3 w-3\" />\n            Active\n          </span>\n        );\n      case 'inactive':\n        return (\n          <span className=\"inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800 dark:bg-gray-900/30 dark:text-gray-300\">\n            <FontAwesomeIcon icon={faTimesCircle} className=\"mr-1 h-3 w-3\" />\n            Inactive\n          </span>\n        );\n      case 'pending':\n        return (\n          <span className=\"inline-flex items-center rounded-full bg-yellow-100 px-2.5 py-0.5 text-xs font-medium text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300\">\n            <FontAwesomeIcon icon={faClock} className=\"mr-1 h-3 w-3\" />\n            Pending\n          </span>\n        );\n      case 'suspended':\n        return (\n          <span className=\"inline-flex items-center rounded-full bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800 dark:bg-red-900/30 dark:text-red-300\">\n            <FontAwesomeIcon icon={faExclamationTriangle} className=\"mr-1 h-3 w-3\" />\n            Suspended\n          </span>\n        );\n    }\n  };\n\n  // Get role badge\n  const getRoleBadge = (role: UserRole) => {\n    switch (role) {\n      case 'admin':\n        return (\n          <span className=\"inline-flex items-center rounded-full bg-purple-100 px-2.5 py-0.5 text-xs font-medium text-purple-800 dark:bg-purple-900/30 dark:text-purple-300\">\n            Admin\n          </span>\n        );\n      case 'manager':\n        return (\n          <span className=\"inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800 dark:bg-blue-900/30 dark:text-blue-300\">\n            Manager\n          </span>\n        );\n      case 'user':\n        return (\n          <span className=\"inline-flex items-center rounded-full bg-indigo-100 px-2.5 py-0.5 text-xs font-medium text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-300\">\n            User\n          </span>\n        );\n      case 'guest':\n        return (\n          <span className=\"inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800 dark:bg-gray-900/30 dark:text-gray-300\">\n            Guest\n          </span>\n        );\n    }\n  };\n\n  return (\n    <div className=\"space-y-8\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <Link\n            href=\"/admin\"\n            className=\"mb-4 inline-flex items-center text-sm font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300\"\n          >\n            <FontAwesomeIcon icon={faArrowLeft} className=\"mr-2 h-4 w-4\" />\n            Back to Admin Dashboard\n          </Link>\n          <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">User Management</h1>\n        </div>\n\n        <Link\n          href=\"/admin/users/new\"\n          className=\"inline-flex items-center rounded-md bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:bg-indigo-500 dark:hover:bg-indigo-600\"\n        >\n          <FontAwesomeIcon icon={faUserPlus} className=\"mr-2 h-4 w-4\" />\n          Add User\n        </Link>\n      </div>\n\n      {/* Filters and search */}\n      <div className=\"flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0\">\n        <div className=\"flex flex-1 flex-col space-y-4 md:flex-row md:space-x-4 md:space-y-0\">\n          <div className=\"relative flex-1\">\n            <div className=\"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3\">\n              <FontAwesomeIcon icon={faSearch} className=\"h-5 w-5 text-gray-400\" />\n            </div>\n            <input\n              type=\"text\"\n              className=\"block w-full rounded-md border-gray-300 py-2 px-3 pl-10 focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white sm:text-sm\"\n              placeholder=\"Search users...\"\n              value={searchQuery}\n              onChange={handleSearch}\n            />\n          </div>\n\n          <div className=\"flex space-x-4\">\n            <div>\n              <label htmlFor=\"role-filter\" className=\"sr-only\">\n                Filter by role\n              </label>\n              <select\n                id=\"role-filter\"\n                className=\"block w-full rounded-md border-gray-300 py-2 px-3 focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white sm:text-sm\"\n                value={roleFilter}\n                onChange={(e) => setRoleFilter(e.target.value as UserRole | 'all')}\n              >\n                <option value=\"all\">All Roles</option>\n                <option value=\"admin\">Admin</option>\n                <option value=\"manager\">Manager</option>\n                <option value=\"user\">User</option>\n                <option value=\"guest\">Guest</option>\n              </select>\n            </div>\n\n            <div>\n              <label htmlFor=\"status-filter\" className=\"sr-only\">\n                Filter by status\n              </label>\n              <select\n                id=\"status-filter\"\n                className=\"block w-full rounded-md border-gray-300 py-2 px-3 focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white sm:text-sm\"\n                value={statusFilter}\n                onChange={(e) => setStatusFilter(e.target.value as UserStatus | 'all')}\n              >\n                <option value=\"all\">All Statuses</option>\n                <option value=\"active\">Active</option>\n                <option value=\"inactive\">Inactive</option>\n                <option value=\"pending\">Pending</option>\n                <option value=\"suspended\">Suspended</option>\n              </select>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Users table */}\n      <div className=\"overflow-hidden rounded-lg border border-gray-200 shadow dark:border-gray-700\">\n        <div className=\"overflow-x-auto\">\n          <table className=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\n            <thead className=\"bg-gray-50 dark:bg-gray-800\">\n              <tr>\n                <th\n                  scope=\"col\"\n                  className=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400\"\n                >\n                  <button\n                    className=\"flex items-center\"\n                    onClick={() => handleSort('name')}\n                  >\n                    User\n                    {sortBy === 'name' && (\n                      <span className=\"ml-1\">\n                        {sortDirection === 'asc' ? '↑' : '↓'}\n                      </span>\n                    )}\n                  </button>\n                </th>\n                <th\n                  scope=\"col\"\n                  className=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400\"\n                >\n                  <button\n                    className=\"flex items-center\"\n                    onClick={() => handleSort('role')}\n                  >\n                    Role\n                    {sortBy === 'role' && (\n                      <span className=\"ml-1\">\n                        {sortDirection === 'asc' ? '↑' : '↓'}\n                      </span>\n                    )}\n                  </button>\n                </th>\n                <th\n                  scope=\"col\"\n                  className=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400\"\n                >\n                  <button\n                    className=\"flex items-center\"\n                    onClick={() => handleSort('status')}\n                  >\n                    Status\n                    {sortBy === 'status' && (\n                      <span className=\"ml-1\">\n                        {sortDirection === 'asc' ? '↑' : '↓'}\n                      </span>\n                    )}\n                  </button>\n                </th>\n                <th\n                  scope=\"col\"\n                  className=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400\"\n                >\n                  <button\n                    className=\"flex items-center\"\n                    onClick={() => handleSort('lastActive')}\n                  >\n                    Last Active\n                    {sortBy === 'lastActive' && (\n                      <span className=\"ml-1\">\n                        {sortDirection === 'asc' ? '↑' : '↓'}\n                      </span>\n                    )}\n                  </button>\n                </th>\n                <th\n                  scope=\"col\"\n                  className=\"px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400\"\n                >\n                  Actions\n                </th>\n              </tr>\n            </thead>\n            <tbody className=\"divide-y divide-gray-200 bg-white dark:divide-gray-700 dark:bg-gray-900\">\n              {filteredUsers.map((user) => (\n                <tr key={user.id} className=\"hover:bg-gray-50 dark:hover:bg-gray-800\">\n                  <td className=\"whitespace-nowrap px-6 py-4\">\n                    <div className=\"flex items-center\">\n                      <div className=\"h-10 w-10 flex-shrink-0\">\n                        <img\n                          className=\"h-10 w-10 rounded-full\"\n                          src={user.profile.avatarUrl || `https://ui-avatars.com/api/?name=${user.profile.firstName}+${user.profile.lastName}&background=random`}\n                          alt={`${user.profile.firstName} ${user.profile.lastName}`}\n                        />\n                      </div>\n                      <div className=\"ml-4\">\n                        <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                          {user.profile.firstName} {user.profile.lastName}\n                        </div>\n                        <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                          {user.email}\n                        </div>\n                      </div>\n                    </div>\n                  </td>\n                  <td className=\"whitespace-nowrap px-6 py-4\">\n                    {getRoleBadge(user.role)}\n                  </td>\n                  <td className=\"whitespace-nowrap px-6 py-4\">\n                    {getStatusBadge(user.status)}\n                  </td>\n                  <td className=\"whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400\">\n                    {formatDate(user.activity.lastActive)}\n                  </td>\n                  <td className=\"whitespace-nowrap px-6 py-4 text-right text-sm font-medium\">\n                    <div className=\"flex justify-end space-x-2\">\n                      <Link\n                        href={`/admin/users/${user.id}`}\n                        className=\"text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300\"\n                      >\n                        <FontAwesomeIcon icon={faEdit} className=\"h-4 w-4\" />\n                        <span className=\"sr-only\">Edit</span>\n                      </Link>\n                      <button\n                        className=\"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300\"\n                        onClick={() => {\n                          // In a real app, this would show a confirmation dialog\n                          alert(`Delete user: ${user.profile.firstName} ${user.profile.lastName}`);\n                        }}\n                      >\n                        <FontAwesomeIcon icon={faTrash} className=\"h-4 w-4\" />\n                        <span className=\"sr-only\">Delete</span>\n                      </button>\n                      {user.status === 'active' ? (\n                        <button\n                          className=\"text-yellow-600 hover:text-yellow-900 dark:text-yellow-400 dark:hover:text-yellow-300\"\n                          onClick={() => {\n                            // In a real app, this would update the user status\n                            alert(`Suspend user: ${user.profile.firstName} ${user.profile.lastName}`);\n                          }}\n                        >\n                          <FontAwesomeIcon icon={faLock} className=\"h-4 w-4\" />\n                          <span className=\"sr-only\">Suspend</span>\n                        </button>\n                      ) : (\n                        <button\n                          className=\"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300\"\n                          onClick={() => {\n                            // In a real app, this would update the user status\n                            alert(`Activate user: ${user.profile.firstName} ${user.profile.lastName}`);\n                          }}\n                        >\n                          <FontAwesomeIcon icon={faUnlock} className=\"h-4 w-4\" />\n                          <span className=\"sr-only\">Activate</span>\n                        </button>\n                      )}\n                    </div>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n\n      {/* Empty state */}\n      {filteredUsers.length === 0 && (\n        <div className=\"flex flex-col items-center justify-center rounded-lg border border-gray-200 bg-white py-12 text-center dark:border-gray-700 dark:bg-gray-900\">\n          <FontAwesomeIcon icon={faUsers} className=\"mb-4 h-12 w-12 text-gray-400\" />\n          <h3 className=\"mb-2 text-lg font-medium text-gray-900 dark:text-white\">No users found</h3>\n          <p className=\"text-gray-500 dark:text-gray-400\">\n            {searchQuery\n              ? \"We couldn't find any users matching your search criteria.\"\n              : \"There are no users in the system yet.\"}\n          </p>\n          {searchQuery && (\n            <button\n              onClick={() => setSearchQuery('')}\n              className=\"mt-4 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700\"\n            >\n              Clear search\n            </button>\n          )}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAcA;;;AAnBA;;;;;;AAsBe,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IAC/D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IACrE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6C;IAChF,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IAEnE,kCAAkC;IAClC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAA<PERSON>;YACR,SAAS,+HAAA,CAAA,YAAS;QACpB;8BAAG,EAAE;IAEL,wBAAwB;IACxB,MAAM,gBAAgB,MACnB,MAAM,CAAC,CAAA;QACN,4BAA4B;QAC5B,IAAI,aAAa;YACf,MAAM,QAAQ,YAAY,WAAW;YACrC,MAAM,WAAW,GAAG,KAAK,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,QAAQ,EAAE,CAAC,WAAW;YACjF,IAAI,CAAC,SAAS,QAAQ,CAAC,UACnB,CAAC,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,UACnC,CAAC,KAAK,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ;gBAChD,OAAO;YACT;QACF;QAEA,oBAAoB;QACpB,IAAI,eAAe,SAAS,KAAK,IAAI,KAAK,YAAY;YACpD,OAAO;QACT;QAEA,sBAAsB;QACtB,IAAI,iBAAiB,SAAS,KAAK,MAAM,KAAK,cAAc;YAC1D,OAAO;QACT;QAEA,OAAO;IACT,GACC,IAAI,CAAC,CAAC,GAAG;QACR,gBAAgB;QAChB,IAAI,WAAW,QAAQ;YACrB,MAAM,QAAQ,GAAG,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,OAAO,CAAC,QAAQ,EAAE;YAC5D,MAAM,QAAQ,GAAG,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,OAAO,CAAC,QAAQ,EAAE;YAC5D,OAAO,kBAAkB,QACrB,MAAM,aAAa,CAAC,SACpB,MAAM,aAAa,CAAC;QAC1B;QAEA,IAAI,WAAW,QAAQ;YACrB,OAAO,kBAAkB,QACrB,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI,IAC3B,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;QACjC;QAEA,IAAI,WAAW,UAAU;YACvB,OAAO,kBAAkB,QACrB,EAAE,MAAM,CAAC,aAAa,CAAC,EAAE,MAAM,IAC/B,EAAE,MAAM,CAAC,aAAa,CAAC,EAAE,MAAM;QACrC;QAEA,IAAI,WAAW,cAAc;YAC3B,OAAO,kBAAkB,QACrB,EAAE,QAAQ,CAAC,UAAU,CAAC,OAAO,KAAK,EAAE,QAAQ,CAAC,UAAU,CAAC,OAAO,KAC/D,EAAE,QAAQ,CAAC,UAAU,CAAC,OAAO,KAAK,EAAE,QAAQ,CAAC,UAAU,CAAC,OAAO;QACrE;QAEA,OAAO;IACT;IAEF,gBAAgB;IAChB,MAAM,eAAe,CAAC;QACpB,eAAe,EAAE,MAAM,CAAC,KAAK;IAC/B;IAEA,cAAc;IACd,MAAM,aAAa,CAAC;QAClB,IAAI,WAAW,QAAQ;YACrB,iBAAiB,kBAAkB,QAAQ,SAAS;QACtD,OAAO;YACL,UAAU;YACV,iBAAiB;QACnB;IACF;IAEA,0BAA0B;IAC1B,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,MAAM,kBAAkB,CAAC,SAAS;YAChD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,mBAAmB;IACnB,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,qBACE,6LAAC;oBAAK,WAAU;;sCACd,6LAAC,uKAAA,CAAA,kBAAe;4BAAC,MAAM,2KAAA,CAAA,gBAAa;4BAAE,WAAU;;;;;;wBAAiB;;;;;;;YAIvE,KAAK;gBACH,qBACE,6LAAC;oBAAK,WAAU;;sCACd,6LAAC,uKAAA,CAAA,kBAAe;4BAAC,MAAM,2KAAA,CAAA,gBAAa;4BAAE,WAAU;;;;;;wBAAiB;;;;;;;YAIvE,KAAK;gBACH,qBACE,6LAAC;oBAAK,WAAU;;sCACd,6LAAC,uKAAA,CAAA,kBAAe;4BAAC,MAAM,2KAAA,CAAA,UAAO;4BAAE,WAAU;;;;;;wBAAiB;;;;;;;YAIjE,KAAK;gBACH,qBACE,6LAAC;oBAAK,WAAU;;sCACd,6LAAC,uKAAA,CAAA,kBAAe;4BAAC,MAAM,2KAAA,CAAA,wBAAqB;4BAAE,WAAU;;;;;;wBAAiB;;;;;;;QAIjF;IACF;IAEA,iBAAiB;IACjB,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBACH,qBACE,6LAAC;oBAAK,WAAU;8BAAmJ;;;;;;YAIvK,KAAK;gBACH,qBACE,6LAAC;oBAAK,WAAU;8BAA2I;;;;;;YAI/J,KAAK;gBACH,qBACE,6LAAC;oBAAK,WAAU;8BAAmJ;;;;;;YAIvK,KAAK;gBACH,qBACE,6LAAC;oBAAK,WAAU;8BAA2I;;;;;;QAIjK;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,6LAAC,uKAAA,CAAA,kBAAe;wCAAC,MAAM,2KAAA,CAAA,cAAW;wCAAE,WAAU;;;;;;oCAAiB;;;;;;;0CAGjE,6LAAC;gCAAG,WAAU;0CAAmD;;;;;;;;;;;;kCAGnE,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;;0CAEV,6LAAC,uKAAA,CAAA,kBAAe;gCAAC,MAAM,2KAAA,CAAA,aAAU;gCAAE,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAMlE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,uKAAA,CAAA,kBAAe;wCAAC,MAAM,2KAAA,CAAA,WAAQ;wCAAE,WAAU;;;;;;;;;;;8CAE7C,6LAAC;oCACC,MAAK;oCACL,WAAU;oCACV,aAAY;oCACZ,OAAO;oCACP,UAAU;;;;;;;;;;;;sCAId,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAc,WAAU;sDAAU;;;;;;sDAGjD,6LAAC;4CACC,IAAG;4CACH,WAAU;4CACV,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;;8DAE7C,6LAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,6LAAC;oDAAO,OAAM;8DAAQ;;;;;;8DACtB,6LAAC;oDAAO,OAAM;8DAAU;;;;;;8DACxB,6LAAC;oDAAO,OAAM;8DAAO;;;;;;8DACrB,6LAAC;oDAAO,OAAM;8DAAQ;;;;;;;;;;;;;;;;;;8CAI1B,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAgB,WAAU;sDAAU;;;;;;sDAGnD,6LAAC;4CACC,IAAG;4CACH,WAAU;4CACV,OAAO;4CACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;;8DAE/C,6LAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,6LAAC;oDAAO,OAAM;8DAAS;;;;;;8DACvB,6LAAC;oDAAO,OAAM;8DAAW;;;;;;8DACzB,6LAAC;oDAAO,OAAM;8DAAU;;;;;;8DACxB,6LAAC;oDAAO,OAAM;8DAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQpC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAM,WAAU;;0CACf,6LAAC;gCAAM,WAAU;0CACf,cAAA,6LAAC;;sDACC,6LAAC;4CACC,OAAM;4CACN,WAAU;sDAEV,cAAA,6LAAC;gDACC,WAAU;gDACV,SAAS,IAAM,WAAW;;oDAC3B;oDAEE,WAAW,wBACV,6LAAC;wDAAK,WAAU;kEACb,kBAAkB,QAAQ,MAAM;;;;;;;;;;;;;;;;;sDAKzC,6LAAC;4CACC,OAAM;4CACN,WAAU;sDAEV,cAAA,6LAAC;gDACC,WAAU;gDACV,SAAS,IAAM,WAAW;;oDAC3B;oDAEE,WAAW,wBACV,6LAAC;wDAAK,WAAU;kEACb,kBAAkB,QAAQ,MAAM;;;;;;;;;;;;;;;;;sDAKzC,6LAAC;4CACC,OAAM;4CACN,WAAU;sDAEV,cAAA,6LAAC;gDACC,WAAU;gDACV,SAAS,IAAM,WAAW;;oDAC3B;oDAEE,WAAW,0BACV,6LAAC;wDAAK,WAAU;kEACb,kBAAkB,QAAQ,MAAM;;;;;;;;;;;;;;;;;sDAKzC,6LAAC;4CACC,OAAM;4CACN,WAAU;sDAEV,cAAA,6LAAC;gDACC,WAAU;gDACV,SAAS,IAAM,WAAW;;oDAC3B;oDAEE,WAAW,8BACV,6LAAC;wDAAK,WAAU;kEACb,kBAAkB,QAAQ,MAAM;;;;;;;;;;;;;;;;;sDAKzC,6LAAC;4CACC,OAAM;4CACN,WAAU;sDACX;;;;;;;;;;;;;;;;;0CAKL,6LAAC;gCAAM,WAAU;0CACd,cAAc,GAAG,CAAC,CAAC,qBAClB,6LAAC;wCAAiB,WAAU;;0DAC1B,6LAAC;gDAAG,WAAU;0DACZ,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEACC,WAAU;gEACV,KAAK,KAAK,OAAO,CAAC,SAAS,IAAI,CAAC,iCAAiC,EAAE,KAAK,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,QAAQ,CAAC,kBAAkB,CAAC;gEACtI,KAAK,GAAG,KAAK,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,QAAQ,EAAE;;;;;;;;;;;sEAG7D,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;wEACZ,KAAK,OAAO,CAAC,SAAS;wEAAC;wEAAE,KAAK,OAAO,CAAC,QAAQ;;;;;;;8EAEjD,6LAAC;oEAAI,WAAU;8EACZ,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;0DAKnB,6LAAC;gDAAG,WAAU;0DACX,aAAa,KAAK,IAAI;;;;;;0DAEzB,6LAAC;gDAAG,WAAU;0DACX,eAAe,KAAK,MAAM;;;;;;0DAE7B,6LAAC;gDAAG,WAAU;0DACX,WAAW,KAAK,QAAQ,CAAC,UAAU;;;;;;0DAEtC,6LAAC;gDAAG,WAAU;0DACZ,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,+JAAA,CAAA,UAAI;4DACH,MAAM,CAAC,aAAa,EAAE,KAAK,EAAE,EAAE;4DAC/B,WAAU;;8EAEV,6LAAC,uKAAA,CAAA,kBAAe;oEAAC,MAAM,2KAAA,CAAA,SAAM;oEAAE,WAAU;;;;;;8EACzC,6LAAC;oEAAK,WAAU;8EAAU;;;;;;;;;;;;sEAE5B,6LAAC;4DACC,WAAU;4DACV,SAAS;gEACP,uDAAuD;gEACvD,MAAM,CAAC,aAAa,EAAE,KAAK,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,QAAQ,EAAE;4DACzE;;8EAEA,6LAAC,uKAAA,CAAA,kBAAe;oEAAC,MAAM,2KAAA,CAAA,UAAO;oEAAE,WAAU;;;;;;8EAC1C,6LAAC;oEAAK,WAAU;8EAAU;;;;;;;;;;;;wDAE3B,KAAK,MAAM,KAAK,yBACf,6LAAC;4DACC,WAAU;4DACV,SAAS;gEACP,mDAAmD;gEACnD,MAAM,CAAC,cAAc,EAAE,KAAK,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,QAAQ,EAAE;4DAC1E;;8EAEA,6LAAC,uKAAA,CAAA,kBAAe;oEAAC,MAAM,2KAAA,CAAA,SAAM;oEAAE,WAAU;;;;;;8EACzC,6LAAC;oEAAK,WAAU;8EAAU;;;;;;;;;;;iFAG5B,6LAAC;4DACC,WAAU;4DACV,SAAS;gEACP,mDAAmD;gEACnD,MAAM,CAAC,eAAe,EAAE,KAAK,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,QAAQ,EAAE;4DAC3E;;8EAEA,6LAAC,uKAAA,CAAA,kBAAe;oEAAC,MAAM,2KAAA,CAAA,WAAQ;oEAAE,WAAU;;;;;;8EAC3C,6LAAC;oEAAK,WAAU;8EAAU;;;;;;;;;;;;;;;;;;;;;;;;uCApE3B,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;YAiFzB,cAAc,MAAM,KAAK,mBACxB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,uKAAA,CAAA,kBAAe;wBAAC,MAAM,2KAAA,CAAA,UAAO;wBAAE,WAAU;;;;;;kCAC1C,6LAAC;wBAAG,WAAU;kCAAyD;;;;;;kCACvE,6LAAC;wBAAE,WAAU;kCACV,cACG,8DACA;;;;;;oBAEL,6BACC,6LAAC;wBACC,SAAS,IAAM,eAAe;wBAC9B,WAAU;kCACX;;;;;;;;;;;;;;;;;;AAQb;GApawB;KAAA", "debugId": null}}]}