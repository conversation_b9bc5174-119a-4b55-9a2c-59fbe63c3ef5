(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/src_components_animations_FuturisticBackground_tsx_5e5472d4._.js", {

"[project]/src/components/animations/FuturisticBackground.tsx [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/src_components_animations_FuturisticBackground_tsx_bd7be1df._.js",
  "static/chunks/src_components_animations_FuturisticBackground_tsx_e3db80ba._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/animations/FuturisticBackground.tsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}}),
}]);