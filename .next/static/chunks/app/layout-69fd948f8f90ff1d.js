(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{347:()=>{},4147:e=>{e.exports={style:{fontFamily:"'Geist', 'Geist Fallback'",fontStyle:"normal"},className:"__className_4d318d",variable:"__variable_4d318d"}},5695:(e,t,s)=>{"use strict";var r=s(8999);s.o(r,"useParams")&&s.d(t,{useParams:function(){return r.useParams}}),s.o(r,"usePathname")&&s.d(t,{usePathname:function(){return r.usePathname}})},5839:(e,t,s)=>{"use strict";s.d(t,{default:()=>h});var r=s(5155),a=s(2115),n=s(6874),o=s.n(n);function i(){let[e,t]=(0,a.useState)(!1);return(0,r.jsx)("header",{className:"sticky top-0 z-50 w-full bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-800 shadow-sm",children:(0,r.jsxs)("div",{className:"flex h-16 items-center justify-between px-4 md:px-6",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsxs)("button",{type:"button",className:"md:hidden p-2 text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300",onClick:()=>{document.documentElement.classList.toggle("sidebar-open")},children:[(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"h-6 w-6",children:[(0,r.jsx)("line",{x1:"4",x2:"20",y1:"12",y2:"12"}),(0,r.jsx)("line",{x1:"4",x2:"20",y1:"6",y2:"6"}),(0,r.jsx)("line",{x1:"4",x2:"20",y1:"18",y2:"18"})]}),(0,r.jsx)("span",{className:"sr-only",children:"Toggle menu"})]}),(0,r.jsxs)(o(),{href:"/",className:"flex items-center ml-4 md:ml-0",children:[(0,r.jsx)("div",{className:"relative h-8 w-8 mr-2",children:(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"h-8 w-8 text-blue-600",children:[(0,r.jsx)("path",{d:"M12 2a4 4 0 0 1 4 4v4a4 4 0 0 1-4 4 4 4 0 0 1-4-4V6a4 4 0 0 1 4-4z"}),(0,r.jsx)("path",{d:"M18 8a4 4 0 0 1 4 4v2a4 4 0 0 1-4 4 4 4 0 0 1-4-4v-2a4 4 0 0 1 4-4z"}),(0,r.jsx)("path",{d:"M6 8a4 4 0 0 1 4 4v2a4 4 0 0 1-4 4 4 4 0 0 1-4-4v-2a4 4 0 0 1 4-4z"})]})}),(0,r.jsx)("span",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:"AI Hub"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"hidden md:flex relative",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none",children:(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"h-4 w-4 text-gray-400",children:[(0,r.jsx)("circle",{cx:"11",cy:"11",r:"8"}),(0,r.jsx)("path",{d:"m21 21-4.3-4.3"})]})}),(0,r.jsx)("input",{type:"search",className:"block w-full rounded-md border border-gray-200 bg-gray-50 py-2 pl-10 pr-3 text-sm text-gray-900 placeholder:text-gray-500 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white dark:placeholder:text-gray-400",placeholder:"Search agents..."})]}),(0,r.jsxs)("button",{type:"button",className:"p-2 text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300",children:[(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"h-6 w-6",children:[(0,r.jsx)("path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9"}),(0,r.jsx)("path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0"})]}),(0,r.jsx)("span",{className:"sr-only",children:"Notifications"})]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("button",{type:"button",className:"flex items-center rounded-full",onClick:()=>t(!e),children:(0,r.jsx)("div",{className:"relative h-8 w-8 rounded-full bg-gray-200 overflow-hidden",children:(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"absolute h-8 w-8 text-gray-400",children:[(0,r.jsx)("path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"}),(0,r.jsx)("circle",{cx:"12",cy:"7",r:"4"})]})})}),e&&(0,r.jsxs)("div",{className:"absolute right-0 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-gray-800 dark:ring-gray-700",children:[(0,r.jsx)(o(),{href:"/profile",className:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700",onClick:()=>t(!1),children:"Your Profile"}),(0,r.jsx)(o(),{href:"/settings",className:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700",onClick:()=>t(!1),children:"Settings"}),(0,r.jsx)("button",{className:"block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700",onClick:()=>t(!1),children:"Sign out"})]})]})]})]})})}var l=s(5695);function d(){let e=(0,l.usePathname)(),[t,s]=(0,a.useState)(!1),[n,i]=(0,a.useState)(["Discover","My Agents"]),d=e=>{i(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])},h=[{title:"Discover",items:[{name:"Home",href:"/",icon:(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"h-5 w-5",children:[(0,r.jsx)("path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"}),(0,r.jsx)("polyline",{points:"9 22 9 12 15 12 15 22"})]})},{name:"Browse Agents",href:"/browse",icon:(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"h-5 w-5",children:[(0,r.jsx)("rect",{width:"7",height:"7",x:"3",y:"3",rx:"1"}),(0,r.jsx)("rect",{width:"7",height:"7",x:"14",y:"3",rx:"1"}),(0,r.jsx)("rect",{width:"7",height:"7",x:"14",y:"14",rx:"1"}),(0,r.jsx)("rect",{width:"7",height:"7",x:"3",y:"14",rx:"1"})]})},{name:"Popular Agents",href:"/popular",icon:(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"h-5 w-5",children:[(0,r.jsx)("path",{d:"M12 2v8"}),(0,r.jsx)("path",{d:"m4.93 10.93 1.41 1.41"}),(0,r.jsx)("path",{d:"M2 18h2"}),(0,r.jsx)("path",{d:"M20 18h2"}),(0,r.jsx)("path",{d:"m19.07 10.93-1.41 1.41"}),(0,r.jsx)("path",{d:"M22 22H2"}),(0,r.jsx)("path",{d:"m16 8-4 4-4-4"}),(0,r.jsx)("path",{d:"M16 16a4 4 0 0 0-8 0"})]})},{name:"New Releases",href:"/new-releases",icon:(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"h-5 w-5",children:[(0,r.jsx)("path",{d:"M12.41 15.4a4 4 0 1 0-4.82 0"}),(0,r.jsx)("path",{d:"m19 15-2-2"}),(0,r.jsx)("path",{d:"m15 11-2-2"}),(0,r.jsx)("path",{d:"m5 15 2-2"}),(0,r.jsx)("path",{d:"m9 11 2-2"}),(0,r.jsx)("path",{d:"M12 19h.01"}),(0,r.jsx)("path",{d:"M18 19h.01"}),(0,r.jsx)("path",{d:"M6 19h.01"}),(0,r.jsx)("path",{d:"M12 15v-2.4"}),(0,r.jsx)("path",{d:"M12 7V3"})]})}]},{title:"My Agents",items:[{name:"Favorites",href:"/favorites",icon:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"h-5 w-5",children:(0,r.jsx)("path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z"})})},{name:"Recent Sessions",href:"/recent",icon:(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"h-5 w-5",children:[(0,r.jsx)("path",{d:"M12 8v4l3 3"}),(0,r.jsx)("circle",{cx:"12",cy:"12",r:"10"})]})},{name:"Saved Sessions",href:"/saved",icon:(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"h-5 w-5",children:[(0,r.jsx)("path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"}),(0,r.jsx)("polyline",{points:"17 21 17 13 7 13 7 21"}),(0,r.jsx)("polyline",{points:"7 3 7 8 15 8"})]})}]},{title:"Admin",items:[{name:"Analytics",href:"/analytics",icon:(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"h-5 w-5",children:[(0,r.jsx)("path",{d:"M3 3v18h18"}),(0,r.jsx)("path",{d:"m19 9-5 5-4-4-3 3"})]})},{name:"User Management",href:"/users",icon:(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"h-5 w-5",children:[(0,r.jsx)("path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"}),(0,r.jsx)("circle",{cx:"9",cy:"7",r:"4"}),(0,r.jsx)("path",{d:"M22 21v-2a4 4 0 0 0-3-3.87"}),(0,r.jsx)("path",{d:"M16 3.13a4 4 0 0 1 0 7.75"})]})},{name:"Agent Management",href:"/manage-agents",icon:(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"h-5 w-5",children:[(0,r.jsx)("path",{d:"M12 12.5a3.5 3.5 0 1 0 0-7 3.5 3.5 0 0 0 0 7Z"}),(0,r.jsx)("path",{d:"M3 16.5v-.75A2.75 2.75 0 0 1 5.75 13h12.5A2.75 2.75 0 0 1 21 15.75v.75"}),(0,r.jsx)("path",{d:"m2 2 20 20"}),(0,r.jsx)("path",{d:"M7.5 7.5a4.5 4.5 0 1 0 9 9"})]})}]}],[c,x]=(0,a.useState)(!1);return a.useEffect(()=>{x(!0)},[]),(0,r.jsxs)("aside",{className:"fixed inset-y-0 left-0 z-40 flex flex-col border-r border-gray-200 bg-white transition-all dark:border-gray-800 dark:bg-gray-900 ".concat(t?"w-16":"w-64"," md:translate-x-0 ").concat(c&&document.documentElement.classList.contains("sidebar-open")?"translate-x-0":"-translate-x-full"),children:[(0,r.jsxs)("div",{className:"flex h-16 items-center justify-between border-b border-gray-200 px-4 dark:border-gray-800",children:[!t&&(0,r.jsx)("span",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Navigation"}),(0,r.jsxs)("button",{type:"button",className:"p-2 text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300 ".concat(t?"mx-auto":""),onClick:()=>s(!t),children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"h-5 w-5",children:t?(0,r.jsx)(r.Fragment,{children:(0,r.jsx)("path",{d:"m9 18 6-6-6-6"})}):(0,r.jsx)(r.Fragment,{children:(0,r.jsx)("path",{d:"m15 18-6-6 6-6"})})}),(0,r.jsx)("span",{className:"sr-only",children:"Toggle sidebar"})]})]}),(0,r.jsx)("div",{className:"flex-1 overflow-y-auto p-4",children:(0,r.jsx)("nav",{className:"space-y-6",children:h.map(s=>(0,r.jsxs)("div",{className:"space-y-2",children:[!t&&(0,r.jsxs)("button",{type:"button",className:"flex w-full items-center justify-between text-sm font-medium text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200",onClick:()=>d(s.title),children:[s.title,(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"h-4 w-4 transition-transform ".concat(n.includes(s.title)?"rotate-180":""),children:(0,r.jsx)("path",{d:"m6 9 6 6 6-6"})})]}),(t||n.includes(s.title))&&(0,r.jsx)("ul",{className:"space-y-1",children:s.items.map(s=>(0,r.jsx)("li",{children:(0,r.jsxs)(o(),{href:s.href,className:"flex items-center rounded-md px-3 py-2 text-sm font-medium ".concat(e===s.href?"bg-blue-50 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400":"text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800"," ").concat(t?"justify-center":""),children:[(0,r.jsx)("span",{className:"mr-3",children:s.icon}),!t&&(0,r.jsx)("span",{children:s.name})]})},s.name))})]},s.title))})})]})}function h(e){let{children:t}=e;return(0,a.useEffect)(()=>{let e=e=>{let t=document.querySelector("aside"),s=document.querySelector('button[aria-label="Toggle menu"]');t&&!t.contains(e.target)&&s&&!s.contains(e.target)&&document.documentElement.classList.contains("sidebar-open")&&document.documentElement.classList.remove("sidebar-open")};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]),(0,r.jsxs)("div",{className:"flex min-h-screen flex-col",children:[(0,r.jsx)(i,{}),(0,r.jsxs)("div",{className:"flex flex-1",children:[(0,r.jsx)(d,{}),(0,r.jsx)("main",{className:"flex-1 transition-all duration-200 md:pl-64",children:(0,r.jsx)("div",{className:"container mx-auto px-4 py-8 md:px-6 lg:px-8",children:t})})]})]})}},8489:e=>{e.exports={style:{fontFamily:"'Geist Mono', 'Geist Mono Fallback'",fontStyle:"normal"},className:"__className_ea5f4b",variable:"__variable_ea5f4b"}},9074:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,4147,23)),Promise.resolve().then(s.t.bind(s,8489,23)),Promise.resolve().then(s.t.bind(s,347,23)),Promise.resolve().then(s.bind(s,5839))}},e=>{var t=t=>e(e.s=t);e.O(0,[896,874,441,684,358],()=>t(9074)),_N_E=e.O()}]);