(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[40],{5285:(e,t,r)=>{Promise.resolve().then(r.bind(r,5778))},5778:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h});var a=r(5155),s=r(2115),n=r(603),i=r(2340),l=r(9608),o=r(9052),d=r(9464),x=r(4217),g=r(6874),c=r.n(g);function h(){let e=(0,s.useMemo)(()=>(0,n.mK)(),[]),[t,r]=(0,s.useState)([]),[g,h]=(0,s.useState)([]),[m,y]=(0,s.useState)(!0),[u,b]=(0,s.useState)("grid"),[w,v]=(0,s.useState)("recent"),[j,k]=(0,s.useState)(1),[p,f]=(0,s.useState)(12);(0,s.useEffect)(()=>{let t=()=>{let t=(0,i.oE)().favoriteAgentIds;r(e.filter(e=>t.includes(e.id))),y(!1)};t();let a=(0,l.xg)(l.qY.FAVORITES_UPDATED,()=>{t()}),s=e=>{"userPreferences"===e.key&&t()};return window.addEventListener("storage",s),()=>{window.removeEventListener("storage",s),a()}},[e]),(0,s.useEffect)(()=>{let e=[...t];switch(w){case"recent":break;case"name":e.sort((e,t)=>e.name.localeCompare(t.name));break;case"popular":e.sort((e,t)=>t.usageCount-e.usageCount);break;case"category":e.sort((e,t)=>e.category.localeCompare(t.category))}r(e)},[w]),(0,s.useEffect)(()=>{let e=Math.min(j,Math.max(1,Math.ceil(t.length/p)));if(e!==j){k(e);return}let r=(e-1)*p;h(t.slice(r,r+p))},[t,j,p]);let N=e=>{b(e)},C=e=>{v(e)};return m?(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,a.jsx)("h1",{className:"mb-6 text-2xl font-bold text-gray-900 dark:text-white md:text-3xl",children:"Favorite Agents"}),(0,a.jsx)("div",{className:"flex h-64 items-center justify-center",children:(0,a.jsx)("div",{className:"h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-blue-600"})})]}):0===t.length?(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,a.jsx)("h1",{className:"mb-6 text-2xl font-bold text-gray-900 dark:text-white md:text-3xl",children:"Favorite Agents"}),(0,a.jsxs)("div",{className:"flex h-64 flex-col items-center justify-center rounded-lg border border-gray-200 bg-white p-6 text-center dark:border-gray-800 dark:bg-gray-900",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"mb-4 h-12 w-12 text-gray-400",children:(0,a.jsx)("path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z"})}),(0,a.jsx)("h3",{className:"mb-2 text-lg font-semibold text-gray-900 dark:text-white",children:"No favorite agents yet"}),(0,a.jsx)("p",{className:"mb-6 text-gray-600 dark:text-gray-400",children:"Start adding agents to your favorites to see them here."}),(0,a.jsx)(c(),{href:"/browse",className:"rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800",children:"Browse Agents"})]})]}):(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,a.jsxs)("div",{className:"mb-8 flex flex-col justify-between gap-4 sm:flex-row sm:items-center",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white md:text-3xl",children:"Favorite Agents"}),(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-2 sm:gap-4",children:[(0,a.jsx)("div",{className:"relative",children:(0,a.jsxs)("select",{value:w,onChange:e=>C(e.target.value),className:"rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700",children:[(0,a.jsx)("option",{value:"recent",children:"Recently Added"}),(0,a.jsx)("option",{value:"name",children:"Name (A-Z)"}),(0,a.jsx)("option",{value:"popular",children:"Most Popular"}),(0,a.jsx)("option",{value:"category",children:"Category"})]})}),(0,a.jsxs)("div",{className:"flex rounded-md border border-gray-300 dark:border-gray-600",children:[(0,a.jsx)("button",{onClick:()=>N("grid"),className:"flex items-center justify-center rounded-l-md px-3 py-2 ".concat("grid"===u?"bg-gray-100 text-gray-900 dark:bg-gray-700 dark:text-white":"bg-white text-gray-700 hover:bg-gray-50 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700"),"aria-label":"Grid view",children:(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"h-4 w-4",children:[(0,a.jsx)("rect",{width:"7",height:"7",x:"3",y:"3",rx:"1"}),(0,a.jsx)("rect",{width:"7",height:"7",x:"14",y:"3",rx:"1"}),(0,a.jsx)("rect",{width:"7",height:"7",x:"14",y:"14",rx:"1"}),(0,a.jsx)("rect",{width:"7",height:"7",x:"3",y:"14",rx:"1"})]})}),(0,a.jsx)("button",{onClick:()=>N("list"),className:"flex items-center justify-center rounded-r-md px-3 py-2 ".concat("list"===u?"bg-gray-100 text-gray-900 dark:bg-gray-700 dark:text-white":"bg-white text-gray-700 hover:bg-gray-50 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700"),"aria-label":"List view",children:(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"h-4 w-4",children:[(0,a.jsx)("line",{x1:"8",x2:"21",y1:"6",y2:"6"}),(0,a.jsx)("line",{x1:"8",x2:"21",y1:"12",y2:"12"}),(0,a.jsx)("line",{x1:"8",x2:"21",y1:"18",y2:"18"}),(0,a.jsx)("line",{x1:"3",x2:"3.01",y1:"6",y2:"6"}),(0,a.jsx)("line",{x1:"3",x2:"3.01",y1:"12",y2:"12"}),(0,a.jsx)("line",{x1:"3",x2:"3.01",y1:"18",y2:"18"})]})})]})]})]}),"grid"===u?(0,a.jsx)(o.A,{agents:g}):(0,a.jsx)(d.A,{agents:g}),t.length>0&&(0,a.jsx)(x.A,{totalItems:t.length,currentPage:j,onPageChange:e=>{k(e),window.scrollTo({top:0,behavior:"smooth"})},onPageSizeChange:e=>{f(e),k(1)}})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[874,766,630,739,441,684,358],()=>t(5285)),_N_E=e.O()}]);