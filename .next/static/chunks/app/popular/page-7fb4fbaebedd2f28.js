(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[380],{3545:(e,t,a)=>{Promise.resolve().then(a.bind(a,7139))},7139:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>T});var r=a(5155),s=a(2115),l=a(603);let i=(e,t)=>Math.floor(Math.random()*(t-e+1))+e,d=function(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:2;return Number((Math.random()*(t-e)+e).toFixed(a))},n=(e,t)=>{let a=100*t,r=.2*a;return{agentId:e,totalUses:i(a-r,a+r),averageRating:d(3.5,4.9,1),completionRate:d(.75,.98,2),averageSessionDuration:i(60,600),popularityScore:t}},o=(e,t)=>{let a=[],r=new Date;for(let s=t-1;s>=0;s--){let l=new Date(r);l.setDate(l.getDate()-s);let i=1,n=l.getDay();(0===n||6===n)&&(i=.7);let o=1+s/t*.5,c=Math.round(e/t*i*d(.8,1.2)*o);a.push({date:l.toISOString().split("T")[0],count:c})}return a},c=()=>{let e=[{rating:1,weight:5},{rating:2,weight:10},{rating:3,weight:20},{rating:4,weight:35},{rating:5,weight:30}],t=e.reduce((e,t)=>e+t.weight,0),a=i(50,500);return e.map(e=>{let r=e.weight/t;return{rating:e.rating,count:Math.round(a*r)}})},g=()=>{let e=[];for(let t=0;t<24;t++){let a=1,r=(t>=0&&t<6?.2:t>=6&&t<9?.5+(t-6)*.25:t>=9&&t<17?1:t>=17&&t<22?1-(t-17)*.15:.3)*d(.8,1.2);e.push({hour:t,count:Math.round(100*r)})}return e},x=()=>["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"].map(e=>{let t=("Saturday"===e||"Sunday"===e?.6:1)*("Wednesday"===e||"Thursday"===e?1.1:1)*d(.9,1.1);return{day:e,count:Math.round(100*t)}}),h=e=>{let t=["How to analyze sales data","Generate monthly report","Forecast Q3 revenue","Compare year-over-year growth","Find outliers in dataset","Visualize customer demographics","Calculate profit margins"],a=["Debug React useEffect","Optimize SQL query","Convert JSON to CSV","Fix memory leak","Write unit test for API","Create Docker container","Implement authentication"],r=["Summarize latest AI papers","Find studies on climate change","Compare research methodologies","Generate literature review","Extract key findings","Organize research notes","Find citation for paper"],s=["Summarize team meeting","Extract action items","Generate meeting minutes","Identify key decisions","Create follow-up tasks","Analyze meeting sentiment","Schedule follow-up meeting"],l=[];switch(e){case"1":l=t;break;case"2":l=a;break;case"3":l=r;break;case"4":l=s;break;case"5":l=["Extract data from PDF","Summarize legal document","Find key contract terms","Compare document versions","Generate document outline","Extract contact information","Identify document type"];break;case"6":l=["Create sales pitch deck","Design executive summary","Generate presentation outline","Create data visualizations","Improve slide design","Add speaker notes","Create product demo slides"];break;default:l=[...t.slice(0,2),...a.slice(0,2),...r.slice(0,2),...s.slice(0,1)]}return l.map(e=>({query:e,count:i(10,100)})).sort((e,t)=>t.count-e.count).slice(0,5)},u=e=>{let t=(0,l.mK)().find(t=>t.id===e);if(!t)throw Error("Agent with ID ".concat(e," not found"));let a=n(e,Math.min(100,t.usageCount/50));return{id:t.id,name:t.name,usageMetrics:a,usageOverTime:o(a.totalUses,30),userFeedback:c(),usageByTime:g(),usageByDay:x(),topQueries:h(e)}},m=()=>(0,l.mK)().map(e=>u(e.id)),b=()=>{let e=(0,l.mK)(),t={};e.forEach(e=>{t[e.category]?t[e.category]++:t[e.category]=1});let a=Object.values(t).reduce((e,t)=>e+t,0);return Object.entries(t).map(e=>{let[t,r]=e;return{category:t,count:r,percentage:r/a*100}}).sort((e,t)=>t.count-e.count)},p=e=>o(m().reduce((e,t)=>e+t.usageMetrics.totalUses,0),e),y=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:5;return m().sort((e,t)=>t.usageMetrics.popularityScore-e.usageMetrics.popularityScore).slice(0,e)};function f(e){let{data:t,title:a,height:s=200}=e,l=Math.max(...t.map(e=>e.count)),i=100/t.length;return(0,r.jsxs)("div",{className:"rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900",children:[(0,r.jsx)("h3",{className:"mb-4 text-lg font-semibold text-gray-900 dark:text-white",children:a}),(0,r.jsxs)("div",{style:{height:"".concat(s,"px")},className:"relative w-full",children:[(0,r.jsxs)("div",{className:"absolute bottom-0 left-0 top-0 flex flex-col justify-between text-xs text-gray-500 dark:text-gray-400",children:[(0,r.jsx)("span",{children:"100%"}),(0,r.jsx)("span",{children:"75%"}),(0,r.jsx)("span",{children:"50%"}),(0,r.jsx)("span",{children:"25%"}),(0,r.jsx)("span",{children:"0%"})]}),(0,r.jsxs)("div",{className:"absolute bottom-0 left-6 right-0 top-0",children:[(0,r.jsxs)("div",{className:"absolute bottom-0 left-0 right-0 top-0 flex flex-col justify-between",children:[(0,r.jsx)("div",{className:"h-px w-full bg-gray-200 dark:bg-gray-700"}),(0,r.jsx)("div",{className:"h-px w-full bg-gray-200 dark:bg-gray-700"}),(0,r.jsx)("div",{className:"h-px w-full bg-gray-200 dark:bg-gray-700"}),(0,r.jsx)("div",{className:"h-px w-full bg-gray-200 dark:bg-gray-700"}),(0,r.jsx)("div",{className:"h-px w-full bg-gray-200 dark:bg-gray-700"})]}),(0,r.jsx)("div",{className:"absolute bottom-0 left-0 right-0 flex h-full items-end",children:t.map((e,t)=>{let a=e.count/l*100;return(0,r.jsxs)("div",{className:"group flex flex-col items-center",style:{width:"".concat(i,"%")},children:[(0,r.jsx)("div",{className:"w-4/5 rounded-t bg-blue-500 transition-all group-hover:bg-blue-600 dark:bg-blue-600 dark:group-hover:bg-blue-700",style:{height:"".concat(a,"%")}}),(0,r.jsxs)("div",{className:"invisible absolute bottom-full mb-2 rounded bg-gray-800 p-2 text-xs text-white opacity-0 transition-opacity group-hover:visible group-hover:opacity-100 dark:bg-gray-700",children:[(0,r.jsx)("div",{className:"font-semibold",children:new Date(e.date).toLocaleDateString()}),(0,r.jsxs)("div",{children:[e.count," uses"]})]})]},t)})})]})]}),(0,r.jsxs)("div",{className:"mt-2 flex justify-between text-xs text-gray-500 dark:text-gray-400",children:[(0,r.jsx)("span",{children:new Date(t[0].date).toLocaleDateString()}),(0,r.jsx)("span",{children:new Date(t[Math.floor(t.length/2)].date).toLocaleDateString()}),(0,r.jsx)("span",{children:new Date(t[t.length-1].date).toLocaleDateString()})]})]})}function v(e){let{data:t,title:a}=e,s=t.reduce((e,t)=>e+t.count,0),l=t.reduce((e,t)=>e+t.rating*t.count,0),i=s>0?(l/s).toFixed(1):"0.0";return(0,r.jsxs)("div",{className:"rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900",children:[(0,r.jsxs)("div",{className:"mb-4 flex items-center justify-between",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:a}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"mr-1 text-xl font-bold text-yellow-500",children:i}),(0,r.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:"/ 5"})]})]}),(0,r.jsx)("div",{className:"space-y-3",children:[5,4,3,2,1].map(e=>{let a=t.find(t=>t.rating===e),l=a?a.count:0,i=s>0?l/s*100:0;return(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"mr-2 w-3 text-sm font-medium text-gray-700 dark:text-gray-300",children:e}),(0,r.jsx)("div",{className:"relative h-4 flex-1 rounded-full bg-gray-200 dark:bg-gray-700",children:(0,r.jsx)("div",{className:"absolute left-0 top-0 h-full rounded-full bg-yellow-500",style:{width:"".concat(i,"%")}})}),(0,r.jsxs)("div",{className:"ml-2 w-12 text-right text-xs text-gray-500 dark:text-gray-400",children:[i.toFixed(1),"%"]})]},e)})}),(0,r.jsxs)("div",{className:"mt-4 text-center text-sm text-gray-500 dark:text-gray-400",children:["Based on ",s," ratings"]})]})}function j(e){let{data:t,title:a}=e,s=Math.max(...t.map(e=>e.count));return(0,r.jsxs)("div",{className:"rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900",children:[(0,r.jsx)("h3",{className:"mb-4 text-lg font-semibold text-gray-900 dark:text-white",children:a}),(0,r.jsx)("div",{className:"space-y-3",children:t.map((e,t)=>{let a=e.count/s*100;return(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:e.query}),(0,r.jsxs)("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:[e.count," queries"]})]}),(0,r.jsx)("div",{className:"relative h-2 w-full rounded-full bg-gray-200 dark:bg-gray-700",children:(0,r.jsx)("div",{className:"absolute left-0 top-0 h-full rounded-full bg-blue-500 dark:bg-blue-600",style:{width:"".concat(a,"%")}})})]},t)})})]})}function k(e){let{data:t,title:a}=e,s=Math.max(...t.map(e=>e.count)),l=e=>0===e?"12 AM":12===e?"12 PM":e<12?"".concat(e," AM"):"".concat(e-12," PM");return(0,r.jsxs)("div",{className:"rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900",children:[(0,r.jsx)("h3",{className:"mb-4 text-lg font-semibold text-gray-900 dark:text-white",children:a}),(0,r.jsx)("div",{className:"h-48 w-full",children:(0,r.jsxs)("div",{className:"relative h-full w-full",children:[(0,r.jsx)("div",{className:"absolute bottom-0 left-0 right-0 top-0 flex flex-col justify-between",children:[0,1,2,3,4].map((e,t)=>(0,r.jsx)("div",{className:"h-px w-full bg-gray-200 dark:bg-gray-700"},t))}),(0,r.jsx)("div",{className:"absolute bottom-0 left-0 right-0 flex h-full items-end",children:t.map((e,t)=>{let a=e.count/s*100,i="bg-blue-500 group-hover:bg-blue-600 dark:bg-blue-600 dark:group-hover:bg-blue-700";return e.hour>=9&&e.hour<17?i="bg-green-500 group-hover:bg-green-600 dark:bg-green-600 dark:group-hover:bg-green-700":e.hour>=17&&e.hour<22?i="bg-purple-500 group-hover:bg-purple-600 dark:bg-purple-600 dark:group-hover:bg-purple-700":(e.hour>=22||e.hour<6)&&(i="bg-indigo-500 group-hover:bg-indigo-600 dark:bg-indigo-600 dark:group-hover:bg-indigo-700"),(0,r.jsxs)("div",{className:"group flex flex-1 flex-col items-center",children:[(0,r.jsx)("div",{className:"w-4/5 rounded-t transition-all ".concat(i),style:{height:"".concat(a,"%")}}),(0,r.jsxs)("div",{className:"invisible absolute bottom-full mb-2 rounded bg-gray-800 p-2 text-xs text-white opacity-0 transition-opacity group-hover:visible group-hover:opacity-100 dark:bg-gray-700",children:[(0,r.jsx)("div",{className:"font-semibold",children:l(e.hour)}),(0,r.jsxs)("div",{children:[e.count," uses"]})]})]},t)})})]})}),(0,r.jsx)("div",{className:"mt-2 flex justify-between text-xs text-gray-500 dark:text-gray-400",children:[0,6,12,18,23].map(e=>(0,r.jsx)("span",{children:l(e)},e))})]})}function w(e){let{data:t,title:a}=e,s=Math.max(...t.map(e=>e.count)),l=["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],i=[...t].sort((e,t)=>l.indexOf(e.day)-l.indexOf(t.day));return(0,r.jsxs)("div",{className:"rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900",children:[(0,r.jsx)("h3",{className:"mb-4 text-lg font-semibold text-gray-900 dark:text-white",children:a}),(0,r.jsx)("div",{className:"h-48 w-full",children:(0,r.jsxs)("div",{className:"relative h-full w-full",children:[(0,r.jsx)("div",{className:"absolute bottom-0 left-0 right-0 top-0 flex flex-col justify-between",children:[0,1,2,3,4].map((e,t)=>(0,r.jsx)("div",{className:"h-px w-full bg-gray-200 dark:bg-gray-700"},t))}),(0,r.jsx)("div",{className:"absolute bottom-0 left-0 right-0 flex h-full items-end justify-between",children:i.map((e,t)=>{let a=e.count/s*100,l="Saturday"===e.day||"Sunday"===e.day;return(0,r.jsxs)("div",{className:"group flex w-10 flex-col items-center",children:[(0,r.jsx)("div",{className:"w-8 rounded-t transition-all ".concat(l?"bg-purple-500 group-hover:bg-purple-600 dark:bg-purple-600 dark:group-hover:bg-purple-700":"bg-blue-500 group-hover:bg-blue-600 dark:bg-blue-600 dark:group-hover:bg-blue-700"),style:{height:"".concat(a,"%")}}),(0,r.jsxs)("div",{className:"invisible absolute bottom-full mb-2 rounded bg-gray-800 p-2 text-xs text-white opacity-0 transition-opacity group-hover:visible group-hover:opacity-100 dark:bg-gray-700",children:[(0,r.jsx)("div",{className:"font-semibold",children:e.day}),(0,r.jsxs)("div",{children:[e.count," uses"]})]})]},t)})})]})}),(0,r.jsx)("div",{className:"mt-2 flex justify-between text-xs text-gray-500 dark:text-gray-400",children:i.map((e,t)=>(0,r.jsx)("span",{children:e.day.substring(0,3)},t))})]})}function N(e){let{title:t,value:a,change:s,icon:l}=e;return(0,r.jsx)("div",{className:"rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:t}),(0,r.jsx)("h3",{className:"mt-1 text-2xl font-bold text-gray-900 dark:text-white",children:a}),void 0!==s&&(0,r.jsxs)("p",{className:"mt-1 flex items-center text-sm ".concat(s>=0?"text-green-600 dark:text-green-400":"text-red-600 dark:text-red-400"),children:[s>=0?(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"mr-1 h-3 w-3",children:(0,r.jsx)("polyline",{points:"18 15 12 9 6 15"})}):(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"mr-1 h-3 w-3",children:(0,r.jsx)("polyline",{points:"6 9 12 15 18 9"})}),Math.abs(s),"% from last period"]})]}),(0,r.jsx)("div",{className:"rounded-full bg-blue-100 p-3 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400",children:l})]})})}var M=a(6874),S=a.n(M),C=a(6766),D=a(2340),A=a(9608);function L(e){let{agent:t,metrics:a,rank:l}=e,[i,d]=(0,s.useState)(!1);return(0,s.useEffect)(()=>{d((0,D.Sl)(t.id));let e=(0,A.xg)(A.qY.FAVORITES_UPDATED,e=>{let{agentId:a,isFavorite:r}=e.detail;a===t.id&&d(r)});return()=>{e()}},[t.id]),(0,r.jsxs)("div",{className:"flex rounded-lg border border-gray-200 bg-white p-4 shadow-sm transition-shadow hover:shadow-md dark:border-gray-800 dark:bg-gray-900",children:[(0,r.jsxs)("div",{className:"mr-4 flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-blue-100 text-xl font-bold text-blue-600 dark:bg-blue-900/30 dark:text-blue-400",children:["#",l]}),(0,r.jsxs)("div",{className:"flex flex-1 flex-col",children:[(0,r.jsxs)("div",{className:"mb-2 flex items-start justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[t.avatarUrl&&(0,r.jsx)("div",{className:"mr-3 h-10 w-10 flex-shrink-0 overflow-hidden rounded-full bg-gray-100 dark:bg-gray-800",children:(0,r.jsx)(C.default,{src:t.avatarUrl,alt:"".concat(t.name," icon"),width:40,height:40,className:"h-full w-full object-cover"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-bold text-gray-900 dark:text-white",children:t.name}),(0,r.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:t.category})]})]}),(0,r.jsx)("button",{onClick:e=>{e.preventDefault(),e.stopPropagation(),d((0,D.C9)(t.id))},className:"rounded-full p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-600 dark:hover:bg-gray-800 dark:hover:text-gray-300","aria-label":i?"Remove from favorites":"Add to favorites",children:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:i?"currentColor":"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"h-5 w-5 ".concat(i?"text-yellow-400":""),children:(0,r.jsx)("path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z"})})})]}),(0,r.jsxs)("div",{className:"mb-3 grid grid-cols-3 gap-2 text-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Uses"}),(0,r.jsx)("p",{className:"font-semibold text-gray-900 dark:text-white",children:a.totalUses.toLocaleString()})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Rating"}),(0,r.jsxs)("p",{className:"font-semibold text-gray-900 dark:text-white",children:[a.averageRating.toFixed(1),"/5"]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Completion"}),(0,r.jsxs)("p",{className:"font-semibold text-gray-900 dark:text-white",children:[(100*a.completionRate).toFixed(0),"%"]})]})]}),(0,r.jsxs)("div",{className:"mt-auto flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:["Popularity Score: ",a.popularityScore.toFixed(0),"/100"]}),(0,r.jsx)(S(),{href:"/agent/".concat(t.id),className:"rounded-md bg-blue-600 px-3 py-1 text-sm font-medium text-white hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800",onClick:()=>{(0,D.rN)(t.id)},children:"View Agent"})]})]})]})}function F(e){let{data:t,title:a}=e,s=["bg-blue-500 dark:bg-blue-600","bg-green-500 dark:bg-green-600","bg-purple-500 dark:bg-purple-600","bg-yellow-500 dark:bg-yellow-600","bg-red-500 dark:bg-red-600","bg-indigo-500 dark:bg-indigo-600","bg-pink-500 dark:bg-pink-600","bg-teal-500 dark:bg-teal-600"];return(0,r.jsxs)("div",{className:"rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900",children:[(0,r.jsx)("h3",{className:"mb-4 text-lg font-semibold text-gray-900 dark:text-white",children:a}),(0,r.jsx)("div",{className:"mb-4 h-48 w-full",children:(0,r.jsxs)("div",{className:"relative h-full w-full",children:[(0,r.jsx)("div",{className:"absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 transform",children:(0,r.jsx)("div",{className:"h-32 w-32 rounded-full border-8 border-gray-100 dark:border-gray-800"})}),(0,r.jsxs)("svg",{viewBox:"0 0 100 100",className:"h-full w-full",children:[(0,r.jsx)("circle",{cx:"50",cy:"50",r:"40",fill:"transparent",stroke:"#e5e7eb",strokeWidth:"20"}),t.map((e,a)=>{let l=t.reduce((e,t)=>e+t.percentage,0),i=t.slice(0,a).reduce((e,t)=>e+t.percentage,0),d=i+e.percentage,n=i/l*360,o=d/l*360,c=Math.PI/180*(n-90),g=Math.PI/180*(o-90),x=50+40*Math.cos(c),h=50+40*Math.sin(c),u=50+40*Math.cos(g),m=50+40*Math.sin(g),b="M 50 50 L ".concat(x," ").concat(h," A 40 40 0 ").concat(o-n<=180?"0":"1"," 1 ").concat(u," ").concat(m," Z");return(0,r.jsx)("path",{d:b,fill:s[a%s.length].split(" ")[0],className:"hover:opacity-80"},a)})]})]})}),(0,r.jsx)("div",{className:"space-y-2",children:t.map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"mr-2 h-3 w-3 rounded-full ".concat(s[t%s.length].split(" ")[0])}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:e.category})]}),(0,r.jsxs)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:[e.percentage.toFixed(1),"% (",e.count,")"]})]},t))})]})}function T(){let[e,t]=(0,s.useState)("month"),[a,i]=(0,s.useState)(!0),[d,n]=(0,s.useState)(y(5)),[o,c]=(0,s.useState)(p(30)),[g,x]=(0,s.useState)(b());(0,s.useEffect)(()=>{i(!1)},[]);let h=e=>{t(e);let a=30;switch(e){case"day":a=1;break;case"week":a=7;break;case"month":a=30;break;case"year":a=365}c(p(a))},u=(0,l.mK)().length,m=d.reduce((e,t)=>e+t.usageMetrics.totalUses,0),M=d.reduce((e,t)=>e+t.usageMetrics.averageRating,0)/d.length,S=d.reduce((e,t)=>e+t.usageMetrics.completionRate,0)/d.length;return a?(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,r.jsx)("h1",{className:"mb-6 text-2xl font-bold text-gray-900 dark:text-white md:text-3xl",children:"Popular Agents"}),(0,r.jsx)("div",{className:"flex h-64 items-center justify-center",children:(0,r.jsx)("div",{className:"h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-blue-600"})})]}):(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,r.jsxs)("div",{className:"mb-6 flex flex-col justify-between gap-4 sm:flex-row sm:items-center",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white md:text-3xl",children:"Popular Agents"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Time Range:"}),(0,r.jsx)("div",{className:"rounded-md border border-gray-300 dark:border-gray-600",children:["day","week","month","year"].map(t=>(0,r.jsx)("button",{onClick:()=>h(t),className:"px-3 py-1.5 text-sm font-medium ".concat(e===t?"bg-blue-600 text-white dark:bg-blue-700":"bg-white text-gray-700 hover:bg-gray-50 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700"),children:t.charAt(0).toUpperCase()+t.slice(1)},t))})]})]}),(0,r.jsxs)("div",{className:"mb-8 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4",children:[(0,r.jsx)(N,{title:"Total Agents",value:u,change:8.5,icon:(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"h-6 w-6",children:[(0,r.jsx)("path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"}),(0,r.jsx)("circle",{cx:"9",cy:"7",r:"4"}),(0,r.jsx)("path",{d:"M22 21v-2a4 4 0 0 0-3-3.87"}),(0,r.jsx)("path",{d:"M16 3.13a4 4 0 0 1 0 7.75"})]})}),(0,r.jsx)(N,{title:"Total Uses",value:m.toLocaleString(),change:12.3,icon:(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"h-6 w-6",children:[(0,r.jsx)("path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"}),(0,r.jsx)("path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"})]})}),(0,r.jsx)(N,{title:"Average Rating",value:"".concat(M.toFixed(1),"/5"),change:3.2,icon:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"h-6 w-6",children:(0,r.jsx)("polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"})})}),(0,r.jsx)(N,{title:"Completion Rate",value:"".concat((100*S).toFixed(0),"%"),change:1.8,icon:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"h-6 w-6",children:(0,r.jsx)("polyline",{points:"20 6 9 17 4 12"})})})]}),(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsx)(f,{data:o,title:"Platform Usage (".concat(e.charAt(0).toUpperCase()+e.slice(1),")"),height:300})}),(0,r.jsx)("h2",{className:"mb-4 text-xl font-bold text-gray-900 dark:text-white",children:"Top 5 Agents"}),(0,r.jsx)("div",{className:"mb-8 grid grid-cols-1 gap-4",children:d.map((e,t)=>(0,r.jsx)(L,{agent:(0,l.mK)().find(t=>t.id===e.id),metrics:e.usageMetrics,rank:t+1},e.id))}),(0,r.jsx)("h2",{className:"mb-4 text-xl font-bold text-gray-900 dark:text-white",children:"Usage Patterns"}),(0,r.jsxs)("div",{className:"mb-8 grid grid-cols-1 gap-6 md:grid-cols-2",children:[(0,r.jsx)(k,{data:d[0].usageByTime,title:"Usage by Time of Day"}),(0,r.jsx)(w,{data:d[0].usageByDay,title:"Usage by Day of Week"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-3",children:[(0,r.jsx)(F,{data:g,title:"Agent Categories"}),(0,r.jsx)(v,{data:d[0].userFeedback,title:"User Ratings"}),(0,r.jsx)(j,{data:d[0].topQueries,title:"Top Queries"})]})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[874,766,630,441,684,358],()=>t(3545)),_N_E=e.O()}]);