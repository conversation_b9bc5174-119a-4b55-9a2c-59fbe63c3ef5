(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[741],{3228:(e,r,t)=>{Promise.resolve().then(t.bind(t,7959))},7959:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>y});var a=t(5155),s=t(2115),l=t(603),n=t(2340);function d(e){let{isOpen:r,onClose:t,onApplyFilters:n,selectedTags:d,selectedCapabilities:i,selectedDateRange:o}=e,[c,g]=(0,s.useState)([]),[x,u]=(0,s.useState)(d),[h,m]=(0,s.useState)(i),[b,y]=(0,s.useState)(o);(0,s.useEffect)(()=>{g((0,l.r)())},[]);let k=e=>{u(r=>r.includes(e)?r.filter(r=>r!==e):[...r,e])},f=e=>{m(r=>r.includes(e)?r.filter(r=>r!==e):[...r,e])};return r?(0,a.jsx)("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50",children:(0,a.jsxs)("div",{className:"relative max-h-[90vh] w-full max-w-3xl overflow-auto rounded-lg bg-white p-6 shadow-xl dark:bg-gray-900",children:[(0,a.jsx)("button",{onClick:t,className:"absolute right-4 top-4 rounded-full p-1 text-gray-500 hover:bg-gray-100 hover:text-gray-700 dark:text-gray-400 dark:hover:bg-gray-800 dark:hover:text-gray-300",children:(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"h-6 w-6",children:[(0,a.jsx)("path",{d:"M18 6 6 18"}),(0,a.jsx)("path",{d:"m6 6 12 12"})]})}),(0,a.jsx)("h2",{className:"mb-6 text-2xl font-bold text-gray-900 dark:text-white",children:"Advanced Filters"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-8 md:grid-cols-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"mb-3 text-lg font-semibold text-gray-900 dark:text-white",children:"Tags"}),(0,a.jsx)("div",{className:"max-h-60 overflow-y-auto rounded-md border border-gray-200 p-3 dark:border-gray-700",children:c.length>0?(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:c.map(e=>(0,a.jsx)("button",{onClick:()=>k(e),className:"rounded-full px-3 py-1 text-sm ".concat(x.includes(e)?"bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400":"bg-gray-100 text-gray-800 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700"),children:e},e))}):(0,a.jsx)("p",{className:"text-center text-gray-500 dark:text-gray-400",children:"Loading tags..."})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"mb-3 text-lg font-semibold text-gray-900 dark:text-white",children:"Capabilities"}),(0,a.jsx)("div",{className:"max-h-60 overflow-y-auto rounded-md border border-gray-200 p-3 dark:border-gray-700",children:(0,a.jsx)("div",{className:"space-y-2",children:["Natural language processing","Data analysis","Content generation","Document processing","Code generation","Summarization","Visualization","Question answering"].map(e=>(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",id:"capability-".concat(e),checked:h.includes(e),onChange:()=>f(e),className:"h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:focus:ring-blue-600"}),(0,a.jsx)("label",{htmlFor:"capability-".concat(e),className:"ml-2 text-sm font-medium text-gray-700 dark:text-gray-200",children:e})]},e))})})]})]}),(0,a.jsxs)("div",{className:"mt-8",children:[(0,a.jsx)("h3",{className:"mb-3 text-lg font-semibold text-gray-900 dark:text-white",children:"Date Range"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"date-from",className:"mb-1 block text-sm font-medium text-gray-700 dark:text-gray-200",children:"From"}),(0,a.jsx)("input",{type:"date",id:"date-from",value:(null==b?void 0:b.from)||"",onChange:e=>{y(r=>({from:e.target.value,to:(null==r?void 0:r.to)||""}))},className:"block w-full rounded-md border border-gray-300 p-2 text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:focus:border-blue-600 dark:focus:ring-blue-600"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"date-to",className:"mb-1 block text-sm font-medium text-gray-700 dark:text-gray-200",children:"To"}),(0,a.jsx)("input",{type:"date",id:"date-to",value:(null==b?void 0:b.to)||"",onChange:e=>{y(r=>({from:(null==r?void 0:r.from)||"",to:e.target.value}))},className:"block w-full rounded-md border border-gray-300 p-2 text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:focus:border-blue-600 dark:focus:ring-blue-600"})]})]})]}),(0,a.jsxs)("div",{className:"mt-8 flex justify-end space-x-4",children:[(0,a.jsx)("button",{onClick:()=>{u([]),m([]),y(null)},className:"rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700",children:"Reset"}),(0,a.jsx)("button",{onClick:()=>{n({tags:x,capabilities:h,dateRange:b&&b.from&&b.to?b:null}),t()},className:"rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800",children:"Apply Filters"})]})]})}):null}function i(e){var r;let{categories:t,onCategoryChange:l,onShowNewOnly:i,onSortChange:o,onViewChange:c,onAdvancedFiltersChange:g}=e,[x,u]=(0,s.useState)([]),[h,m]=(0,s.useState)(!1),[b,y]=(0,s.useState)("popular"),[k,f]=(0,s.useState)("grid"),[p,w]=(0,s.useState)(!1),[v,j]=(0,s.useState)(!1),[N,C]=(0,s.useState)(!1),[S,L]=(0,s.useState)([]),[A,E]=(0,s.useState)([]),[T,F]=(0,s.useState)(null),B=[{label:"Most Popular",value:"popular"},{label:"Newest",value:"newest"},{label:"Alphabetical (A-Z)",value:"alphabetical"}],M=e=>{u(r=>r.includes(e)?r.filter(r=>r!==e):[...r,e])},D=()=>{m(!h)},W=e=>{y(e),j(!1)},z=e=>{f(e),(0,n.M5)(e)};return(0,s.useEffect)(()=>{{let e=(0,n.j$)();f(e),c(e)}},[c]),(0,s.useEffect)(()=>{l(x)},[x,l]),(0,s.useEffect)(()=>{i(h)},[h,i]),(0,s.useEffect)(()=>{o(b)},[b,o]),(0,s.useEffect)(()=>{c(k)},[k,c]),(0,a.jsxs)("div",{className:"mb-8 space-y-4",children:[(0,a.jsxs)("div",{className:"flex flex-col justify-between gap-4 sm:flex-row sm:items-center",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white md:text-3xl",children:"Browse Agents"}),(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-2 sm:gap-4",children:[(0,a.jsxs)("button",{onClick:()=>C(!0),className:"flex items-center rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700",children:["Advanced Filters",(S.length>0||A.length>0||T)&&(0,a.jsx)("span",{className:"ml-1.5 rounded-full bg-blue-100 px-2 py-0.5 text-xs font-medium text-blue-800 dark:bg-blue-900/30 dark:text-blue-400",children:S.length+A.length+ +!!T})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("button",{onClick:()=>w(!p),className:"flex items-center rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700",children:["Categories",x.length>0&&(0,a.jsx)("span",{className:"ml-1.5 rounded-full bg-blue-100 px-2 py-0.5 text-xs font-medium text-blue-800 dark:bg-blue-900/30 dark:text-blue-400",children:x.length}),(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"ml-1 h-4 w-4",children:(0,a.jsx)("path",{d:"m6 9 6 6 6-6"})})]}),p&&(0,a.jsx)("div",{className:"absolute right-0 z-10 mt-2 w-56 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-gray-800 dark:ring-gray-700",children:(0,a.jsx)("div",{className:"p-2",children:t.map(e=>(0,a.jsxs)("div",{className:"flex items-center px-3 py-2",children:[(0,a.jsx)("input",{type:"checkbox",id:"category-".concat(e),checked:x.includes(e),onChange:()=>M(e),className:"h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:focus:ring-blue-600"}),(0,a.jsx)("label",{htmlFor:"category-".concat(e),className:"ml-2 text-sm font-medium text-gray-700 dark:text-gray-200",children:e})]},e))})})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",id:"new-only",checked:h,onChange:D,className:"h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:focus:ring-blue-600"}),(0,a.jsx)("label",{htmlFor:"new-only",className:"ml-2 text-sm font-medium text-gray-700 dark:text-gray-200",children:"New Only"})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("button",{onClick:()=>j(!v),className:"flex items-center rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700",children:["Sort: ",null===(r=B.find(e=>e.value===b))||void 0===r?void 0:r.label,(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"ml-1 h-4 w-4",children:(0,a.jsx)("path",{d:"m6 9 6 6 6-6"})})]}),v&&(0,a.jsx)("div",{className:"absolute right-0 z-10 mt-2 w-56 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-gray-800 dark:ring-gray-700",children:(0,a.jsx)("div",{className:"py-1",children:B.map(e=>(0,a.jsx)("button",{onClick:()=>W(e.value),className:"block w-full px-4 py-2 text-left text-sm ".concat(b===e.value?"bg-gray-100 text-gray-900 dark:bg-gray-700 dark:text-white":"text-gray-700 hover:bg-gray-50 dark:text-gray-200 dark:hover:bg-gray-700"),children:e.label},e.value))})})]}),(0,a.jsxs)("div",{className:"flex rounded-md border border-gray-300 dark:border-gray-600",children:[(0,a.jsx)("button",{onClick:()=>z("grid"),className:"flex items-center justify-center rounded-l-md px-3 py-2 ".concat("grid"===k?"bg-gray-100 text-gray-900 dark:bg-gray-700 dark:text-white":"bg-white text-gray-700 hover:bg-gray-50 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700"),"aria-label":"Grid view",children:(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"h-4 w-4",children:[(0,a.jsx)("rect",{width:"7",height:"7",x:"3",y:"3",rx:"1"}),(0,a.jsx)("rect",{width:"7",height:"7",x:"14",y:"3",rx:"1"}),(0,a.jsx)("rect",{width:"7",height:"7",x:"14",y:"14",rx:"1"}),(0,a.jsx)("rect",{width:"7",height:"7",x:"3",y:"14",rx:"1"})]})}),(0,a.jsx)("button",{onClick:()=>z("list"),className:"flex items-center justify-center rounded-r-md px-3 py-2 ".concat("list"===k?"bg-gray-100 text-gray-900 dark:bg-gray-700 dark:text-white":"bg-white text-gray-700 hover:bg-gray-50 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700"),"aria-label":"List view",children:(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"h-4 w-4",children:[(0,a.jsx)("line",{x1:"8",x2:"21",y1:"6",y2:"6"}),(0,a.jsx)("line",{x1:"8",x2:"21",y1:"12",y2:"12"}),(0,a.jsx)("line",{x1:"8",x2:"21",y1:"18",y2:"18"}),(0,a.jsx)("line",{x1:"3",x2:"3.01",y1:"6",y2:"6"}),(0,a.jsx)("line",{x1:"3",x2:"3.01",y1:"12",y2:"12"}),(0,a.jsx)("line",{x1:"3",x2:"3.01",y1:"18",y2:"18"})]})})]})]})]}),(x.length>0||h)&&(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-2",children:[(0,a.jsx)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Active filters:"}),x.map(e=>(0,a.jsxs)("span",{className:"inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800 dark:bg-blue-900/30 dark:text-blue-400",children:[e,(0,a.jsx)("button",{type:"button",onClick:()=>M(e),className:"ml-1 inline-flex h-4 w-4 items-center justify-center rounded-full text-blue-400 hover:bg-blue-200 hover:text-blue-600 focus:outline-none dark:hover:bg-blue-800",children:(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"h-3 w-3",children:[(0,a.jsx)("path",{d:"M18 6 6 18"}),(0,a.jsx)("path",{d:"m6 6 12 12"})]})})]},e)),h&&(0,a.jsxs)("span",{className:"inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800 dark:bg-green-900/30 dark:text-green-400",children:["New Only",(0,a.jsx)("button",{type:"button",onClick:D,className:"ml-1 inline-flex h-4 w-4 items-center justify-center rounded-full text-green-400 hover:bg-green-200 hover:text-green-600 focus:outline-none dark:hover:bg-green-800",children:(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"h-3 w-3",children:[(0,a.jsx)("path",{d:"M18 6 6 18"}),(0,a.jsx)("path",{d:"m6 6 12 12"})]})})]}),(0,a.jsx)("button",{type:"button",onClick:()=>{u([]),m(!1)},className:"text-xs text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300",children:"Clear all"})]}),(0,a.jsx)(d,{isOpen:N,onClose:()=>C(!1),onApplyFilters:e=>{L(e.tags),F(e.dateRange),E(e.capabilities),g(e)},selectedTags:S,selectedCapabilities:A,selectedDateRange:T})]})}function o(e){let{onSearch:r}=e,[t,l]=(0,s.useState)("");return(0,s.useEffect)(()=>{let e=setTimeout(()=>{r(t)},300);return()=>clearTimeout(e)},[t,r]),(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"h-5 w-5 text-gray-400",children:[(0,a.jsx)("circle",{cx:"11",cy:"11",r:"8"}),(0,a.jsx)("path",{d:"m21 21-4.3-4.3"})]})}),(0,a.jsx)("input",{type:"search",className:"block w-full rounded-lg border border-gray-300 bg-white p-4 pl-10 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500",placeholder:"Search for agents by name, description, or category...",value:t,onChange:e=>l(e.target.value)})]})})}var c=t(9052),g=t(9464),x=t(4217),u=t(6874),h=t.n(u),m=t(6766);function b(){let[e,r]=(0,s.useState)([]),[t,d]=(0,s.useState)(!0);return((0,s.useEffect)(()=>{let e=(0,n.s)();if(0===e.length){d(!1);return}r((0,l.o_)(e)),d(!1)},[]),t)?(0,a.jsxs)("div",{className:"mb-8 rounded-lg border border-gray-200 bg-white p-6 dark:border-gray-800 dark:bg-gray-900",children:[(0,a.jsx)("h2",{className:"mb-4 text-xl font-bold text-gray-900 dark:text-white",children:"Recently Viewed"}),(0,a.jsx)("div",{className:"flex justify-center py-4",children:(0,a.jsx)("div",{className:"h-6 w-6 animate-spin rounded-full border-b-2 border-t-2 border-blue-600"})})]}):0===e.length?null:(0,a.jsxs)("div",{className:"mb-8 rounded-lg border border-gray-200 bg-white p-6 dark:border-gray-800 dark:bg-gray-900",children:[(0,a.jsx)("h2",{className:"mb-4 text-xl font-bold text-gray-900 dark:text-white",children:"Recently Viewed"}),(0,a.jsxs)("div",{className:"space-y-4",children:[e.slice(0,3).map(e=>(0,a.jsxs)(h(),{href:"/agent/".concat(e.id),className:"flex items-center rounded-lg border border-gray-200 p-3 transition-colors hover:bg-gray-50 dark:border-gray-700 dark:hover:bg-gray-800",children:[e.avatarUrl&&(0,a.jsx)("div",{className:"mr-3 h-10 w-10 flex-shrink-0 overflow-hidden rounded-full bg-gray-100 dark:bg-gray-800",children:(0,a.jsx)(m.default,{src:e.avatarUrl,alt:"".concat(e.name," icon"),width:40,height:40,className:"h-full w-full object-cover"})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("h3",{className:"font-medium text-gray-900 dark:text-white",children:e.name}),e.isNew&&(0,a.jsx)("span",{className:"ml-2 rounded-full bg-green-100 px-2 py-0.5 text-xs font-medium text-green-800 dark:bg-green-900/30 dark:text-green-400",children:"New"})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:e.category})]})]},e.id)),e.length>3&&(0,a.jsxs)(h(),{href:"/recent",className:"block text-center text-sm text-blue-600 hover:underline dark:text-blue-400",children:["View all ",e.length," recently viewed agents"]})]})]})}function y(){let e=(0,s.useMemo)(()=>(0,l.mK)(),[]),[r,t]=(0,s.useState)(e),[n,d]=(0,s.useState)([]),[u,h]=(0,s.useState)([]),[m,y]=(0,s.useState)(!1),[k,f]=(0,s.useState)("popular"),[p,w]=(0,s.useState)(""),[v,j]=(0,s.useState)("grid"),[N,C]=(0,s.useState)([]),[S,L]=(0,s.useState)([]),[A,E]=(0,s.useState)(null),[T,F]=(0,s.useState)(1),[B,M]=(0,s.useState)(()=>{try{let e=localStorage.getItem("userPreferences");if(e)return JSON.parse(e).pageSize||12}catch(e){console.error("Error loading page size from preferences:",e)}return 12}),D=(0,s.useMemo)(()=>(0,l.QU)(),[]);return(0,s.useEffect)(()=>{let r=[...e];if(u.length>0&&(r=r.filter(e=>u.includes(e.category))),m&&(r=r.filter(e=>e.isNew)),N.length>0&&(r=r.filter(e=>{var r;return null===(r=e.tags)||void 0===r?void 0:r.some(e=>N.includes(e))})),S.length>0&&(r=r.filter(e=>e.capabilities.some(e=>S.some(r=>e.toLowerCase().includes(r.toLowerCase()))))),A&&A.from&&A.to){let e=new Date(A.from).getTime(),t=new Date(A.to).getTime();r=r.filter(r=>{let a=new Date(r.createdAt).getTime();return a>=e&&a<=t})}if(p){let e=p.toLowerCase();r=r.filter(r=>{var t;return r.name.toLowerCase().includes(e)||r.description.toLowerCase().includes(e)||r.category.toLowerCase().includes(e)||(null===(t=r.tags)||void 0===t?void 0:t.some(r=>r.toLowerCase().includes(e)))||r.capabilities.some(r=>r.toLowerCase().includes(e))})}switch(k){case"popular":r.sort((e,r)=>r.usageCount-e.usageCount);break;case"newest":r.sort((e,r)=>new Date(r.createdAt).getTime()-new Date(e.createdAt).getTime());break;case"alphabetical":r.sort((e,r)=>e.name.localeCompare(r.name))}t(r),F(1)},[e,u,m,k,p,N,S,A]),(0,s.useEffect)(()=>{let e=Math.min(T,Math.max(1,Math.ceil(r.length/B)));if(e!==T){F(e);return}let t=(e-1)*B;d(r.slice(t,t+B))},[r,T,B]),(0,a.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-8 lg:grid-cols-4",children:[(0,a.jsxs)("div",{className:"lg:col-span-3",children:[(0,a.jsx)(o,{onSearch:w}),(0,a.jsx)(i,{categories:D,onCategoryChange:h,onShowNewOnly:y,onSortChange:f,onViewChange:j,onAdvancedFiltersChange:e=>{C(e.tags),E(e.dateRange),L(e.capabilities)}}),r.length>0?"grid"===v?(0,a.jsx)(c.A,{agents:n}):(0,a.jsx)(g.A,{agents:n}):(0,a.jsxs)("div",{className:"flex h-64 flex-col items-center justify-center rounded-lg border border-gray-200 bg-white p-6 text-center dark:border-gray-800 dark:bg-gray-900",children:[(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"mb-4 h-12 w-12 text-gray-400",children:[(0,a.jsx)("circle",{cx:"12",cy:"12",r:"10"}),(0,a.jsx)("line",{x1:"12",x2:"12",y1:"8",y2:"12"}),(0,a.jsx)("line",{x1:"12",x2:"12.01",y1:"16",y2:"16"})]}),(0,a.jsx)("h3",{className:"mb-2 text-lg font-semibold text-gray-900 dark:text-white",children:"No agents found"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Try adjusting your search or filter criteria to find what you're looking for."})]}),r.length>0&&(0,a.jsx)(x.A,{totalItems:r.length,currentPage:T,onPageChange:e=>{F(e),window.scrollTo({top:0,behavior:"smooth"})},onPageSizeChange:e=>{M(e),F(1)}})]}),(0,a.jsx)("div",{className:"lg:col-span-1",children:(0,a.jsx)(b,{})})]})})}}},e=>{var r=r=>e(e.s=r);e.O(0,[874,766,630,739,441,684,358],()=>r(3228)),_N_E=e.O()}]);