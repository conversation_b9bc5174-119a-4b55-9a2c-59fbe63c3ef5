(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[670],{1769:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>b});var a=r(5155),s=r(2115),n=r(5695),d=r(603),l=r(6874),i=r.n(l);function o(e){let{agent:t}=e,[r,n]=(0,s.useState)(!1);return(0,a.jsxs)("div",{className:"mb-8 border-b border-gray-200 pb-6 dark:border-gray-800",children:[(0,a.jsxs)("div",{className:"mb-4 flex items-center justify-between",children:[(0,a.jsxs)(i(),{href:"/",className:"flex items-center text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"mr-2 h-4 w-4",children:(0,a.jsx)("path",{d:"m15 18-6-6 6-6"})}),"Back to Home"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>n(!r),className:"rounded-full p-2 text-gray-500 hover:bg-gray-100 hover:text-gray-700 dark:text-gray-400 dark:hover:bg-gray-800 dark:hover:text-gray-300","aria-label":r?"Remove from favorites":"Add to favorites",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:r?"currentColor":"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"h-5 w-5",children:(0,a.jsx)("path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z"})})}),(0,a.jsx)("button",{className:"rounded-full p-2 text-gray-500 hover:bg-gray-100 hover:text-gray-700 dark:text-gray-400 dark:hover:bg-gray-800 dark:hover:text-gray-300","aria-label":"Share agent",children:(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"h-5 w-5",children:[(0,a.jsx)("path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8"}),(0,a.jsx)("polyline",{points:"16 6 12 2 8 6"}),(0,a.jsx)("line",{x1:"12",x2:"12",y1:"2",y2:"15"})]})})]})]}),(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white md:text-3xl",children:t.name}),t.isNew&&(0,a.jsx)("span",{className:"ml-3 rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800 dark:bg-green-900/30 dark:text-green-400",children:"New"})]}),(0,a.jsxs)("div",{className:"mt-2 flex items-center text-sm text-gray-500 dark:text-gray-400",children:[(0,a.jsx)("span",{className:"mr-2 rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800 dark:bg-gray-800 dark:text-gray-200",children:t.category}),(0,a.jsx)("span",{className:"mr-2",children:"•"}),(0,a.jsxs)("span",{children:["Version ",t.version]}),(0,a.jsx)("span",{className:"mx-2",children:"•"}),(0,a.jsxs)("span",{children:["Updated ",new Date(t.updatedAt).toLocaleDateString()]}),(0,a.jsx)("span",{className:"mx-2",children:"•"}),(0,a.jsxs)("span",{children:[t.usageCount.toLocaleString()," uses"]})]})]}),(0,a.jsx)("button",{className:"rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800",children:"New Chat"})]})]})}function c(e){let{agent:t}=e;return(0,a.jsxs)("div",{className:"mb-8 grid grid-cols-1 gap-6 lg:grid-cols-3",children:[(0,a.jsx)("div",{className:"lg:col-span-2",children:(0,a.jsxs)("div",{className:"rounded-lg border border-gray-200 bg-white p-6 dark:border-gray-800 dark:bg-gray-900",children:[(0,a.jsx)("h2",{className:"mb-4 text-xl font-bold text-gray-900 dark:text-white",children:"About"}),(0,a.jsx)("p",{className:"mb-4 text-gray-700 dark:text-gray-300",children:t.longDescription||t.description}),(0,a.jsx)("h3",{className:"mb-2 mt-6 text-lg font-semibold text-gray-900 dark:text-white",children:"Capabilities"}),(0,a.jsx)("ul",{className:"list-inside list-disc space-y-1 text-gray-700 dark:text-gray-300",children:t.capabilities.map((e,t)=>(0,a.jsx)("li",{children:e},t))})]})}),(0,a.jsx)("div",{children:(0,a.jsxs)("div",{className:"rounded-lg border border-gray-200 bg-white p-6 dark:border-gray-800 dark:bg-gray-900",children:[(0,a.jsx)("h2",{className:"mb-4 text-xl font-bold text-gray-900 dark:text-white",children:"Details"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Created by"}),(0,a.jsx)("p",{className:"text-gray-900 dark:text-white",children:t.creator||"Internal Team"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Created on"}),(0,a.jsx)("p",{className:"text-gray-900 dark:text-white",children:new Date(t.createdAt).toLocaleDateString()})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Last updated"}),(0,a.jsx)("p",{className:"text-gray-900 dark:text-white",children:new Date(t.updatedAt).toLocaleDateString()})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Version"}),(0,a.jsx)("p",{className:"text-gray-900 dark:text-white",children:t.version})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Category"}),(0,a.jsx)("p",{className:"text-gray-900 dark:text-white",children:t.category})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Usage"}),(0,a.jsxs)("p",{className:"text-gray-900 dark:text-white",children:[t.usageCount.toLocaleString()," times"]})]})]})]})})]})}function x(e){let{agentName:t,initialMessages:r=[]}=e,[n,l]=(0,s.useState)(r.length>0?r:[(0,d.oH)("Hello! I'm ".concat(t,". How can I help you today?"),"assistant")]),[i,o]=(0,s.useState)(""),[c,x]=(0,s.useState)(!1),g=(0,s.useRef)(null),h=()=>{var e;null===(e=g.current)||void 0===e||e.scrollIntoView({behavior:"smooth"})};(0,s.useEffect)(()=>{h()},[n]);let m=async e=>{if(e.preventDefault(),!i.trim())return;let r=(0,d.oH)(i,"user");l(e=>[...e,r]),o(""),x(!0),setTimeout(()=>{let e=(0,d.oH)("This is a simulated response from ".concat(t,'. In a real application, this would be generated by the AI agent based on your message: "').concat(i,'"'),"assistant");l(t=>[...t,e]),x(!1)},1500)};return(0,a.jsxs)("div",{className:"flex h-[600px] flex-col rounded-lg border border-gray-200 bg-white dark:border-gray-800 dark:bg-gray-900",children:[(0,a.jsx)("div",{className:"border-b border-gray-200 p-4 dark:border-gray-800",children:(0,a.jsxs)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:["Chat with ",t]})}),(0,a.jsx)("div",{className:"flex-1 overflow-y-auto p-4",children:(0,a.jsxs)("div",{className:"space-y-4",children:[n.map(e=>(0,a.jsx)("div",{className:"flex ".concat("user"===e.role?"justify-end":"justify-start"),children:(0,a.jsxs)("div",{className:"max-w-[80%] rounded-lg px-4 py-2 ".concat("user"===e.role?"bg-blue-600 text-white":"bg-gray-100 text-gray-900 dark:bg-gray-800 dark:text-white"),children:[(0,a.jsx)("p",{className:"whitespace-pre-wrap",children:e.content}),(0,a.jsx)("div",{className:"mt-1 text-xs ".concat("user"===e.role?"text-blue-200":"text-gray-500 dark:text-gray-400"),children:new Date(e.timestamp).toLocaleTimeString()})]})},e.id)),c&&(0,a.jsx)("div",{className:"flex justify-start",children:(0,a.jsx)("div",{className:"max-w-[80%] rounded-lg bg-gray-100 px-4 py-2 dark:bg-gray-800",children:(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("div",{className:"h-2 w-2 animate-bounce rounded-full bg-gray-500 dark:bg-gray-400"}),(0,a.jsx)("div",{className:"h-2 w-2 animate-bounce rounded-full bg-gray-500 dark:bg-gray-400",style:{animationDelay:"0.2s"}}),(0,a.jsx)("div",{className:"h-2 w-2 animate-bounce rounded-full bg-gray-500 dark:bg-gray-400",style:{animationDelay:"0.4s"}})]})})}),(0,a.jsx)("div",{ref:g})]})}),(0,a.jsx)("div",{className:"border-t border-gray-200 p-4 dark:border-gray-800",children:(0,a.jsxs)("form",{onSubmit:m,className:"flex space-x-2",children:[(0,a.jsx)("input",{type:"text",value:i,onChange:e=>o(e.target.value),placeholder:"Type your message...",className:"flex-1 rounded-md border border-gray-300 px-4 py-2 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white dark:focus:border-blue-600 dark:focus:ring-blue-600",disabled:c}),(0,a.jsx)("button",{type:"submit",disabled:c||!i.trim(),className:"rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700 disabled:cursor-not-allowed disabled:opacity-50 dark:bg-blue-700 dark:hover:bg-blue-800",children:"Send"})]})})]})}function g(e){let{sessions:t,onSelectSession:r}=e,[n,d]=(0,s.useState)(!0),l=e=>{let t=new Date(e),r=new Date,a=new Date(r);return(a.setDate(a.getDate()-1),t.toDateString()===r.toDateString())?"Today at ".concat(t.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})):t.toDateString()===a.toDateString()?"Yesterday at ".concat(t.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})):t.toLocaleDateString([],{month:"short",day:"numeric"})+" at ".concat(t.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}))};return 0===t.length?(0,a.jsxs)("div",{className:"mb-6 rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Session History"}),(0,a.jsx)("button",{onClick:()=>d(!n),className:"rounded p-1 text-gray-500 hover:bg-gray-100 hover:text-gray-700 dark:text-gray-400 dark:hover:bg-gray-800 dark:hover:text-gray-300",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"h-5 w-5 transition-transform ".concat(n?"rotate-180":""),children:(0,a.jsx)("path",{d:"m18 15-6-6-6 6"})})})]}),n&&(0,a.jsxs)("div",{className:"mt-4 text-center text-gray-500 dark:text-gray-400",children:[(0,a.jsx)("p",{children:"No previous sessions found."}),(0,a.jsx)("p",{className:"mt-2 text-sm",children:"Start a new chat to begin."})]})]}):(0,a.jsxs)("div",{className:"mb-6 rounded-lg border border-gray-200 bg-white dark:border-gray-800 dark:bg-gray-900",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between border-b border-gray-200 p-4 dark:border-gray-800",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Session History"}),(0,a.jsx)("button",{onClick:()=>d(!n),className:"rounded p-1 text-gray-500 hover:bg-gray-100 hover:text-gray-700 dark:text-gray-400 dark:hover:bg-gray-800 dark:hover:text-gray-300",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"h-5 w-5 transition-transform ".concat(n?"rotate-180":""),children:(0,a.jsx)("path",{d:"m18 15-6-6-6 6"})})})]}),n&&(0,a.jsx)("div",{className:"max-h-80 overflow-y-auto p-4",children:(0,a.jsx)("div",{className:"space-y-2",children:t.map(e=>{var t;return(0,a.jsxs)("button",{onClick:()=>r(e),className:"w-full rounded-md border border-gray-200 bg-white p-3 text-left hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:hover:bg-gray-700",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h3",{className:"font-medium text-gray-900 dark:text-white",children:e.title}),e.isSaved&&(0,a.jsx)("span",{className:"rounded-full bg-blue-100 px-2 py-0.5 text-xs font-medium text-blue-800 dark:bg-blue-900/30 dark:text-blue-400",children:"Saved"})]}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:l(e.updatedAt)}),(0,a.jsx)("p",{className:"mt-2 line-clamp-2 text-sm text-gray-600 dark:text-gray-300",children:(null===(t=e.messages[e.messages.length-1])||void 0===t?void 0:t.content)||"No messages"})]},e.id)})})})]})}function h(e){let{onSaveSession:t,onClearChat:r,onExportChat:n}=e,[d,l]=(0,s.useState)(!1);return(0,a.jsxs)("div",{className:"mb-6 flex items-center justify-between rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Current Session"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("button",{onClick:()=>l(!d),className:"rounded-md border border-gray-300 bg-white px-3 py-1.5 text-sm font-medium text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700",children:["Actions",(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"ml-1 inline-block h-4 w-4",children:(0,a.jsx)("path",{d:"m6 9 6 6 6-6"})})]}),d&&(0,a.jsx)("div",{className:"absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-gray-800 dark:ring-gray-700",children:(0,a.jsxs)("div",{className:"py-1",children:[(0,a.jsxs)("button",{onClick:()=>{t(),l(!1)},className:"block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700",children:[(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"mr-2 inline-block h-4 w-4",children:[(0,a.jsx)("path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"}),(0,a.jsx)("polyline",{points:"17 21 17 13 7 13 7 21"}),(0,a.jsx)("polyline",{points:"7 3 7 8 15 8"})]}),"Save Session"]}),(0,a.jsxs)("button",{onClick:()=>{n(),l(!1)},className:"block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700",children:[(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"mr-2 inline-block h-4 w-4",children:[(0,a.jsx)("path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"}),(0,a.jsx)("polyline",{points:"7 10 12 15 17 10"}),(0,a.jsx)("line",{x1:"12",x2:"12",y1:"15",y2:"3"})]}),"Export Chat"]}),(0,a.jsxs)("button",{onClick:()=>{r(),l(!1)},className:"block w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-gray-100 dark:text-red-400 dark:hover:bg-gray-700",children:[(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"mr-2 inline-block h-4 w-4",children:[(0,a.jsx)("path",{d:"M3 6h18"}),(0,a.jsx)("path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"}),(0,a.jsx)("path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"}),(0,a.jsx)("line",{x1:"10",x2:"10",y1:"11",y2:"17"}),(0,a.jsx)("line",{x1:"14",x2:"14",y1:"11",y2:"17"})]}),"Clear Chat"]})]})})]})]})}var m=r(6766),u=r(2340);function y(e){let{agentId:t}=e,[r,n]=(0,s.useState)([]),[l,o]=(0,s.useState)(!0);(0,s.useEffect)(()=>{n((0,d.y6)(t)),o(!1)},[t]);let c=e=>{(0,u.rN)(e)};return l?(0,a.jsxs)("div",{className:"rounded-lg border border-gray-200 bg-white p-6 dark:border-gray-800 dark:bg-gray-900",children:[(0,a.jsx)("h2",{className:"mb-4 text-xl font-bold text-gray-900 dark:text-white",children:"Related Agents"}),(0,a.jsx)("div",{className:"flex justify-center py-4",children:(0,a.jsx)("div",{className:"h-6 w-6 animate-spin rounded-full border-b-2 border-t-2 border-blue-600"})})]}):0===r.length?null:(0,a.jsxs)("div",{className:"rounded-lg border border-gray-200 bg-white p-6 dark:border-gray-800 dark:bg-gray-900",children:[(0,a.jsx)("h2",{className:"mb-4 text-xl font-bold text-gray-900 dark:text-white",children:"Related Agents"}),(0,a.jsx)("div",{className:"space-y-4",children:r.map(e=>(0,a.jsxs)(i(),{href:"/agent/".concat(e.id),className:"flex items-center rounded-lg border border-gray-200 p-3 transition-colors hover:bg-gray-50 dark:border-gray-700 dark:hover:bg-gray-800",onClick:()=>c(e.id),children:[e.avatarUrl&&(0,a.jsx)("div",{className:"mr-3 h-10 w-10 flex-shrink-0 overflow-hidden rounded-full bg-gray-100 dark:bg-gray-800",children:(0,a.jsx)(m.default,{src:e.avatarUrl,alt:"".concat(e.name," icon"),width:40,height:40,className:"h-full w-full object-cover"})}),(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-gray-900 dark:text-white",children:e.name}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:e.category})]}),e.isNew&&(0,a.jsx)("span",{className:"rounded-full bg-green-100 px-2 py-0.5 text-xs font-medium text-green-800 dark:bg-green-900/30 dark:text-green-400",children:"New"})]})})]},e.id))})]})}function b(){let e=(0,n.useParams)().id,[t,r]=(0,s.useState)((0,d.GM)(e)),[l,i]=(0,s.useState)((0,d.q$)(e)),[m,b]=(0,s.useState)(null),[j,v]=(0,s.useState)([]);return((0,s.useEffect)(()=>{m||v([(0,d.oH)("Hello! I'm ".concat(null==t?void 0:t.name,". How can I help you today?"),"assistant")]),t&&(0,u.rN)(e)},[t,m,e]),t)?(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,a.jsx)(o,{agent:t}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-8 lg:grid-cols-3",children:[(0,a.jsxs)("div",{className:"lg:col-span-2",children:[(0,a.jsx)(h,{onSaveSession:()=>{if(j.length<=1){alert("Cannot save an empty session. Please start a conversation first.");return}let t=j.filter(e=>"user"===e.role);if(0===t.length){alert("Cannot save a session without any user messages.");return}let r=t[0].content.substring(0,30)+(t[0].content.length>30?"...":""),a={id:"session_".concat(Date.now()),agentId:e,title:r,messages:[...j],createdAt:j[0].timestamp,updatedAt:Date.now(),isSaved:!0};i(e=>[a,...e]),b(a),alert("Session saved successfully!")},onClearChat:()=>{confirm("Are you sure you want to clear the current chat? This cannot be undone.")&&(v([(0,d.oH)("Hello! I'm ".concat(null==t?void 0:t.name,". How can I help you today?"),"assistant")]),b(null))},onExportChat:()=>{if(j.length<=1){alert("There is nothing to export. Please start a conversation first.");return}let e=new Blob([j.map(e=>{let r="assistant"===e.role?(null==t?void 0:t.name)||"Assistant":"You",a=new Date(e.timestamp).toLocaleString();return"".concat(r," (").concat(a,"):\n").concat(e.content,"\n")}).join("\n")],{type:"text/plain"}),r=URL.createObjectURL(e),a=document.createElement("a");a.href=r,a.download="Chat with ".concat(null==t?void 0:t.name," - ").concat(new Date().toLocaleDateString(),".txt"),document.body.appendChild(a),a.click(),document.body.removeChild(a),URL.revokeObjectURL(r)}}),(0,a.jsx)(x,{agentName:t.name,initialMessages:j})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(g,{sessions:l,onSelectSession:e=>{b(e),v(e.messages)}}),(0,a.jsx)(c,{agent:t}),(0,a.jsx)("div",{className:"mt-6",children:(0,a.jsx)(y,{agentId:e})})]})]})]}):(0,a.jsx)("div",{className:"flex min-h-screen items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h1",{className:"mb-4 text-2xl font-bold text-gray-900 dark:text-white",children:"Agent Not Found"}),(0,a.jsx)("p",{className:"mb-6 text-gray-600 dark:text-gray-400",children:"The agent you're looking for doesn't exist or has been removed."}),(0,a.jsx)("a",{href:"/",className:"rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800",children:"Return to Home"})]})})}},5695:(e,t,r)=>{"use strict";var a=r(8999);r.o(a,"useParams")&&r.d(t,{useParams:function(){return a.useParams}}),r.o(a,"usePathname")&&r.d(t,{usePathname:function(){return a.usePathname}})},6069:(e,t,r)=>{Promise.resolve().then(r.bind(r,1769))}},e=>{var t=t=>e(e.s=t);e.O(0,[874,766,630,441,684,358],()=>t(6069)),_N_E=e.O()}]);