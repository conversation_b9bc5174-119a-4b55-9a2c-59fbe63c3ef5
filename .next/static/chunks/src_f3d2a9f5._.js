(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/src_f3d2a9f5._.js", {

"[project]/src/data/resources.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "getCategoryById": (()=>getCategoryById),
    "getResourcesByCategory": (()=>getResourcesByCategory),
    "resourceCategories": (()=>resourceCategories),
    "resources": (()=>resources)
});
const resourceCategories = [
    {
        id: 'productivity',
        name: 'Productivity Tools',
        description: 'Tools to enhance personal and team productivity, including task management, note-taking, and time tracking.',
        icon: 'bolt'
    },
    {
        id: 'project-management',
        name: 'Project Management',
        description: 'Software and platforms for planning, executing, and monitoring projects of all sizes.',
        icon: 'tasks'
    },
    {
        id: 'design',
        name: 'Design Tools',
        description: 'Resources for UI/UX design, graphic design, prototyping, and visual collaboration.',
        icon: 'palette'
    },
    {
        id: 'development',
        name: 'Development Tools',
        description: 'Tools and services for software development, coding, testing, and deployment.',
        icon: 'code'
    },
    {
        id: 'research',
        name: 'Research Tools',
        description: 'Resources for market research, user research, academic research, and data collection.',
        icon: 'magnifying-glass-chart'
    },
    {
        id: 'analytics',
        name: 'Analytics & Data',
        description: 'Tools for data analysis, visualization, business intelligence, and reporting.',
        icon: 'chart-line'
    },
    {
        id: 'communication',
        name: 'Communication',
        description: 'Platforms for team communication, client meetings, presentations, and email management.',
        icon: 'comments'
    },
    {
        id: 'collaboration',
        name: 'Collaboration',
        description: 'Tools that facilitate teamwork, knowledge sharing, and cross-functional collaboration.',
        icon: 'users-gear'
    }
];
const resources = [
    // Productivity Tools
    {
        id: 'notion-ai',
        name: 'Notion AI',
        description: 'AI-powered writing assistant integrated into Notion that helps draft, edit, summarize, and brainstorm content directly in your workspace.',
        url: 'https://www.notion.so/product/ai',
        category: 'productivity',
        tags: [
            'writing-assistant',
            'content-generation',
            'summarization'
        ],
        pricing: 'paid',
        logoUrl: 'https://upload.wikimedia.org/wikipedia/commons/4/45/Notion_app_logo.png'
    },
    {
        id: 'mem',
        name: 'Mem',
        description: 'AI-powered workspace that automatically organizes your notes and knowledge with powerful search and retrieval capabilities.',
        url: 'https://mem.ai',
        category: 'productivity',
        tags: [
            'note-taking',
            'knowledge-management',
            'ai-organization'
        ],
        pricing: 'freemium',
        logoUrl: 'https://mem.ai/assets/favicons/favicon.svg'
    },
    {
        id: 'otter',
        name: 'Otter.ai',
        description: 'AI meeting assistant that records, transcribes, and summarizes meetings in real-time with speaker identification.',
        url: 'https://otter.ai',
        category: 'productivity',
        tags: [
            'transcription',
            'meeting-assistant',
            'note-taking'
        ],
        pricing: 'freemium',
        logoUrl: 'https://assets-global.website-files.com/618e9316785b3582a5178502/6230b90e3dceec1c2208f309_favicon-256x256.png'
    },
    {
        id: 'reclaim',
        name: 'Reclaim.ai',
        description: 'AI calendar assistant that automatically schedules your tasks, habits, and meetings to optimize your time and protect your calendar.',
        url: 'https://reclaim.ai',
        category: 'productivity',
        tags: [
            'calendar-management',
            'time-blocking',
            'scheduling'
        ],
        pricing: 'freemium',
        logoUrl: 'https://reclaim.ai/favicon/favicon-32x32.png'
    },
    {
        id: 'timetask',
        name: 'TimeTask AI',
        description: 'AI-powered time tracking tool that automatically categorizes your work and provides insights to improve productivity.',
        url: 'https://timetask.ai',
        category: 'productivity',
        tags: [
            'time-tracking',
            'productivity-analytics',
            'work-insights'
        ],
        pricing: 'freemium',
        logoUrl: 'https://timetask.ai/favicon.ico'
    },
    // Project Management
    {
        id: 'asana-ai',
        name: 'Asana AI',
        description: 'AI assistant integrated into Asana that helps teams plan projects, write task descriptions, summarize discussions, and automate workflows.',
        url: 'https://asana.com/ai',
        category: 'project-management',
        tags: [
            'task-management',
            'ai-assistant',
            'workflow-automation'
        ],
        pricing: 'paid',
        logoUrl: 'https://upload.wikimedia.org/wikipedia/commons/3/3b/Asana_logo.svg'
    },
    {
        id: 'clickup-ai',
        name: 'ClickUp AI',
        description: 'AI-powered project management assistant that writes, edits, summarizes, and generates content directly within your project management workflow.',
        url: 'https://clickup.com/ai',
        category: 'project-management',
        tags: [
            'content-generation',
            'task-automation',
            'summarization'
        ],
        pricing: 'paid',
        logoUrl: 'https://clickup.com/landing/images/clickup-logo.svg'
    },
    {
        id: 'motion',
        name: 'Motion',
        description: 'AI-powered project management tool that automatically schedules and prioritizes tasks based on deadlines, priorities, and team capacity.',
        url: 'https://www.usemotion.com',
        category: 'project-management',
        tags: [
            'ai-scheduling',
            'task-prioritization',
            'time-management'
        ],
        pricing: 'paid',
        logoUrl: 'https://www.usemotion.com/favicon.png'
    },
    {
        id: 'forecast',
        name: 'Forecast',
        description: 'AI-native platform for project and resource management that predicts project delivery dates, resource needs, and budget requirements.',
        url: 'https://www.forecast.app',
        category: 'project-management',
        tags: [
            'resource-planning',
            'project-forecasting',
            'budget-management'
        ],
        pricing: 'paid',
        logoUrl: 'https://www.forecast.app/hubfs/favicon-1.png'
    },
    {
        id: 'teamwork-ai',
        name: 'Teamwork AI',
        description: 'AI-powered project management platform that automates routine tasks, provides insights, and helps teams deliver projects more efficiently.',
        url: 'https://www.teamwork.com/ai-project-management-software',
        category: 'project-management',
        tags: [
            'task-automation',
            'project-insights',
            'team-collaboration'
        ],
        pricing: 'paid',
        logoUrl: 'https://www.teamwork.com/app/themes/teamwork-theme/dist/images/favicon/apple-touch-icon.png'
    },
    // Design Tools
    {
        id: 'midjourney',
        name: 'Midjourney',
        description: 'AI image generation tool that creates stunning visuals from text descriptions, ideal for concept art, illustrations, and design inspiration.',
        url: 'https://www.midjourney.com',
        category: 'design',
        tags: [
            'ai-image-generation',
            'concept-art',
            'visual-design'
        ],
        pricing: 'paid',
        logoUrl: 'https://upload.wikimedia.org/wikipedia/commons/e/e6/Midjourney_Emblem.png'
    },
    {
        id: 'figma-ai',
        name: 'Figma AI',
        description: 'AI-powered design features in Figma that help generate and edit designs, create variations, and improve design workflows.',
        url: 'https://www.figma.com/ai',
        category: 'design',
        tags: [
            'ui-design',
            'design-generation',
            'workflow-automation'
        ],
        pricing: 'paid',
        logoUrl: 'https://upload.wikimedia.org/wikipedia/commons/3/33/Figma-logo.svg'
    },
    {
        id: 'gamma',
        name: 'Gamma',
        description: 'AI-powered presentation platform that transforms simple text prompts into beautiful, presentation-ready slides and documents.',
        url: 'https://gamma.app',
        category: 'design',
        tags: [
            'presentations',
            'content-generation',
            'slide-design'
        ],
        pricing: 'freemium',
        logoUrl: 'https://assets-global.website-files.com/6127a84dfe068e153ef20572/62b1399ae4703b5c4f195f80_Favicon.png'
    },
    {
        id: 'canva-ai',
        name: 'Canva AI',
        description: 'Suite of AI tools in Canva that help generate designs, text, images, and edit photos to create professional-looking content quickly.',
        url: 'https://www.canva.com/ai-image-generator/',
        category: 'design',
        tags: [
            'graphic-design',
            'image-generation',
            'text-to-image'
        ],
        pricing: 'freemium',
        logoUrl: 'https://upload.wikimedia.org/wikipedia/commons/0/08/Canva_icon_2021.svg'
    },
    {
        id: 'runway',
        name: 'Runway',
        description: 'AI-powered creative suite for generating and editing videos, images, and 3D content with text prompts and intuitive controls.',
        url: 'https://runwayml.com',
        category: 'design',
        tags: [
            'video-generation',
            'image-editing',
            'creative-tools'
        ],
        pricing: 'freemium',
        logoUrl: 'https://runwayml.com/favicon.svg'
    },
    // Development Tools
    {
        id: 'github-copilot',
        name: 'GitHub Copilot',
        description: 'AI pair programmer that suggests code completions and entire functions in real-time, directly in your editor.',
        url: 'https://github.com/features/copilot',
        category: 'development',
        tags: [
            'code-completion',
            'pair-programming',
            'ai-coding'
        ],
        pricing: 'paid',
        logoUrl: 'https://github.githubassets.com/assets/GitHub-Mark-ea2971cee799.png'
    },
    {
        id: 'augment-agent',
        name: 'Augment Agent',
        description: 'AI coding assistant that helps developers understand, modify, and debug code with natural language, providing context-aware suggestions.',
        url: 'https://www.augmentcode.com/',
        category: 'development',
        tags: [
            'code-understanding',
            'debugging',
            'ai-assistant'
        ],
        pricing: 'paid',
        logoUrl: 'https://www.augmentcode.com/favicon.ico'
    },
    {
        id: 'tabnine',
        name: 'Tabnine',
        description: 'AI code completion tool that predicts and suggests code based on context and patterns, supporting multiple programming languages and IDEs.',
        url: 'https://www.tabnine.com',
        category: 'development',
        tags: [
            'code-completion',
            'ai-assistant',
            'multi-language'
        ],
        pricing: 'freemium',
        logoUrl: 'https://www.tabnine.com/favicon.ico'
    },
    {
        id: 'codeium',
        name: 'Codeium',
        description: 'Free AI-powered code completion and chat tool that helps developers write code faster with context-aware suggestions.',
        url: 'https://codeium.com',
        category: 'development',
        tags: [
            'code-completion',
            'code-chat',
            'free'
        ],
        pricing: 'freemium',
        logoUrl: 'https://codeium.com/favicon.ico'
    },
    {
        id: 'warp',
        name: 'Warp',
        description: 'AI-powered terminal that makes the command line more productive with features like AI command search, suggestions, and explanations.',
        url: 'https://www.warp.dev',
        category: 'development',
        tags: [
            'terminal',
            'command-line',
            'productivity'
        ],
        pricing: 'freemium',
        logoUrl: 'https://assets.warp.dev/warp_logo.png'
    },
    // Research Tools
    {
        id: 'elicit',
        name: 'Elicit',
        description: 'AI research assistant that helps you find, summarize, and analyze academic papers, extracting key insights and generating literature reviews.',
        url: 'https://elicit.org',
        category: 'research',
        tags: [
            'academic-research',
            'paper-summaries',
            'literature-review'
        ],
        pricing: 'freemium',
        logoUrl: 'https://elicit.org/favicon.ico'
    },
    {
        id: 'consensus',
        name: 'Consensus',
        description: 'AI-powered search engine for scientific research that provides summaries of papers and highlights key findings with citations.',
        url: 'https://consensus.app',
        category: 'research',
        tags: [
            'scientific-search',
            'paper-summaries',
            'evidence-based'
        ],
        pricing: 'freemium',
        logoUrl: 'https://consensus.app/favicon.ico'
    },
    {
        id: 'perplexity',
        name: 'Perplexity AI',
        description: 'AI-powered answer engine that provides comprehensive, cited responses to complex research questions with sources and references.',
        url: 'https://www.perplexity.ai',
        category: 'research',
        tags: [
            'answer-engine',
            'information-search',
            'cited-responses'
        ],
        pricing: 'freemium',
        logoUrl: 'https://www.perplexity.ai/favicon.ico'
    },
    {
        id: 'scholarai',
        name: 'ScholarAI',
        description: 'AI research assistant that helps academics search, summarize, and analyze scientific papers, generating insights and connections.',
        url: 'https://scholarai.io',
        category: 'research',
        tags: [
            'academic-research',
            'paper-analysis',
            'literature-review'
        ],
        pricing: 'freemium',
        logoUrl: 'https://scholarai.io/favicon.ico'
    },
    {
        id: 'researchrabbit',
        name: 'ResearchRabbit',
        description: 'AI-powered literature discovery tool that helps researchers find relevant papers and visualize connections between publications.',
        url: 'https://www.researchrabbit.ai',
        category: 'research',
        tags: [
            'literature-discovery',
            'citation-network',
            'research-mapping'
        ],
        pricing: 'freemium',
        logoUrl: 'https://www.researchrabbit.ai/favicon.png'
    },
    // Analytics & Data
    {
        id: 'obviously-ai',
        name: 'Obviously AI',
        description: 'No-code AI platform that allows anyone to build and deploy machine learning models for prediction and analysis without coding.',
        url: 'https://www.obviously.ai',
        category: 'analytics',
        tags: [
            'no-code-ai',
            'predictive-analytics',
            'machine-learning'
        ],
        pricing: 'paid',
        logoUrl: 'https://www.obviously.ai/favicon.ico'
    },
    {
        id: 'akkio',
        name: 'Akkio',
        description: 'AI platform that makes it easy for businesses to build, deploy, and use predictive models without data science expertise.',
        url: 'https://www.akkio.com',
        category: 'analytics',
        tags: [
            'predictive-analytics',
            'no-code',
            'business-intelligence'
        ],
        pricing: 'paid',
        logoUrl: 'https://www.akkio.com/favicon.ico'
    },
    {
        id: 'deepnote',
        name: 'Deepnote',
        description: 'AI-enhanced collaborative data notebook that combines the best of notebooks, spreadsheets, and visual interfaces for data analysis.',
        url: 'https://deepnote.com',
        category: 'analytics',
        tags: [
            'data-science',
            'notebooks',
            'collaboration'
        ],
        pricing: 'freemium',
        logoUrl: 'https://deepnote.com/favicon.ico'
    },
    {
        id: 'hex',
        name: 'Hex',
        description: 'Collaborative data platform with AI capabilities that helps teams explore, analyze, and share data insights through notebooks and apps.',
        url: 'https://hex.tech',
        category: 'analytics',
        tags: [
            'data-science',
            'collaboration',
            'data-apps'
        ],
        pricing: 'freemium',
        logoUrl: 'https://hex.tech/favicon.ico'
    },
    {
        id: 'einblick',
        name: 'Einblick',
        description: 'AI-powered data science platform that accelerates analysis with collaborative canvas, automated insights, and predictive capabilities.',
        url: 'https://www.einblick.ai',
        category: 'analytics',
        tags: [
            'data-science',
            'visual-analytics',
            'collaboration'
        ],
        pricing: 'freemium',
        logoUrl: 'https://www.einblick.ai/favicon.ico'
    },
    // Communication
    {
        id: 'chatgpt',
        name: 'ChatGPT',
        description: 'AI assistant that can draft emails, summarize conversations, generate content, and help with various communication tasks.',
        url: 'https://chat.openai.com',
        category: 'communication',
        tags: [
            'ai-assistant',
            'content-generation',
            'writing-help'
        ],
        pricing: 'freemium',
        logoUrl: 'https://chat.openai.com/apple-touch-icon.png'
    },
    {
        id: 'fireflies',
        name: 'Fireflies.ai',
        description: 'AI meeting assistant that records, transcribes, and analyzes voice conversations, creating searchable notes and action items.',
        url: 'https://fireflies.ai',
        category: 'communication',
        tags: [
            'meeting-assistant',
            'transcription',
            'meeting-insights'
        ],
        pricing: 'freemium',
        logoUrl: 'https://fireflies.ai/favicon.ico'
    },
    {
        id: 'grammarly',
        name: 'Grammarly',
        description: 'AI writing assistant that helps improve grammar, clarity, engagement, and delivery in all types of written communication.',
        url: 'https://www.grammarly.com',
        category: 'communication',
        tags: [
            'writing-assistant',
            'grammar-checker',
            'communication-improvement'
        ],
        pricing: 'freemium',
        logoUrl: 'https://static.grammarly.com/assets/files/efe57d016d9efff36da7884c193b646b/favicon-32x32.png'
    },
    {
        id: 'summary-ai',
        name: 'Summary AI',
        description: 'AI tool that automatically summarizes meetings, articles, and documents to extract key points and action items.',
        url: 'https://www.summary.ai',
        category: 'communication',
        tags: [
            'summarization',
            'meeting-notes',
            'information-extraction'
        ],
        pricing: 'freemium',
        logoUrl: 'https://www.summary.ai/favicon.ico'
    },
    {
        id: 'descript',
        name: 'Descript',
        description: 'All-in-one audio and video editing platform with AI features like transcription, content editing, and voice generation.',
        url: 'https://www.descript.com',
        category: 'communication',
        tags: [
            'video-editing',
            'transcription',
            'content-creation'
        ],
        pricing: 'freemium',
        logoUrl: 'https://www.descript.com/favicon.ico'
    },
    // Collaboration
    {
        id: 'coda-ai',
        name: 'Coda AI',
        description: 'AI-powered collaborative document platform that combines docs, spreadsheets, and apps with AI assistance for content generation and analysis.',
        url: 'https://coda.io/product/ai-alpha',
        category: 'collaboration',
        tags: [
            'documents',
            'ai-writing',
            'team-workspace'
        ],
        pricing: 'freemium',
        logoUrl: 'https://cdn.coda.io/icons/png/color/coda-192.png'
    },
    {
        id: 'tome',
        name: 'Tome',
        description: 'AI-powered storytelling format that helps teams create beautiful, interactive presentations and documents with generative AI.',
        url: 'https://tome.app',
        category: 'collaboration',
        tags: [
            'presentations',
            'storytelling',
            'content-generation'
        ],
        pricing: 'freemium',
        logoUrl: 'https://tome.app/favicon.ico'
    },
    {
        id: 'mural-ai',
        name: 'MURAL AI',
        description: 'AI-enhanced digital workspace for visual collaboration that helps teams brainstorm, plan, and solve problems together.',
        url: 'https://www.mural.co/ai-features',
        category: 'collaboration',
        tags: [
            'visual-collaboration',
            'brainstorming',
            'workshops'
        ],
        pricing: 'paid',
        logoUrl: 'https://assets-global.website-files.com/62e11362da2667ac3d0e6ed5/62e3a2c2d605d15a6bffc4a2_favicon-32.png'
    },
    {
        id: 'notion-ai-collab',
        name: 'Notion AI for Teams',
        description: 'AI-powered collaborative workspace that helps teams create, organize, and share knowledge with AI assistance for content and insights.',
        url: 'https://www.notion.so/product/ai',
        category: 'collaboration',
        tags: [
            'team-workspace',
            'knowledge-management',
            'ai-writing'
        ],
        pricing: 'paid',
        logoUrl: 'https://upload.wikimedia.org/wikipedia/commons/4/45/Notion_app_logo.png'
    },
    {
        id: 'craft-ai',
        name: 'Craft AI',
        description: 'AI-powered document editor and knowledge base that helps teams create beautiful documents and organize information.',
        url: 'https://www.craft.do/features/ai',
        category: 'collaboration',
        tags: [
            'documents',
            'knowledge-base',
            'content-creation'
        ],
        pricing: 'freemium',
        logoUrl: 'https://www.craft.do/images/favicon.ico'
    }
];
const getResourcesByCategory = (category)=>{
    return resources.filter((resource)=>resource.category === category);
};
const getCategoryById = (id)=>{
    return resourceCategories.find((category)=>category.id === id);
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/learning/resources/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>ResourcesPage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$react$2d$fontawesome$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@fortawesome/react-fontawesome/index.es.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@fortawesome/free-solid-svg-icons/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$resources$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/data/resources.ts [app-client] (ecmascript)");
'use client';
;
;
;
;
;
// Map category IDs to FontAwesome icons
const categoryIcons = {
    'productivity': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["faBolt"],
    'project-management': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["faTasks"],
    'design': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["faPalette"],
    'development': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["faCode"],
    'research': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["faMagnifyingGlassChart"],
    'analytics': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["faChartLine"],
    'communication': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["faComments"],
    'collaboration': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["faUsersGear"]
};
function ResourcesPage() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "space-y-8",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "rounded-lg bg-gradient-to-r from-teal-600 to-emerald-700 p-8 text-white shadow-lg",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                        className: "mb-4 text-3xl font-bold",
                        children: "Resources Directory"
                    }, void 0, false, {
                        fileName: "[project]/src/app/learning/resources/page.tsx",
                        lineNumber: 35,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "mb-6 text-lg",
                        children: "Explore our curated collection of tools and services for consulting professionals. Find resources for productivity, project management, design, development, research, and more."
                    }, void 0, false, {
                        fileName: "[project]/src/app/learning/resources/page.tsx",
                        lineNumber: 36,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/learning/resources/page.tsx",
                lineNumber: 34,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",
                children: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$resources$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["resourceCategories"].map((category)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        href: `/learning/resources/${category.id}`,
                        className: "group flex flex-col rounded-lg border border-gray-200 bg-white p-6 shadow-sm transition hover:shadow-md dark:border-gray-800 dark:bg-gray-900",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-teal-100 text-teal-600 dark:bg-teal-900/30 dark:text-teal-400",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$react$2d$fontawesome$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FontAwesomeIcon"], {
                                    icon: categoryIcons[category.id],
                                    className: "h-6 w-6"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/learning/resources/page.tsx",
                                    lineNumber: 49,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/learning/resources/page.tsx",
                                lineNumber: 48,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "mb-2 text-xl font-bold text-gray-900 group-hover:text-teal-600 dark:text-white dark:group-hover:text-teal-400",
                                children: category.name
                            }, void 0, false, {
                                fileName: "[project]/src/app/learning/resources/page.tsx",
                                lineNumber: 51,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "mb-4 flex-1 text-gray-600 dark:text-gray-400",
                                children: category.description
                            }, void 0, false, {
                                fileName: "[project]/src/app/learning/resources/page.tsx",
                                lineNumber: 54,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "mt-auto flex items-center text-sm font-medium text-teal-600 dark:text-teal-400",
                                children: [
                                    "Browse resources",
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$react$2d$fontawesome$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FontAwesomeIcon"], {
                                        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["faArrowRight"],
                                        className: "ml-1 h-3 w-3"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/learning/resources/page.tsx",
                                        lineNumber: 57,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/learning/resources/page.tsx",
                                lineNumber: 55,
                                columnNumber: 13
                            }, this)
                        ]
                    }, category.id, true, {
                        fileName: "[project]/src/app/learning/resources/page.tsx",
                        lineNumber: 43,
                        columnNumber: 11
                    }, this))
            }, void 0, false, {
                fileName: "[project]/src/app/learning/resources/page.tsx",
                lineNumber: 41,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-800 dark:bg-gray-900",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        className: "mb-4 text-2xl font-bold",
                        children: "About Our Resources Directory"
                    }, void 0, false, {
                        fileName: "[project]/src/app/learning/resources/page.tsx",
                        lineNumber: 64,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "mb-4 text-gray-600 dark:text-gray-400",
                        children: "This directory is regularly updated with the latest tools and services that can help consulting professionals work more efficiently and deliver better results for clients. Our team carefully evaluates each resource before adding it to the directory."
                    }, void 0, false, {
                        fileName: "[project]/src/app/learning/resources/page.tsx",
                        lineNumber: 65,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "mb-4 text-gray-600 dark:text-gray-400",
                        children: "Each category includes a variety of tools with different pricing models, from free to enterprise-level solutions, allowing you to find options that fit your specific needs and budget."
                    }, void 0, false, {
                        fileName: "[project]/src/app/learning/resources/page.tsx",
                        lineNumber: 68,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mt-4 rounded-md bg-teal-50 p-4 dark:bg-teal-900/20",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "mb-2 font-semibold text-teal-800 dark:text-teal-300",
                                children: "Have a suggestion?"
                            }, void 0, false, {
                                fileName: "[project]/src/app/learning/resources/page.tsx",
                                lineNumber: 72,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-teal-700 dark:text-teal-400",
                                children: "If you know of a great tool that should be included in our directory, please let us know. We're always looking to expand our collection with valuable resources."
                            }, void 0, false, {
                                fileName: "[project]/src/app/learning/resources/page.tsx",
                                lineNumber: 73,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/learning/resources/page.tsx",
                        lineNumber: 71,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/learning/resources/page.tsx",
                lineNumber: 63,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/learning/resources/page.tsx",
        lineNumber: 33,
        columnNumber: 5
    }, this);
}
_c = ResourcesPage;
var _c;
__turbopack_context__.k.register(_c, "ResourcesPage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_f3d2a9f5._.js.map