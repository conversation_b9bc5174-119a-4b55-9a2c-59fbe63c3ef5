{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { User, AuthState, LoginCredentials, RegisterCredentials } from '@/types/auth';\n\ninterface AuthContextType extends AuthState {\n  login: (credentials: LoginCredentials) => Promise<void>;\n  register: (credentials: RegisterCredentials) => Promise<void>;\n  logout: () => Promise<void>;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const router = useRouter();\n\n  const [authState, setAuthState] = useState<AuthState>({\n    user: null,\n    isLoading: true,\n    error: null,\n  });\n\n  // Load user from localStorage on mount\n  useEffect(() => {\n    const loadUser = () => {\n      try {\n        console.log('Loading user from localStorage...');\n        const userJson = localStorage.getItem('user');\n        console.log('User JSON from localStorage:', userJson);\n\n        if (userJson) {\n          const user = JSON.parse(userJson) as User;\n          console.log('Parsed user:', user);\n          setAuthState({\n            user,\n            isLoading: false,\n            error: null,\n          });\n        } else {\n          console.log('No user found in localStorage');\n          setAuthState({\n            user: null,\n            isLoading: false,\n            error: null,\n          });\n        }\n      } catch (error) {\n        console.error('Error loading user from localStorage:', error);\n        setAuthState({\n          user: null,\n          isLoading: false,\n          error: 'Failed to load user data',\n        });\n      }\n    };\n\n    // Only run in browser environment\n    if (typeof window !== 'undefined') {\n      loadUser();\n    }\n  }, []);\n\n  const login = async (credentials: LoginCredentials) => {\n    console.log('Login function called with:', credentials.email);\n    try {\n      console.log('Setting loading state...');\n      setAuthState((prev) => ({ ...prev, isLoading: true, error: null }));\n\n      console.log('Sending login request to API...');\n      const response = await fetch('/api/login', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(credentials),\n      });\n\n      console.log('API response status:', response.status);\n      const data = await response.json();\n      console.log('API response data:', data);\n\n      if (!response.ok || !data.success) {\n        setAuthState((prev) => ({\n          ...prev,\n          isLoading: false,\n          error: data.message || 'Login failed',\n        }));\n        return;\n      }\n\n      console.log('Login successful, storing user data...');\n      // Store user in localStorage\n      localStorage.setItem('user', JSON.stringify(data.user));\n\n      // Also set a cookie for the middleware\n      document.cookie = `user=${JSON.stringify(data.user)}; path=/; max-age=86400`;\n\n      console.log('Updating auth state...');\n      // Update auth state\n      setAuthState({\n        user: data.user,\n        isLoading: false,\n        error: null,\n      });\n\n      console.log('Redirecting to homepage...');\n      router.push('/');\n    } catch (error) {\n      setAuthState((prev) => ({\n        ...prev,\n        isLoading: false,\n        error: 'An unexpected error occurred',\n      }));\n    }\n  };\n\n  const register = async (credentials: RegisterCredentials) => {\n    try {\n      setAuthState((prev) => ({ ...prev, isLoading: true, error: null }));\n\n      // In a real app, this would be an API call to create a new user\n      const response = await fetch('/api/auth/register', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(credentials),\n      });\n\n      const data = await response.json();\n\n      if (!response.ok) {\n        setAuthState((prev) => ({\n          ...prev,\n          isLoading: false,\n          error: data.message || 'Registration failed',\n        }));\n        return;\n      }\n\n      // Automatically log in after successful registration\n      await login({\n        email: credentials.email,\n        password: credentials.password,\n      });\n    } catch (error) {\n      setAuthState((prev) => ({\n        ...prev,\n        isLoading: false,\n        error: 'An unexpected error occurred',\n      }));\n    }\n  };\n\n  const logout = async () => {\n    try {\n      setAuthState((prev) => ({ ...prev, isLoading: true }));\n\n      // Remove user from localStorage\n      localStorage.removeItem('user');\n\n      // Also clear the cookie\n      document.cookie = 'user=; path=/; max-age=0';\n\n      // Update auth state\n      setAuthState({\n        user: null,\n        isLoading: false,\n        error: null,\n      });\n\n      router.push('/auth/login');\n    } catch (error) {\n      setAuthState((prev) => ({\n        ...prev,\n        isLoading: false,\n        error: 'Logout failed',\n      }));\n    }\n  };\n\n  return (\n    <AuthContext.Provider\n      value={{\n        ...authState,\n        login,\n        register,\n        logout,\n      }}\n    >\n      {children}\n    </AuthContext.Provider>\n  );\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;AAHA;;;AAYA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;;IACtE,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;QACpD,MAAM;QACN,WAAW;QACX,OAAO;IACT;IAEA,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM;mDAAW;oBACf,IAAI;wBACF,QAAQ,GAAG,CAAC;wBACZ,MAAM,WAAW,aAAa,OAAO,CAAC;wBACtC,QAAQ,GAAG,CAAC,gCAAgC;wBAE5C,IAAI,UAAU;4BACZ,MAAM,OAAO,KAAK,KAAK,CAAC;4BACxB,QAAQ,GAAG,CAAC,gBAAgB;4BAC5B,aAAa;gCACX;gCACA,WAAW;gCACX,OAAO;4BACT;wBACF,OAAO;4BACL,QAAQ,GAAG,CAAC;4BACZ,aAAa;gCACX,MAAM;gCACN,WAAW;gCACX,OAAO;4BACT;wBACF;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,yCAAyC;wBACvD,aAAa;4BACX,MAAM;4BACN,WAAW;4BACX,OAAO;wBACT;oBACF;gBACF;;YAEA,kCAAkC;YAClC,wCAAmC;gBACjC;YACF;QACF;iCAAG,EAAE;IAEL,MAAM,QAAQ,OAAO;QACnB,QAAQ,GAAG,CAAC,+BAA+B,YAAY,KAAK;QAC5D,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,aAAa,CAAC,OAAS,CAAC;oBAAE,GAAG,IAAI;oBAAE,WAAW;oBAAM,OAAO;gBAAK,CAAC;YAEjE,QAAQ,GAAG,CAAC;YACZ,MAAM,WAAW,MAAM,MAAM,cAAc;gBACzC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,QAAQ,GAAG,CAAC,wBAAwB,SAAS,MAAM;YACnD,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,QAAQ,GAAG,CAAC,sBAAsB;YAElC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK,OAAO,EAAE;gBACjC,aAAa,CAAC,OAAS,CAAC;wBACtB,GAAG,IAAI;wBACP,WAAW;wBACX,OAAO,KAAK,OAAO,IAAI;oBACzB,CAAC;gBACD;YACF;YAEA,QAAQ,GAAG,CAAC;YACZ,6BAA6B;YAC7B,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC,KAAK,IAAI;YAErD,uCAAuC;YACvC,SAAS,MAAM,GAAG,CAAC,KAAK,EAAE,KAAK,SAAS,CAAC,KAAK,IAAI,EAAE,uBAAuB,CAAC;YAE5E,QAAQ,GAAG,CAAC;YACZ,oBAAoB;YACpB,aAAa;gBACX,MAAM,KAAK,IAAI;gBACf,WAAW;gBACX,OAAO;YACT;YAEA,QAAQ,GAAG,CAAC;YACZ,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,aAAa,CAAC,OAAS,CAAC;oBACtB,GAAG,IAAI;oBACP,WAAW;oBACX,OAAO;gBACT,CAAC;QACH;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,IAAI;YACF,aAAa,CAAC,OAAS,CAAC;oBAAE,GAAG,IAAI;oBAAE,WAAW;oBAAM,OAAO;gBAAK,CAAC;YAEjE,gEAAgE;YAChE,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,aAAa,CAAC,OAAS,CAAC;wBACtB,GAAG,IAAI;wBACP,WAAW;wBACX,OAAO,KAAK,OAAO,IAAI;oBACzB,CAAC;gBACD;YACF;YAEA,qDAAqD;YACrD,MAAM,MAAM;gBACV,OAAO,YAAY,KAAK;gBACxB,UAAU,YAAY,QAAQ;YAChC;QACF,EAAE,OAAO,OAAO;YACd,aAAa,CAAC,OAAS,CAAC;oBACtB,GAAG,IAAI;oBACP,WAAW;oBACX,OAAO;gBACT,CAAC;QACH;IACF;IAEA,MAAM,SAAS;QACb,IAAI;YACF,aAAa,CAAC,OAAS,CAAC;oBAAE,GAAG,IAAI;oBAAE,WAAW;gBAAK,CAAC;YAEpD,gCAAgC;YAChC,aAAa,UAAU,CAAC;YAExB,wBAAwB;YACxB,SAAS,MAAM,GAAG;YAElB,oBAAoB;YACpB,aAAa;gBACX,MAAM;gBACN,WAAW;gBACX,OAAO;YACT;YAEA,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,aAAa,CAAC,OAAS,CAAC;oBACtB,GAAG,IAAI;oBACP,WAAW;oBACX,OAAO;gBACT,CAAC;QACH;IACF;IAEA,qBACE,6LAAC,YAAY,QAAQ;QACnB,OAAO;YACL,GAAG,SAAS;YACZ;YACA;YACA;QACF;kBAEC;;;;;;AAGP;GApLgB;;QACC,qIAAA,CAAA,YAAS;;;KADV;AAsLT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}, {"offset": {"line": 221, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Image from 'next/image';\nimport Link from 'next/link';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport {\n  faSearch,\n  faBell,\n  faUser,\n  faGear,\n  faRightFromBracket,\n  faMoon,\n  faSun,\n  faBars\n} from '@fortawesome/free-solid-svg-icons';\n\nexport default function Header() {\n  const { user, logout } = useAuth();\n  const [isProfileOpen, setIsProfileOpen] = useState(false);\n\n  return (\n    <header className=\"fixed top-0 left-0 right-0 z-50 w-full\">\n      <div className=\"flex h-16 items-center justify-between px-4 md:px-6\">\n        <div className=\"flex items-center\">\n          {/* Hamburger menu button (visible only on tablet/mobile) */}\n          <button\n            type=\"button\"\n            className=\"p-2 text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300\"\n            onClick={() => {\n              if (typeof window !== 'undefined') {\n                // Toggle sidebar-open class for mobile view\n                document.documentElement.classList.toggle('sidebar-open');\n\n                // Dispatch a custom event to notify the sidebar component\n                window.dispatchEvent(new Event('sidebar-toggle'));\n              }\n            }}\n            aria-label=\"Toggle menu\"\n          >\n            <FontAwesomeIcon icon={faBars} className=\"h-6 w-6\" />\n            <span className=\"sr-only\">Toggle sidebar</span>\n          </button>\n\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center ml-4 md:ml-0\">\n            <div className=\"relative h-8 w-8 mr-2\">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                viewBox=\"0 0 24 24\"\n                fill=\"none\"\n                stroke=\"currentColor\"\n                strokeWidth=\"2\"\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                className=\"h-8 w-8 text-blue-600\"\n              >\n                <path d=\"M12 2a4 4 0 0 1 4 4v4a4 4 0 0 1-4 4 4 4 0 0 1-4-4V6a4 4 0 0 1 4-4z\" />\n                <path d=\"M18 8a4 4 0 0 1 4 4v2a4 4 0 0 1-4 4 4 4 0 0 1-4-4v-2a4 4 0 0 1 4-4z\" />\n                <path d=\"M6 8a4 4 0 0 1 4 4v2a4 4 0 0 1-4 4 4 4 0 0 1-4-4v-2a4 4 0 0 1 4-4z\" />\n              </svg>\n            </div>\n            <span className=\"text-xl font-semibold text-gray-900 dark:text-white\">AI Hub</span>\n          </Link>\n        </div>\n\n        <div className=\"flex items-center space-x-4\">\n          {/* Search */}\n          <div className=\"hidden md:flex relative\">\n            <div className=\"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none\">\n              <FontAwesomeIcon icon={faSearch} className=\"h-4 w-4 text-gray-400\" />\n            </div>\n            <input\n              type=\"search\"\n              className=\"block w-full rounded-md border border-gray-200 bg-gray-50 py-2 pl-10 pr-3 text-sm text-gray-900 placeholder:text-gray-500 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white dark:placeholder:text-gray-400\"\n              placeholder=\"Search agents...\"\n            />\n          </div>\n\n          {/* Notifications */}\n          <button\n            type=\"button\"\n            className=\"p-2 text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300\"\n          >\n            <FontAwesomeIcon icon={faBell} className=\"h-6 w-6\" />\n            <span className=\"sr-only\">Notifications</span>\n          </button>\n\n          {/* Profile dropdown */}\n          <div className=\"relative\">\n            {user ? (\n              <>\n                <button\n                  type=\"button\"\n                  className=\"flex items-center rounded-full\"\n                  onClick={() => setIsProfileOpen(!isProfileOpen)}\n                >\n                  <div className=\"relative h-8 w-8 rounded-full bg-gray-200 overflow-hidden\">\n                    {user.image ? (\n                      <Image\n                        src={user.image}\n                        alt={user.name || 'User'}\n                        width={32}\n                        height={32}\n                        className=\"h-full w-full object-cover\"\n                      />\n                    ) : (\n                      <FontAwesomeIcon icon={faUser} className=\"absolute h-8 w-8 text-gray-400\" />\n                    )}\n                  </div>\n                </button>\n\n                {isProfileOpen && (\n                  <div className=\"absolute right-0 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-gray-800 dark:ring-gray-700\">\n                    <div className=\"border-b border-gray-200 px-4 py-2 dark:border-gray-700\">\n                      <p className=\"text-sm font-medium text-gray-900 dark:text-white\">{user.name}</p>\n                      <p className=\"truncate text-xs text-gray-500 dark:text-gray-400\">{user.email}</p>\n                    </div>\n                    <Link\n                      href=\"/profile\"\n                      className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700\"\n                      onClick={() => setIsProfileOpen(false)}\n                    >\n                      Your Profile\n                    </Link>\n                    <Link\n                      href=\"/settings\"\n                      className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700\"\n                      onClick={() => setIsProfileOpen(false)}\n                    >\n                      Settings\n                    </Link>\n                    <button\n                      className=\"block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700\"\n                      onClick={() => {\n                        setIsProfileOpen(false);\n                        logout();\n                      }}\n                    >\n                      Sign out\n                    </button>\n                  </div>\n                )}\n              </>\n            ) : (\n              <Link\n                href=\"/auth/login\"\n                className=\"rounded-md bg-blue-600 px-3 py-1.5 text-sm font-medium text-white hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800\"\n              >\n                Sign in\n              </Link>\n            )}\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AAkBe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BACC,MAAK;4BACL,WAAU;4BACV,SAAS;gCACP,wCAAmC;oCACjC,4CAA4C;oCAC5C,SAAS,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC;oCAE1C,0DAA0D;oCAC1D,OAAO,aAAa,CAAC,IAAI,MAAM;gCACjC;4BACF;4BACA,cAAW;;8CAEX,6LAAC,uKAAA,CAAA,kBAAe;oCAAC,MAAM,2KAAA,CAAA,SAAM;oCAAE,WAAU;;;;;;8CACzC,6LAAC;oCAAK,WAAU;8CAAU;;;;;;;;;;;;sCAI5B,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,OAAM;wCACN,SAAQ;wCACR,MAAK;wCACL,QAAO;wCACP,aAAY;wCACZ,eAAc;wCACd,gBAAe;wCACf,WAAU;;0DAEV,6LAAC;gDAAK,GAAE;;;;;;0DACR,6LAAC;gDAAK,GAAE;;;;;;0DACR,6LAAC;gDAAK,GAAE;;;;;;;;;;;;;;;;;8CAGZ,6LAAC;oCAAK,WAAU;8CAAsD;;;;;;;;;;;;;;;;;;8BAI1E,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,uKAAA,CAAA,kBAAe;wCAAC,MAAM,2KAAA,CAAA,WAAQ;wCAAE,WAAU;;;;;;;;;;;8CAE7C,6LAAC;oCACC,MAAK;oCACL,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAKhB,6LAAC;4BACC,MAAK;4BACL,WAAU;;8CAEV,6LAAC,uKAAA,CAAA,kBAAe;oCAAC,MAAM,2KAAA,CAAA,SAAM;oCAAE,WAAU;;;;;;8CACzC,6LAAC;oCAAK,WAAU;8CAAU;;;;;;;;;;;;sCAI5B,6LAAC;4BAAI,WAAU;sCACZ,qBACC;;kDACE,6LAAC;wCACC,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,iBAAiB,CAAC;kDAEjC,cAAA,6LAAC;4CAAI,WAAU;sDACZ,KAAK,KAAK,iBACT,6LAAC,gIAAA,CAAA,UAAK;gDACJ,KAAK,KAAK,KAAK;gDACf,KAAK,KAAK,IAAI,IAAI;gDAClB,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;qEAGZ,6LAAC,uKAAA,CAAA,kBAAe;gDAAC,MAAM,2KAAA,CAAA,SAAM;gDAAE,WAAU;;;;;;;;;;;;;;;;oCAK9C,+BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAqD,KAAK,IAAI;;;;;;kEAC3E,6LAAC;wDAAE,WAAU;kEAAqD,KAAK,KAAK;;;;;;;;;;;;0DAE9E,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,iBAAiB;0DACjC;;;;;;0DAGD,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,iBAAiB;0DACjC;;;;;;0DAGD,6LAAC;gDACC,WAAU;gDACV,SAAS;oDACP,iBAAiB;oDACjB;gDACF;0DACD;;;;;;;;;;;;;6DAOP,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GA5IwB;;QACG,kIAAA,CAAA,UAAO;;;KADV", "debugId": null}}, {"offset": {"line": 571, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/types/navigation.ts"], "sourcesContent": ["// Enhanced Navigation Types for Open WebUI Integration\n\nexport interface SidebarSection {\n  id: string;\n  title: string;\n  icon: string;\n  isCollapsed: boolean;\n  badgeCount?: number;\n  children: SidebarItem[];\n  permissions?: string[];\n  order: number;\n}\n\nexport interface SidebarItem {\n  id: string;\n  label: string;\n  icon?: string;\n  path: string;\n  isActive?: boolean;\n  badge?: string | number;\n  onClick?: () => void;\n  permissions?: string[];\n  isNew?: boolean;\n  children?: SidebarItem[];\n}\n\nexport interface QuickAction {\n  id: string;\n  icon: string;\n  label: string;\n  action: () => void;\n  contexts: string[]; // ['chat', 'knowledge', 'agents', '*']\n  position: 'floating' | 'sidebar';\n  priority: number;\n  isVisible?: boolean;\n  badge?: string | number;\n}\n\nexport interface NavigationState {\n  activeSection: string | null;\n  activePath: string;\n  collapsedSections: string[];\n  sidebarCollapsed: boolean;\n  quickActions: QuickAction[];\n  context: NavigationContext;\n}\n\nexport interface NavigationContext {\n  currentArea: 'dashboard' | 'agents' | 'conversations' | 'knowledge' | 'tools' | 'admin';\n  activeChat?: string;\n  hasActiveSession?: boolean;\n  isInKnowledgeArea?: boolean;\n  userRole?: 'user' | 'admin' | 'moderator';\n}\n\nexport interface ChatSession {\n  id: string;\n  title: string;\n  agentId?: string;\n  agentName?: string;\n  lastMessage?: string;\n  timestamp: number;\n  isActive: boolean;\n  isPinned?: boolean;\n  folder?: string;\n  unreadCount?: number;\n}\n\nexport interface DocumentItem {\n  id: string;\n  name: string;\n  type: string;\n  size: number;\n  uploadedAt: number;\n  isProcessed: boolean;\n  tags?: string[];\n}\n\nexport interface NavigationConfig {\n  enableQuickActions: boolean;\n  enableBadges: boolean;\n  enableAnimations: boolean;\n  persistState: boolean;\n  mobileBreakpoint: number;\n  tabletBreakpoint: number;\n}\n\n// Navigation Events\nexport type NavigationEvent = \n  | { type: 'SECTION_TOGGLE'; sectionId: string }\n  | { type: 'SIDEBAR_TOGGLE' }\n  | { type: 'CONTEXT_CHANGE'; context: NavigationContext }\n  | { type: 'QUICK_ACTION'; actionId: string }\n  | { type: 'CHAT_SELECT'; chatId: string }\n  | { type: 'DOCUMENT_SELECT'; documentId: string };\n\n// Responsive Breakpoints\nexport const BREAKPOINTS = {\n  mobile: 768,\n  tablet: 1024,\n  desktop: 1280\n} as const;\n\n// Default Navigation Configuration\nexport const DEFAULT_NAV_CONFIG: NavigationConfig = {\n  enableQuickActions: true,\n  enableBadges: true,\n  enableAnimations: true,\n  persistState: true,\n  mobileBreakpoint: BREAKPOINTS.mobile,\n  tabletBreakpoint: BREAKPOINTS.tablet\n};\n\n// Permission Levels\nexport type PermissionLevel = 'public' | 'user' | 'admin' | 'moderator';\n\n// Navigation Item Groups\nexport type NavigationGroup = \n  | 'dashboard'\n  | 'agents' \n  | 'conversations'\n  | 'knowledge'\n  | 'tools'\n  | 'admin';\n"], "names": [], "mappings": "AAAA,uDAAuD;;;;;AAiGhD,MAAM,cAAc;IACzB,QAAQ;IACR,QAAQ;IACR,SAAS;AACX;AAGO,MAAM,qBAAuC;IAClD,oBAAoB;IACpB,cAAc;IACd,kBAAkB;IAClB,cAAc;IACd,kBAAkB,YAAY,MAAM;IACpC,kBAAkB,YAAY,MAAM;AACtC", "debugId": null}}, {"offset": {"line": 598, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/contexts/NavigationContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useReducer, useEffect, useRef, ReactNode } from 'react';\nimport { usePathname } from 'next/navigation';\nimport {\n  NavigationState,\n  NavigationEvent,\n  NavigationContext as NavContextType,\n  QuickAction,\n  ChatSession,\n  DEFAULT_NAV_CONFIG\n} from '@/types/navigation';\n\n// Initial state\nconst initialState: NavigationState = {\n  activeSection: null,\n  activePath: '/',\n  collapsedSections: [],\n  sidebarCollapsed: false,\n  quickActions: [],\n  context: {\n    currentArea: 'dashboard',\n    hasActiveSession: false,\n    isInKnowledgeArea: false,\n    userRole: 'user'\n  }\n};\n\n// Navigation reducer\nfunction navigationReducer(state: NavigationState, action: NavigationEvent): NavigationState {\n  switch (action.type) {\n    case 'SECTION_TOGGLE':\n      const isCollapsed = state.collapsedSections.includes(action.sectionId);\n      return {\n        ...state,\n        collapsedSections: isCollapsed\n          ? state.collapsedSections.filter(id => id !== action.sectionId)\n          : [...state.collapsedSections, action.sectionId]\n      };\n\n    case 'SIDEBAR_TOGGLE':\n      return {\n        ...state,\n        sidebarCollapsed: !state.sidebarCollapsed\n      };\n\n    case 'CONTEXT_CHANGE':\n      return {\n        ...state,\n        context: { ...state.context, ...action.context }\n      };\n\n    case 'QUICK_ACTION':\n      // Handle quick action execution\n      const action_item = state.quickActions.find(qa => qa.id === action.actionId);\n      if (action_item) {\n        action_item.action();\n      }\n      return state;\n\n    case 'CHAT_SELECT':\n      return {\n        ...state,\n        context: {\n          ...state.context,\n          activeChat: action.chatId,\n          hasActiveSession: true,\n          currentArea: 'conversations'\n        }\n      };\n\n    case 'DOCUMENT_SELECT':\n      return {\n        ...state,\n        context: {\n          ...state.context,\n          currentArea: 'knowledge',\n          isInKnowledgeArea: true\n        }\n      };\n\n    default:\n      return state;\n  }\n}\n\n// Context type\ninterface NavigationContextType {\n  state: NavigationState;\n  dispatch: React.Dispatch<NavigationEvent>;\n  toggleSection: (sectionId: string) => void;\n  toggleSidebar: () => void;\n  updateContext: (context: Partial<NavContextType>) => void;\n  executeQuickAction: (actionId: string) => void;\n  selectChat: (chatId: string) => void;\n  selectDocument: (documentId: string) => void;\n  getVisibleQuickActions: () => QuickAction[];\n  isCurrentArea: (area: string) => boolean;\n}\n\n// Create context\nconst NavigationContext = createContext<NavigationContextType | undefined>(undefined);\n\n// Provider component\nexport function NavigationProvider({ children }: { children: ReactNode }) {\n  const [state, dispatch] = useReducer(navigationReducer, initialState);\n  const pathname = usePathname();\n  const previousAreaRef = useRef<string>('dashboard');\n  const previousKnowledgeRef = useRef<boolean>(false);\n\n  // Update active path and context based on current route\n  useEffect(() => {\n    let currentArea: NavContextType['currentArea'] = 'dashboard';\n    let isInKnowledgeArea = false;\n\n    if (pathname.startsWith('/browse') || pathname.startsWith('/popular') || pathname.startsWith('/favorites')) {\n      currentArea = 'agents';\n    } else if (pathname.startsWith('/conversations') || pathname.startsWith('/chat')) {\n      currentArea = 'conversations';\n    } else if (pathname.startsWith('/knowledge') || pathname.startsWith('/documents') || pathname.startsWith('/learning')) {\n      currentArea = 'knowledge';\n      isInKnowledgeArea = true;\n    } else if (pathname.startsWith('/tools') || pathname.startsWith('/settings')) {\n      currentArea = 'tools';\n    } else if (pathname.startsWith('/admin')) {\n      currentArea = 'admin';\n    }\n\n    // Only dispatch if the area actually changed\n    if (previousAreaRef.current !== currentArea || previousKnowledgeRef.current !== isInKnowledgeArea) {\n      previousAreaRef.current = currentArea;\n      previousKnowledgeRef.current = isInKnowledgeArea;\n\n      dispatch({\n        type: 'CONTEXT_CHANGE',\n        context: {\n          currentArea,\n          isInKnowledgeArea\n        }\n      });\n    }\n  }, [pathname]);\n\n  // Load persisted state from localStorage\n  useEffect(() => {\n    if (typeof window !== 'undefined' && DEFAULT_NAV_CONFIG.persistState) {\n      const savedCollapsed = localStorage.getItem('sidebarCollapsed');\n      const savedSections = localStorage.getItem('collapsedSections');\n\n      if (savedCollapsed) {\n        dispatch({ type: 'SIDEBAR_TOGGLE' });\n      }\n\n      if (savedSections) {\n        try {\n          const sections = JSON.parse(savedSections);\n          sections.forEach((sectionId: string) => {\n            dispatch({ type: 'SECTION_TOGGLE', sectionId });\n          });\n        } catch (error) {\n          console.warn('Failed to parse saved collapsed sections:', error);\n        }\n      }\n    }\n  }, []);\n\n  // Persist state changes to localStorage\n  useEffect(() => {\n    if (typeof window !== 'undefined' && DEFAULT_NAV_CONFIG.persistState) {\n      localStorage.setItem('sidebarCollapsed', state.sidebarCollapsed.toString());\n      localStorage.setItem('collapsedSections', JSON.stringify(state.collapsedSections));\n    }\n  }, [state.sidebarCollapsed, state.collapsedSections]);\n\n  // Context methods\n  const toggleSection = (sectionId: string) => {\n    dispatch({ type: 'SECTION_TOGGLE', sectionId });\n  };\n\n  const toggleSidebar = () => {\n    dispatch({ type: 'SIDEBAR_TOGGLE' });\n  };\n\n  const updateContext = (context: Partial<NavContextType>) => {\n    dispatch({ type: 'CONTEXT_CHANGE', context });\n  };\n\n  const executeQuickAction = (actionId: string) => {\n    dispatch({ type: 'QUICK_ACTION', actionId });\n  };\n\n  const selectChat = (chatId: string) => {\n    dispatch({ type: 'CHAT_SELECT', chatId });\n  };\n\n  const selectDocument = (documentId: string) => {\n    dispatch({ type: 'DOCUMENT_SELECT', documentId });\n  };\n\n  const getVisibleQuickActions = (): QuickAction[] => {\n    return state.quickActions.filter(action => {\n      if (action.contexts.includes('*')) return true;\n      return action.contexts.includes(state.context.currentArea);\n    });\n  };\n\n  const isCurrentArea = (area: string): boolean => {\n    return state.context.currentArea === area;\n  };\n\n  const value: NavigationContextType = {\n    state,\n    dispatch,\n    toggleSection,\n    toggleSidebar,\n    updateContext,\n    executeQuickAction,\n    selectChat,\n    selectDocument,\n    getVisibleQuickActions,\n    isCurrentArea\n  };\n\n  return (\n    <NavigationContext.Provider value={value}>\n      {children}\n    </NavigationContext.Provider>\n  );\n}\n\n// Hook to use navigation context\nexport function useNavigation() {\n  const context = useContext(NavigationContext);\n  if (context === undefined) {\n    throw new Error('useNavigation must be used within a NavigationProvider');\n  }\n  return context;\n}\n\n// Hook for quick actions\nexport function useQuickActions() {\n  const { state, executeQuickAction, getVisibleQuickActions } = useNavigation();\n\n  return {\n    quickActions: getVisibleQuickActions(),\n    executeAction: executeQuickAction,\n    context: state.context\n  };\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;;;AAJA;;;;AAaA,gBAAgB;AAChB,MAAM,eAAgC;IACpC,eAAe;IACf,YAAY;IACZ,mBAAmB,EAAE;IACrB,kBAAkB;IAClB,cAAc,EAAE;IAChB,SAAS;QACP,aAAa;QACb,kBAAkB;QAClB,mBAAmB;QACnB,UAAU;IACZ;AACF;AAEA,qBAAqB;AACrB,SAAS,kBAAkB,KAAsB,EAAE,MAAuB;IACxE,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,MAAM,cAAc,MAAM,iBAAiB,CAAC,QAAQ,CAAC,OAAO,SAAS;YACrE,OAAO;gBACL,GAAG,KAAK;gBACR,mBAAmB,cACf,MAAM,iBAAiB,CAAC,MAAM,CAAC,CAAA,KAAM,OAAO,OAAO,SAAS,IAC5D;uBAAI,MAAM,iBAAiB;oBAAE,OAAO,SAAS;iBAAC;YACpD;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,kBAAkB,CAAC,MAAM,gBAAgB;YAC3C;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,SAAS;oBAAE,GAAG,MAAM,OAAO;oBAAE,GAAG,OAAO,OAAO;gBAAC;YACjD;QAEF,KAAK;YACH,gCAAgC;YAChC,MAAM,cAAc,MAAM,YAAY,CAAC,IAAI,CAAC,CAAA,KAAM,GAAG,EAAE,KAAK,OAAO,QAAQ;YAC3E,IAAI,aAAa;gBACf,YAAY,MAAM;YACpB;YACA,OAAO;QAET,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,SAAS;oBACP,GAAG,MAAM,OAAO;oBAChB,YAAY,OAAO,MAAM;oBACzB,kBAAkB;oBAClB,aAAa;gBACf;YACF;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,SAAS;oBACP,GAAG,MAAM,OAAO;oBAChB,aAAa;oBACb,mBAAmB;gBACrB;YACF;QAEF;YACE,OAAO;IACX;AACF;AAgBA,iBAAiB;AACjB,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAqC;AAGpE,SAAS,mBAAmB,EAAE,QAAQ,EAA2B;;IACtE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,mBAAmB;IACxD,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAU;IACvC,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAW;IAE7C,wDAAwD;IACxD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,cAA6C;YACjD,IAAI,oBAAoB;YAExB,IAAI,SAAS,UAAU,CAAC,cAAc,SAAS,UAAU,CAAC,eAAe,SAAS,UAAU,CAAC,eAAe;gBAC1G,cAAc;YAChB,OAAO,IAAI,SAAS,UAAU,CAAC,qBAAqB,SAAS,UAAU,CAAC,UAAU;gBAChF,cAAc;YAChB,OAAO,IAAI,SAAS,UAAU,CAAC,iBAAiB,SAAS,UAAU,CAAC,iBAAiB,SAAS,UAAU,CAAC,cAAc;gBACrH,cAAc;gBACd,oBAAoB;YACtB,OAAO,IAAI,SAAS,UAAU,CAAC,aAAa,SAAS,UAAU,CAAC,cAAc;gBAC5E,cAAc;YAChB,OAAO,IAAI,SAAS,UAAU,CAAC,WAAW;gBACxC,cAAc;YAChB;YAEA,6CAA6C;YAC7C,IAAI,gBAAgB,OAAO,KAAK,eAAe,qBAAqB,OAAO,KAAK,mBAAmB;gBACjG,gBAAgB,OAAO,GAAG;gBAC1B,qBAAqB,OAAO,GAAG;gBAE/B,SAAS;oBACP,MAAM;oBACN,SAAS;wBACP;wBACA;oBACF;gBACF;YACF;QACF;uCAAG;QAAC;KAAS;IAEb,yCAAyC;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,aAAkB,eAAe,6HAAA,CAAA,qBAAkB,CAAC,YAAY,EAAE;gBACpE,MAAM,iBAAiB,aAAa,OAAO,CAAC;gBAC5C,MAAM,gBAAgB,aAAa,OAAO,CAAC;gBAE3C,IAAI,gBAAgB;oBAClB,SAAS;wBAAE,MAAM;oBAAiB;gBACpC;gBAEA,IAAI,eAAe;oBACjB,IAAI;wBACF,MAAM,WAAW,KAAK,KAAK,CAAC;wBAC5B,SAAS,OAAO;4DAAC,CAAC;gCAChB,SAAS;oCAAE,MAAM;oCAAkB;gCAAU;4BAC/C;;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,IAAI,CAAC,6CAA6C;oBAC5D;gBACF;YACF;QACF;uCAAG,EAAE;IAEL,wCAAwC;IACxC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,aAAkB,eAAe,6HAAA,CAAA,qBAAkB,CAAC,YAAY,EAAE;gBACpE,aAAa,OAAO,CAAC,oBAAoB,MAAM,gBAAgB,CAAC,QAAQ;gBACxE,aAAa,OAAO,CAAC,qBAAqB,KAAK,SAAS,CAAC,MAAM,iBAAiB;YAClF;QACF;uCAAG;QAAC,MAAM,gBAAgB;QAAE,MAAM,iBAAiB;KAAC;IAEpD,kBAAkB;IAClB,MAAM,gBAAgB,CAAC;QACrB,SAAS;YAAE,MAAM;YAAkB;QAAU;IAC/C;IAEA,MAAM,gBAAgB;QACpB,SAAS;YAAE,MAAM;QAAiB;IACpC;IAEA,MAAM,gBAAgB,CAAC;QACrB,SAAS;YAAE,MAAM;YAAkB;QAAQ;IAC7C;IAEA,MAAM,qBAAqB,CAAC;QAC1B,SAAS;YAAE,MAAM;YAAgB;QAAS;IAC5C;IAEA,MAAM,aAAa,CAAC;QAClB,SAAS;YAAE,MAAM;YAAe;QAAO;IACzC;IAEA,MAAM,iBAAiB,CAAC;QACtB,SAAS;YAAE,MAAM;YAAmB;QAAW;IACjD;IAEA,MAAM,yBAAyB;QAC7B,OAAO,MAAM,YAAY,CAAC,MAAM,CAAC,CAAA;YAC/B,IAAI,OAAO,QAAQ,CAAC,QAAQ,CAAC,MAAM,OAAO;YAC1C,OAAO,OAAO,QAAQ,CAAC,QAAQ,CAAC,MAAM,OAAO,CAAC,WAAW;QAC3D;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAO,MAAM,OAAO,CAAC,WAAW,KAAK;IACvC;IAEA,MAAM,QAA+B;QACnC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,kBAAkB,QAAQ;QAAC,OAAO;kBAChC;;;;;;AAGP;GA5HgB;;QAEG,qIAAA,CAAA,cAAW;;;KAFd;AA+HT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB;AAST,SAAS;;IACd,MAAM,EAAE,KAAK,EAAE,kBAAkB,EAAE,sBAAsB,EAAE,GAAG;IAE9D,OAAO;QACL,cAAc;QACd,eAAe;QACf,SAAS,MAAM,OAAO;IACxB;AACF;IARgB;;QACgD", "debugId": null}}, {"offset": {"line": 870, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/components/navigation/EnhancedSidebar.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport {\n  faHome,\n  faRobot,\n  faComments,\n  faBook,\n  faCog,\n  faUserShield,\n  faChevronDown,\n  faChevronRight,\n  faBars,\n  faPlus,\n  faSearch,\n  faTableCells,\n  faChartLine,\n  faHeart,\n  faHistory,\n  faThumbtack,\n  faFolder,\n  faFile,\n  faUpload,\n  faGraduationCap,\n  faVideo,\n  faNewspaper,\n  faBlog,\n  faToolbox,\n  faKey,\n  faLink,\n  faMicrophone,\n  faGear,\n  faChartBar,\n  faUsers,\n  faSitemap,\n  faServer\n} from '@fortawesome/free-solid-svg-icons';\nimport { useNavigation } from '@/contexts/NavigationContext';\nimport { SidebarSection, SidebarItem } from '@/types/navigation';\n\nexport default function EnhancedSidebar() {\n  const pathname = usePathname();\n  const { state, toggleSection, toggleSidebar } = useNavigation();\n  const [searchQuery, setSearchQuery] = useState('');\n  const [filteredSections, setFilteredSections] = useState<SidebarSection[]>([]);\n\n  // Define sidebar sections with new structure\n  const sidebarSections: SidebarSection[] = [\n    {\n      id: 'dashboard',\n      title: 'Dashboard',\n      icon: 'home',\n      isCollapsed: false,\n      order: 1,\n      children: [\n        {\n          id: 'home',\n          label: 'Home',\n          icon: 'home',\n          path: '/'\n        }\n      ]\n    },\n    {\n      id: 'agents',\n      title: 'Agents',\n      icon: 'robot',\n      isCollapsed: state.collapsedSections.includes('agents'),\n      order: 2,\n      children: [\n        {\n          id: 'browse-agents',\n          label: 'Browse All',\n          icon: 'table-cells',\n          path: '/browse'\n        },\n        {\n          id: 'popular-agents',\n          label: 'Popular',\n          icon: 'chart-line',\n          path: '/popular'\n        },\n        {\n          id: 'favorite-agents',\n          label: 'Favorites',\n          icon: 'heart',\n          path: '/favorites',\n          badge: '3' // Example badge\n        },\n        {\n          id: 'new-agent-chat',\n          label: 'New Agent Chat',\n          icon: 'plus',\n          path: '/agent/new',\n          isNew: true\n        }\n      ]\n    },\n    {\n      id: 'conversations',\n      title: 'Conversations',\n      icon: 'comments',\n      isCollapsed: state.collapsedSections.includes('conversations'),\n      badgeCount: 2, // Example: 2 unread conversations\n      order: 3,\n      children: [\n        {\n          id: 'active-sessions',\n          label: 'Active Sessions',\n          icon: 'comments',\n          path: '/conversations/active',\n          badge: '2'\n        },\n        {\n          id: 'chat-history',\n          label: 'Chat History',\n          icon: 'history',\n          path: '/conversations/history'\n        },\n        {\n          id: 'pinned-chats',\n          label: 'Pinned Chats',\n          icon: 'thumbtack',\n          path: '/conversations/pinned'\n        },\n        {\n          id: 'chat-folders',\n          label: 'Chat Folders',\n          icon: 'folder',\n          path: '/conversations/folders'\n        },\n        {\n          id: 'new-conversation',\n          label: 'New Conversation',\n          icon: 'plus',\n          path: '/conversations/new',\n          isNew: true\n        }\n      ]\n    },\n    {\n      id: 'knowledge',\n      title: 'Knowledge Base',\n      icon: 'book',\n      isCollapsed: state.collapsedSections.includes('knowledge'),\n      order: 4,\n      children: [\n        {\n          id: 'my-documents',\n          label: 'My Documents',\n          icon: 'file',\n          path: '/knowledge/documents'\n        },\n        {\n          id: 'upload-files',\n          label: 'Upload Files',\n          icon: 'upload',\n          path: '/knowledge/upload',\n          isNew: true\n        },\n        {\n          id: 'learning-hub',\n          label: 'Learning Hub',\n          icon: 'graduation-cap',\n          path: '/learning',\n          children: [\n            {\n              id: 'videos',\n              label: 'Videos',\n              icon: 'video',\n              path: '/learning/videos'\n            },\n            {\n              id: 'articles',\n              label: 'Articles',\n              icon: 'newspaper',\n              path: '/learning/articles'\n            },\n            {\n              id: 'blog',\n              label: 'Blog Posts',\n              icon: 'blog',\n              path: '/learning/blog'\n            },\n            {\n              id: 'resources',\n              label: 'Resources',\n              icon: 'toolbox',\n              path: '/learning/resources'\n            }\n          ]\n        },\n        {\n          id: 'search-knowledge',\n          label: 'Search Knowledge',\n          icon: 'search',\n          path: '/knowledge/search'\n        }\n      ]\n    },\n    {\n      id: 'tools',\n      title: 'Tools & Settings',\n      icon: 'cog',\n      isCollapsed: state.collapsedSections.includes('tools'),\n      order: 5,\n      children: [\n        {\n          id: 'ai-models',\n          label: 'AI Models',\n          icon: 'robot',\n          path: '/tools/models'\n        },\n        {\n          id: 'integrations',\n          label: 'Integrations',\n          icon: 'link',\n          path: '/tools/integrations'\n        },\n        {\n          id: 'api-keys',\n          label: 'API Keys',\n          icon: 'key',\n          path: '/tools/api-keys'\n        },\n        {\n          id: 'voice-audio',\n          label: 'Voice & Audio',\n          icon: 'microphone',\n          path: '/tools/voice'\n        },\n        {\n          id: 'preferences',\n          label: 'Preferences',\n          icon: 'gear',\n          path: '/tools/preferences'\n        },\n        {\n          id: 'usage-analytics',\n          label: 'Usage Analytics',\n          icon: 'chart-bar',\n          path: '/tools/analytics'\n        }\n      ]\n    }\n  ];\n\n  // Add admin section conditionally\n  const adminSection: SidebarSection = {\n    id: 'admin',\n    title: 'Admin',\n    icon: 'user-shield',\n    isCollapsed: state.collapsedSections.includes('admin'),\n    order: 6,\n    permissions: ['admin'],\n    children: [\n      {\n        id: 'user-management',\n        label: 'User Management',\n        icon: 'users',\n        path: '/admin/users'\n      },\n      {\n        id: 'ai-organization',\n        label: 'AI Organization',\n        icon: 'sitemap',\n        path: '/admin/ai-org'\n      },\n      {\n        id: 'system-settings',\n        label: 'System Settings',\n        icon: 'server',\n        path: '/admin/settings'\n      },\n      {\n        id: 'platform-analytics',\n        label: 'Platform Analytics',\n        icon: 'chart-bar',\n        path: '/admin/analytics'\n      }\n    ]\n  };\n\n  // Get icon component\n  const getIconComponent = (iconName: string) => {\n    const iconMap: Record<string, any> = {\n      home: faHome,\n      robot: faRobot,\n      comments: faComments,\n      book: faBook,\n      cog: faCog,\n      'user-shield': faUserShield,\n      'table-cells': faTableCells,\n      'chart-line': faChartLine,\n      heart: faHeart,\n      history: faHistory,\n      thumbtack: faThumbtack,\n      folder: faFolder,\n      file: faFile,\n      upload: faUpload,\n      'graduation-cap': faGraduationCap,\n      video: faVideo,\n      newspaper: faNewspaper,\n      blog: faBlog,\n      toolbox: faToolbox,\n      key: faKey,\n      link: faLink,\n      microphone: faMicrophone,\n      gear: faGear,\n      'chart-bar': faChartBar,\n      users: faUsers,\n      sitemap: faSitemap,\n      server: faServer,\n      search: faSearch,\n      plus: faPlus\n    };\n\n    return iconMap[iconName] || faHome;\n  };\n\n  // Filter sections based on search\n  useEffect(() => {\n    if (!searchQuery.trim()) {\n      setFilteredSections([...sidebarSections, adminSection]);\n      return;\n    }\n\n    const filtered = sidebarSections.map(section => ({\n      ...section,\n      children: section.children.filter(item =>\n        item.label.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        (item.children && item.children.some(child =>\n          child.label.toLowerCase().includes(searchQuery.toLowerCase())\n        ))\n      )\n    })).filter(section => section.children.length > 0);\n\n    setFilteredSections(filtered);\n  }, [searchQuery]);\n\n  const handleSectionToggle = (sectionId: string) => {\n    toggleSection(sectionId);\n  };\n\n  const isItemActive = (item: SidebarItem): boolean => {\n    if (item.path === pathname) return true;\n    if (item.children) {\n      return item.children.some(child => child.path === pathname);\n    }\n    return false;\n  };\n\n  return (\n    <aside\n      className={`fixed left-0 z-50 flex flex-col h-[calc(100vh-64px)] transition-all duration-300 bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-800 ${\n        state.sidebarCollapsed ? 'w-16' : 'w-80'\n      } md:translate-x-0 ${\n        typeof window !== 'undefined' && document.documentElement.classList.contains('sidebar-open')\n          ? 'translate-x-0'\n          : '-translate-x-full'\n      }`}\n    >\n      {/* Sidebar Header */}\n      <div className=\"flex h-16 items-center justify-between border-b border-gray-200/30 px-4 dark:border-gray-800/30 mt-2\">\n        <span className={`text-lg font-semibold text-gray-900 dark:text-white whitespace-nowrap overflow-hidden ${state.sidebarCollapsed ? 'hidden' : ''}`}>\n          AI Hub\n        </span>\n        \n        <button\n          type=\"button\"\n          className={`p-2 text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300 ${state.sidebarCollapsed ? 'mx-auto' : ''}`}\n          onClick={toggleSidebar}\n          aria-label=\"Toggle sidebar\"\n        >\n          <FontAwesomeIcon icon={faBars} className=\"h-5 w-5\" />\n        </button>\n      </div>\n\n      {/* Search Bar */}\n      {!state.sidebarCollapsed && (\n        <div className=\"p-4 border-b border-gray-200/30 dark:border-gray-800/30\">\n          <div className=\"relative\">\n            <FontAwesomeIcon\n              icon={faSearch}\n              className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n            />\n            <input\n              type=\"text\"\n              placeholder=\"Search navigation...\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              className=\"w-full pl-10 pr-4 py-2 text-sm border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:border-gray-700 dark:bg-gray-800 dark:text-white\"\n            />\n          </div>\n        </div>\n      )}\n\n      {/* Navigation Content */}\n      <div className=\"flex-1 overflow-y-auto p-4\">\n        <nav className=\"space-y-2\">\n          {filteredSections.map((section) => (\n            <div key={section.id} className=\"space-y-1\">\n              {/* Section Header */}\n              <button\n                onClick={() => handleSectionToggle(section.id)}\n                className={`w-full flex items-center justify-between px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800 rounded-md ${\n                  state.sidebarCollapsed ? 'justify-center' : ''\n                }`}\n                title={state.sidebarCollapsed ? section.title : undefined}\n              >\n                <div className=\"flex items-center\">\n                  <FontAwesomeIcon\n                    icon={getIconComponent(section.icon)}\n                    className={`h-5 w-5 ${state.sidebarCollapsed ? '' : 'mr-3'}`}\n                  />\n                  {!state.sidebarCollapsed && (\n                    <span className=\"flex-1 text-left\">{section.title}</span>\n                  )}\n                </div>\n                \n                {!state.sidebarCollapsed && (\n                  <div className=\"flex items-center space-x-2\">\n                    {section.badgeCount && (\n                      <span className=\"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full dark:bg-blue-900 dark:text-blue-200\">\n                        {section.badgeCount}\n                      </span>\n                    )}\n                    <FontAwesomeIcon\n                      icon={section.isCollapsed ? faChevronRight : faChevronDown}\n                      className=\"h-3 w-3\"\n                    />\n                  </div>\n                )}\n              </button>\n\n              {/* Section Items */}\n              {(!section.isCollapsed || state.sidebarCollapsed) && (\n                <div className={`space-y-1 ${state.sidebarCollapsed ? '' : 'ml-4'}`}>\n                  {section.children.map((item) => (\n                    <div key={item.id}>\n                      <Link\n                        href={item.path}\n                        className={`flex items-center px-3 py-2 text-sm rounded-md transition-colors ${\n                          isItemActive(item)\n                            ? 'bg-blue-50 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400'\n                            : 'text-gray-600 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-800'\n                        } ${state.sidebarCollapsed ? 'justify-center' : ''}`}\n                        title={state.sidebarCollapsed ? item.label : undefined}\n                      >\n                        {item.icon && (\n                          <FontAwesomeIcon\n                            icon={getIconComponent(item.icon)}\n                            className={`h-4 w-4 ${state.sidebarCollapsed ? '' : 'mr-3'}`}\n                          />\n                        )}\n                        {!state.sidebarCollapsed && (\n                          <span className=\"flex-1\">{item.label}</span>\n                        )}\n                        {!state.sidebarCollapsed && item.badge && (\n                          <span className=\"bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full dark:bg-red-900 dark:text-red-200\">\n                            {item.badge}\n                          </span>\n                        )}\n                        {!state.sidebarCollapsed && item.isNew && (\n                          <span className=\"bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full dark:bg-green-900 dark:text-green-200\">\n                            New\n                          </span>\n                        )}\n                      </Link>\n                      \n                      {/* Sub-items */}\n                      {!state.sidebarCollapsed && item.children && (\n                        <div className=\"ml-6 space-y-1\">\n                          {item.children.map((subItem) => (\n                            <Link\n                              key={subItem.id}\n                              href={subItem.path}\n                              className={`flex items-center px-3 py-1 text-sm rounded-md transition-colors ${\n                                pathname === subItem.path\n                                  ? 'bg-blue-50 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400'\n                                  : 'text-gray-500 hover:bg-gray-100 dark:text-gray-500 dark:hover:bg-gray-800'\n                              }`}\n                            >\n                              {subItem.icon && (\n                                <FontAwesomeIcon\n                                  icon={getIconComponent(subItem.icon)}\n                                  className=\"h-3 w-3 mr-2\"\n                                />\n                              )}\n                              <span>{subItem.label}</span>\n                            </Link>\n                          ))}\n                        </div>\n                      )}\n                    </div>\n                  ))}\n                </div>\n              )}\n            </div>\n          ))}\n        </nav>\n      </div>\n    </aside>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAkCA;;;AAxCA;;;;;;;AA2Ce,SAAS;;IACtB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,gBAAa,AAAD;IAC5D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IAE7E,6CAA6C;IAC7C,MAAM,kBAAoC;QACxC;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,aAAa;YACb,OAAO;YACP,UAAU;gBACR;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;gBACR;aACD;QACH;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,aAAa,MAAM,iBAAiB,CAAC,QAAQ,CAAC;YAC9C,OAAO;YACP,UAAU;gBACR;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;gBACR;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;gBACR;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,OAAO,IAAI,gBAAgB;gBAC7B;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,OAAO;gBACT;aACD;QACH;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,aAAa,MAAM,iBAAiB,CAAC,QAAQ,CAAC;YAC9C,YAAY;YACZ,OAAO;YACP,UAAU;gBACR;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,OAAO;gBACT;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;gBACR;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;gBACR;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;gBACR;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,OAAO;gBACT;aACD;QACH;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,aAAa,MAAM,iBAAiB,CAAC,QAAQ,CAAC;YAC9C,OAAO;YACP,UAAU;gBACR;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;gBACR;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,OAAO;gBACT;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,UAAU;wBACR;4BACE,IAAI;4BACJ,OAAO;4BACP,MAAM;4BACN,MAAM;wBACR;wBACA;4BACE,IAAI;4BACJ,OAAO;4BACP,MAAM;4BACN,MAAM;wBACR;wBACA;4BACE,IAAI;4BACJ,OAAO;4BACP,MAAM;4BACN,MAAM;wBACR;wBACA;4BACE,IAAI;4BACJ,OAAO;4BACP,MAAM;4BACN,MAAM;wBACR;qBACD;gBACH;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;gBACR;aACD;QACH;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,aAAa,MAAM,iBAAiB,CAAC,QAAQ,CAAC;YAC9C,OAAO;YACP,UAAU;gBACR;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;gBACR;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;gBACR;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;gBACR;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;gBACR;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;gBACR;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;gBACR;aACD;QACH;KACD;IAED,kCAAkC;IAClC,MAAM,eAA+B;QACnC,IAAI;QACJ,OAAO;QACP,MAAM;QACN,aAAa,MAAM,iBAAiB,CAAC,QAAQ,CAAC;QAC9C,OAAO;QACP,aAAa;YAAC;SAAQ;QACtB,UAAU;YACR;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;YACR;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;YACR;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;YACR;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;YACR;SACD;IACH;IAEA,qBAAqB;IACrB,MAAM,mBAAmB,CAAC;QACxB,MAAM,UAA+B;YACnC,MAAM,2KAAA,CAAA,SAAM;YACZ,OAAO,2KAAA,CAAA,UAAO;YACd,UAAU,2KAAA,CAAA,aAAU;YACpB,MAAM,2KAAA,CAAA,SAAM;YACZ,KAAK,2KAAA,CAAA,QAAK;YACV,eAAe,2KAAA,CAAA,eAAY;YAC3B,eAAe,2KAAA,CAAA,eAAY;YAC3B,cAAc,2KAAA,CAAA,cAAW;YACzB,OAAO,2KAAA,CAAA,UAAO;YACd,SAAS,2KAAA,CAAA,YAAS;YAClB,WAAW,2KAAA,CAAA,cAAW;YACtB,QAAQ,2KAAA,CAAA,WAAQ;YAChB,MAAM,2KAAA,CAAA,SAAM;YACZ,QAAQ,2KAAA,CAAA,WAAQ;YAChB,kBAAkB,2KAAA,CAAA,kBAAe;YACjC,OAAO,2KAAA,CAAA,UAAO;YACd,WAAW,2KAAA,CAAA,cAAW;YACtB,MAAM,2KAAA,CAAA,SAAM;YACZ,SAAS,2KAAA,CAAA,YAAS;YAClB,KAAK,2KAAA,CAAA,QAAK;YACV,MAAM,2KAAA,CAAA,SAAM;YACZ,YAAY,2KAAA,CAAA,eAAY;YACxB,MAAM,2KAAA,CAAA,SAAM;YACZ,aAAa,2KAAA,CAAA,aAAU;YACvB,OAAO,2KAAA,CAAA,UAAO;YACd,SAAS,2KAAA,CAAA,YAAS;YAClB,QAAQ,2KAAA,CAAA,WAAQ;YAChB,QAAQ,2KAAA,CAAA,WAAQ;YAChB,MAAM,2KAAA,CAAA,SAAM;QACd;QAEA,OAAO,OAAO,CAAC,SAAS,IAAI,2KAAA,CAAA,SAAM;IACpC;IAEA,kCAAkC;IAClC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,CAAC,YAAY,IAAI,IAAI;gBACvB,oBAAoB;uBAAI;oBAAiB;iBAAa;gBACtD;YACF;YAEA,MAAM,WAAW,gBAAgB,GAAG;sDAAC,CAAA,UAAW,CAAC;wBAC/C,GAAG,OAAO;wBACV,UAAU,QAAQ,QAAQ,CAAC,MAAM;kEAAC,CAAA,OAChC,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OACxD,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,IAAI;0EAAC,CAAA,QACnC,MAAM,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;;;oBAGhE,CAAC;qDAAG,MAAM;sDAAC,CAAA,UAAW,QAAQ,QAAQ,CAAC,MAAM,GAAG;;YAEhD,oBAAoB;QACtB;oCAAG;QAAC;KAAY;IAEhB,MAAM,sBAAsB,CAAC;QAC3B,cAAc;IAChB;IAEA,MAAM,eAAe,CAAC;QACpB,IAAI,KAAK,IAAI,KAAK,UAAU,OAAO;QACnC,IAAI,KAAK,QAAQ,EAAE;YACjB,OAAO,KAAK,QAAQ,CAAC,IAAI,CAAC,CAAA,QAAS,MAAM,IAAI,KAAK;QACpD;QACA,OAAO;IACT;IAEA,qBACE,6LAAC;QACC,WAAW,CAAC,yJAAyJ,EACnK,MAAM,gBAAgB,GAAG,SAAS,OACnC,kBAAkB,EACjB,aAAkB,eAAe,SAAS,eAAe,CAAC,SAAS,CAAC,QAAQ,CAAC,kBACzE,kBACA,qBACJ;;0BAGF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAK,WAAW,CAAC,sFAAsF,EAAE,MAAM,gBAAgB,GAAG,WAAW,IAAI;kCAAE;;;;;;kCAIpJ,6LAAC;wBACC,MAAK;wBACL,WAAW,CAAC,kFAAkF,EAAE,MAAM,gBAAgB,GAAG,YAAY,IAAI;wBACzI,SAAS;wBACT,cAAW;kCAEX,cAAA,6LAAC,uKAAA,CAAA,kBAAe;4BAAC,MAAM,2KAAA,CAAA,SAAM;4BAAE,WAAU;;;;;;;;;;;;;;;;;YAK5C,CAAC,MAAM,gBAAgB,kBACtB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,uKAAA,CAAA,kBAAe;4BACd,MAAM,2KAAA,CAAA,WAAQ;4BACd,WAAU;;;;;;sCAEZ,6LAAC;4BACC,MAAK;4BACL,aAAY;4BACZ,OAAO;4BACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4BAC9C,WAAU;;;;;;;;;;;;;;;;;0BAOlB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,6LAAC;4BAAqB,WAAU;;8CAE9B,6LAAC;oCACC,SAAS,IAAM,oBAAoB,QAAQ,EAAE;oCAC7C,WAAW,CAAC,4JAA4J,EACtK,MAAM,gBAAgB,GAAG,mBAAmB,IAC5C;oCACF,OAAO,MAAM,gBAAgB,GAAG,QAAQ,KAAK,GAAG;;sDAEhD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uKAAA,CAAA,kBAAe;oDACd,MAAM,iBAAiB,QAAQ,IAAI;oDACnC,WAAW,CAAC,QAAQ,EAAE,MAAM,gBAAgB,GAAG,KAAK,QAAQ;;;;;;gDAE7D,CAAC,MAAM,gBAAgB,kBACtB,6LAAC;oDAAK,WAAU;8DAAoB,QAAQ,KAAK;;;;;;;;;;;;wCAIpD,CAAC,MAAM,gBAAgB,kBACtB,6LAAC;4CAAI,WAAU;;gDACZ,QAAQ,UAAU,kBACjB,6LAAC;oDAAK,WAAU;8DACb,QAAQ,UAAU;;;;;;8DAGvB,6LAAC,uKAAA,CAAA,kBAAe;oDACd,MAAM,QAAQ,WAAW,GAAG,2KAAA,CAAA,iBAAc,GAAG,2KAAA,CAAA,gBAAa;oDAC1D,WAAU;;;;;;;;;;;;;;;;;;gCAOjB,CAAC,CAAC,QAAQ,WAAW,IAAI,MAAM,gBAAgB,mBAC9C,6LAAC;oCAAI,WAAW,CAAC,UAAU,EAAE,MAAM,gBAAgB,GAAG,KAAK,QAAQ;8CAChE,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,qBACrB,6LAAC;;8DACC,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAW,CAAC,iEAAiE,EAC3E,aAAa,QACT,oEACA,4EACL,CAAC,EAAE,MAAM,gBAAgB,GAAG,mBAAmB,IAAI;oDACpD,OAAO,MAAM,gBAAgB,GAAG,KAAK,KAAK,GAAG;;wDAE5C,KAAK,IAAI,kBACR,6LAAC,uKAAA,CAAA,kBAAe;4DACd,MAAM,iBAAiB,KAAK,IAAI;4DAChC,WAAW,CAAC,QAAQ,EAAE,MAAM,gBAAgB,GAAG,KAAK,QAAQ;;;;;;wDAG/D,CAAC,MAAM,gBAAgB,kBACtB,6LAAC;4DAAK,WAAU;sEAAU,KAAK,KAAK;;;;;;wDAErC,CAAC,MAAM,gBAAgB,IAAI,KAAK,KAAK,kBACpC,6LAAC;4DAAK,WAAU;sEACb,KAAK,KAAK;;;;;;wDAGd,CAAC,MAAM,gBAAgB,IAAI,KAAK,KAAK,kBACpC,6LAAC;4DAAK,WAAU;sEAAmG;;;;;;;;;;;;gDAOtH,CAAC,MAAM,gBAAgB,IAAI,KAAK,QAAQ,kBACvC,6LAAC;oDAAI,WAAU;8DACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,wBAClB,6LAAC,+JAAA,CAAA,UAAI;4DAEH,MAAM,QAAQ,IAAI;4DAClB,WAAW,CAAC,iEAAiE,EAC3E,aAAa,QAAQ,IAAI,GACrB,oEACA,6EACJ;;gEAED,QAAQ,IAAI,kBACX,6LAAC,uKAAA,CAAA,kBAAe;oEACd,MAAM,iBAAiB,QAAQ,IAAI;oEACnC,WAAU;;;;;;8EAGd,6LAAC;8EAAM,QAAQ,KAAK;;;;;;;2DAdf,QAAQ,EAAE;;;;;;;;;;;2CApCf,KAAK,EAAE;;;;;;;;;;;2BAtCf,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;AAuGhC;GAhdwB;;QACL,qIAAA,CAAA,cAAW;QACoB,wIAAA,CAAA,gBAAa;;;KAFvC", "debugId": null}}, {"offset": {"line": 1476, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/components/navigation/QuickActions.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport {\n  faPlus,\n  faUpload,\n  faMicrophone,\n  faExchange,\n  faSave,\n  faSearch,\n  faThumbtack,\n  faTrash,\n  faEllipsisV\n} from '@fortawesome/free-solid-svg-icons';\nimport { useQuickActions } from '@/contexts/NavigationContext';\nimport { QuickAction, NavigationContext } from '@/types/navigation';\n\ninterface QuickActionsProps {\n  className?: string;\n}\n\n// Define default quick actions based on context (moved outside component)\nconst getDefaultActions = (context: NavigationContext): QuickAction[] => {\n    const baseActions: QuickAction[] = [\n      {\n        id: 'new-chat',\n        icon: 'plus',\n        label: 'New Chat',\n        action: () => {\n          // TODO: Implement new chat creation\n          console.log('Creating new chat...');\n        },\n        contexts: ['*'],\n        position: 'floating',\n        priority: 1\n      },\n      {\n        id: 'global-search',\n        icon: 'search',\n        label: 'Search',\n        action: () => {\n          // TODO: Implement global search\n          console.log('Opening global search...');\n        },\n        contexts: ['*'],\n        position: 'floating',\n        priority: 2\n      }\n    ];\n\n    // Context-specific actions\n    if (context.currentArea === 'knowledge' || context.isInKnowledgeArea) {\n      baseActions.push({\n        id: 'upload-document',\n        icon: 'upload',\n        label: 'Upload Document',\n        action: () => {\n          // TODO: Implement document upload\n          console.log('Opening document upload...');\n        },\n        contexts: ['knowledge', 'chat'],\n        position: 'floating',\n        priority: 3\n      });\n    }\n\n    if (context.currentArea === 'conversations' || context.hasActiveSession) {\n      baseActions.push(\n        {\n          id: 'voice-mode',\n          icon: 'microphone',\n          label: 'Voice Chat',\n          action: () => {\n            // TODO: Implement voice chat\n            console.log('Starting voice chat...');\n          },\n          contexts: ['conversations'],\n          position: 'floating',\n          priority: 4\n        },\n        {\n          id: 'switch-model',\n          icon: 'exchange',\n          label: 'Switch Model',\n          action: () => {\n            // TODO: Implement model switching\n            console.log('Opening model selector...');\n          },\n          contexts: ['conversations'],\n          position: 'floating',\n          priority: 5\n        },\n        {\n          id: 'save-session',\n          icon: 'save',\n          label: 'Save Session',\n          action: () => {\n            // TODO: Implement session saving\n            console.log('Saving current session...');\n          },\n          contexts: ['conversations'],\n          position: 'floating',\n          priority: 6\n        }\n      );\n    }\n\n    if (context.activeChat) {\n      baseActions.push(\n        {\n          id: 'pin-chat',\n          icon: 'thumbtack',\n          label: 'Pin Chat',\n          action: () => {\n            // TODO: Implement chat pinning\n            console.log('Pinning chat...');\n          },\n          contexts: ['conversations'],\n          position: 'floating',\n          priority: 7\n        }\n      );\n    }\n\n    return baseActions;\n};\n\nexport default function QuickActions({ className = '' }: QuickActionsProps) {\n  const { quickActions, executeAction, context } = useQuickActions();\n  const [isExpanded, setIsExpanded] = useState(false);\n  const [visibleActions, setVisibleActions] = useState<QuickAction[]>([]);\n\n  // Update visible actions based on context\n  useEffect(() => {\n    const defaultActions = getDefaultActions(context);\n    const contextActions = defaultActions.filter(action => {\n      if (action.contexts.includes('*')) return true;\n      return action.contexts.includes(context.currentArea);\n    });\n\n    // Merge with any custom quick actions and sort by priority\n    const allActions = [...contextActions, ...quickActions]\n      .sort((a, b) => a.priority - b.priority)\n      .slice(0, 6); // Limit to 6 actions max\n\n    setVisibleActions(allActions);\n  }, [context.currentArea, context.hasActiveSession, context.isInKnowledgeArea, context.activeChat, quickActions]);\n\n  const getIconComponent = (iconName: string) => {\n    const iconMap: Record<string, any> = {\n      plus: faPlus,\n      upload: faUpload,\n      microphone: faMicrophone,\n      exchange: faExchange,\n      save: faSave,\n      search: faSearch,\n      thumbtack: faThumbtack,\n      trash: faTrash\n    };\n\n    return iconMap[iconName] || faPlus;\n  };\n\n  const handleActionClick = (action: QuickAction) => {\n    executeAction(action.id);\n    setIsExpanded(false);\n  };\n\n  const primaryAction = visibleActions[0];\n  const secondaryActions = visibleActions.slice(1);\n\n  return (\n    <div className={`fixed bottom-6 right-6 z-50 ${className}`}>\n      <div className=\"flex flex-col items-end space-y-3\">\n        {/* Secondary actions (shown when expanded) */}\n        {isExpanded && secondaryActions.length > 0 && (\n          <div className=\"flex flex-col items-end space-y-2 animate-in slide-in-from-bottom-2 duration-200\">\n            {secondaryActions.map((action) => (\n              <button\n                key={action.id}\n                onClick={() => handleActionClick(action)}\n                className=\"group relative flex h-12 w-12 items-center justify-center rounded-full bg-white shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 border border-gray-200 dark:bg-gray-800 dark:border-gray-700\"\n                title={action.label}\n              >\n                <FontAwesomeIcon\n                  icon={getIconComponent(action.icon)}\n                  className=\"h-5 w-5 text-gray-600 dark:text-gray-300\"\n                />\n\n                {/* Tooltip */}\n                <div className=\"absolute right-14 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none\">\n                  <div className=\"bg-gray-900 text-white text-sm px-2 py-1 rounded whitespace-nowrap\">\n                    {action.label}\n                  </div>\n                  <div className=\"absolute left-full top-1/2 -translate-y-1/2 border-4 border-transparent border-l-gray-900\"></div>\n                </div>\n\n                {/* Badge */}\n                {action.badge && (\n                  <div className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\">\n                    {action.badge}\n                  </div>\n                )}\n              </button>\n            ))}\n          </div>\n        )}\n\n        {/* Primary action button */}\n        {primaryAction && (\n          <div className=\"relative\">\n            <button\n              onClick={() => handleActionClick(primaryAction)}\n              className=\"flex h-14 w-14 items-center justify-center rounded-full bg-blue-600 text-white shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 hover:bg-blue-700\"\n              title={primaryAction.label}\n            >\n              <FontAwesomeIcon\n                icon={getIconComponent(primaryAction.icon)}\n                className=\"h-6 w-6\"\n              />\n\n              {/* Badge */}\n              {primaryAction.badge && (\n                <div className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\">\n                  {primaryAction.badge}\n                </div>\n              )}\n            </button>\n\n            {/* Expand/collapse button for secondary actions */}\n            {secondaryActions.length > 0 && (\n              <button\n                onClick={() => setIsExpanded(!isExpanded)}\n                className=\"absolute -top-2 -left-2 flex h-6 w-6 items-center justify-center rounded-full bg-gray-600 text-white shadow-md hover:shadow-lg transition-all duration-200 hover:scale-105\"\n                title={isExpanded ? 'Collapse' : 'More actions'}\n              >\n                <FontAwesomeIcon\n                  icon={faEllipsisV}\n                  className={`h-3 w-3 transition-transform duration-200 ${\n                    isExpanded ? 'rotate-90' : ''\n                  }`}\n                />\n              </button>\n            )}\n          </div>\n        )}\n      </div>\n\n      {/* Backdrop for mobile */}\n      {isExpanded && (\n        <div\n          className=\"fixed inset-0 bg-black bg-opacity-20 md:hidden -z-10\"\n          onClick={() => setIsExpanded(false)}\n        />\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAWA;;;AAfA;;;;;AAsBA,0EAA0E;AAC1E,MAAM,oBAAoB,CAAC;IACvB,MAAM,cAA6B;QACjC;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;gBACN,oCAAoC;gBACpC,QAAQ,GAAG,CAAC;YACd;YACA,UAAU;gBAAC;aAAI;YACf,UAAU;YACV,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;gBACN,gCAAgC;gBAChC,QAAQ,GAAG,CAAC;YACd;YACA,UAAU;gBAAC;aAAI;YACf,UAAU;YACV,UAAU;QACZ;KACD;IAED,2BAA2B;IAC3B,IAAI,QAAQ,WAAW,KAAK,eAAe,QAAQ,iBAAiB,EAAE;QACpE,YAAY,IAAI,CAAC;YACf,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;gBACN,kCAAkC;gBAClC,QAAQ,GAAG,CAAC;YACd;YACA,UAAU;gBAAC;gBAAa;aAAO;YAC/B,UAAU;YACV,UAAU;QACZ;IACF;IAEA,IAAI,QAAQ,WAAW,KAAK,mBAAmB,QAAQ,gBAAgB,EAAE;QACvE,YAAY,IAAI,CACd;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;gBACN,6BAA6B;gBAC7B,QAAQ,GAAG,CAAC;YACd;YACA,UAAU;gBAAC;aAAgB;YAC3B,UAAU;YACV,UAAU;QACZ,GACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;gBACN,kCAAkC;gBAClC,QAAQ,GAAG,CAAC;YACd;YACA,UAAU;gBAAC;aAAgB;YAC3B,UAAU;YACV,UAAU;QACZ,GACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;gBACN,iCAAiC;gBACjC,QAAQ,GAAG,CAAC;YACd;YACA,UAAU;gBAAC;aAAgB;YAC3B,UAAU;YACV,UAAU;QACZ;IAEJ;IAEA,IAAI,QAAQ,UAAU,EAAE;QACtB,YAAY,IAAI,CACd;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;gBACN,+BAA+B;gBAC/B,QAAQ,GAAG,CAAC;YACd;YACA,UAAU;gBAAC;aAAgB;YAC3B,UAAU;YACV,UAAU;QACZ;IAEJ;IAEA,OAAO;AACX;AAEe,SAAS,aAAa,EAAE,YAAY,EAAE,EAAqB;;IACxE,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,kBAAe,AAAD;IAC/D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAEtE,0CAA0C;IAC1C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM,iBAAiB,kBAAkB;YACzC,MAAM,iBAAiB,eAAe,MAAM;yDAAC,CAAA;oBAC3C,IAAI,OAAO,QAAQ,CAAC,QAAQ,CAAC,MAAM,OAAO;oBAC1C,OAAO,OAAO,QAAQ,CAAC,QAAQ,CAAC,QAAQ,WAAW;gBACrD;;YAEA,2DAA2D;YAC3D,MAAM,aAAa;mBAAI;mBAAmB;aAAa,CACpD,IAAI;qDAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;oDACtC,KAAK,CAAC,GAAG,IAAI,yBAAyB;YAEzC,kBAAkB;QACpB;iCAAG;QAAC,QAAQ,WAAW;QAAE,QAAQ,gBAAgB;QAAE,QAAQ,iBAAiB;QAAE,QAAQ,UAAU;QAAE;KAAa;IAE/G,MAAM,mBAAmB,CAAC;QACxB,MAAM,UAA+B;YACnC,MAAM,2KAAA,CAAA,SAAM;YACZ,QAAQ,2KAAA,CAAA,WAAQ;YAChB,YAAY,2KAAA,CAAA,eAAY;YACxB,UAAU,2KAAA,CAAA,aAAU;YACpB,MAAM,2KAAA,CAAA,SAAM;YACZ,QAAQ,2KAAA,CAAA,WAAQ;YAChB,WAAW,2KAAA,CAAA,cAAW;YACtB,OAAO,2KAAA,CAAA,UAAO;QAChB;QAEA,OAAO,OAAO,CAAC,SAAS,IAAI,2KAAA,CAAA,SAAM;IACpC;IAEA,MAAM,oBAAoB,CAAC;QACzB,cAAc,OAAO,EAAE;QACvB,cAAc;IAChB;IAEA,MAAM,gBAAgB,cAAc,CAAC,EAAE;IACvC,MAAM,mBAAmB,eAAe,KAAK,CAAC;IAE9C,qBACE,6LAAC;QAAI,WAAW,CAAC,4BAA4B,EAAE,WAAW;;0BACxD,6LAAC;gBAAI,WAAU;;oBAEZ,cAAc,iBAAiB,MAAM,GAAG,mBACvC,6LAAC;wBAAI,WAAU;kCACZ,iBAAiB,GAAG,CAAC,CAAC,uBACrB,6LAAC;gCAEC,SAAS,IAAM,kBAAkB;gCACjC,WAAU;gCACV,OAAO,OAAO,KAAK;;kDAEnB,6LAAC,uKAAA,CAAA,kBAAe;wCACd,MAAM,iBAAiB,OAAO,IAAI;wCAClC,WAAU;;;;;;kDAIZ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACZ,OAAO,KAAK;;;;;;0DAEf,6LAAC;gDAAI,WAAU;;;;;;;;;;;;oCAIhB,OAAO,KAAK,kBACX,6LAAC;wCAAI,WAAU;kDACZ,OAAO,KAAK;;;;;;;+BArBZ,OAAO,EAAE;;;;;;;;;;oBA8BrB,+BACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,kBAAkB;gCACjC,WAAU;gCACV,OAAO,cAAc,KAAK;;kDAE1B,6LAAC,uKAAA,CAAA,kBAAe;wCACd,MAAM,iBAAiB,cAAc,IAAI;wCACzC,WAAU;;;;;;oCAIX,cAAc,KAAK,kBAClB,6LAAC;wCAAI,WAAU;kDACZ,cAAc,KAAK;;;;;;;;;;;;4BAMzB,iBAAiB,MAAM,GAAG,mBACzB,6LAAC;gCACC,SAAS,IAAM,cAAc,CAAC;gCAC9B,WAAU;gCACV,OAAO,aAAa,aAAa;0CAEjC,cAAA,6LAAC,uKAAA,CAAA,kBAAe;oCACd,MAAM,2KAAA,CAAA,cAAW;oCACjB,WAAW,CAAC,0CAA0C,EACpD,aAAa,cAAc,IAC3B;;;;;;;;;;;;;;;;;;;;;;;YASb,4BACC,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,cAAc;;;;;;;;;;;;AAKvC;GAlIwB;;QAC2B,wIAAA,CAAA,kBAAe;;;KAD1C", "debugId": null}}, {"offset": {"line": 1806, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/components/layout/Layout.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect } from 'react';\nimport Header from './Header';\nimport EnhancedSidebar from '../navigation/EnhancedSidebar';\nimport QuickActions from '../navigation/QuickActions';\nimport { NavigationProvider } from '@/contexts/NavigationContext';\n\nexport default function Layout({ children }: { children: React.ReactNode }) {\n  // We don't need to track sidebar state anymore since it doesn't affect main content width\n  // But we keep the event listeners for other components that might need it\n\n  // Check if sidebar is collapsed on mount and listen for changes\n  useEffect(() => {\n    if (typeof window === 'undefined') return;\n\n    const updateSidebarState = () => {\n      // We no longer need to update state, just dispatch events for other components\n      // This function is kept for consistency with the event listeners\n    };\n\n    // Initial load\n    updateSidebarState();\n\n    // Listen for changes to the sidebar state\n    const handleStorageChange = (e: StorageEvent) => {\n      if (e.key === 'sidebarCollapsed') {\n        updateSidebarState();\n      }\n    };\n\n    // Listen for custom sidebar toggle event\n    const handleSidebarToggle = () => {\n      updateSidebarState();\n    };\n\n    window.addEventListener('storage', handleStorageChange);\n    window.addEventListener('sidebar-toggle', handleSidebarToggle);\n\n    return () => {\n      window.removeEventListener('storage', handleStorageChange);\n      window.removeEventListener('sidebar-toggle', handleSidebarToggle);\n    };\n  }, []);\n\n  // Close sidebar when clicking outside on mobile\n  useEffect(() => {\n    if (typeof window === 'undefined') return;\n\n    const handleClickOutside = (event: MouseEvent) => {\n      const sidebar = document.querySelector('aside');\n      const sidebarButton = document.querySelector('button[aria-label=\"Toggle sidebar\"]');\n      const menuButton = document.querySelector('button[aria-label=\"Toggle menu\"]');\n\n      // Check if we clicked outside the sidebar and both toggle buttons\n      if (\n        sidebar &&\n        !sidebar.contains(event.target as Node) &&\n        sidebarButton &&\n        !sidebarButton.contains(event.target as Node) &&\n        menuButton &&\n        !menuButton.contains(event.target as Node) &&\n        document.documentElement.classList.contains('sidebar-open')\n      ) {\n        document.documentElement.classList.remove('sidebar-open');\n      }\n    };\n\n    // Also close sidebar when clicking on the overlay (the ::before element)\n    const handleOverlayClick = (event: MouseEvent) => {\n      const html = document.documentElement;\n\n      // Check if we clicked on the overlay (not on the sidebar or buttons)\n      if (\n        html.classList.contains('sidebar-open') &&\n        event.target === html\n      ) {\n        html.classList.remove('sidebar-open');\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    document.addEventListener('click', handleOverlayClick, true);\n\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n      document.removeEventListener('click', handleOverlayClick, true);\n    };\n  }, []);\n\n  return (\n    <NavigationProvider>\n      <div className=\"flex min-h-screen flex-col overflow-hidden\">\n        <Header />\n        <div className=\"flex flex-1 pt-16\">\n          <EnhancedSidebar />\n          <main\n            className=\"flex-1 transition-all duration-300 w-full overflow-y-auto h-[calc(100vh-64px)]\"\n          >\n            <div className=\"container mx-auto px-4 py-8 pb-16 md:px-6 lg:px-8 min-h-[calc(100vh-64px)]\">\n              {children}\n            </div>\n          </main>\n          <QuickActions />\n        </div>\n      </div>\n    </NavigationProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAQe,SAAS,OAAO,EAAE,QAAQ,EAAiC;;IACxE,0FAA0F;IAC1F,0EAA0E;IAE1E,gEAAgE;IAChE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,uCAAmC;;YAAM;YAEzC,MAAM;uDAAqB;gBACzB,+EAA+E;gBAC/E,iEAAiE;gBACnE;;YAEA,eAAe;YACf;YAEA,0CAA0C;YAC1C,MAAM;wDAAsB,CAAC;oBAC3B,IAAI,EAAE,GAAG,KAAK,oBAAoB;wBAChC;oBACF;gBACF;;YAEA,yCAAyC;YACzC,MAAM;wDAAsB;oBAC1B;gBACF;;YAEA,OAAO,gBAAgB,CAAC,WAAW;YACnC,OAAO,gBAAgB,CAAC,kBAAkB;YAE1C;oCAAO;oBACL,OAAO,mBAAmB,CAAC,WAAW;oBACtC,OAAO,mBAAmB,CAAC,kBAAkB;gBAC/C;;QACF;2BAAG,EAAE;IAEL,gDAAgD;IAChD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,uCAAmC;;YAAM;YAEzC,MAAM;uDAAqB,CAAC;oBAC1B,MAAM,UAAU,SAAS,aAAa,CAAC;oBACvC,MAAM,gBAAgB,SAAS,aAAa,CAAC;oBAC7C,MAAM,aAAa,SAAS,aAAa,CAAC;oBAE1C,kEAAkE;oBAClE,IACE,WACA,CAAC,QAAQ,QAAQ,CAAC,MAAM,MAAM,KAC9B,iBACA,CAAC,cAAc,QAAQ,CAAC,MAAM,MAAM,KACpC,cACA,CAAC,WAAW,QAAQ,CAAC,MAAM,MAAM,KACjC,SAAS,eAAe,CAAC,SAAS,CAAC,QAAQ,CAAC,iBAC5C;wBACA,SAAS,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC;oBAC5C;gBACF;;YAEA,yEAAyE;YACzE,MAAM;uDAAqB,CAAC;oBAC1B,MAAM,OAAO,SAAS,eAAe;oBAErC,qEAAqE;oBACrE,IACE,KAAK,SAAS,CAAC,QAAQ,CAAC,mBACxB,MAAM,MAAM,KAAK,MACjB;wBACA,KAAK,SAAS,CAAC,MAAM,CAAC;oBACxB;gBACF;;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC,SAAS,gBAAgB,CAAC,SAAS,oBAAoB;YAEvD;oCAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;oBAC1C,SAAS,mBAAmB,CAAC,SAAS,oBAAoB;gBAC5D;;QACF;2BAAG,EAAE;IAEL,qBACE,6LAAC,wIAAA,CAAA,qBAAkB;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,yIAAA,CAAA,UAAM;;;;;8BACP,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,sJAAA,CAAA,UAAe;;;;;sCAChB,6LAAC;4BACC,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;sCAGL,6LAAC,mJAAA,CAAA,UAAY;;;;;;;;;;;;;;;;;;;;;;AAKvB;GApGwB;KAAA", "debugId": null}}, {"offset": {"line": 1970, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/components/layout/ConditionalLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { usePathname } from 'next/navigation';\nimport Layout from './Layout';\n\nexport default function ConditionalLayout({ children }: { children: React.ReactNode }) {\n  const pathname = usePathname();\n  \n  // Check if the current path is an auth route\n  const isAuthRoute = pathname?.startsWith('/auth');\n  \n  // If it's an auth route, render children directly without the main layout\n  if (isAuthRoute) {\n    return <>{children}</>;\n  }\n  \n  // For all other routes, use the main layout with header and sidebar\n  return <Layout>{children}</Layout>;\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;;;AAJA;;;AAMe,SAAS,kBAAkB,EAAE,QAAQ,EAAiC;;IACnF,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,6CAA6C;IAC7C,MAAM,cAAc,UAAU,WAAW;IAEzC,0EAA0E;IAC1E,IAAI,aAAa;QACf,qBAAO;sBAAG;;IACZ;IAEA,oEAAoE;IACpE,qBAAO,6LAAC,yIAAA,CAAA,UAAM;kBAAE;;;;;;AAClB;GAbwB;;QACL,qIAAA,CAAA,cAAW;;;KADN", "debugId": null}}, {"offset": {"line": 2018, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/app/providers.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { AuthProvider } from '@/contexts/AuthContext';\n\nexport default function Providers({ children }: { children: React.ReactNode }) {\n  return (\n    <AuthProvider>{children}</AuthProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKe,SAAS,UAAU,EAAE,QAAQ,EAAiC;IAC3E,qBACE,6LAAC,kIAAA,CAAA,eAAY;kBAAE;;;;;;AAEnB;KAJwB", "debugId": null}}]}