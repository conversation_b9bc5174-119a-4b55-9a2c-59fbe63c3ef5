{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/lib/agents.ts"], "sourcesContent": ["import { Agent, Message, Session } from '@/types/agent';\n\n// Mock data for agents\nconst agents: Agent[] = [\n  {\n    id: '1',\n    name: 'Data Analyst',\n    description: 'Analyze data sets and generate insights with natural language queries.',\n    longDescription: 'The Data Analyst agent helps you explore and understand your data through natural language. Ask questions about your data, request visualizations, or get statistical summaries without writing complex queries or code. This agent can work with various data formats including CSV, Excel, SQL databases, and more.',\n    category: 'Analytics',\n    capabilities: [\n      'Natural language data queries',\n      'Data visualization generation',\n      'Statistical analysis',\n      'Anomaly detection',\n      'Trend identification',\n      'Report generation'\n    ],\n    usageCount: 1245,\n    isNew: false,\n    isFeatured: true,\n    createdAt: '2023-10-15',\n    updatedAt: '2024-03-01',\n    version: '2.3.0',\n    creator: 'Data Science Team',\n    avatarUrl: '/agents/data-analyst.svg',\n    tags: ['data', 'analytics', 'visualization', 'statistics'],\n    relatedAgentIds: ['5', '7', '8']\n  },\n  {\n    id: '2',\n    name: 'Code Assistant',\n    description: 'Help with coding tasks, debugging, and code reviews across multiple languages.',\n    longDescription: 'The Code Assistant agent is your programming partner. It can help write, debug, and optimize code in multiple languages including Python, JavaScript, Java, C++, and more. Ask for code examples, get help with debugging, or request code reviews. The agent can also explain complex code and suggest improvements.',\n    category: 'Development',\n    capabilities: [\n      'Code generation',\n      'Debugging assistance',\n      'Code optimization',\n      'Code explanation',\n      'Multiple language support',\n      'Best practices recommendations'\n    ],\n    usageCount: 3421,\n    isNew: false,\n    isFeatured: true,\n    createdAt: '2023-08-10',\n    updatedAt: '2024-02-15',\n    version: '3.1.0',\n    creator: 'Engineering Team',\n    avatarUrl: '/agents/code-assistant.svg',\n    tags: ['coding', 'programming', 'development', 'debugging'],\n    relatedAgentIds: ['9', '10']\n  },\n  {\n    id: '3',\n    name: 'Research Companion',\n    description: 'Find, summarize, and organize research papers and articles.',\n    longDescription: 'The Research Companion agent helps you stay on top of the latest research in your field. It can find relevant papers, summarize key findings, and help you organize your research materials. Ask for papers on specific topics, get summaries of complex articles, or request literature reviews.',\n    category: 'Research',\n    capabilities: [\n      'Research paper search',\n      'Article summarization',\n      'Literature review assistance',\n      'Citation generation',\n      'Research organization',\n      'Key findings extraction'\n    ],\n    usageCount: 876,\n    isNew: true,\n    isFeatured: true,\n    createdAt: '2024-01-20',\n    updatedAt: '2024-03-10',\n    version: '1.2.0',\n    creator: 'Research & Development',\n    avatarUrl: '/agents/research-companion.svg',\n    tags: ['research', 'papers', 'academic', 'literature'],\n    relatedAgentIds: ['11', '5']\n  },\n  {\n    id: '4',\n    name: 'Meeting Summarizer',\n    description: 'Generate concise summaries and action items from meeting transcripts.',\n    longDescription: 'The Meeting Summarizer agent helps you capture the important points from your meetings. Upload a transcript or recording, and the agent will identify key discussion points, decisions made, and action items assigned. It can also generate meeting minutes in various formats.',\n    category: 'Productivity',\n    capabilities: [\n      'Meeting transcript analysis',\n      'Key point extraction',\n      'Action item identification',\n      'Decision tracking',\n      'Meeting minutes generation',\n      'Follow-up reminder creation'\n    ],\n    usageCount: 2134,\n    isNew: false,\n    isFeatured: true,\n    createdAt: '2023-11-05',\n    updatedAt: '2024-02-28',\n    version: '2.0.1',\n    creator: 'Productivity Team',\n    avatarUrl: '/agents/meeting-summarizer.svg',\n    tags: ['meetings', 'productivity', 'transcription', 'summaries'],\n    relatedAgentIds: ['5', '6', '12']\n  },\n  {\n    id: '5',\n    name: 'Document Analyzer',\n    description: 'Extract key information from documents and generate summaries.',\n    longDescription: 'The Document Analyzer agent helps you process and understand documents quickly. Upload any document (PDF, Word, etc.), and the agent will extract key information, generate summaries, and answer questions about the content. It can handle contracts, reports, articles, and more.',\n    category: 'Productivity',\n    capabilities: [\n      'Document parsing',\n      'Key information extraction',\n      'Summary generation',\n      'Question answering',\n      'Multiple format support',\n      'Entity recognition'\n    ],\n    usageCount: 567,\n    isNew: false,\n    isFeatured: false,\n    createdAt: '2023-09-12',\n    updatedAt: '2024-01-30',\n    version: '1.5.2',\n    creator: 'Content Team',\n    avatarUrl: '/agents/document-analyzer.svg',\n    tags: ['documents', 'analysis', 'extraction', 'summaries'],\n    relatedAgentIds: ['1', '3', '4']\n  },\n  {\n    id: '6',\n    name: 'Presentation Creator',\n    description: 'Generate professional presentations from outlines or topics.',\n    longDescription: 'The Presentation Creator agent helps you build compelling presentations quickly. Provide a topic or outline, and the agent will generate slide content, suggest visuals, and help you structure your presentation for maximum impact. It can create presentations for various purposes including business, education, and sales.',\n    category: 'Productivity',\n    capabilities: [\n      'Slide content generation',\n      'Presentation structure suggestions',\n      'Visual element recommendations',\n      'Talking points creation',\n      'Multiple template support',\n      'Export to PowerPoint/Google Slides'\n    ],\n    usageCount: 321,\n    isNew: true,\n    isFeatured: false,\n    createdAt: '2024-02-01',\n    updatedAt: '2024-03-15',\n    version: '1.0.0',\n    creator: 'Marketing Team',\n    avatarUrl: '/agents/presentation-creator.svg',\n    tags: ['presentations', 'slides', 'design', 'content'],\n    relatedAgentIds: ['4', '13']\n  },\n  {\n    id: '7',\n    name: 'Data Visualization Expert',\n    description: 'Create beautiful and informative data visualizations from your datasets.',\n    longDescription: 'The Data Visualization Expert agent helps you transform raw data into compelling visual stories. Upload your data and describe what you want to highlight, and the agent will generate appropriate charts, graphs, and interactive visualizations that make your data insights clear and impactful.',\n    category: 'Analytics',\n    capabilities: [\n      'Chart and graph generation',\n      'Interactive visualization creation',\n      'Color palette optimization',\n      'Data storytelling assistance',\n      'Multiple export formats',\n      'Accessibility considerations'\n    ],\n    usageCount: 892,\n    isNew: false,\n    isFeatured: false,\n    createdAt: '2023-11-18',\n    updatedAt: '2024-02-10',\n    version: '1.8.0',\n    creator: 'Data Science Team',\n    avatarUrl: '/agents/data-analyst.svg',\n    tags: ['visualization', 'charts', 'graphs', 'data'],\n    relatedAgentIds: ['1', '8']\n  },\n  {\n    id: '8',\n    name: 'Predictive Analytics Agent',\n    description: 'Forecast trends and make predictions based on historical data.',\n    longDescription: 'The Predictive Analytics Agent uses machine learning algorithms to analyze your historical data and generate forecasts and predictions. It can identify patterns, trends, and anomalies to help you make data-driven decisions about future outcomes.',\n    category: 'Analytics',\n    capabilities: [\n      'Time series forecasting',\n      'Trend analysis',\n      'Anomaly detection',\n      'Predictive modeling',\n      'Scenario planning',\n      'Confidence interval calculation'\n    ],\n    usageCount: 754,\n    isNew: true,\n    isFeatured: false,\n    createdAt: '2024-01-05',\n    updatedAt: '2024-03-20',\n    version: '1.2.1',\n    creator: 'Data Science Team',\n    avatarUrl: '/agents/data-analyst.svg',\n    tags: ['predictions', 'forecasting', 'analytics', 'trends'],\n    relatedAgentIds: ['1', '7']\n  },\n  {\n    id: '9',\n    name: 'API Documentation Generator',\n    description: 'Automatically generate comprehensive API documentation from your code.',\n    longDescription: 'The API Documentation Generator analyzes your API code and generates clear, comprehensive documentation. It can create reference docs, guides, examples, and interactive API explorers to help developers understand and use your APIs effectively.',\n    category: 'Development',\n    capabilities: [\n      'API reference generation',\n      'Code example creation',\n      'Interactive API explorer',\n      'Multiple format support',\n      'Versioning assistance',\n      'Consistency checking'\n    ],\n    usageCount: 623,\n    isNew: false,\n    isFeatured: false,\n    createdAt: '2023-10-08',\n    updatedAt: '2024-02-12',\n    version: '2.1.0',\n    creator: 'Engineering Team',\n    avatarUrl: '/agents/code-assistant.svg',\n    tags: ['documentation', 'API', 'development', 'reference'],\n    relatedAgentIds: ['2', '10']\n  },\n  {\n    id: '10',\n    name: 'Code Reviewer',\n    description: 'Get detailed code reviews with suggestions for improvements and best practices.',\n    longDescription: 'The Code Reviewer agent analyzes your code for bugs, security issues, performance problems, and adherence to best practices. It provides detailed feedback with specific suggestions for improvements and explanations of potential issues.',\n    category: 'Development',\n    capabilities: [\n      'Bug detection',\n      'Security vulnerability scanning',\n      'Performance optimization',\n      'Best practice enforcement',\n      'Code style consistency',\n      'Refactoring suggestions'\n    ],\n    usageCount: 1876,\n    isNew: false,\n    isFeatured: false,\n    createdAt: '2023-09-20',\n    updatedAt: '2024-03-05',\n    version: '2.4.0',\n    creator: 'Engineering Team',\n    avatarUrl: '/agents/code-assistant.svg',\n    tags: ['code review', 'quality', 'security', 'best practices'],\n    relatedAgentIds: ['2', '9']\n  },\n  {\n    id: '11',\n    name: 'Literature Review Assistant',\n    description: 'Compile comprehensive literature reviews on any research topic.',\n    longDescription: 'The Literature Review Assistant helps researchers compile thorough literature reviews by finding relevant papers, organizing them by themes, identifying key findings, and highlighting research gaps. It can save weeks of manual research work.',\n    category: 'Research',\n    capabilities: [\n      'Research paper discovery',\n      'Thematic organization',\n      'Gap analysis',\n      'Citation management',\n      'Summary generation',\n      'Trend identification'\n    ],\n    usageCount: 542,\n    isNew: true,\n    isFeatured: false,\n    createdAt: '2024-02-15',\n    updatedAt: '2024-03-18',\n    version: '1.0.2',\n    creator: 'Research & Development',\n    avatarUrl: '/agents/research-companion.svg',\n    tags: ['literature review', 'research', 'academic', 'papers'],\n    relatedAgentIds: ['3']\n  },\n  {\n    id: '12',\n    name: 'Project Manager Assistant',\n    description: 'Track projects, manage tasks, and coordinate team activities efficiently.',\n    longDescription: 'The Project Manager Assistant helps you plan, track, and manage projects from start to finish. It can create project timelines, assign and track tasks, identify risks, generate status reports, and facilitate team communication to keep your projects on track.',\n    category: 'Productivity',\n    capabilities: [\n      'Project planning',\n      'Task management',\n      'Timeline creation',\n      'Risk assessment',\n      'Status reporting',\n      'Resource allocation'\n    ],\n    usageCount: 1432,\n    isNew: false,\n    isFeatured: false,\n    createdAt: '2023-08-25',\n    updatedAt: '2024-02-20',\n    version: '2.2.0',\n    creator: 'Productivity Team',\n    avatarUrl: '/agents/meeting-summarizer.svg',\n    tags: ['project management', 'tasks', 'planning', 'coordination'],\n    relatedAgentIds: ['4', '13']\n  },\n  {\n    id: '13',\n    name: 'Content Creator',\n    description: 'Generate high-quality content for blogs, social media, and marketing materials.',\n    longDescription: 'The Content Creator agent helps you produce engaging content for various platforms. Provide a topic and target audience, and it will generate blog posts, social media updates, marketing copy, and more, tailored to your brand voice and content strategy.',\n    category: 'Marketing',\n    capabilities: [\n      'Blog post generation',\n      'Social media content creation',\n      'Marketing copy writing',\n      'SEO optimization',\n      'Brand voice consistency',\n      'Content strategy alignment'\n    ],\n    usageCount: 2187,\n    isNew: false,\n    isFeatured: false,\n    createdAt: '2023-07-12',\n    updatedAt: '2024-03-08',\n    version: '3.0.1',\n    creator: 'Marketing Team',\n    avatarUrl: '/agents/presentation-creator.svg',\n    tags: ['content', 'writing', 'marketing', 'social media'],\n    relatedAgentIds: ['6', '12']\n  }\n];\n\n// Mock data for sessions\nconst sessions: Session[] = [\n  {\n    id: 'session1',\n    agentId: '2',\n    title: 'JavaScript Debugging Help',\n    messages: [\n      {\n        id: 'msg1',\n        content: 'Hello! How can I help you with coding today?',\n        role: 'assistant',\n        timestamp: Date.now() - 3600000\n      },\n      {\n        id: 'msg2',\n        content: 'I have a bug in my JavaScript code. The event listener is not working.',\n        role: 'user',\n        timestamp: Date.now() - 3500000\n      },\n      {\n        id: 'msg3',\n        content: 'Let\\'s take a look. Can you share the code that\\'s not working?',\n        role: 'assistant',\n        timestamp: Date.now() - 3400000\n      }\n    ],\n    createdAt: Date.now() - 3600000,\n    updatedAt: Date.now() - 3400000,\n    isSaved: true\n  },\n  {\n    id: 'session2',\n    agentId: '1',\n    title: 'Sales Data Analysis',\n    messages: [\n      {\n        id: 'msg1',\n        content: 'Welcome to Data Analyst. What data would you like to analyze today?',\n        role: 'assistant',\n        timestamp: Date.now() - 86400000\n      },\n      {\n        id: 'msg2',\n        content: 'I need to analyze our Q1 sales data to find trends.',\n        role: 'user',\n        timestamp: Date.now() - 86300000\n      },\n      {\n        id: 'msg3',\n        content: 'I can help with that. Do you have the sales data file you can upload?',\n        role: 'assistant',\n        timestamp: Date.now() - 86200000\n      }\n    ],\n    createdAt: Date.now() - 86400000,\n    updatedAt: Date.now() - 86200000,\n    isSaved: true\n  }\n];\n\n// Function to get all agents\nexport function getAllAgents(): Agent[] {\n  return agents;\n}\n\n// Function to get an agent by ID\nexport function getAgentById(id: string): Agent | undefined {\n  return agents.find(agent => agent.id === id);\n}\n\n// Function to get featured agents\nexport function getFeaturedAgents(): Agent[] {\n  return agents.filter(agent => agent.isFeatured);\n}\n\n// Function to get new agents\nexport function getNewAgents(): Agent[] {\n  return agents.filter(agent => agent.isNew);\n}\n\n// Function to get agents by category\nexport function getAgentsByCategory(category: string): Agent[] {\n  return agents.filter(agent => agent.category === category);\n}\n\n// Function to get agents by tag\nexport function getAgentsByTag(tag: string): Agent[] {\n  return agents.filter(agent => agent.tags?.includes(tag));\n}\n\n// Function to get all unique categories\nexport function getAllCategories(): string[] {\n  const categories = new Set<string>();\n  agents.forEach(agent => categories.add(agent.category));\n  return Array.from(categories).sort();\n}\n\n// Function to get all unique tags\nexport function getAllTags(): string[] {\n  const tags = new Set<string>();\n  agents.forEach(agent => {\n    agent.tags?.forEach(tag => tags.add(tag));\n  });\n  return Array.from(tags).sort();\n}\n\n// Function to get related agents\nexport function getRelatedAgents(agentId: string): Agent[] {\n  const agent = getAgentById(agentId);\n  if (!agent || !agent.relatedAgentIds || agent.relatedAgentIds.length === 0) {\n    return [];\n  }\n\n  return agent.relatedAgentIds\n    .map(id => getAgentById(id))\n    .filter((agent): agent is Agent => agent !== undefined);\n}\n\n// Function to get agents by IDs\nexport function getAgentsByIds(ids: string[]): Agent[] {\n  return ids\n    .map(id => getAgentById(id))\n    .filter((agent): agent is Agent => agent !== undefined);\n}\n\n// Function to search agents\nexport function searchAgents(query: string): Agent[] {\n  if (!query) return agents;\n\n  const lowercaseQuery = query.toLowerCase();\n  return agents.filter(agent =>\n    agent.name.toLowerCase().includes(lowercaseQuery) ||\n    agent.description.toLowerCase().includes(lowercaseQuery) ||\n    agent.category.toLowerCase().includes(lowercaseQuery) ||\n    agent.tags?.some(tag => tag.toLowerCase().includes(lowercaseQuery)) ||\n    agent.capabilities.some(capability => capability.toLowerCase().includes(lowercaseQuery))\n  );\n}\n\n// Function to get sessions for an agent\nexport function getSessionsForAgent(agentId: string): Session[] {\n  return sessions.filter(session => session.agentId === agentId);\n}\n\n// Function to get a session by ID\nexport function getSessionById(sessionId: string): Session | undefined {\n  return sessions.find(session => session.id === sessionId);\n}\n\n// Function to create a new message\nexport function createMessage(content: string, role: 'user' | 'assistant' | 'system'): Message {\n  return {\n    id: `msg_${Date.now()}`,\n    content,\n    role,\n    timestamp: Date.now()\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAEA,uBAAuB;AACvB,MAAM,SAAkB;IACtB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAQ;YAAa;YAAiB;SAAa;QAC1D,iBAAiB;YAAC;YAAK;YAAK;SAAI;IAClC;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAU;YAAe;YAAe;SAAY;QAC3D,iBAAiB;YAAC;YAAK;SAAK;IAC9B;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAY;YAAU;YAAY;SAAa;QACtD,iBAAiB;YAAC;YAAM;SAAI;IAC9B;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAY;YAAgB;YAAiB;SAAY;QAChE,iBAAiB;YAAC;YAAK;YAAK;SAAK;IACnC;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAa;YAAY;YAAc;SAAY;QAC1D,iBAAiB;YAAC;YAAK;YAAK;SAAI;IAClC;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAiB;YAAU;YAAU;SAAU;QACtD,iBAAiB;YAAC;YAAK;SAAK;IAC9B;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAiB;YAAU;YAAU;SAAO;QACnD,iBAAiB;YAAC;YAAK;SAAI;IAC7B;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAe;YAAe;YAAa;SAAS;QAC3D,iBAAiB;YAAC;YAAK;SAAI;IAC7B;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAiB;YAAO;YAAe;SAAY;QAC1D,iBAAiB;YAAC;YAAK;SAAK;IAC9B;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAe;YAAW;YAAY;SAAiB;QAC9D,iBAAiB;YAAC;YAAK;SAAI;IAC7B;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAqB;YAAY;YAAY;SAAS;QAC7D,iBAAiB;YAAC;SAAI;IACxB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAsB;YAAS;YAAY;SAAe;QACjE,iBAAiB;YAAC;YAAK;SAAK;IAC9B;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAW;YAAW;YAAa;SAAe;QACzD,iBAAiB;YAAC;YAAK;SAAK;IAC9B;CACD;AAED,yBAAyB;AACzB,MAAM,WAAsB;IAC1B;QACE,IAAI;QACJ,SAAS;QACT,OAAO;QACP,UAAU;YACR;gBACE,IAAI;gBACJ,SAAS;gBACT,MAAM;gBACN,WAAW,KAAK,GAAG,KAAK;YAC1B;YACA;gBACE,IAAI;gBACJ,SAAS;gBACT,MAAM;gBACN,WAAW,KAAK,GAAG,KAAK;YAC1B;YACA;gBACE,IAAI;gBACJ,SAAS;gBACT,MAAM;gBACN,WAAW,KAAK,GAAG,KAAK;YAC1B;SACD;QACD,WAAW,KAAK,GAAG,KAAK;QACxB,WAAW,KAAK,GAAG,KAAK;QACxB,SAAS;IACX;IACA;QACE,IAAI;QACJ,SAAS;QACT,OAAO;QACP,UAAU;YACR;gBACE,IAAI;gBACJ,SAAS;gBACT,MAAM;gBACN,WAAW,KAAK,GAAG,KAAK;YAC1B;YACA;gBACE,IAAI;gBACJ,SAAS;gBACT,MAAM;gBACN,WAAW,KAAK,GAAG,KAAK;YAC1B;YACA;gBACE,IAAI;gBACJ,SAAS;gBACT,MAAM;gBACN,WAAW,KAAK,GAAG,KAAK;YAC1B;SACD;QACD,WAAW,KAAK,GAAG,KAAK;QACxB,WAAW,KAAK,GAAG,KAAK;QACxB,SAAS;IACX;CACD;AAGM,SAAS;IACd,OAAO;AACT;AAGO,SAAS,aAAa,EAAU;IACrC,OAAO,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;AAC3C;AAGO,SAAS;IACd,OAAO,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,UAAU;AAChD;AAGO,SAAS;IACd,OAAO,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,KAAK;AAC3C;AAGO,SAAS,oBAAoB,QAAgB;IAClD,OAAO,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,QAAQ,KAAK;AACnD;AAGO,SAAS,eAAe,GAAW;IACxC,OAAO,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,IAAI,EAAE,SAAS;AACrD;AAGO,SAAS;IACd,MAAM,aAAa,IAAI;IACvB,OAAO,OAAO,CAAC,CAAA,QAAS,WAAW,GAAG,CAAC,MAAM,QAAQ;IACrD,OAAO,MAAM,IAAI,CAAC,YAAY,IAAI;AACpC;AAGO,SAAS;IACd,MAAM,OAAO,IAAI;IACjB,OAAO,OAAO,CAAC,CAAA;QACb,MAAM,IAAI,EAAE,QAAQ,CAAA,MAAO,KAAK,GAAG,CAAC;IACtC;IACA,OAAO,MAAM,IAAI,CAAC,MAAM,IAAI;AAC9B;AAGO,SAAS,iBAAiB,OAAe;IAC9C,MAAM,QAAQ,aAAa;IAC3B,IAAI,CAAC,SAAS,CAAC,MAAM,eAAe,IAAI,MAAM,eAAe,CAAC,MAAM,KAAK,GAAG;QAC1E,OAAO,EAAE;IACX;IAEA,OAAO,MAAM,eAAe,CACzB,GAAG,CAAC,CAAA,KAAM,aAAa,KACvB,MAAM,CAAC,CAAC,QAA0B,UAAU;AACjD;AAGO,SAAS,eAAe,GAAa;IAC1C,OAAO,IACJ,GAAG,CAAC,CAAA,KAAM,aAAa,KACvB,MAAM,CAAC,CAAC,QAA0B,UAAU;AACjD;AAGO,SAAS,aAAa,KAAa;IACxC,IAAI,CAAC,OAAO,OAAO;IAEnB,MAAM,iBAAiB,MAAM,WAAW;IACxC,OAAO,OAAO,MAAM,CAAC,CAAA,QACnB,MAAM,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,mBAClC,MAAM,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,mBACzC,MAAM,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,mBACtC,MAAM,IAAI,EAAE,KAAK,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC,oBACnD,MAAM,YAAY,CAAC,IAAI,CAAC,CAAA,aAAc,WAAW,WAAW,GAAG,QAAQ,CAAC;AAE5E;AAGO,SAAS,oBAAoB,OAAe;IACjD,OAAO,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,OAAO,KAAK;AACxD;AAGO,SAAS,eAAe,SAAiB;IAC9C,OAAO,SAAS,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;AACjD;AAGO,SAAS,cAAc,OAAe,EAAE,IAAqC;IAClF,OAAO;QACL,IAAI,CAAC,IAAI,EAAE,KAAK,GAAG,IAAI;QACvB;QACA;QACA,WAAW,KAAK,GAAG;IACrB;AACF", "debugId": null}}, {"offset": {"line": 584, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/lib/metrics.ts"], "sourcesContent": ["'use client';\n\nimport { getAllAgents } from './agents';\nimport { \n  UsageMetric, \n  UsageOverTime, \n  CategoryDistribution, \n  UserFeedback, \n  UsageByTime, \n  UsageByDay, \n  TopQuery, \n  AgentComparison,\n  AgentMetrics,\n  TimeRange\n} from '@/types/metrics';\n\n// Generate random number between min and max\nconst randomNumber = (min: number, max: number): number => {\n  return Math.floor(Math.random() * (max - min + 1)) + min;\n};\n\n// Generate random decimal between min and max with specified precision\nconst randomDecimal = (min: number, max: number, precision: number = 2): number => {\n  const value = Math.random() * (max - min) + min;\n  return Number(value.toFixed(precision));\n};\n\n// Generate usage metrics for an agent\nexport const generateUsageMetric = (agentId: string, popularity: number): UsageMetric => {\n  // Scale usage based on popularity (0-100)\n  const baseUses = popularity * 100;\n  const variability = baseUses * 0.2; // 20% variability\n  \n  return {\n    agentId,\n    totalUses: randomNumber(baseUses - variability, baseUses + variability),\n    averageRating: randomDecimal(3.5, 4.9, 1),\n    completionRate: randomDecimal(0.75, 0.98, 2),\n    averageSessionDuration: randomNumber(60, 600), // 1-10 minutes\n    popularityScore: popularity\n  };\n};\n\n// Generate usage over time data\nexport const generateUsageOverTime = (baseUses: number, days: number): UsageOverTime[] => {\n  const data: UsageOverTime[] = [];\n  const today = new Date();\n  \n  for (let i = days - 1; i >= 0; i--) {\n    const date = new Date(today);\n    date.setDate(date.getDate() - i);\n    \n    // Create some patterns in the data\n    let modifier = 1;\n    \n    // Weekend dip\n    const dayOfWeek = date.getDay();\n    if (dayOfWeek === 0 || dayOfWeek === 6) {\n      modifier = 0.7; // Less usage on weekends\n    }\n    \n    // Gradual growth trend\n    const trendFactor = 1 + (i / days) * 0.5;\n    \n    // Random daily fluctuation\n    const fluctuation = randomDecimal(0.8, 1.2);\n    \n    const dailyUses = Math.round((baseUses / days) * modifier * fluctuation * trendFactor);\n    \n    data.push({\n      date: date.toISOString().split('T')[0], // YYYY-MM-DD\n      count: dailyUses\n    });\n  }\n  \n  return data;\n};\n\n// Generate user feedback distribution\nexport const generateUserFeedback = (): UserFeedback[] => {\n  // Most agents should have good ratings\n  const baseDistribution = [\n    { rating: 1, weight: 5 },\n    { rating: 2, weight: 10 },\n    { rating: 3, weight: 20 },\n    { rating: 4, weight: 35 },\n    { rating: 5, weight: 30 }\n  ];\n  \n  const totalWeight = baseDistribution.reduce((sum, item) => sum + item.weight, 0);\n  const totalRatings = randomNumber(50, 500);\n  \n  return baseDistribution.map(item => {\n    const percentage = item.weight / totalWeight;\n    return {\n      rating: item.rating,\n      count: Math.round(totalRatings * percentage)\n    };\n  });\n};\n\n// Generate usage by time of day\nexport const generateUsageByTime = (): UsageByTime[] => {\n  const data: UsageByTime[] = [];\n  \n  for (let hour = 0; hour < 24; hour++) {\n    // Create a realistic distribution with peak during work hours\n    let baseFactor = 1;\n    \n    // Early morning (low usage)\n    if (hour >= 0 && hour < 6) {\n      baseFactor = 0.2;\n    }\n    // Morning ramp-up\n    else if (hour >= 6 && hour < 9) {\n      baseFactor = 0.5 + (hour - 6) * 0.25;\n    }\n    // Work hours (peak usage)\n    else if (hour >= 9 && hour < 17) {\n      baseFactor = 1.0;\n    }\n    // Evening wind-down\n    else if (hour >= 17 && hour < 22) {\n      baseFactor = 1.0 - (hour - 17) * 0.15;\n    }\n    // Late night (low usage)\n    else {\n      baseFactor = 0.3;\n    }\n    \n    // Add some randomness\n    const factor = baseFactor * randomDecimal(0.8, 1.2);\n    \n    data.push({\n      hour,\n      count: Math.round(100 * factor)\n    });\n  }\n  \n  return data;\n};\n\n// Generate usage by day of week\nexport const generateUsageByDay = (): UsageByDay[] => {\n  const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];\n  \n  return days.map(day => {\n    // Weekdays have higher usage than weekends\n    const isWeekend = day === 'Saturday' || day === 'Sunday';\n    const baseFactor = isWeekend ? 0.6 : 1.0;\n    \n    // Wednesday and Thursday slightly higher\n    const dayFactor = (day === 'Wednesday' || day === 'Thursday') ? 1.1 : 1.0;\n    \n    // Add some randomness\n    const factor = baseFactor * dayFactor * randomDecimal(0.9, 1.1);\n    \n    return {\n      day,\n      count: Math.round(100 * factor)\n    };\n  });\n};\n\n// Generate top queries\nexport const generateTopQueries = (agentId: string): TopQuery[] => {\n  const dataQueries = [\n    'How to analyze sales data',\n    'Generate monthly report',\n    'Forecast Q3 revenue',\n    'Compare year-over-year growth',\n    'Find outliers in dataset',\n    'Visualize customer demographics',\n    'Calculate profit margins'\n  ];\n  \n  const codeQueries = [\n    'Debug React useEffect',\n    'Optimize SQL query',\n    'Convert JSON to CSV',\n    'Fix memory leak',\n    'Write unit test for API',\n    'Create Docker container',\n    'Implement authentication'\n  ];\n  \n  const researchQueries = [\n    'Summarize latest AI papers',\n    'Find studies on climate change',\n    'Compare research methodologies',\n    'Generate literature review',\n    'Extract key findings',\n    'Organize research notes',\n    'Find citation for paper'\n  ];\n  \n  const meetingQueries = [\n    'Summarize team meeting',\n    'Extract action items',\n    'Generate meeting minutes',\n    'Identify key decisions',\n    'Create follow-up tasks',\n    'Analyze meeting sentiment',\n    'Schedule follow-up meeting'\n  ];\n  \n  const documentQueries = [\n    'Extract data from PDF',\n    'Summarize legal document',\n    'Find key contract terms',\n    'Compare document versions',\n    'Generate document outline',\n    'Extract contact information',\n    'Identify document type'\n  ];\n  \n  const presentationQueries = [\n    'Create sales pitch deck',\n    'Design executive summary',\n    'Generate presentation outline',\n    'Create data visualizations',\n    'Improve slide design',\n    'Add speaker notes',\n    'Create product demo slides'\n  ];\n  \n  // Select appropriate queries based on agent ID\n  let queries: string[] = [];\n  \n  switch (agentId) {\n    case '1': // Data Analyst\n      queries = dataQueries;\n      break;\n    case '2': // Code Assistant\n      queries = codeQueries;\n      break;\n    case '3': // Research Companion\n      queries = researchQueries;\n      break;\n    case '4': // Meeting Summarizer\n      queries = meetingQueries;\n      break;\n    case '5': // Document Analyzer\n      queries = documentQueries;\n      break;\n    case '6': // Presentation Creator\n      queries = presentationQueries;\n      break;\n    default:\n      // Mix of all queries for other agents\n      queries = [\n        ...dataQueries.slice(0, 2),\n        ...codeQueries.slice(0, 2),\n        ...researchQueries.slice(0, 2),\n        ...meetingQueries.slice(0, 1)\n      ];\n  }\n  \n  // Randomize counts\n  return queries.map(query => ({\n    query,\n    count: randomNumber(10, 100)\n  }))\n  .sort((a, b) => b.count - a.count) // Sort by count descending\n  .slice(0, 5); // Take top 5\n};\n\n// Generate metrics for a specific agent\nexport const generateAgentMetrics = (agentId: string): AgentMetrics => {\n  const agent = getAllAgents().find(a => a.id === agentId);\n  \n  if (!agent) {\n    throw new Error(`Agent with ID ${agentId} not found`);\n  }\n  \n  // Base popularity on usage count\n  const popularity = Math.min(100, agent.usageCount / 50);\n  \n  const usageMetrics = generateUsageMetric(agentId, popularity);\n  \n  return {\n    id: agent.id,\n    name: agent.name,\n    usageMetrics,\n    usageOverTime: generateUsageOverTime(usageMetrics.totalUses, 30), // Last 30 days\n    userFeedback: generateUserFeedback(),\n    usageByTime: generateUsageByTime(),\n    usageByDay: generateUsageByDay(),\n    topQueries: generateTopQueries(agentId)\n  };\n};\n\n// Get metrics for all agents\nexport const getAllAgentMetrics = (): AgentMetrics[] => {\n  return getAllAgents().map(agent => generateAgentMetrics(agent.id));\n};\n\n// Get top agents by a specific metric\nexport const getTopAgentsByMetric = (metric: keyof UsageMetric, limit: number = 5): AgentComparison[] => {\n  const allMetrics = getAllAgentMetrics();\n  \n  return allMetrics\n    .map(agentMetric => ({\n      agentId: agentMetric.id,\n      metric,\n      value: agentMetric.usageMetrics[metric] as number\n    }))\n    .sort((a, b) => b.value - a.value)\n    .slice(0, limit);\n};\n\n// Get category distribution\nexport const getCategoryDistribution = (): CategoryDistribution[] => {\n  const agents = getAllAgents();\n  const categories: Record<string, number> = {};\n  \n  // Count agents by category\n  agents.forEach(agent => {\n    if (categories[agent.category]) {\n      categories[agent.category]++;\n    } else {\n      categories[agent.category] = 1;\n    }\n  });\n  \n  // Calculate total\n  const total = Object.values(categories).reduce((sum, count) => sum + count, 0);\n  \n  // Convert to array with percentages\n  return Object.entries(categories).map(([category, count]) => ({\n    category,\n    count,\n    percentage: (count / total) * 100\n  }))\n  .sort((a, b) => b.count - a.count); // Sort by count descending\n};\n\n// Get overall platform usage over time\nexport const getPlatformUsageOverTime = (days: number): UsageOverTime[] => {\n  const allMetrics = getAllAgentMetrics();\n  const totalUses = allMetrics.reduce((sum, agent) => sum + agent.usageMetrics.totalUses, 0);\n  \n  return generateUsageOverTime(totalUses, days);\n};\n\n// Get popular agents\nexport const getPopularAgents = (limit: number = 5): AgentMetrics[] => {\n  const allMetrics = getAllAgentMetrics();\n  \n  return allMetrics\n    .sort((a, b) => b.usageMetrics.popularityScore - a.usageMetrics.popularityScore)\n    .slice(0, limit);\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAEA;AAFA;;AAgBA,6CAA6C;AAC7C,MAAM,eAAe,CAAC,KAAa;IACjC,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,MAAM,MAAM,CAAC,KAAK;AACvD;AAEA,uEAAuE;AACvE,MAAM,gBAAgB,CAAC,KAAa,KAAa,YAAoB,CAAC;IACpE,MAAM,QAAQ,KAAK,MAAM,KAAK,CAAC,MAAM,GAAG,IAAI;IAC5C,OAAO,OAAO,MAAM,OAAO,CAAC;AAC9B;AAGO,MAAM,sBAAsB,CAAC,SAAiB;IACnD,0CAA0C;IAC1C,MAAM,WAAW,aAAa;IAC9B,MAAM,cAAc,WAAW,KAAK,kBAAkB;IAEtD,OAAO;QACL;QACA,WAAW,aAAa,WAAW,aAAa,WAAW;QAC3D,eAAe,cAAc,KAAK,KAAK;QACvC,gBAAgB,cAAc,MAAM,MAAM;QAC1C,wBAAwB,aAAa,IAAI;QACzC,iBAAiB;IACnB;AACF;AAGO,MAAM,wBAAwB,CAAC,UAAkB;IACtD,MAAM,OAAwB,EAAE;IAChC,MAAM,QAAQ,IAAI;IAElB,IAAK,IAAI,IAAI,OAAO,GAAG,KAAK,GAAG,IAAK;QAClC,MAAM,OAAO,IAAI,KAAK;QACtB,KAAK,OAAO,CAAC,KAAK,OAAO,KAAK;QAE9B,mCAAmC;QACnC,IAAI,WAAW;QAEf,cAAc;QACd,MAAM,YAAY,KAAK,MAAM;QAC7B,IAAI,cAAc,KAAK,cAAc,GAAG;YACtC,WAAW,KAAK,yBAAyB;QAC3C;QAEA,uBAAuB;QACvB,MAAM,cAAc,IAAI,AAAC,IAAI,OAAQ;QAErC,2BAA2B;QAC3B,MAAM,cAAc,cAAc,KAAK;QAEvC,MAAM,YAAY,KAAK,KAAK,CAAC,AAAC,WAAW,OAAQ,WAAW,cAAc;QAE1E,KAAK,IAAI,CAAC;YACR,MAAM,KAAK,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YACtC,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAGO,MAAM,uBAAuB;IAClC,uCAAuC;IACvC,MAAM,mBAAmB;QACvB;YAAE,QAAQ;YAAG,QAAQ;QAAE;QACvB;YAAE,QAAQ;YAAG,QAAQ;QAAG;QACxB;YAAE,QAAQ;YAAG,QAAQ;QAAG;QACxB;YAAE,QAAQ;YAAG,QAAQ;QAAG;QACxB;YAAE,QAAQ;YAAG,QAAQ;QAAG;KACzB;IAED,MAAM,cAAc,iBAAiB,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,MAAM,EAAE;IAC9E,MAAM,eAAe,aAAa,IAAI;IAEtC,OAAO,iBAAiB,GAAG,CAAC,CAAA;QAC1B,MAAM,aAAa,KAAK,MAAM,GAAG;QACjC,OAAO;YACL,QAAQ,KAAK,MAAM;YACnB,OAAO,KAAK,KAAK,CAAC,eAAe;QACnC;IACF;AACF;AAGO,MAAM,sBAAsB;IACjC,MAAM,OAAsB,EAAE;IAE9B,IAAK,IAAI,OAAO,GAAG,OAAO,IAAI,OAAQ;QACpC,8DAA8D;QAC9D,IAAI,aAAa;QAEjB,4BAA4B;QAC5B,IAAI,QAAQ,KAAK,OAAO,GAAG;YACzB,aAAa;QACf,OAEK,IAAI,QAAQ,KAAK,OAAO,GAAG;YAC9B,aAAa,MAAM,CAAC,OAAO,CAAC,IAAI;QAClC,OAEK,IAAI,QAAQ,KAAK,OAAO,IAAI;YAC/B,aAAa;QACf,OAEK,IAAI,QAAQ,MAAM,OAAO,IAAI;YAChC,aAAa,MAAM,CAAC,OAAO,EAAE,IAAI;QACnC,OAEK;YACH,aAAa;QACf;QAEA,sBAAsB;QACtB,MAAM,SAAS,aAAa,cAAc,KAAK;QAE/C,KAAK,IAAI,CAAC;YACR;YACA,OAAO,KAAK,KAAK,CAAC,MAAM;QAC1B;IACF;IAEA,OAAO;AACT;AAGO,MAAM,qBAAqB;IAChC,MAAM,OAAO;QAAC;QAAU;QAAW;QAAa;QAAY;QAAU;QAAY;KAAS;IAE3F,OAAO,KAAK,GAAG,CAAC,CAAA;QACd,2CAA2C;QAC3C,MAAM,YAAY,QAAQ,cAAc,QAAQ;QAChD,MAAM,aAAa,YAAY,MAAM;QAErC,yCAAyC;QACzC,MAAM,YAAY,AAAC,QAAQ,eAAe,QAAQ,aAAc,MAAM;QAEtE,sBAAsB;QACtB,MAAM,SAAS,aAAa,YAAY,cAAc,KAAK;QAE3D,OAAO;YACL;YACA,OAAO,KAAK,KAAK,CAAC,MAAM;QAC1B;IACF;AACF;AAGO,MAAM,qBAAqB,CAAC;IACjC,MAAM,cAAc;QAClB;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,cAAc;QAClB;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,kBAAkB;QACtB;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,iBAAiB;QACrB;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,kBAAkB;QACtB;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,sBAAsB;QAC1B;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,+CAA+C;IAC/C,IAAI,UAAoB,EAAE;IAE1B,OAAQ;QACN,KAAK;YACH,UAAU;YACV;QACF,KAAK;YACH,UAAU;YACV;QACF,KAAK;YACH,UAAU;YACV;QACF,KAAK;YACH,UAAU;YACV;QACF,KAAK;YACH,UAAU;YACV;QACF,KAAK;YACH,UAAU;YACV;QACF;YACE,sCAAsC;YACtC,UAAU;mBACL,YAAY,KAAK,CAAC,GAAG;mBACrB,YAAY,KAAK,CAAC,GAAG;mBACrB,gBAAgB,KAAK,CAAC,GAAG;mBACzB,eAAe,KAAK,CAAC,GAAG;aAC5B;IACL;IAEA,mBAAmB;IACnB,OAAO,QAAQ,GAAG,CAAC,CAAA,QAAS,CAAC;YAC3B;YACA,OAAO,aAAa,IAAI;QAC1B,CAAC,GACA,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK,EAAE,2BAA2B;KAC7D,KAAK,CAAC,GAAG,IAAI,aAAa;AAC7B;AAGO,MAAM,uBAAuB,CAAC;IACnC,MAAM,QAAQ,CAAA,GAAA,uHAAA,CAAA,eAAY,AAAD,IAAI,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAEhD,IAAI,CAAC,OAAO;QACV,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE,QAAQ,UAAU,CAAC;IACtD;IAEA,iCAAiC;IACjC,MAAM,aAAa,KAAK,GAAG,CAAC,KAAK,MAAM,UAAU,GAAG;IAEpD,MAAM,eAAe,oBAAoB,SAAS;IAElD,OAAO;QACL,IAAI,MAAM,EAAE;QACZ,MAAM,MAAM,IAAI;QAChB;QACA,eAAe,sBAAsB,aAAa,SAAS,EAAE;QAC7D,cAAc;QACd,aAAa;QACb,YAAY;QACZ,YAAY,mBAAmB;IACjC;AACF;AAGO,MAAM,qBAAqB;IAChC,OAAO,CAAA,GAAA,uHAAA,CAAA,eAAY,AAAD,IAAI,GAAG,CAAC,CAAA,QAAS,qBAAqB,MAAM,EAAE;AAClE;AAGO,MAAM,uBAAuB,CAAC,QAA2B,QAAgB,CAAC;IAC/E,MAAM,aAAa;IAEnB,OAAO,WACJ,GAAG,CAAC,CAAA,cAAe,CAAC;YACnB,SAAS,YAAY,EAAE;YACvB;YACA,OAAO,YAAY,YAAY,CAAC,OAAO;QACzC,CAAC,GACA,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK,EAChC,KAAK,CAAC,GAAG;AACd;AAGO,MAAM,0BAA0B;IACrC,MAAM,SAAS,CAAA,GAAA,uHAAA,CAAA,eAAY,AAAD;IAC1B,MAAM,aAAqC,CAAC;IAE5C,2BAA2B;IAC3B,OAAO,OAAO,CAAC,CAAA;QACb,IAAI,UAAU,CAAC,MAAM,QAAQ,CAAC,EAAE;YAC9B,UAAU,CAAC,MAAM,QAAQ,CAAC;QAC5B,OAAO;YACL,UAAU,CAAC,MAAM,QAAQ,CAAC,GAAG;QAC/B;IACF;IAEA,kBAAkB;IAClB,MAAM,QAAQ,OAAO,MAAM,CAAC,YAAY,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,OAAO;IAE5E,oCAAoC;IACpC,OAAO,OAAO,OAAO,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC,UAAU,MAAM,GAAK,CAAC;YAC5D;YACA;YACA,YAAY,AAAC,QAAQ,QAAS;QAChC,CAAC,GACA,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK,GAAG,2BAA2B;AACjE;AAGO,MAAM,2BAA2B,CAAC;IACvC,MAAM,aAAa;IACnB,MAAM,YAAY,WAAW,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,YAAY,CAAC,SAAS,EAAE;IAExF,OAAO,sBAAsB,WAAW;AAC1C;AAGO,MAAM,mBAAmB,CAAC,QAAgB,CAAC;IAChD,MAAM,aAAa;IAEnB,OAAO,WACJ,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,YAAY,CAAC,eAAe,GAAG,EAAE,YAAY,CAAC,eAAe,EAC9E,KAAK,CAAC,GAAG;AACd", "debugId": null}}, {"offset": {"line": 892, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/app/popular/components/UsageChart.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { UsageOverTime } from '@/types/metrics';\n\ninterface UsageChartProps {\n  data: UsageOverTime[];\n  title: string;\n  height?: number;\n}\n\nexport default function UsageChart({ data, title, height = 200 }: UsageChartProps) {\n  // Find the maximum value for scaling\n  const maxValue = Math.max(...data.map(item => item.count));\n  \n  // Calculate the width of each bar based on the number of data points\n  const barWidth = 100 / data.length;\n  \n  return (\n    <div className=\"rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900\">\n      <h3 className=\"mb-4 text-lg font-semibold text-gray-900 dark:text-white\">{title}</h3>\n      \n      <div style={{ height: `${height}px` }} className=\"relative w-full\">\n        {/* Y-axis labels */}\n        <div className=\"absolute bottom-0 left-0 top-0 flex flex-col justify-between text-xs text-gray-500 dark:text-gray-400\">\n          <span>100%</span>\n          <span>75%</span>\n          <span>50%</span>\n          <span>25%</span>\n          <span>0%</span>\n        </div>\n        \n        {/* Chart area */}\n        <div className=\"absolute bottom-0 left-6 right-0 top-0\">\n          {/* Horizontal grid lines */}\n          <div className=\"absolute bottom-0 left-0 right-0 top-0 flex flex-col justify-between\">\n            <div className=\"h-px w-full bg-gray-200 dark:bg-gray-700\"></div>\n            <div className=\"h-px w-full bg-gray-200 dark:bg-gray-700\"></div>\n            <div className=\"h-px w-full bg-gray-200 dark:bg-gray-700\"></div>\n            <div className=\"h-px w-full bg-gray-200 dark:bg-gray-700\"></div>\n            <div className=\"h-px w-full bg-gray-200 dark:bg-gray-700\"></div>\n          </div>\n          \n          {/* Bars */}\n          <div className=\"absolute bottom-0 left-0 right-0 flex h-full items-end\">\n            {data.map((item, index) => {\n              const height = (item.count / maxValue) * 100;\n              \n              return (\n                <div\n                  key={index}\n                  className=\"group flex flex-col items-center\"\n                  style={{ width: `${barWidth}%` }}\n                >\n                  <div\n                    className=\"w-4/5 rounded-t bg-blue-500 transition-all group-hover:bg-blue-600 dark:bg-blue-600 dark:group-hover:bg-blue-700\"\n                    style={{ height: `${height}%` }}\n                  ></div>\n                  \n                  {/* Tooltip */}\n                  <div className=\"invisible absolute bottom-full mb-2 rounded bg-gray-800 p-2 text-xs text-white opacity-0 transition-opacity group-hover:visible group-hover:opacity-100 dark:bg-gray-700\">\n                    <div className=\"font-semibold\">{new Date(item.date).toLocaleDateString()}</div>\n                    <div>{item.count} uses</div>\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n        </div>\n      </div>\n      \n      {/* X-axis labels */}\n      <div className=\"mt-2 flex justify-between text-xs text-gray-500 dark:text-gray-400\">\n        <span>{new Date(data[0].date).toLocaleDateString()}</span>\n        <span>{new Date(data[Math.floor(data.length / 2)].date).toLocaleDateString()}</span>\n        <span>{new Date(data[data.length - 1].date).toLocaleDateString()}</span>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAWe,SAAS,WAAW,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,GAAG,EAAmB;IAC/E,qCAAqC;IACrC,MAAM,WAAW,KAAK,GAAG,IAAI,KAAK,GAAG,CAAC,CAAA,OAAQ,KAAK,KAAK;IAExD,qEAAqE;IACrE,MAAM,WAAW,MAAM,KAAK,MAAM;IAElC,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAA4D;;;;;;0BAE1E,6LAAC;gBAAI,OAAO;oBAAE,QAAQ,GAAG,OAAO,EAAE,CAAC;gBAAC;gBAAG,WAAU;;kCAE/C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;0CAAK;;;;;;0CACN,6LAAC;0CAAK;;;;;;0CACN,6LAAC;0CAAK;;;;;;0CACN,6LAAC;0CAAK;;;;;;0CACN,6LAAC;0CAAK;;;;;;;;;;;;kCAIR,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;;;;;;;0CAIjB,6LAAC;gCAAI,WAAU;0CACZ,KAAK,GAAG,CAAC,CAAC,MAAM;oCACf,MAAM,SAAS,AAAC,KAAK,KAAK,GAAG,WAAY;oCAEzC,qBACE,6LAAC;wCAEC,WAAU;wCACV,OAAO;4CAAE,OAAO,GAAG,SAAS,CAAC,CAAC;wCAAC;;0DAE/B,6LAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,QAAQ,GAAG,OAAO,CAAC,CAAC;gDAAC;;;;;;0DAIhC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAiB,IAAI,KAAK,KAAK,IAAI,EAAE,kBAAkB;;;;;;kEACtE,6LAAC;;4DAAK,KAAK,KAAK;4DAAC;;;;;;;;;;;;;;uCAZd;;;;;gCAgBX;;;;;;;;;;;;;;;;;;0BAMN,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;kCAAM,IAAI,KAAK,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,kBAAkB;;;;;;kCAChD,6LAAC;kCAAM,IAAI,KAAK,IAAI,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,GAAG,GAAG,CAAC,IAAI,EAAE,kBAAkB;;;;;;kCAC1E,6LAAC;kCAAM,IAAI,KAAK,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE,CAAC,IAAI,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;AAItE;KApEwB", "debugId": null}}, {"offset": {"line": 1131, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/app/popular/components/RatingDistribution.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { UserFeedback } from '@/types/metrics';\n\ninterface RatingDistributionProps {\n  data: UserFeedback[];\n  title: string;\n}\n\nexport default function RatingDistribution({ data, title }: RatingDistributionProps) {\n  // Calculate total ratings\n  const totalRatings = data.reduce((sum, item) => sum + item.count, 0);\n  \n  // Calculate average rating\n  const weightedSum = data.reduce((sum, item) => sum + (item.rating * item.count), 0);\n  const averageRating = totalRatings > 0 ? (weightedSum / totalRatings).toFixed(1) : '0.0';\n  \n  return (\n    <div className=\"rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900\">\n      <div className=\"mb-4 flex items-center justify-between\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">{title}</h3>\n        <div className=\"flex items-center\">\n          <div className=\"mr-1 text-xl font-bold text-yellow-500\">{averageRating}</div>\n          <div className=\"text-sm text-gray-500 dark:text-gray-400\">/ 5</div>\n        </div>\n      </div>\n      \n      <div className=\"space-y-3\">\n        {[5, 4, 3, 2, 1].map(rating => {\n          const ratingData = data.find(item => item.rating === rating);\n          const count = ratingData ? ratingData.count : 0;\n          const percentage = totalRatings > 0 ? (count / totalRatings) * 100 : 0;\n          \n          return (\n            <div key={rating} className=\"flex items-center\">\n              <div className=\"mr-2 w-3 text-sm font-medium text-gray-700 dark:text-gray-300\">\n                {rating}\n              </div>\n              <div className=\"relative h-4 flex-1 rounded-full bg-gray-200 dark:bg-gray-700\">\n                <div\n                  className=\"absolute left-0 top-0 h-full rounded-full bg-yellow-500\"\n                  style={{ width: `${percentage}%` }}\n                ></div>\n              </div>\n              <div className=\"ml-2 w-12 text-right text-xs text-gray-500 dark:text-gray-400\">\n                {percentage.toFixed(1)}%\n              </div>\n            </div>\n          );\n        })}\n      </div>\n      \n      <div className=\"mt-4 text-center text-sm text-gray-500 dark:text-gray-400\">\n        Based on {totalRatings} ratings\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAUe,SAAS,mBAAmB,EAAE,IAAI,EAAE,KAAK,EAA2B;IACjF,0BAA0B;IAC1B,MAAM,eAAe,KAAK,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,KAAK,EAAE;IAElE,2BAA2B;IAC3B,MAAM,cAAc,KAAK,MAAM,CAAC,CAAC,KAAK,OAAS,MAAO,KAAK,MAAM,GAAG,KAAK,KAAK,EAAG;IACjF,MAAM,gBAAgB,eAAe,IAAI,CAAC,cAAc,YAAY,EAAE,OAAO,CAAC,KAAK;IAEnF,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAuD;;;;;;kCACrE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAA0C;;;;;;0CACzD,6LAAC;gCAAI,WAAU;0CAA2C;;;;;;;;;;;;;;;;;;0BAI9D,6LAAC;gBAAI,WAAU;0BACZ;oBAAC;oBAAG;oBAAG;oBAAG;oBAAG;iBAAE,CAAC,GAAG,CAAC,CAAA;oBACnB,MAAM,aAAa,KAAK,IAAI,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK;oBACrD,MAAM,QAAQ,aAAa,WAAW,KAAK,GAAG;oBAC9C,MAAM,aAAa,eAAe,IAAI,AAAC,QAAQ,eAAgB,MAAM;oBAErE,qBACE,6LAAC;wBAAiB,WAAU;;0CAC1B,6LAAC;gCAAI,WAAU;0CACZ;;;;;;0CAEH,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,OAAO,GAAG,WAAW,CAAC,CAAC;oCAAC;;;;;;;;;;;0CAGrC,6LAAC;gCAAI,WAAU;;oCACZ,WAAW,OAAO,CAAC;oCAAG;;;;;;;;uBAXjB;;;;;gBAed;;;;;;0BAGF,6LAAC;gBAAI,WAAU;;oBAA4D;oBAC/D;oBAAa;;;;;;;;;;;;;AAI/B;KAhDwB", "debugId": null}}, {"offset": {"line": 1282, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/app/popular/components/TopQueries.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { TopQuery } from '@/types/metrics';\n\ninterface TopQueriesProps {\n  data: TopQuery[];\n  title: string;\n}\n\nexport default function TopQueries({ data, title }: TopQueriesProps) {\n  // Find the maximum count for scaling\n  const maxCount = Math.max(...data.map(item => item.count));\n  \n  return (\n    <div className=\"rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900\">\n      <h3 className=\"mb-4 text-lg font-semibold text-gray-900 dark:text-white\">{title}</h3>\n      \n      <div className=\"space-y-3\">\n        {data.map((item, index) => {\n          const percentage = (item.count / maxCount) * 100;\n          \n          return (\n            <div key={index} className=\"space-y-1\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                  {item.query}\n                </div>\n                <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n                  {item.count} queries\n                </div>\n              </div>\n              <div className=\"relative h-2 w-full rounded-full bg-gray-200 dark:bg-gray-700\">\n                <div\n                  className=\"absolute left-0 top-0 h-full rounded-full bg-blue-500 dark:bg-blue-600\"\n                  style={{ width: `${percentage}%` }}\n                ></div>\n              </div>\n            </div>\n          );\n        })}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAUe,SAAS,WAAW,EAAE,IAAI,EAAE,KAAK,EAAmB;IACjE,qCAAqC;IACrC,MAAM,WAAW,KAAK,GAAG,IAAI,KAAK,GAAG,CAAC,CAAA,OAAQ,KAAK,KAAK;IAExD,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAA4D;;;;;;0BAE1E,6LAAC;gBAAI,WAAU;0BACZ,KAAK,GAAG,CAAC,CAAC,MAAM;oBACf,MAAM,aAAa,AAAC,KAAK,KAAK,GAAG,WAAY;oBAE7C,qBACE,6LAAC;wBAAgB,WAAU;;0CACzB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACZ,KAAK,KAAK;;;;;;kDAEb,6LAAC;wCAAI,WAAU;;4CACZ,KAAK,KAAK;4CAAC;;;;;;;;;;;;;0CAGhB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,OAAO,GAAG,WAAW,CAAC,CAAC;oCAAC;;;;;;;;;;;;uBAZ7B;;;;;gBAiBd;;;;;;;;;;;;AAIR;KAlCwB", "debugId": null}}, {"offset": {"line": 1385, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/app/popular/components/UsageByTimeChart.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { UsageByTime } from '@/types/metrics';\n\ninterface UsageByTimeChartProps {\n  data: UsageByTime[];\n  title: string;\n}\n\nexport default function UsageByTimeChart({ data, title }: UsageByTimeChartProps) {\n  // Find the maximum value for scaling\n  const maxValue = Math.max(...data.map(item => item.count));\n  \n  // Format hour for display\n  const formatHour = (hour: number): string => {\n    if (hour === 0) return '12 AM';\n    if (hour === 12) return '12 PM';\n    return hour < 12 ? `${hour} AM` : `${hour - 12} PM`;\n  };\n  \n  return (\n    <div className=\"rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900\">\n      <h3 className=\"mb-4 text-lg font-semibold text-gray-900 dark:text-white\">{title}</h3>\n      \n      <div className=\"h-48 w-full\">\n        <div className=\"relative h-full w-full\">\n          {/* Y-axis grid lines */}\n          <div className=\"absolute bottom-0 left-0 right-0 top-0 flex flex-col justify-between\">\n            {[0, 1, 2, 3, 4].map((_, index) => (\n              <div key={index} className=\"h-px w-full bg-gray-200 dark:bg-gray-700\"></div>\n            ))}\n          </div>\n          \n          {/* Bars */}\n          <div className=\"absolute bottom-0 left-0 right-0 flex h-full items-end\">\n            {data.map((item, index) => {\n              const height = (item.count / maxValue) * 100;\n              \n              // Determine color based on time of day\n              let colorClass = 'bg-blue-500 group-hover:bg-blue-600 dark:bg-blue-600 dark:group-hover:bg-blue-700';\n              \n              // Work hours (9 AM - 5 PM)\n              if (item.hour >= 9 && item.hour < 17) {\n                colorClass = 'bg-green-500 group-hover:bg-green-600 dark:bg-green-600 dark:group-hover:bg-green-700';\n              }\n              // Evening (5 PM - 10 PM)\n              else if (item.hour >= 17 && item.hour < 22) {\n                colorClass = 'bg-purple-500 group-hover:bg-purple-600 dark:bg-purple-600 dark:group-hover:bg-purple-700';\n              }\n              // Night (10 PM - 6 AM)\n              else if (item.hour >= 22 || item.hour < 6) {\n                colorClass = 'bg-indigo-500 group-hover:bg-indigo-600 dark:bg-indigo-600 dark:group-hover:bg-indigo-700';\n              }\n              \n              return (\n                <div\n                  key={index}\n                  className=\"group flex flex-1 flex-col items-center\"\n                >\n                  <div\n                    className={`w-4/5 rounded-t transition-all ${colorClass}`}\n                    style={{ height: `${height}%` }}\n                  ></div>\n                  \n                  {/* Tooltip */}\n                  <div className=\"invisible absolute bottom-full mb-2 rounded bg-gray-800 p-2 text-xs text-white opacity-0 transition-opacity group-hover:visible group-hover:opacity-100 dark:bg-gray-700\">\n                    <div className=\"font-semibold\">{formatHour(item.hour)}</div>\n                    <div>{item.count} uses</div>\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n        </div>\n      </div>\n      \n      {/* X-axis labels */}\n      <div className=\"mt-2 flex justify-between text-xs text-gray-500 dark:text-gray-400\">\n        {[0, 6, 12, 18, 23].map(hour => (\n          <span key={hour}>{formatHour(hour)}</span>\n        ))}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAUe,SAAS,iBAAiB,EAAE,IAAI,EAAE,KAAK,EAAyB;IAC7E,qCAAqC;IACrC,MAAM,WAAW,KAAK,GAAG,IAAI,KAAK,GAAG,CAAC,CAAA,OAAQ,KAAK,KAAK;IAExD,0BAA0B;IAC1B,MAAM,aAAa,CAAC;QAClB,IAAI,SAAS,GAAG,OAAO;QACvB,IAAI,SAAS,IAAI,OAAO;QACxB,OAAO,OAAO,KAAK,GAAG,KAAK,GAAG,CAAC,GAAG,GAAG,OAAO,GAAG,GAAG,CAAC;IACrD;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAA4D;;;;;;0BAE1E,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACZ;gCAAC;gCAAG;gCAAG;gCAAG;gCAAG;6BAAE,CAAC,GAAG,CAAC,CAAC,GAAG,sBACvB,6LAAC;oCAAgB,WAAU;mCAAjB;;;;;;;;;;sCAKd,6LAAC;4BAAI,WAAU;sCACZ,KAAK,GAAG,CAAC,CAAC,MAAM;gCACf,MAAM,SAAS,AAAC,KAAK,KAAK,GAAG,WAAY;gCAEzC,uCAAuC;gCACvC,IAAI,aAAa;gCAEjB,2BAA2B;gCAC3B,IAAI,KAAK,IAAI,IAAI,KAAK,KAAK,IAAI,GAAG,IAAI;oCACpC,aAAa;gCACf,OAEK,IAAI,KAAK,IAAI,IAAI,MAAM,KAAK,IAAI,GAAG,IAAI;oCAC1C,aAAa;gCACf,OAEK,IAAI,KAAK,IAAI,IAAI,MAAM,KAAK,IAAI,GAAG,GAAG;oCACzC,aAAa;gCACf;gCAEA,qBACE,6LAAC;oCAEC,WAAU;;sDAEV,6LAAC;4CACC,WAAW,CAAC,+BAA+B,EAAE,YAAY;4CACzD,OAAO;gDAAE,QAAQ,GAAG,OAAO,CAAC,CAAC;4CAAC;;;;;;sDAIhC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAiB,WAAW,KAAK,IAAI;;;;;;8DACpD,6LAAC;;wDAAK,KAAK,KAAK;wDAAC;;;;;;;;;;;;;;mCAXd;;;;;4BAeX;;;;;;;;;;;;;;;;;0BAMN,6LAAC;gBAAI,WAAU;0BACZ;oBAAC;oBAAG;oBAAG;oBAAI;oBAAI;iBAAG,CAAC,GAAG,CAAC,CAAA,qBACtB,6LAAC;kCAAiB,WAAW;uBAAlB;;;;;;;;;;;;;;;;AAKrB;KA3EwB", "debugId": null}}, {"offset": {"line": 1552, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/app/popular/components/UsageByDayChart.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { UsageByDay } from '@/types/metrics';\n\ninterface UsageByDayChartProps {\n  data: UsageByDay[];\n  title: string;\n}\n\nexport default function UsageByDayChart({ data, title }: UsageByDayChartProps) {\n  // Find the maximum value for scaling\n  const maxValue = Math.max(...data.map(item => item.count));\n  \n  // Sort days in correct order\n  const daysOrder = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];\n  const sortedData = [...data].sort((a, b) => \n    daysOrder.indexOf(a.day) - daysOrder.indexOf(b.day)\n  );\n  \n  return (\n    <div className=\"rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900\">\n      <h3 className=\"mb-4 text-lg font-semibold text-gray-900 dark:text-white\">{title}</h3>\n      \n      <div className=\"h-48 w-full\">\n        <div className=\"relative h-full w-full\">\n          {/* Y-axis grid lines */}\n          <div className=\"absolute bottom-0 left-0 right-0 top-0 flex flex-col justify-between\">\n            {[0, 1, 2, 3, 4].map((_, index) => (\n              <div key={index} className=\"h-px w-full bg-gray-200 dark:bg-gray-700\"></div>\n            ))}\n          </div>\n          \n          {/* Bars */}\n          <div className=\"absolute bottom-0 left-0 right-0 flex h-full items-end justify-between\">\n            {sortedData.map((item, index) => {\n              const height = (item.count / maxValue) * 100;\n              \n              // Weekend days get a different color\n              const isWeekend = item.day === 'Saturday' || item.day === 'Sunday';\n              const colorClass = isWeekend \n                ? 'bg-purple-500 group-hover:bg-purple-600 dark:bg-purple-600 dark:group-hover:bg-purple-700'\n                : 'bg-blue-500 group-hover:bg-blue-600 dark:bg-blue-600 dark:group-hover:bg-blue-700';\n              \n              return (\n                <div\n                  key={index}\n                  className=\"group flex w-10 flex-col items-center\"\n                >\n                  <div\n                    className={`w-8 rounded-t transition-all ${colorClass}`}\n                    style={{ height: `${height}%` }}\n                  ></div>\n                  \n                  {/* Tooltip */}\n                  <div className=\"invisible absolute bottom-full mb-2 rounded bg-gray-800 p-2 text-xs text-white opacity-0 transition-opacity group-hover:visible group-hover:opacity-100 dark:bg-gray-700\">\n                    <div className=\"font-semibold\">{item.day}</div>\n                    <div>{item.count} uses</div>\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n        </div>\n      </div>\n      \n      {/* X-axis labels */}\n      <div className=\"mt-2 flex justify-between text-xs text-gray-500 dark:text-gray-400\">\n        {sortedData.map((item, index) => (\n          <span key={index}>{item.day.substring(0, 3)}</span>\n        ))}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAUe,SAAS,gBAAgB,EAAE,IAAI,EAAE,KAAK,EAAwB;IAC3E,qCAAqC;IACrC,MAAM,WAAW,KAAK,GAAG,IAAI,KAAK,GAAG,CAAC,CAAA,OAAQ,KAAK,KAAK;IAExD,6BAA6B;IAC7B,MAAM,YAAY;QAAC;QAAU;QAAW;QAAa;QAAY;QAAU;QAAY;KAAS;IAChG,MAAM,aAAa;WAAI;KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,IACpC,UAAU,OAAO,CAAC,EAAE,GAAG,IAAI,UAAU,OAAO,CAAC,EAAE,GAAG;IAGpD,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAA4D;;;;;;0BAE1E,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACZ;gCAAC;gCAAG;gCAAG;gCAAG;gCAAG;6BAAE,CAAC,GAAG,CAAC,CAAC,GAAG,sBACvB,6LAAC;oCAAgB,WAAU;mCAAjB;;;;;;;;;;sCAKd,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,MAAM;gCACrB,MAAM,SAAS,AAAC,KAAK,KAAK,GAAG,WAAY;gCAEzC,qCAAqC;gCACrC,MAAM,YAAY,KAAK,GAAG,KAAK,cAAc,KAAK,GAAG,KAAK;gCAC1D,MAAM,aAAa,YACf,8FACA;gCAEJ,qBACE,6LAAC;oCAEC,WAAU;;sDAEV,6LAAC;4CACC,WAAW,CAAC,6BAA6B,EAAE,YAAY;4CACvD,OAAO;gDAAE,QAAQ,GAAG,OAAO,CAAC,CAAC;4CAAC;;;;;;sDAIhC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAiB,KAAK,GAAG;;;;;;8DACxC,6LAAC;;wDAAK,KAAK,KAAK;wDAAC;;;;;;;;;;;;;;mCAXd;;;;;4BAeX;;;;;;;;;;;;;;;;;0BAMN,6LAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC,MAAM,sBACrB,6LAAC;kCAAkB,KAAK,GAAG,CAAC,SAAS,CAAC,GAAG;uBAA9B;;;;;;;;;;;;;;;;AAKrB;KAhEwB", "debugId": null}}, {"offset": {"line": 1713, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/app/popular/components/MetricCard.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\n\ninterface MetricCardProps {\n  title: string;\n  value: string | number;\n  change?: number;\n  icon: React.ReactNode;\n}\n\nexport default function MetricCard({ title, value, change, icon }: MetricCardProps) {\n  return (\n    <div className=\"rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900\">\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">{title}</p>\n          <h3 className=\"mt-1 text-2xl font-bold text-gray-900 dark:text-white\">{value}</h3>\n          \n          {change !== undefined && (\n            <p className={`mt-1 flex items-center text-sm ${\n              change >= 0 \n                ? 'text-green-600 dark:text-green-400' \n                : 'text-red-600 dark:text-red-400'\n            }`}>\n              {change >= 0 ? (\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  width=\"24\"\n                  height=\"24\"\n                  viewBox=\"0 0 24 24\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  strokeWidth=\"2\"\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  className=\"mr-1 h-3 w-3\"\n                >\n                  <polyline points=\"18 15 12 9 6 15\" />\n                </svg>\n              ) : (\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  width=\"24\"\n                  height=\"24\"\n                  viewBox=\"0 0 24 24\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  strokeWidth=\"2\"\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  className=\"mr-1 h-3 w-3\"\n                >\n                  <polyline points=\"6 9 12 15 18 9\" />\n                </svg>\n              )}\n              {Math.abs(change)}% from last period\n            </p>\n          )}\n        </div>\n        \n        <div className=\"rounded-full bg-blue-100 p-3 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400\">\n          {icon}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAWe,SAAS,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAmB;IAChF,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;;sCACC,6LAAC;4BAAE,WAAU;sCAAwD;;;;;;sCACrE,6LAAC;4BAAG,WAAU;sCAAyD;;;;;;wBAEtE,WAAW,2BACV,6LAAC;4BAAE,WAAW,CAAC,+BAA+B,EAC5C,UAAU,IACN,uCACA,kCACJ;;gCACC,UAAU,kBACT,6LAAC;oCACC,OAAM;oCACN,OAAM;oCACN,QAAO;oCACP,SAAQ;oCACR,MAAK;oCACL,QAAO;oCACP,aAAY;oCACZ,eAAc;oCACd,gBAAe;oCACf,WAAU;8CAEV,cAAA,6LAAC;wCAAS,QAAO;;;;;;;;;;yDAGnB,6LAAC;oCACC,OAAM;oCACN,OAAM;oCACN,QAAO;oCACP,SAAQ;oCACR,MAAK;oCACL,QAAO;oCACP,aAAY;oCACZ,eAAc;oCACd,gBAAe;oCACf,WAAU;8CAEV,cAAA,6LAAC;wCAAS,QAAO;;;;;;;;;;;gCAGpB,KAAK,GAAG,CAAC;gCAAQ;;;;;;;;;;;;;8BAKxB,6LAAC;oBAAI,WAAU;8BACZ;;;;;;;;;;;;;;;;;AAKX;KAxDwB", "debugId": null}}, {"offset": {"line": 1837, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/lib/events.ts"], "sourcesContent": ["'use client';\n\n// Custom event types\nexport const EVENTS = {\n  FAVORITES_UPDATED: 'favorites-updated',\n  RECENTLY_VIEWED_UPDATED: 'recently-viewed-updated',\n};\n\n// Function to dispatch a custom event\nexport function dispatchCustomEvent(eventName: string, detail?: any) {\n  if (typeof window === 'undefined') return;\n  \n  const event = new CustomEvent(eventName, { detail });\n  window.dispatchEvent(event);\n}\n\n// Function to add an event listener\nexport function addCustomEventListener(eventName: string, callback: (event: CustomEvent) => void) {\n  if (typeof window === 'undefined') return () => {};\n  \n  const eventListener = (e: Event) => callback(e as CustomEvent);\n  window.addEventListener(eventName, eventListener);\n  \n  // Return a function to remove the event listener\n  return () => {\n    window.removeEventListener(eventName, eventListener);\n  };\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAGO,MAAM,SAAS;IACpB,mBAAmB;IACnB,yBAAyB;AAC3B;AAGO,SAAS,oBAAoB,SAAiB,EAAE,MAAY;IACjE,uCAAmC;;IAAM;IAEzC,MAAM,QAAQ,IAAI,YAAY,WAAW;QAAE;IAAO;IAClD,OAAO,aAAa,CAAC;AACvB;AAGO,SAAS,uBAAuB,SAAiB,EAAE,QAAsC;IAC9F,uCAAmC;;IAAe;IAElD,MAAM,gBAAgB,CAAC,IAAa,SAAS;IAC7C,OAAO,gBAAgB,CAAC,WAAW;IAEnC,iDAAiD;IACjD,OAAO;QACL,OAAO,mBAAmB,CAAC,WAAW;IACxC;AACF", "debugId": null}}, {"offset": {"line": 1876, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/lib/userPreferences.ts"], "sourcesContent": ["'use client';\n\nimport { UserPreferences } from '@/types/auth';\nimport { dispatchCustomEvent, EVENTS } from './events';\n\n// Default user preferences\nconst defaultPreferences: UserPreferences = {\n  favoriteAgentIds: [],\n  recentlyViewedAgentIds: [],\n  darkMode: false,\n  defaultView: 'grid',\n  pageSize: 12,\n};\n\n// Get user preferences from localStorage\nexport function getUserPreferences(userId?: string): UserPreferences {\n  if (typeof window === 'undefined') {\n    return defaultPreferences;\n  }\n\n  // If userId is provided, use it to get user-specific preferences\n  const storageKey = userId ? `userPreferences_${userId}` : 'userPreferences';\n  const storedPreferences = localStorage.getItem(storageKey);\n  if (!storedPreferences) {\n    return defaultPreferences;\n  }\n\n  try {\n    return JSON.parse(storedPreferences) as UserPreferences;\n  } catch (error) {\n    console.error('Error parsing user preferences:', error);\n    return defaultPreferences;\n  }\n}\n\n// Save user preferences to localStorage\nexport function saveUserPreferences(preferences: UserPreferences, userId?: string): void {\n  if (typeof window === 'undefined') {\n    return;\n  }\n\n  // If userId is provided, use it to save user-specific preferences\n  const storageKey = userId ? `userPreferences_${userId}` : 'userPreferences';\n  localStorage.setItem(storageKey, JSON.stringify(preferences));\n}\n\n// Add an agent to favorites\nexport function toggleFavoriteAgent(agentId: string, userId?: string): boolean {\n  const preferences = getUserPreferences(userId);\n  const isFavorite = preferences.favoriteAgentIds.includes(agentId);\n\n  if (isFavorite) {\n    preferences.favoriteAgentIds = preferences.favoriteAgentIds.filter(id => id !== agentId);\n  } else {\n    preferences.favoriteAgentIds.push(agentId);\n  }\n\n  saveUserPreferences(preferences, userId);\n\n  // Dispatch event to notify other components\n  dispatchCustomEvent(EVENTS.FAVORITES_UPDATED, {\n    agentId,\n    isFavorite: !isFavorite,\n    userId\n  });\n\n  return !isFavorite; // Return the new favorite status\n}\n\n// Check if an agent is favorited\nexport function isAgentFavorited(agentId: string, userId?: string): boolean {\n  const preferences = getUserPreferences(userId);\n  return preferences.favoriteAgentIds.includes(agentId);\n}\n\n// Add an agent to recently viewed\nexport function addToRecentlyViewed(agentId: string, userId?: string): void {\n  const preferences = getUserPreferences(userId);\n\n  // Remove if already exists to avoid duplicates\n  preferences.recentlyViewedAgentIds = preferences.recentlyViewedAgentIds.filter(id => id !== agentId);\n\n  // Add to the beginning of the array\n  preferences.recentlyViewedAgentIds.unshift(agentId);\n\n  // Keep only the 10 most recent\n  if (preferences.recentlyViewedAgentIds.length > 10) {\n    preferences.recentlyViewedAgentIds = preferences.recentlyViewedAgentIds.slice(0, 10);\n  }\n\n  saveUserPreferences(preferences, userId);\n\n  // Dispatch event to notify other components\n  dispatchCustomEvent(EVENTS.RECENTLY_VIEWED_UPDATED, {\n    agentId,\n    recentlyViewedIds: preferences.recentlyViewedAgentIds,\n    userId\n  });\n}\n\n// Get recently viewed agents\nexport function getRecentlyViewedAgentIds(userId?: string): string[] {\n  const preferences = getUserPreferences(userId);\n  return preferences.recentlyViewedAgentIds;\n}\n\n// Update page size preference\nexport function updatePageSize(pageSize: number, userId?: string): void {\n  const preferences = getUserPreferences(userId);\n  preferences.pageSize = pageSize;\n  saveUserPreferences(preferences, userId);\n}\n\n// Get page size preference\nexport function getPageSize(userId?: string): number {\n  const preferences = getUserPreferences(userId);\n  return preferences.pageSize;\n}\n\n// Update default view preference\nexport function updateDefaultView(view: 'grid' | 'list', userId?: string): void {\n  const preferences = getUserPreferences(userId);\n  preferences.defaultView = view;\n  saveUserPreferences(preferences, userId);\n}\n\n// Get default view preference\nexport function getDefaultView(userId?: string): 'grid' | 'list' {\n  const preferences = getUserPreferences(userId);\n  return preferences.defaultView;\n}\n\n// Toggle dark mode\nexport function toggleDarkMode(userId?: string): boolean {\n  const preferences = getUserPreferences(userId);\n  preferences.darkMode = !preferences.darkMode;\n  saveUserPreferences(preferences, userId);\n  return preferences.darkMode;\n}\n\n// Get dark mode preference\nexport function getDarkMode(userId?: string): boolean {\n  const preferences = getUserPreferences(userId);\n  return preferences.darkMode;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAGA;AAHA;;AAKA,2BAA2B;AAC3B,MAAM,qBAAsC;IAC1C,kBAAkB,EAAE;IACpB,wBAAwB,EAAE;IAC1B,UAAU;IACV,aAAa;IACb,UAAU;AACZ;AAGO,SAAS,mBAAmB,MAAe;IAChD,uCAAmC;;IAEnC;IAEA,iEAAiE;IACjE,MAAM,aAAa,SAAS,CAAC,gBAAgB,EAAE,QAAQ,GAAG;IAC1D,MAAM,oBAAoB,aAAa,OAAO,CAAC;IAC/C,IAAI,CAAC,mBAAmB;QACtB,OAAO;IACT;IAEA,IAAI;QACF,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO;IACT;AACF;AAGO,SAAS,oBAAoB,WAA4B,EAAE,MAAe;IAC/E,uCAAmC;;IAEnC;IAEA,kEAAkE;IAClE,MAAM,aAAa,SAAS,CAAC,gBAAgB,EAAE,QAAQ,GAAG;IAC1D,aAAa,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC;AAClD;AAGO,SAAS,oBAAoB,OAAe,EAAE,MAAe;IAClE,MAAM,cAAc,mBAAmB;IACvC,MAAM,aAAa,YAAY,gBAAgB,CAAC,QAAQ,CAAC;IAEzD,IAAI,YAAY;QACd,YAAY,gBAAgB,GAAG,YAAY,gBAAgB,CAAC,MAAM,CAAC,CAAA,KAAM,OAAO;IAClF,OAAO;QACL,YAAY,gBAAgB,CAAC,IAAI,CAAC;IACpC;IAEA,oBAAoB,aAAa;IAEjC,4CAA4C;IAC5C,CAAA,GAAA,uHAAA,CAAA,sBAAmB,AAAD,EAAE,uHAAA,CAAA,SAAM,CAAC,iBAAiB,EAAE;QAC5C;QACA,YAAY,CAAC;QACb;IACF;IAEA,OAAO,CAAC,YAAY,iCAAiC;AACvD;AAGO,SAAS,iBAAiB,OAAe,EAAE,MAAe;IAC/D,MAAM,cAAc,mBAAmB;IACvC,OAAO,YAAY,gBAAgB,CAAC,QAAQ,CAAC;AAC/C;AAGO,SAAS,oBAAoB,OAAe,EAAE,MAAe;IAClE,MAAM,cAAc,mBAAmB;IAEvC,+CAA+C;IAC/C,YAAY,sBAAsB,GAAG,YAAY,sBAAsB,CAAC,MAAM,CAAC,CAAA,KAAM,OAAO;IAE5F,oCAAoC;IACpC,YAAY,sBAAsB,CAAC,OAAO,CAAC;IAE3C,+BAA+B;IAC/B,IAAI,YAAY,sBAAsB,CAAC,MAAM,GAAG,IAAI;QAClD,YAAY,sBAAsB,GAAG,YAAY,sBAAsB,CAAC,KAAK,CAAC,GAAG;IACnF;IAEA,oBAAoB,aAAa;IAEjC,4CAA4C;IAC5C,CAAA,GAAA,uHAAA,CAAA,sBAAmB,AAAD,EAAE,uHAAA,CAAA,SAAM,CAAC,uBAAuB,EAAE;QAClD;QACA,mBAAmB,YAAY,sBAAsB;QACrD;IACF;AACF;AAGO,SAAS,0BAA0B,MAAe;IACvD,MAAM,cAAc,mBAAmB;IACvC,OAAO,YAAY,sBAAsB;AAC3C;AAGO,SAAS,eAAe,QAAgB,EAAE,MAAe;IAC9D,MAAM,cAAc,mBAAmB;IACvC,YAAY,QAAQ,GAAG;IACvB,oBAAoB,aAAa;AACnC;AAGO,SAAS,YAAY,MAAe;IACzC,MAAM,cAAc,mBAAmB;IACvC,OAAO,YAAY,QAAQ;AAC7B;AAGO,SAAS,kBAAkB,IAAqB,EAAE,MAAe;IACtE,MAAM,cAAc,mBAAmB;IACvC,YAAY,WAAW,GAAG;IAC1B,oBAAoB,aAAa;AACnC;AAGO,SAAS,eAAe,MAAe;IAC5C,MAAM,cAAc,mBAAmB;IACvC,OAAO,YAAY,WAAW;AAChC;AAGO,SAAS,eAAe,MAAe;IAC5C,MAAM,cAAc,mBAAmB;IACvC,YAAY,QAAQ,GAAG,CAAC,YAAY,QAAQ;IAC5C,oBAAoB,aAAa;IACjC,OAAO,YAAY,QAAQ;AAC7B;AAGO,SAAS,YAAY,MAAe;IACzC,MAAM,cAAc,mBAAmB;IACvC,OAAO,YAAY,QAAQ;AAC7B", "debugId": null}}, {"offset": {"line": 2006, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/app/popular/components/PopularAgentCard.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { Agent } from '@/types/agent';\nimport { UsageMetric } from '@/types/metrics';\nimport { isAgentFavorited, toggleFavoriteAgent, addToRecentlyViewed } from '@/lib/userPreferences';\nimport { addCustomEventListener, EVENTS } from '@/lib/events';\n\ninterface PopularAgentCardProps {\n  agent: Agent;\n  metrics: UsageMetric;\n  rank: number;\n}\n\nexport default function PopularAgentCard({ agent, metrics, rank }: PopularAgentCardProps) {\n  const [isFavorite, setIsFavorite] = useState(false);\n  \n  // Load favorite status\n  useEffect(() => {\n    if (typeof window === 'undefined') return;\n    \n    const loadFavoriteStatus = () => {\n      setIsFavorite(isAgentFavorited(agent.id));\n    };\n    \n    loadFavoriteStatus();\n    \n    // Listen for favorites updates\n    const removeListener = addCustomEventListener(EVENTS.FAVORITES_UPDATED, (event) => {\n      const { agentId, isFavorite } = event.detail;\n      if (agentId === agent.id) {\n        setIsFavorite(isFavorite);\n      }\n    });\n    \n    return () => {\n      removeListener();\n    };\n  }, [agent.id]);\n  \n  // Handle favorite toggle\n  const handleFavoriteToggle = (e: React.MouseEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n    \n    const newStatus = toggleFavoriteAgent(agent.id);\n    setIsFavorite(newStatus);\n  };\n  \n  // Handle agent click to track recently viewed\n  const handleAgentClick = () => {\n    addToRecentlyViewed(agent.id);\n  };\n  \n  return (\n    <div className=\"flex rounded-lg border border-gray-200 bg-white p-4 shadow-sm transition-shadow hover:shadow-md dark:border-gray-800 dark:bg-gray-900\">\n      <div className=\"mr-4 flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-blue-100 text-xl font-bold text-blue-600 dark:bg-blue-900/30 dark:text-blue-400\">\n        #{rank}\n      </div>\n      \n      <div className=\"flex flex-1 flex-col\">\n        <div className=\"mb-2 flex items-start justify-between\">\n          <div className=\"flex items-center\">\n            {agent.avatarUrl && (\n              <div className=\"mr-3 h-10 w-10 flex-shrink-0 overflow-hidden rounded-full bg-gray-100 dark:bg-gray-800\">\n                <Image\n                  src={agent.avatarUrl}\n                  alt={`${agent.name} icon`}\n                  width={40}\n                  height={40}\n                  className=\"h-full w-full object-cover\"\n                />\n              </div>\n            )}\n            <div>\n              <h3 className=\"font-bold text-gray-900 dark:text-white\">{agent.name}</h3>\n              <p className=\"text-sm text-gray-500 dark:text-gray-400\">{agent.category}</p>\n            </div>\n          </div>\n          \n          <button\n            onClick={handleFavoriteToggle}\n            className=\"rounded-full p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-600 dark:hover:bg-gray-800 dark:hover:text-gray-300\"\n            aria-label={isFavorite ? 'Remove from favorites' : 'Add to favorites'}\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              width=\"24\"\n              height=\"24\"\n              viewBox=\"0 0 24 24\"\n              fill={isFavorite ? 'currentColor' : 'none'}\n              stroke=\"currentColor\"\n              strokeWidth=\"2\"\n              strokeLinecap=\"round\"\n              strokeLinejoin=\"round\"\n              className={`h-5 w-5 ${isFavorite ? 'text-yellow-400' : ''}`}\n            >\n              <path d=\"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z\" />\n            </svg>\n          </button>\n        </div>\n        \n        <div className=\"mb-3 grid grid-cols-3 gap-2 text-center\">\n          <div>\n            <p className=\"text-xs text-gray-500 dark:text-gray-400\">Uses</p>\n            <p className=\"font-semibold text-gray-900 dark:text-white\">{metrics.totalUses.toLocaleString()}</p>\n          </div>\n          <div>\n            <p className=\"text-xs text-gray-500 dark:text-gray-400\">Rating</p>\n            <p className=\"font-semibold text-gray-900 dark:text-white\">{metrics.averageRating.toFixed(1)}/5</p>\n          </div>\n          <div>\n            <p className=\"text-xs text-gray-500 dark:text-gray-400\">Completion</p>\n            <p className=\"font-semibold text-gray-900 dark:text-white\">{(metrics.completionRate * 100).toFixed(0)}%</p>\n          </div>\n        </div>\n        \n        <div className=\"mt-auto flex items-center justify-between\">\n          <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n            Popularity Score: {metrics.popularityScore.toFixed(0)}/100\n          </div>\n          <Link\n            href={`/agent/${agent.id}`}\n            className=\"rounded-md bg-blue-600 px-3 py-1 text-sm font-medium text-white hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800\"\n            onClick={handleAgentClick}\n          >\n            View Agent\n          </Link>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAGA;AACA;;;AARA;;;;;;AAgBe,SAAS,iBAAiB,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAyB;;IACtF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,uBAAuB;IACvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,uCAAmC;;YAAM;YAEzC,MAAM;iEAAqB;oBACzB,cAAc,CAAA,GAAA,gIAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM,EAAE;gBACzC;;YAEA;YAEA,+BAA+B;YAC/B,MAAM,iBAAiB,CAAA,GAAA,uHAAA,CAAA,yBAAsB,AAAD,EAAE,uHAAA,CAAA,SAAM,CAAC,iBAAiB;6DAAE,CAAC;oBACvE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,MAAM,MAAM;oBAC5C,IAAI,YAAY,MAAM,EAAE,EAAE;wBACxB,cAAc;oBAChB;gBACF;;YAEA;8CAAO;oBACL;gBACF;;QACF;qCAAG;QAAC,MAAM,EAAE;KAAC;IAEb,yBAAyB;IACzB,MAAM,uBAAuB,CAAC;QAC5B,EAAE,cAAc;QAChB,EAAE,eAAe;QAEjB,MAAM,YAAY,CAAA,GAAA,gIAAA,CAAA,sBAAmB,AAAD,EAAE,MAAM,EAAE;QAC9C,cAAc;IAChB;IAEA,8CAA8C;IAC9C,MAAM,mBAAmB;QACvB,CAAA,GAAA,gIAAA,CAAA,sBAAmB,AAAD,EAAE,MAAM,EAAE;IAC9B;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;oBAAgK;oBAC3K;;;;;;;0BAGJ,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;oCACZ,MAAM,SAAS,kBACd,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4CACJ,KAAK,MAAM,SAAS;4CACpB,KAAK,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC;4CACzB,OAAO;4CACP,QAAQ;4CACR,WAAU;;;;;;;;;;;kDAIhB,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAA2C,MAAM,IAAI;;;;;;0DACnE,6LAAC;gDAAE,WAAU;0DAA4C,MAAM,QAAQ;;;;;;;;;;;;;;;;;;0CAI3E,6LAAC;gCACC,SAAS;gCACT,WAAU;gCACV,cAAY,aAAa,0BAA0B;0CAEnD,cAAA,6LAAC;oCACC,OAAM;oCACN,OAAM;oCACN,QAAO;oCACP,SAAQ;oCACR,MAAM,aAAa,iBAAiB;oCACpC,QAAO;oCACP,aAAY;oCACZ,eAAc;oCACd,gBAAe;oCACf,WAAW,CAAC,QAAQ,EAAE,aAAa,oBAAoB,IAAI;8CAE3D,cAAA,6LAAC;wCAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;kCAKd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAE,WAAU;kDAA2C;;;;;;kDACxD,6LAAC;wCAAE,WAAU;kDAA+C,QAAQ,SAAS,CAAC,cAAc;;;;;;;;;;;;0CAE9F,6LAAC;;kDACC,6LAAC;wCAAE,WAAU;kDAA2C;;;;;;kDACxD,6LAAC;wCAAE,WAAU;;4CAA+C,QAAQ,aAAa,CAAC,OAAO,CAAC;4CAAG;;;;;;;;;;;;;0CAE/F,6LAAC;;kDACC,6LAAC;wCAAE,WAAU;kDAA2C;;;;;;kDACxD,6LAAC;wCAAE,WAAU;;4CAA+C,CAAC,QAAQ,cAAc,GAAG,GAAG,EAAE,OAAO,CAAC;4CAAG;;;;;;;;;;;;;;;;;;;kCAI1G,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;oCAA2C;oCACrC,QAAQ,eAAe,CAAC,OAAO,CAAC;oCAAG;;;;;;;0CAExD,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAM,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gCAC1B,WAAU;gCACV,SAAS;0CACV;;;;;;;;;;;;;;;;;;;;;;;;AAOX;GAtHwB;KAAA", "debugId": null}}, {"offset": {"line": 2320, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/app/popular/components/CategoryDistributionChart.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { CategoryDistribution } from '@/types/metrics';\n\ninterface CategoryDistributionChartProps {\n  data: CategoryDistribution[];\n  title: string;\n}\n\nexport default function CategoryDistributionChart({ data, title }: CategoryDistributionChartProps) {\n  // Generate colors for each category\n  const colors = [\n    'bg-blue-500 dark:bg-blue-600',\n    'bg-green-500 dark:bg-green-600',\n    'bg-purple-500 dark:bg-purple-600',\n    'bg-yellow-500 dark:bg-yellow-600',\n    'bg-red-500 dark:bg-red-600',\n    'bg-indigo-500 dark:bg-indigo-600',\n    'bg-pink-500 dark:bg-pink-600',\n    'bg-teal-500 dark:bg-teal-600',\n  ];\n  \n  return (\n    <div className=\"rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900\">\n      <h3 className=\"mb-4 text-lg font-semibold text-gray-900 dark:text-white\">{title}</h3>\n      \n      <div className=\"mb-4 h-48 w-full\">\n        <div className=\"relative h-full w-full\">\n          <div className=\"absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 transform\">\n            <div className=\"h-32 w-32 rounded-full border-8 border-gray-100 dark:border-gray-800\"></div>\n          </div>\n          \n          {/* Pie chart segments */}\n          <svg viewBox=\"0 0 100 100\" className=\"h-full w-full\">\n            <circle cx=\"50\" cy=\"50\" r=\"40\" fill=\"transparent\" stroke=\"#e5e7eb\" strokeWidth=\"20\" />\n            \n            {data.map((category, index) => {\n              // Calculate the segment's start and end angles\n              const totalPercentage = data.reduce((sum, item) => sum + item.percentage, 0);\n              const startPercentage = data.slice(0, index).reduce((sum, item) => sum + item.percentage, 0);\n              const endPercentage = startPercentage + category.percentage;\n              \n              const startAngle = (startPercentage / totalPercentage) * 360;\n              const endAngle = (endPercentage / totalPercentage) * 360;\n              \n              // Convert angles to radians\n              const startRad = (startAngle - 90) * (Math.PI / 180);\n              const endRad = (endAngle - 90) * (Math.PI / 180);\n              \n              // Calculate the SVG arc path\n              const x1 = 50 + 40 * Math.cos(startRad);\n              const y1 = 50 + 40 * Math.sin(startRad);\n              const x2 = 50 + 40 * Math.cos(endRad);\n              const y2 = 50 + 40 * Math.sin(endRad);\n              \n              // Determine if the arc should be drawn the long way around\n              const largeArcFlag = endAngle - startAngle <= 180 ? \"0\" : \"1\";\n              \n              // Create the SVG path\n              const d = `M 50 50 L ${x1} ${y1} A 40 40 0 ${largeArcFlag} 1 ${x2} ${y2} Z`;\n              \n              return (\n                <path\n                  key={index}\n                  d={d}\n                  fill={colors[index % colors.length].split(' ')[0]}\n                  className=\"hover:opacity-80\"\n                />\n              );\n            })}\n          </svg>\n        </div>\n      </div>\n      \n      <div className=\"space-y-2\">\n        {data.map((category, index) => (\n          <div key={index} className=\"flex items-center justify-between\">\n            <div className=\"flex items-center\">\n              <div className={`mr-2 h-3 w-3 rounded-full ${colors[index % colors.length].split(' ')[0]}`}></div>\n              <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                {category.category}\n              </span>\n            </div>\n            <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n              {category.percentage.toFixed(1)}% ({category.count})\n            </div>\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAUe,SAAS,0BAA0B,EAAE,IAAI,EAAE,KAAK,EAAkC;IAC/F,oCAAoC;IACpC,MAAM,SAAS;QACb;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAA4D;;;;;;0BAE1E,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;;;;;;;;;;sCAIjB,6LAAC;4BAAI,SAAQ;4BAAc,WAAU;;8CACnC,6LAAC;oCAAO,IAAG;oCAAK,IAAG;oCAAK,GAAE;oCAAK,MAAK;oCAAc,QAAO;oCAAU,aAAY;;;;;;gCAE9E,KAAK,GAAG,CAAC,CAAC,UAAU;oCACnB,+CAA+C;oCAC/C,MAAM,kBAAkB,KAAK,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,UAAU,EAAE;oCAC1E,MAAM,kBAAkB,KAAK,KAAK,CAAC,GAAG,OAAO,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,UAAU,EAAE;oCAC1F,MAAM,gBAAgB,kBAAkB,SAAS,UAAU;oCAE3D,MAAM,aAAa,AAAC,kBAAkB,kBAAmB;oCACzD,MAAM,WAAW,AAAC,gBAAgB,kBAAmB;oCAErD,4BAA4B;oCAC5B,MAAM,WAAW,CAAC,aAAa,EAAE,IAAI,CAAC,KAAK,EAAE,GAAG,GAAG;oCACnD,MAAM,SAAS,CAAC,WAAW,EAAE,IAAI,CAAC,KAAK,EAAE,GAAG,GAAG;oCAE/C,6BAA6B;oCAC7B,MAAM,KAAK,KAAK,KAAK,KAAK,GAAG,CAAC;oCAC9B,MAAM,KAAK,KAAK,KAAK,KAAK,GAAG,CAAC;oCAC9B,MAAM,KAAK,KAAK,KAAK,KAAK,GAAG,CAAC;oCAC9B,MAAM,KAAK,KAAK,KAAK,KAAK,GAAG,CAAC;oCAE9B,2DAA2D;oCAC3D,MAAM,eAAe,WAAW,cAAc,MAAM,MAAM;oCAE1D,sBAAsB;oCACtB,MAAM,IAAI,CAAC,UAAU,EAAE,GAAG,CAAC,EAAE,GAAG,WAAW,EAAE,aAAa,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC;oCAE3E,qBACE,6LAAC;wCAEC,GAAG;wCACH,MAAM,MAAM,CAAC,QAAQ,OAAO,MAAM,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;wCACjD,WAAU;uCAHL;;;;;gCAMX;;;;;;;;;;;;;;;;;;0BAKN,6LAAC;gBAAI,WAAU;0BACZ,KAAK,GAAG,CAAC,CAAC,UAAU,sBACnB,6LAAC;wBAAgB,WAAU;;0CACzB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAW,CAAC,0BAA0B,EAAE,MAAM,CAAC,QAAQ,OAAO,MAAM,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE;;;;;;kDAC1F,6LAAC;wCAAK,WAAU;kDACb,SAAS,QAAQ;;;;;;;;;;;;0CAGtB,6LAAC;gCAAI,WAAU;;oCACZ,SAAS,UAAU,CAAC,OAAO,CAAC;oCAAG;oCAAI,SAAS,KAAK;oCAAC;;;;;;;;uBAR7C;;;;;;;;;;;;;;;;AAepB;KAlFwB", "debugId": null}}, {"offset": {"line": 2502, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/app/popular/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { \n  getPopularAgents, \n  getPlatformUsageOverTime, \n  getCategoryDistribution,\n  getTopAgentsByMetric\n} from '@/lib/metrics';\nimport { getAllAgents } from '@/lib/agents';\nimport { TimeRange } from '@/types/metrics';\nimport UsageChart from './components/UsageChart';\nimport RatingDistribution from './components/RatingDistribution';\nimport TopQueries from './components/TopQueries';\nimport UsageByTimeChart from './components/UsageByTimeChart';\nimport UsageByDayChart from './components/UsageByDayChart';\nimport MetricCard from './components/MetricCard';\nimport PopularAgentCard from './components/PopularAgentCard';\nimport CategoryDistributionChart from './components/CategoryDistributionChart';\n\nexport default function PopularPage() {\n  const [timeRange, setTimeRange] = useState<TimeRange>('month');\n  const [isLoading, setIsLoading] = useState(true);\n  \n  // Load data\n  const [popularAgents, setPopularAgents] = useState(getPopularAgents(5));\n  const [platformUsage, setPlatformUsage] = useState(getPlatformUsageOverTime(30));\n  const [categoryDistribution, setCategoryDistribution] = useState(getCategoryDistribution());\n  \n  // Set loading state\n  useEffect(() => {\n    setIsLoading(false);\n  }, []);\n  \n  // Handle time range change\n  const handleTimeRangeChange = (range: TimeRange) => {\n    setTimeRange(range);\n    \n    // Update platform usage data based on time range\n    let days = 30;\n    switch (range) {\n      case 'day':\n        days = 1;\n        break;\n      case 'week':\n        days = 7;\n        break;\n      case 'month':\n        days = 30;\n        break;\n      case 'year':\n        days = 365;\n        break;\n    }\n    \n    setPlatformUsage(getPlatformUsageOverTime(days));\n  };\n  \n  // Calculate total platform metrics\n  const totalAgents = getAllAgents().length;\n  const totalUses = popularAgents.reduce((sum, agent) => sum + agent.usageMetrics.totalUses, 0);\n  const averageRating = popularAgents.reduce((sum, agent) => sum + agent.usageMetrics.averageRating, 0) / popularAgents.length;\n  const averageCompletionRate = popularAgents.reduce((sum, agent) => sum + agent.usageMetrics.completionRate, 0) / popularAgents.length;\n  \n  // Render loading state\n  if (isLoading) {\n    return (\n      <div className=\"container mx-auto px-4 py-8\">\n        <h1 className=\"mb-6 text-2xl font-bold text-gray-900 dark:text-white md:text-3xl\">Popular Agents</h1>\n        <div className=\"flex h-64 items-center justify-center\">\n          <div className=\"h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-blue-600\"></div>\n        </div>\n      </div>\n    );\n  }\n  \n  return (\n    <div className=\"container mx-auto px-4 py-8\">\n      <div className=\"mb-6 flex flex-col justify-between gap-4 sm:flex-row sm:items-center\">\n        <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white md:text-3xl\">Popular Agents</h1>\n        \n        <div className=\"flex items-center space-x-2\">\n          <span className=\"text-sm text-gray-500 dark:text-gray-400\">Time Range:</span>\n          <div className=\"rounded-md border border-gray-300 dark:border-gray-600\">\n            {(['day', 'week', 'month', 'year'] as TimeRange[]).map((range) => (\n              <button\n                key={range}\n                onClick={() => handleTimeRangeChange(range)}\n                className={`px-3 py-1.5 text-sm font-medium ${\n                  timeRange === range\n                    ? 'bg-blue-600 text-white dark:bg-blue-700'\n                    : 'bg-white text-gray-700 hover:bg-gray-50 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700'\n                }`}\n              >\n                {range.charAt(0).toUpperCase() + range.slice(1)}\n              </button>\n            ))}\n          </div>\n        </div>\n      </div>\n      \n      {/* Overview metrics */}\n      <div className=\"mb-8 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4\">\n        <MetricCard\n          title=\"Total Agents\"\n          value={totalAgents}\n          change={8.5}\n          icon={\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              width=\"24\"\n              height=\"24\"\n              viewBox=\"0 0 24 24\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              strokeWidth=\"2\"\n              strokeLinecap=\"round\"\n              strokeLinejoin=\"round\"\n              className=\"h-6 w-6\"\n            >\n              <path d=\"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\" />\n              <circle cx=\"9\" cy=\"7\" r=\"4\" />\n              <path d=\"M22 21v-2a4 4 0 0 0-3-3.87\" />\n              <path d=\"M16 3.13a4 4 0 0 1 0 7.75\" />\n            </svg>\n          }\n        />\n        \n        <MetricCard\n          title=\"Total Uses\"\n          value={totalUses.toLocaleString()}\n          change={12.3}\n          icon={\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              width=\"24\"\n              height=\"24\"\n              viewBox=\"0 0 24 24\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              strokeWidth=\"2\"\n              strokeLinecap=\"round\"\n              strokeLinejoin=\"round\"\n              className=\"h-6 w-6\"\n            >\n              <path d=\"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z\" />\n              <path d=\"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z\" />\n            </svg>\n          }\n        />\n        \n        <MetricCard\n          title=\"Average Rating\"\n          value={`${averageRating.toFixed(1)}/5`}\n          change={3.2}\n          icon={\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              width=\"24\"\n              height=\"24\"\n              viewBox=\"0 0 24 24\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              strokeWidth=\"2\"\n              strokeLinecap=\"round\"\n              strokeLinejoin=\"round\"\n              className=\"h-6 w-6\"\n            >\n              <polygon points=\"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2\" />\n            </svg>\n          }\n        />\n        \n        <MetricCard\n          title=\"Completion Rate\"\n          value={`${(averageCompletionRate * 100).toFixed(0)}%`}\n          change={1.8}\n          icon={\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              width=\"24\"\n              height=\"24\"\n              viewBox=\"0 0 24 24\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              strokeWidth=\"2\"\n              strokeLinecap=\"round\"\n              strokeLinejoin=\"round\"\n              className=\"h-6 w-6\"\n            >\n              <polyline points=\"20 6 9 17 4 12\" />\n            </svg>\n          }\n        />\n      </div>\n      \n      {/* Usage chart */}\n      <div className=\"mb-8\">\n        <UsageChart\n          data={platformUsage}\n          title={`Platform Usage (${timeRange.charAt(0).toUpperCase() + timeRange.slice(1)})`}\n          height={300}\n        />\n      </div>\n      \n      {/* Popular agents */}\n      <h2 className=\"mb-4 text-xl font-bold text-gray-900 dark:text-white\">Top 5 Agents</h2>\n      <div className=\"mb-8 grid grid-cols-1 gap-4\">\n        {popularAgents.map((agent, index) => (\n          <PopularAgentCard\n            key={agent.id}\n            agent={getAllAgents().find(a => a.id === agent.id)!}\n            metrics={agent.usageMetrics}\n            rank={index + 1}\n          />\n        ))}\n      </div>\n      \n      {/* Usage patterns */}\n      <h2 className=\"mb-4 text-xl font-bold text-gray-900 dark:text-white\">Usage Patterns</h2>\n      <div className=\"mb-8 grid grid-cols-1 gap-6 md:grid-cols-2\">\n        <UsageByTimeChart\n          data={popularAgents[0].usageByTime}\n          title=\"Usage by Time of Day\"\n        />\n        \n        <UsageByDayChart\n          data={popularAgents[0].usageByDay}\n          title=\"Usage by Day of Week\"\n        />\n      </div>\n      \n      {/* Additional metrics */}\n      <div className=\"grid grid-cols-1 gap-6 md:grid-cols-3\">\n        <CategoryDistributionChart\n          data={categoryDistribution}\n          title=\"Agent Categories\"\n        />\n        \n        <RatingDistribution\n          data={popularAgents[0].userFeedback}\n          title=\"User Ratings\"\n        />\n        \n        <TopQueries\n          data={popularAgents[0].topQueries}\n          title=\"Top Queries\"\n        />\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAMA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAlBA;;;;;;;;;;;;AAoBe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;IACtD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,YAAY;IACZ,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,wHAAA,CAAA,mBAAgB,AAAD,EAAE;IACpE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,wHAAA,CAAA,2BAAwB,AAAD,EAAE;IAC5E,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,wHAAA,CAAA,0BAAuB,AAAD;IAEvF,oBAAoB;IACpB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,aAAa;QACf;gCAAG,EAAE;IAEL,2BAA2B;IAC3B,MAAM,wBAAwB,CAAC;QAC7B,aAAa;QAEb,iDAAiD;QACjD,IAAI,OAAO;QACX,OAAQ;YACN,KAAK;gBACH,OAAO;gBACP;YACF,KAAK;gBACH,OAAO;gBACP;YACF,KAAK;gBACH,OAAO;gBACP;YACF,KAAK;gBACH,OAAO;gBACP;QACJ;QAEA,iBAAiB,CAAA,GAAA,wHAAA,CAAA,2BAAwB,AAAD,EAAE;IAC5C;IAEA,mCAAmC;IACnC,MAAM,cAAc,CAAA,GAAA,uHAAA,CAAA,eAAY,AAAD,IAAI,MAAM;IACzC,MAAM,YAAY,cAAc,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,YAAY,CAAC,SAAS,EAAE;IAC3F,MAAM,gBAAgB,cAAc,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,YAAY,CAAC,aAAa,EAAE,KAAK,cAAc,MAAM;IAC5H,MAAM,wBAAwB,cAAc,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,YAAY,CAAC,cAAc,EAAE,KAAK,cAAc,MAAM;IAErI,uBAAuB;IACvB,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAAoE;;;;;;8BAClF,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;IAIvB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA+D;;;;;;kCAE7E,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAA2C;;;;;;0CAC3D,6LAAC;gCAAI,WAAU;0CACZ,AAAC;oCAAC;oCAAO;oCAAQ;oCAAS;iCAAO,CAAiB,GAAG,CAAC,CAAC,sBACtD,6LAAC;wCAEC,SAAS,IAAM,sBAAsB;wCACrC,WAAW,CAAC,gCAAgC,EAC1C,cAAc,QACV,4CACA,sGACJ;kDAED,MAAM,MAAM,CAAC,GAAG,WAAW,KAAK,MAAM,KAAK,CAAC;uCARxC;;;;;;;;;;;;;;;;;;;;;;0BAgBf,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qJAAA,CAAA,UAAU;wBACT,OAAM;wBACN,OAAO;wBACP,QAAQ;wBACR,oBACE,6LAAC;4BACC,OAAM;4BACN,OAAM;4BACN,QAAO;4BACP,SAAQ;4BACR,MAAK;4BACL,QAAO;4BACP,aAAY;4BACZ,eAAc;4BACd,gBAAe;4BACf,WAAU;;8CAEV,6LAAC;oCAAK,GAAE;;;;;;8CACR,6LAAC;oCAAO,IAAG;oCAAI,IAAG;oCAAI,GAAE;;;;;;8CACxB,6LAAC;oCAAK,GAAE;;;;;;8CACR,6LAAC;oCAAK,GAAE;;;;;;;;;;;;;;;;;kCAKd,6LAAC,qJAAA,CAAA,UAAU;wBACT,OAAM;wBACN,OAAO,UAAU,cAAc;wBAC/B,QAAQ;wBACR,oBACE,6LAAC;4BACC,OAAM;4BACN,OAAM;4BACN,QAAO;4BACP,SAAQ;4BACR,MAAK;4BACL,QAAO;4BACP,aAAY;4BACZ,eAAc;4BACd,gBAAe;4BACf,WAAU;;8CAEV,6LAAC;oCAAK,GAAE;;;;;;8CACR,6LAAC;oCAAK,GAAE;;;;;;;;;;;;;;;;;kCAKd,6LAAC,qJAAA,CAAA,UAAU;wBACT,OAAM;wBACN,OAAO,GAAG,cAAc,OAAO,CAAC,GAAG,EAAE,CAAC;wBACtC,QAAQ;wBACR,oBACE,6LAAC;4BACC,OAAM;4BACN,OAAM;4BACN,QAAO;4BACP,SAAQ;4BACR,MAAK;4BACL,QAAO;4BACP,aAAY;4BACZ,eAAc;4BACd,gBAAe;4BACf,WAAU;sCAEV,cAAA,6LAAC;gCAAQ,QAAO;;;;;;;;;;;;;;;;kCAKtB,6LAAC,qJAAA,CAAA,UAAU;wBACT,OAAM;wBACN,OAAO,GAAG,CAAC,wBAAwB,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;wBACrD,QAAQ;wBACR,oBACE,6LAAC;4BACC,OAAM;4BACN,OAAM;4BACN,QAAO;4BACP,SAAQ;4BACR,MAAK;4BACL,QAAO;4BACP,aAAY;4BACZ,eAAc;4BACd,gBAAe;4BACf,WAAU;sCAEV,cAAA,6LAAC;gCAAS,QAAO;;;;;;;;;;;;;;;;;;;;;;0BAOzB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,qJAAA,CAAA,UAAU;oBACT,MAAM;oBACN,OAAO,CAAC,gBAAgB,EAAE,UAAU,MAAM,CAAC,GAAG,WAAW,KAAK,UAAU,KAAK,CAAC,GAAG,CAAC,CAAC;oBACnF,QAAQ;;;;;;;;;;;0BAKZ,6LAAC;gBAAG,WAAU;0BAAuD;;;;;;0BACrE,6LAAC;gBAAI,WAAU;0BACZ,cAAc,GAAG,CAAC,CAAC,OAAO,sBACzB,6LAAC,2JAAA,CAAA,UAAgB;wBAEf,OAAO,CAAA,GAAA,uHAAA,CAAA,eAAY,AAAD,IAAI,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,MAAM,EAAE;wBACjD,SAAS,MAAM,YAAY;wBAC3B,MAAM,QAAQ;uBAHT,MAAM,EAAE;;;;;;;;;;0BASnB,6LAAC;gBAAG,WAAU;0BAAuD;;;;;;0BACrE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,2JAAA,CAAA,UAAgB;wBACf,MAAM,aAAa,CAAC,EAAE,CAAC,WAAW;wBAClC,OAAM;;;;;;kCAGR,6LAAC,0JAAA,CAAA,UAAe;wBACd,MAAM,aAAa,CAAC,EAAE,CAAC,UAAU;wBACjC,OAAM;;;;;;;;;;;;0BAKV,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oKAAA,CAAA,UAAyB;wBACxB,MAAM;wBACN,OAAM;;;;;;kCAGR,6LAAC,6JAAA,CAAA,UAAkB;wBACjB,MAAM,aAAa,CAAC,EAAE,CAAC,YAAY;wBACnC,OAAM;;;;;;kCAGR,6LAAC,qJAAA,CAAA,UAAU;wBACT,MAAM,aAAa,CAAC,EAAE,CAAC,UAAU;wBACjC,OAAM;;;;;;;;;;;;;;;;;;AAKhB;GAvOwB;KAAA", "debugId": null}}]}