{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/data/resources.ts"], "sourcesContent": ["import { Resource, ResourceCategoryInfo } from '@/types/resources';\n\n// Resource categories information\nexport const resourceCategories: ResourceCategoryInfo[] = [\n  {\n    id: 'productivity',\n    name: 'Productivity Tools',\n    description: 'Tools to enhance personal and team productivity, including task management, note-taking, and time tracking.',\n    icon: 'bolt'\n  },\n  {\n    id: 'project-management',\n    name: 'Project Management',\n    description: 'Software and platforms for planning, executing, and monitoring projects of all sizes.',\n    icon: 'tasks'\n  },\n  {\n    id: 'design',\n    name: 'Design Tools',\n    description: 'Resources for UI/UX design, graphic design, prototyping, and visual collaboration.',\n    icon: 'palette'\n  },\n  {\n    id: 'development',\n    name: 'Development Tools',\n    description: 'Tools and services for software development, coding, testing, and deployment.',\n    icon: 'code'\n  },\n  {\n    id: 'research',\n    name: 'Research Tools',\n    description: 'Resources for market research, user research, academic research, and data collection.',\n    icon: 'magnifying-glass-chart'\n  },\n  {\n    id: 'analytics',\n    name: 'Analytics & Data',\n    description: 'Tools for data analysis, visualization, business intelligence, and reporting.',\n    icon: 'chart-line'\n  },\n  {\n    id: 'communication',\n    name: 'Communication',\n    description: 'Platforms for team communication, client meetings, presentations, and email management.',\n    icon: 'comments'\n  },\n  {\n    id: 'collaboration',\n    name: 'Collaboration',\n    description: 'Tools that facilitate teamwork, knowledge sharing, and cross-functional collaboration.',\n    icon: 'users-gear'\n  }\n];\n\n// Sample resources data\nexport const resources: Resource[] = [\n  // Productivity Tools\n  {\n    id: 'notion-ai',\n    name: 'Notion AI',\n    description: 'AI-powered writing assistant integrated into Notion that helps draft, edit, summarize, and brainstorm content directly in your workspace.',\n    url: 'https://www.notion.so/product/ai',\n    category: 'productivity',\n    tags: ['writing-assistant', 'content-generation', 'summarization'],\n    pricing: 'paid',\n    logoUrl: 'https://upload.wikimedia.org/wikipedia/commons/4/45/Notion_app_logo.png'\n  },\n  {\n    id: 'mem',\n    name: 'Mem',\n    description: 'AI-powered workspace that automatically organizes your notes and knowledge with powerful search and retrieval capabilities.',\n    url: 'https://mem.ai',\n    category: 'productivity',\n    tags: ['note-taking', 'knowledge-management', 'ai-organization'],\n    pricing: 'freemium',\n    logoUrl: 'https://mem.ai/assets/favicons/favicon.svg'\n  },\n  {\n    id: 'otter',\n    name: 'Otter.ai',\n    description: 'AI meeting assistant that records, transcribes, and summarizes meetings in real-time with speaker identification.',\n    url: 'https://otter.ai',\n    category: 'productivity',\n    tags: ['transcription', 'meeting-assistant', 'note-taking'],\n    pricing: 'freemium',\n    logoUrl: 'https://assets-global.website-files.com/618e9316785b3582a5178502/6230b90e3dceec1c2208f309_favicon-256x256.png'\n  },\n  {\n    id: 'reclaim',\n    name: 'Reclaim.ai',\n    description: 'AI calendar assistant that automatically schedules your tasks, habits, and meetings to optimize your time and protect your calendar.',\n    url: 'https://reclaim.ai',\n    category: 'productivity',\n    tags: ['calendar-management', 'time-blocking', 'scheduling'],\n    pricing: 'freemium',\n    logoUrl: 'https://reclaim.ai/favicon/favicon-32x32.png'\n  },\n  {\n    id: 'timetask',\n    name: 'TimeTask AI',\n    description: 'AI-powered time tracking tool that automatically categorizes your work and provides insights to improve productivity.',\n    url: 'https://timetask.ai',\n    category: 'productivity',\n    tags: ['time-tracking', 'productivity-analytics', 'work-insights'],\n    pricing: 'freemium',\n    logoUrl: 'https://timetask.ai/favicon.ico'\n  },\n\n  // Project Management\n  {\n    id: 'asana-ai',\n    name: 'Asana AI',\n    description: 'AI assistant integrated into Asana that helps teams plan projects, write task descriptions, summarize discussions, and automate workflows.',\n    url: 'https://asana.com/ai',\n    category: 'project-management',\n    tags: ['task-management', 'ai-assistant', 'workflow-automation'],\n    pricing: 'paid',\n    logoUrl: 'https://upload.wikimedia.org/wikipedia/commons/3/3b/Asana_logo.svg'\n  },\n  {\n    id: 'clickup-ai',\n    name: 'ClickUp AI',\n    description: 'AI-powered project management assistant that writes, edits, summarizes, and generates content directly within your project management workflow.',\n    url: 'https://clickup.com/ai',\n    category: 'project-management',\n    tags: ['content-generation', 'task-automation', 'summarization'],\n    pricing: 'paid',\n    logoUrl: 'https://clickup.com/landing/images/clickup-logo.svg'\n  },\n  {\n    id: 'motion',\n    name: 'Motion',\n    description: 'AI-powered project management tool that automatically schedules and prioritizes tasks based on deadlines, priorities, and team capacity.',\n    url: 'https://www.usemotion.com',\n    category: 'project-management',\n    tags: ['ai-scheduling', 'task-prioritization', 'time-management'],\n    pricing: 'paid',\n    logoUrl: 'https://www.usemotion.com/favicon.png'\n  },\n  {\n    id: 'forecast',\n    name: 'Forecast',\n    description: 'AI-native platform for project and resource management that predicts project delivery dates, resource needs, and budget requirements.',\n    url: 'https://www.forecast.app',\n    category: 'project-management',\n    tags: ['resource-planning', 'project-forecasting', 'budget-management'],\n    pricing: 'paid',\n    logoUrl: 'https://www.forecast.app/hubfs/favicon-1.png'\n  },\n  {\n    id: 'teamwork-ai',\n    name: 'Teamwork AI',\n    description: 'AI-powered project management platform that automates routine tasks, provides insights, and helps teams deliver projects more efficiently.',\n    url: 'https://www.teamwork.com/ai-project-management-software',\n    category: 'project-management',\n    tags: ['task-automation', 'project-insights', 'team-collaboration'],\n    pricing: 'paid',\n    logoUrl: 'https://www.teamwork.com/app/themes/teamwork-theme/dist/images/favicon/apple-touch-icon.png'\n  },\n\n  // Design Tools\n  {\n    id: 'midjourney',\n    name: 'Midjourney',\n    description: 'AI image generation tool that creates stunning visuals from text descriptions, ideal for concept art, illustrations, and design inspiration.',\n    url: 'https://www.midjourney.com',\n    category: 'design',\n    tags: ['ai-image-generation', 'concept-art', 'visual-design'],\n    pricing: 'paid',\n    logoUrl: 'https://upload.wikimedia.org/wikipedia/commons/e/e6/Midjourney_Emblem.png'\n  },\n  {\n    id: 'figma-ai',\n    name: 'Figma AI',\n    description: 'AI-powered design features in Figma that help generate and edit designs, create variations, and improve design workflows.',\n    url: 'https://www.figma.com/ai',\n    category: 'design',\n    tags: ['ui-design', 'design-generation', 'workflow-automation'],\n    pricing: 'paid',\n    logoUrl: 'https://upload.wikimedia.org/wikipedia/commons/3/33/Figma-logo.svg'\n  },\n  {\n    id: 'gamma',\n    name: 'Gamma',\n    description: 'AI-powered presentation platform that transforms simple text prompts into beautiful, presentation-ready slides and documents.',\n    url: 'https://gamma.app',\n    category: 'design',\n    tags: ['presentations', 'content-generation', 'slide-design'],\n    pricing: 'freemium',\n    logoUrl: 'https://assets-global.website-files.com/6127a84dfe068e153ef20572/62b1399ae4703b5c4f195f80_Favicon.png'\n  },\n  {\n    id: 'canva-ai',\n    name: 'Canva AI',\n    description: 'Suite of AI tools in Canva that help generate designs, text, images, and edit photos to create professional-looking content quickly.',\n    url: 'https://www.canva.com/ai-image-generator/',\n    category: 'design',\n    tags: ['graphic-design', 'image-generation', 'text-to-image'],\n    pricing: 'freemium',\n    logoUrl: 'https://upload.wikimedia.org/wikipedia/commons/0/08/Canva_icon_2021.svg'\n  },\n  {\n    id: 'runway',\n    name: 'Runway',\n    description: 'AI-powered creative suite for generating and editing videos, images, and 3D content with text prompts and intuitive controls.',\n    url: 'https://runwayml.com',\n    category: 'design',\n    tags: ['video-generation', 'image-editing', 'creative-tools'],\n    pricing: 'freemium',\n    logoUrl: 'https://runwayml.com/favicon.svg'\n  },\n\n  // Development Tools\n  {\n    id: 'github-copilot',\n    name: 'GitHub Copilot',\n    description: 'AI pair programmer that suggests code completions and entire functions in real-time, directly in your editor.',\n    url: 'https://github.com/features/copilot',\n    category: 'development',\n    tags: ['code-completion', 'pair-programming', 'ai-coding'],\n    pricing: 'paid',\n    logoUrl: 'https://github.githubassets.com/assets/GitHub-Mark-ea2971cee799.png'\n  },\n  {\n    id: 'augment-agent',\n    name: 'Augment Agent',\n    description: 'AI coding assistant that helps developers understand, modify, and debug code with natural language, providing context-aware suggestions.',\n    url: 'https://www.augmentcode.com/',\n    category: 'development',\n    tags: ['code-understanding', 'debugging', 'ai-assistant'],\n    pricing: 'paid',\n    logoUrl: 'https://www.augmentcode.com/favicon.ico'\n  },\n  {\n    id: 'tabnine',\n    name: 'Tabnine',\n    description: 'AI code completion tool that predicts and suggests code based on context and patterns, supporting multiple programming languages and IDEs.',\n    url: 'https://www.tabnine.com',\n    category: 'development',\n    tags: ['code-completion', 'ai-assistant', 'multi-language'],\n    pricing: 'freemium',\n    logoUrl: 'https://www.tabnine.com/favicon.ico'\n  },\n  {\n    id: 'codeium',\n    name: 'Codeium',\n    description: 'Free AI-powered code completion and chat tool that helps developers write code faster with context-aware suggestions.',\n    url: 'https://codeium.com',\n    category: 'development',\n    tags: ['code-completion', 'code-chat', 'free'],\n    pricing: 'freemium',\n    logoUrl: 'https://codeium.com/favicon.ico'\n  },\n  {\n    id: 'warp',\n    name: 'Warp',\n    description: 'AI-powered terminal that makes the command line more productive with features like AI command search, suggestions, and explanations.',\n    url: 'https://www.warp.dev',\n    category: 'development',\n    tags: ['terminal', 'command-line', 'productivity'],\n    pricing: 'freemium',\n    logoUrl: 'https://assets.warp.dev/warp_logo.png'\n  },\n\n  // Research Tools\n  {\n    id: 'elicit',\n    name: 'Elicit',\n    description: 'AI research assistant that helps you find, summarize, and analyze academic papers, extracting key insights and generating literature reviews.',\n    url: 'https://elicit.org',\n    category: 'research',\n    tags: ['academic-research', 'paper-summaries', 'literature-review'],\n    pricing: 'freemium',\n    logoUrl: 'https://elicit.org/favicon.ico'\n  },\n  {\n    id: 'consensus',\n    name: 'Consensus',\n    description: 'AI-powered search engine for scientific research that provides summaries of papers and highlights key findings with citations.',\n    url: 'https://consensus.app',\n    category: 'research',\n    tags: ['scientific-search', 'paper-summaries', 'evidence-based'],\n    pricing: 'freemium',\n    logoUrl: 'https://consensus.app/favicon.ico'\n  },\n  {\n    id: 'perplexity',\n    name: 'Perplexity AI',\n    description: 'AI-powered answer engine that provides comprehensive, cited responses to complex research questions with sources and references.',\n    url: 'https://www.perplexity.ai',\n    category: 'research',\n    tags: ['answer-engine', 'information-search', 'cited-responses'],\n    pricing: 'freemium',\n    logoUrl: 'https://www.perplexity.ai/favicon.ico'\n  },\n  {\n    id: 'scholarai',\n    name: 'ScholarAI',\n    description: 'AI research assistant that helps academics search, summarize, and analyze scientific papers, generating insights and connections.',\n    url: 'https://scholarai.io',\n    category: 'research',\n    tags: ['academic-research', 'paper-analysis', 'literature-review'],\n    pricing: 'freemium',\n    logoUrl: 'https://scholarai.io/favicon.ico'\n  },\n  {\n    id: 'researchrabbit',\n    name: 'ResearchRabbit',\n    description: 'AI-powered literature discovery tool that helps researchers find relevant papers and visualize connections between publications.',\n    url: 'https://www.researchrabbit.ai',\n    category: 'research',\n    tags: ['literature-discovery', 'citation-network', 'research-mapping'],\n    pricing: 'freemium',\n    logoUrl: 'https://www.researchrabbit.ai/favicon.png'\n  },\n\n  // Analytics & Data\n  {\n    id: 'obviously-ai',\n    name: 'Obviously AI',\n    description: 'No-code AI platform that allows anyone to build and deploy machine learning models for prediction and analysis without coding.',\n    url: 'https://www.obviously.ai',\n    category: 'analytics',\n    tags: ['no-code-ai', 'predictive-analytics', 'machine-learning'],\n    pricing: 'paid',\n    logoUrl: 'https://www.obviously.ai/favicon.ico'\n  },\n  {\n    id: 'akkio',\n    name: 'Akkio',\n    description: 'AI platform that makes it easy for businesses to build, deploy, and use predictive models without data science expertise.',\n    url: 'https://www.akkio.com',\n    category: 'analytics',\n    tags: ['predictive-analytics', 'no-code', 'business-intelligence'],\n    pricing: 'paid',\n    logoUrl: 'https://www.akkio.com/favicon.ico'\n  },\n  {\n    id: 'deepnote',\n    name: 'Deepnote',\n    description: 'AI-enhanced collaborative data notebook that combines the best of notebooks, spreadsheets, and visual interfaces for data analysis.',\n    url: 'https://deepnote.com',\n    category: 'analytics',\n    tags: ['data-science', 'notebooks', 'collaboration'],\n    pricing: 'freemium',\n    logoUrl: 'https://deepnote.com/favicon.ico'\n  },\n  {\n    id: 'hex',\n    name: 'Hex',\n    description: 'Collaborative data platform with AI capabilities that helps teams explore, analyze, and share data insights through notebooks and apps.',\n    url: 'https://hex.tech',\n    category: 'analytics',\n    tags: ['data-science', 'collaboration', 'data-apps'],\n    pricing: 'freemium',\n    logoUrl: 'https://hex.tech/favicon.ico'\n  },\n  {\n    id: 'einblick',\n    name: 'Einblick',\n    description: 'AI-powered data science platform that accelerates analysis with collaborative canvas, automated insights, and predictive capabilities.',\n    url: 'https://www.einblick.ai',\n    category: 'analytics',\n    tags: ['data-science', 'visual-analytics', 'collaboration'],\n    pricing: 'freemium',\n    logoUrl: 'https://www.einblick.ai/favicon.ico'\n  },\n\n  // Communication\n  {\n    id: 'chatgpt',\n    name: 'ChatGPT',\n    description: 'AI assistant that can draft emails, summarize conversations, generate content, and help with various communication tasks.',\n    url: 'https://chat.openai.com',\n    category: 'communication',\n    tags: ['ai-assistant', 'content-generation', 'writing-help'],\n    pricing: 'freemium',\n    logoUrl: 'https://chat.openai.com/apple-touch-icon.png'\n  },\n  {\n    id: 'fireflies',\n    name: 'Fireflies.ai',\n    description: 'AI meeting assistant that records, transcribes, and analyzes voice conversations, creating searchable notes and action items.',\n    url: 'https://fireflies.ai',\n    category: 'communication',\n    tags: ['meeting-assistant', 'transcription', 'meeting-insights'],\n    pricing: 'freemium',\n    logoUrl: 'https://fireflies.ai/favicon.ico'\n  },\n  {\n    id: 'grammarly',\n    name: 'Grammarly',\n    description: 'AI writing assistant that helps improve grammar, clarity, engagement, and delivery in all types of written communication.',\n    url: 'https://www.grammarly.com',\n    category: 'communication',\n    tags: ['writing-assistant', 'grammar-checker', 'communication-improvement'],\n    pricing: 'freemium',\n    logoUrl: 'https://static.grammarly.com/assets/files/efe57d016d9efff36da7884c193b646b/favicon-32x32.png'\n  },\n  {\n    id: 'summary-ai',\n    name: 'Summary AI',\n    description: 'AI tool that automatically summarizes meetings, articles, and documents to extract key points and action items.',\n    url: 'https://www.summary.ai',\n    category: 'communication',\n    tags: ['summarization', 'meeting-notes', 'information-extraction'],\n    pricing: 'freemium',\n    logoUrl: 'https://www.summary.ai/favicon.ico'\n  },\n  {\n    id: 'descript',\n    name: 'Descript',\n    description: 'All-in-one audio and video editing platform with AI features like transcription, content editing, and voice generation.',\n    url: 'https://www.descript.com',\n    category: 'communication',\n    tags: ['video-editing', 'transcription', 'content-creation'],\n    pricing: 'freemium',\n    logoUrl: 'https://www.descript.com/favicon.ico'\n  },\n\n  // Collaboration\n  {\n    id: 'coda-ai',\n    name: 'Coda AI',\n    description: 'AI-powered collaborative document platform that combines docs, spreadsheets, and apps with AI assistance for content generation and analysis.',\n    url: 'https://coda.io/product/ai-alpha',\n    category: 'collaboration',\n    tags: ['documents', 'ai-writing', 'team-workspace'],\n    pricing: 'freemium',\n    logoUrl: 'https://cdn.coda.io/icons/png/color/coda-192.png'\n  },\n  {\n    id: 'tome',\n    name: 'Tome',\n    description: 'AI-powered storytelling format that helps teams create beautiful, interactive presentations and documents with generative AI.',\n    url: 'https://tome.app',\n    category: 'collaboration',\n    tags: ['presentations', 'storytelling', 'content-generation'],\n    pricing: 'freemium',\n    logoUrl: 'https://tome.app/favicon.ico'\n  },\n  {\n    id: 'mural-ai',\n    name: 'MURAL AI',\n    description: 'AI-enhanced digital workspace for visual collaboration that helps teams brainstorm, plan, and solve problems together.',\n    url: 'https://www.mural.co/ai-features',\n    category: 'collaboration',\n    tags: ['visual-collaboration', 'brainstorming', 'workshops'],\n    pricing: 'paid',\n    logoUrl: 'https://assets-global.website-files.com/62e11362da2667ac3d0e6ed5/62e3a2c2d605d15a6bffc4a2_favicon-32.png'\n  },\n  {\n    id: 'notion-ai-collab',\n    name: 'Notion AI for Teams',\n    description: 'AI-powered collaborative workspace that helps teams create, organize, and share knowledge with AI assistance for content and insights.',\n    url: 'https://www.notion.so/product/ai',\n    category: 'collaboration',\n    tags: ['team-workspace', 'knowledge-management', 'ai-writing'],\n    pricing: 'paid',\n    logoUrl: 'https://upload.wikimedia.org/wikipedia/commons/4/45/Notion_app_logo.png'\n  },\n  {\n    id: 'craft-ai',\n    name: 'Craft AI',\n    description: 'AI-powered document editor and knowledge base that helps teams create beautiful documents and organize information.',\n    url: 'https://www.craft.do/features/ai',\n    category: 'collaboration',\n    tags: ['documents', 'knowledge-base', 'content-creation'],\n    pricing: 'freemium',\n    logoUrl: 'https://www.craft.do/images/favicon.ico'\n  }\n];\n\n// Helper functions to filter resources by category\nexport const getResourcesByCategory = (category: string) => {\n  return resources.filter(resource => resource.category === category);\n};\n\n// Helper function to get a category by ID\nexport const getCategoryById = (id: string) => {\n  return resourceCategories.find(category => category.id === id);\n};\n"], "names": [], "mappings": ";;;;;;AAGO,MAAM,qBAA6C;IACxD;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;IACR;CACD;AAGM,MAAM,YAAwB;IACnC,qBAAqB;IACrB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAqB;YAAsB;SAAgB;QAClE,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAe;YAAwB;SAAkB;QAChE,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAiB;YAAqB;SAAc;QAC3D,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAuB;YAAiB;SAAa;QAC5D,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAiB;YAA0B;SAAgB;QAClE,SAAS;QACT,SAAS;IACX;IAEA,qBAAqB;IACrB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAmB;YAAgB;SAAsB;QAChE,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAsB;YAAmB;SAAgB;QAChE,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAiB;YAAuB;SAAkB;QACjE,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAqB;YAAuB;SAAoB;QACvE,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAmB;YAAoB;SAAqB;QACnE,SAAS;QACT,SAAS;IACX;IAEA,eAAe;IACf;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAuB;YAAe;SAAgB;QAC7D,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAa;YAAqB;SAAsB;QAC/D,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAiB;YAAsB;SAAe;QAC7D,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAkB;YAAoB;SAAgB;QAC7D,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAoB;YAAiB;SAAiB;QAC7D,SAAS;QACT,SAAS;IACX;IAEA,oBAAoB;IACpB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAmB;YAAoB;SAAY;QAC1D,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAsB;YAAa;SAAe;QACzD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAmB;YAAgB;SAAiB;QAC3D,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAmB;YAAa;SAAO;QAC9C,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAY;YAAgB;SAAe;QAClD,SAAS;QACT,SAAS;IACX;IAEA,iBAAiB;IACjB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAqB;YAAmB;SAAoB;QACnE,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAqB;YAAmB;SAAiB;QAChE,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAiB;YAAsB;SAAkB;QAChE,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAqB;YAAkB;SAAoB;QAClE,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAwB;YAAoB;SAAmB;QACtE,SAAS;QACT,SAAS;IACX;IAEA,mBAAmB;IACnB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAc;YAAwB;SAAmB;QAChE,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAwB;YAAW;SAAwB;QAClE,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAgB;YAAa;SAAgB;QACpD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAgB;YAAiB;SAAY;QACpD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAgB;YAAoB;SAAgB;QAC3D,SAAS;QACT,SAAS;IACX;IAEA,gBAAgB;IAChB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAgB;YAAsB;SAAe;QAC5D,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAqB;YAAiB;SAAmB;QAChE,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAqB;YAAmB;SAA4B;QAC3E,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAiB;YAAiB;SAAyB;QAClE,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAiB;YAAiB;SAAmB;QAC5D,SAAS;QACT,SAAS;IACX;IAEA,gBAAgB;IAChB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAa;YAAc;SAAiB;QACnD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAiB;YAAgB;SAAqB;QAC7D,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAwB;YAAiB;SAAY;QAC5D,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAkB;YAAwB;SAAa;QAC9D,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAa;YAAkB;SAAmB;QACzD,SAAS;QACT,SAAS;IACX;CACD;AAGM,MAAM,yBAAyB,CAAC;IACrC,OAAO,UAAU,MAAM,CAAC,CAAA,WAAY,SAAS,QAAQ,KAAK;AAC5D;AAGO,MAAM,kBAAkB,CAAC;IAC9B,OAAO,mBAAmB,IAAI,CAAC,CAAA,WAAY,SAAS,EAAE,KAAK;AAC7D", "debugId": null}}, {"offset": {"line": 648, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/learning/resources/%5Bcategory%5D/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport { useParams } from 'next/navigation';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { \n  faArrowLeft, \n  faSearch, \n  faExternalLinkAlt, \n  faBolt, \n  faTasks, \n  faPalette, \n  faCode, \n  faMagnifyingGlassChart, \n  faChartLine, \n  faComments, \n  faUsersGear,\n  faFilter,\n  faTag\n} from '@fortawesome/free-solid-svg-icons';\nimport { getResourcesByCategory, getCategoryById } from '@/data/resources';\n\n// Map category IDs to FontAwesome icons\nconst categoryIcons: Record<string, any> = {\n  'productivity': faBolt,\n  'project-management': faTasks,\n  'design': faPalette,\n  'development': faCode,\n  'research': faMagnifyingGlassChart,\n  'analytics': faChartLine,\n  'communication': faComments,\n  'collaboration': faUsersGear\n};\n\nexport default function CategoryPage() {\n  const params = useParams();\n  const categoryId = params.category as string;\n  const category = getCategoryById(categoryId);\n  const resources = getResourcesByCategory(categoryId);\n\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedPricing, setSelectedPricing] = useState<string[]>([]);\n\n  // Get all unique tags from resources in this category\n  const allTags = Array.from(\n    new Set(resources.flatMap(resource => resource.tags || []))\n  ).sort();\n\n  // Get all unique pricing options from resources in this category\n  const allPricingOptions = Array.from(\n    new Set(resources.map(resource => resource.pricing))\n  ).filter(Boolean) as string[];\n\n  // Filter resources based on search term and pricing filter\n  const filteredResources = resources.filter(resource => {\n    const matchesSearch = \n      resource.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      resource.description.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      (resource.tags && resource.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase())));\n    \n    const matchesPricing = \n      selectedPricing.length === 0 || \n      (resource.pricing && selectedPricing.includes(resource.pricing));\n    \n    return matchesSearch && matchesPricing;\n  });\n\n  // Toggle pricing filter\n  const togglePricingFilter = (pricing: string) => {\n    if (selectedPricing.includes(pricing)) {\n      setSelectedPricing(selectedPricing.filter(p => p !== pricing));\n    } else {\n      setSelectedPricing([...selectedPricing, pricing]);\n    }\n  };\n\n  if (!category) {\n    return (\n      <div className=\"flex flex-col items-center justify-center py-12 text-center\">\n        <h2 className=\"mb-4 text-2xl font-bold\">Category Not Found</h2>\n        <p className=\"mb-6 text-gray-600 dark:text-gray-400\">The resource category you're looking for doesn't exist.</p>\n        <Link\n          href=\"/learning/resources\"\n          className=\"flex items-center rounded-md bg-teal-600 px-4 py-2 text-white hover:bg-teal-700\"\n        >\n          <FontAwesomeIcon icon={faArrowLeft} className=\"mr-2 h-4 w-4\" />\n          Back to Resources\n        </Link>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-8\">\n      <div className=\"rounded-lg bg-gradient-to-r from-teal-600 to-emerald-700 p-8 text-white shadow-lg\">\n        <Link\n          href=\"/learning/resources\"\n          className=\"mb-4 inline-flex items-center rounded-md bg-white bg-opacity-20 px-3 py-1 text-sm font-medium text-white backdrop-blur-sm transition hover:bg-opacity-30\"\n        >\n          <FontAwesomeIcon icon={faArrowLeft} className=\"mr-2 h-3 w-3\" />\n          Back to Resources\n        </Link>\n        \n        <div className=\"flex items-center\">\n          <div className=\"mr-4 flex h-16 w-16 items-center justify-center rounded-full bg-white bg-opacity-20 backdrop-blur-sm\">\n            <FontAwesomeIcon icon={categoryIcons[category.id]} className=\"h-8 w-8\" />\n          </div>\n          <div>\n            <h1 className=\"mb-2 text-3xl font-bold\">{category.name}</h1>\n            <p className=\"text-lg\">{category.description}</p>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"flex flex-col space-y-4 md:flex-row md:space-y-0 md:space-x-4\">\n        {/* Search and filters */}\n        <div className=\"w-full md:w-1/4\">\n          <div className=\"rounded-lg border border-gray-200 bg-white p-4 shadow-sm dark:border-gray-800 dark:bg-gray-900\">\n            <h3 className=\"mb-4 font-semibold\">Search & Filter</h3>\n            \n            {/* Search input */}\n            <div className=\"mb-4\">\n              <label htmlFor=\"search\" className=\"mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                Search\n              </label>\n              <div className=\"relative\">\n                <div className=\"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3\">\n                  <FontAwesomeIcon icon={faSearch} className=\"h-4 w-4 text-gray-400\" />\n                </div>\n                <input\n                  type=\"text\"\n                  id=\"search\"\n                  className=\"block w-full rounded-md border-gray-300 pl-10 focus:border-teal-500 focus:ring-teal-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white sm:text-sm\"\n                  placeholder=\"Search resources...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                />\n              </div>\n            </div>\n            \n            {/* Pricing filter */}\n            <div className=\"mb-4\">\n              <h4 className=\"mb-2 flex items-center text-sm font-medium text-gray-700 dark:text-gray-300\">\n                <FontAwesomeIcon icon={faFilter} className=\"mr-2 h-3 w-3\" />\n                Pricing\n              </h4>\n              <div className=\"space-y-2\">\n                {allPricingOptions.map((pricing) => (\n                  <div key={pricing} className=\"flex items-center\">\n                    <input\n                      id={`pricing-${pricing}`}\n                      type=\"checkbox\"\n                      className=\"h-4 w-4 rounded border-gray-300 text-teal-600 focus:ring-teal-500 dark:border-gray-700 dark:bg-gray-800\"\n                      checked={selectedPricing.includes(pricing)}\n                      onChange={() => togglePricingFilter(pricing)}\n                    />\n                    <label\n                      htmlFor={`pricing-${pricing}`}\n                      className=\"ml-2 text-sm text-gray-700 dark:text-gray-300\"\n                    >\n                      {pricing.charAt(0).toUpperCase() + pricing.slice(1)}\n                    </label>\n                  </div>\n                ))}\n              </div>\n            </div>\n            \n            {/* Popular tags */}\n            <div>\n              <h4 className=\"mb-2 flex items-center text-sm font-medium text-gray-700 dark:text-gray-300\">\n                <FontAwesomeIcon icon={faTag} className=\"mr-2 h-3 w-3\" />\n                Popular Tags\n              </h4>\n              <div className=\"flex flex-wrap gap-2\">\n                {allTags.slice(0, 10).map((tag) => (\n                  <button\n                    key={tag}\n                    onClick={() => setSearchTerm(tag)}\n                    className=\"inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700\"\n                  >\n                    {tag}\n                  </button>\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n        \n        {/* Resources table */}\n        <div className=\"w-full md:w-3/4\">\n          <div className=\"rounded-lg border border-gray-200 bg-white shadow-sm dark:border-gray-800 dark:bg-gray-900\">\n            <div className=\"overflow-x-auto\">\n              <table className=\"min-w-full divide-y divide-gray-200 dark:divide-gray-800\">\n                <thead className=\"bg-gray-50 dark:bg-gray-800\">\n                  <tr>\n                    <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400\">\n                      Name\n                    </th>\n                    <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400\">\n                      Description\n                    </th>\n                    <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400\">\n                      Pricing\n                    </th>\n                    <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400\">\n                      Link\n                    </th>\n                  </tr>\n                </thead>\n                <tbody className=\"divide-y divide-gray-200 bg-white dark:divide-gray-800 dark:bg-gray-900\">\n                  {filteredResources.length > 0 ? (\n                    filteredResources.map((resource) => (\n                      <tr key={resource.id} className=\"hover:bg-gray-50 dark:hover:bg-gray-800\">\n                        <td className=\"whitespace-nowrap px-6 py-4\">\n                          <div className=\"flex items-center\">\n                            {resource.logoUrl && (\n                              <img\n                                src={resource.logoUrl}\n                                alt={`${resource.name} logo`}\n                                className=\"mr-3 h-8 w-8 rounded-full object-contain\"\n                              />\n                            )}\n                            <div className=\"font-medium text-gray-900 dark:text-white\">{resource.name}</div>\n                          </div>\n                        </td>\n                        <td className=\"px-6 py-4\">\n                          <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n                            {resource.description}\n                            {resource.tags && resource.tags.length > 0 && (\n                              <div className=\"mt-2 flex flex-wrap gap-1\">\n                                {resource.tags.map((tag) => (\n                                  <span\n                                    key={tag}\n                                    className=\"inline-flex items-center rounded-full bg-teal-100 px-2 py-0.5 text-xs font-medium text-teal-800 dark:bg-teal-900/30 dark:text-teal-300\"\n                                  >\n                                    {tag}\n                                  </span>\n                                ))}\n                              </div>\n                            )}\n                          </div>\n                        </td>\n                        <td className=\"whitespace-nowrap px-6 py-4\">\n                          <span className=\"inline-flex rounded-full px-2 text-xs font-semibold leading-5\">\n                            {resource.pricing === 'free' && (\n                              <span className=\"rounded-full bg-green-100 px-2 py-0.5 text-xs font-medium text-green-800 dark:bg-green-900/30 dark:text-green-300\">\n                                Free\n                              </span>\n                            )}\n                            {resource.pricing === 'freemium' && (\n                              <span className=\"rounded-full bg-blue-100 px-2 py-0.5 text-xs font-medium text-blue-800 dark:bg-blue-900/30 dark:text-blue-300\">\n                                Freemium\n                              </span>\n                            )}\n                            {resource.pricing === 'paid' && (\n                              <span className=\"rounded-full bg-purple-100 px-2 py-0.5 text-xs font-medium text-purple-800 dark:bg-purple-900/30 dark:text-purple-300\">\n                                Paid\n                              </span>\n                            )}\n                            {resource.pricing === 'enterprise' && (\n                              <span className=\"rounded-full bg-gray-100 px-2 py-0.5 text-xs font-medium text-gray-800 dark:bg-gray-700 dark:text-gray-300\">\n                                Enterprise\n                              </span>\n                            )}\n                          </span>\n                        </td>\n                        <td className=\"whitespace-nowrap px-6 py-4 text-sm font-medium\">\n                          <a\n                            href={resource.url}\n                            target=\"_blank\"\n                            rel=\"noopener noreferrer\"\n                            className=\"text-teal-600 hover:text-teal-900 dark:text-teal-400 dark:hover:text-teal-300\"\n                          >\n                            <FontAwesomeIcon icon={faExternalLinkAlt} className=\"mr-1 h-3 w-3\" />\n                            Visit\n                          </a>\n                        </td>\n                      </tr>\n                    ))\n                  ) : (\n                    <tr>\n                      <td colSpan={4} className=\"px-6 py-10 text-center\">\n                        <p className=\"text-gray-500 dark:text-gray-400\">No resources found matching your criteria.</p>\n                        <button\n                          onClick={() => {\n                            setSearchTerm('');\n                            setSelectedPricing([]);\n                          }}\n                          className=\"mt-2 text-sm font-medium text-teal-600 hover:text-teal-500 dark:text-teal-400\"\n                        >\n                          Clear filters\n                        </button>\n                      </td>\n                    </tr>\n                  )}\n                </tbody>\n              </table>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAeA;;;AArBA;;;;;;;AAuBA,wCAAwC;AACxC,MAAM,gBAAqC;IACzC,gBAAgB,2KAAA,CAAA,SAAM;IACtB,sBAAsB,2KAAA,CAAA,UAAO;IAC7B,UAAU,2KAAA,CAAA,YAAS;IACnB,eAAe,2KAAA,CAAA,SAAM;IACrB,YAAY,2KAAA,CAAA,yBAAsB;IAClC,aAAa,2KAAA,CAAA,cAAW;IACxB,iBAAiB,2KAAA,CAAA,aAAU;IAC3B,iBAAiB,2KAAA,CAAA,cAAW;AAC9B;AAEe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,aAAa,OAAO,QAAQ;IAClC,MAAM,WAAW,CAAA,GAAA,2HAAA,CAAA,kBAAe,AAAD,EAAE;IACjC,MAAM,YAAY,CAAA,GAAA,2HAAA,CAAA,yBAAsB,AAAD,EAAE;IAEzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAEnE,sDAAsD;IACtD,MAAM,UAAU,MAAM,IAAI,CACxB,IAAI,IAAI,UAAU,OAAO,CAAC,CAAA,WAAY,SAAS,IAAI,IAAI,EAAE,IACzD,IAAI;IAEN,iEAAiE;IACjE,MAAM,oBAAoB,MAAM,IAAI,CAClC,IAAI,IAAI,UAAU,GAAG,CAAC,CAAA,WAAY,SAAS,OAAO,IAClD,MAAM,CAAC;IAET,2DAA2D;IAC3D,MAAM,oBAAoB,UAAU,MAAM,CAAC,CAAA;QACzC,MAAM,gBACJ,SAAS,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC3D,SAAS,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACjE,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAE/F,MAAM,iBACJ,gBAAgB,MAAM,KAAK,KAC1B,SAAS,OAAO,IAAI,gBAAgB,QAAQ,CAAC,SAAS,OAAO;QAEhE,OAAO,iBAAiB;IAC1B;IAEA,wBAAwB;IACxB,MAAM,sBAAsB,CAAC;QAC3B,IAAI,gBAAgB,QAAQ,CAAC,UAAU;YACrC,mBAAmB,gBAAgB,MAAM,CAAC,CAAA,IAAK,MAAM;QACvD,OAAO;YACL,mBAAmB;mBAAI;gBAAiB;aAAQ;QAClD;IACF;IAEA,IAAI,CAAC,UAAU;QACb,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAA0B;;;;;;8BACxC,6LAAC;oBAAE,WAAU;8BAAwC;;;;;;8BACrD,6LAAC,+JAAA,CAAA,UAAI;oBACH,MAAK;oBACL,WAAU;;sCAEV,6LAAC,uKAAA,CAAA,kBAAe;4BAAC,MAAM,2KAAA,CAAA,cAAW;4BAAE,WAAU;;;;;;wBAAiB;;;;;;;;;;;;;IAKvE;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;;0CAEV,6LAAC,uKAAA,CAAA,kBAAe;gCAAC,MAAM,2KAAA,CAAA,cAAW;gCAAE,WAAU;;;;;;4BAAiB;;;;;;;kCAIjE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,uKAAA,CAAA,kBAAe;oCAAC,MAAM,aAAa,CAAC,SAAS,EAAE,CAAC;oCAAE,WAAU;;;;;;;;;;;0CAE/D,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA2B,SAAS,IAAI;;;;;;kDACtD,6LAAC;wCAAE,WAAU;kDAAW,SAAS,WAAW;;;;;;;;;;;;;;;;;;;;;;;;0BAKlD,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAqB;;;;;;8CAGnC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,SAAQ;4CAAS,WAAU;sDAAkE;;;;;;sDAGpG,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,uKAAA,CAAA,kBAAe;wDAAC,MAAM,2KAAA,CAAA,WAAQ;wDAAE,WAAU;;;;;;;;;;;8DAE7C,6LAAC;oDACC,MAAK;oDACL,IAAG;oDACH,WAAU;oDACV,aAAY;oDACZ,OAAO;oDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;8CAMnD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC,uKAAA,CAAA,kBAAe;oDAAC,MAAM,2KAAA,CAAA,WAAQ;oDAAE,WAAU;;;;;;gDAAiB;;;;;;;sDAG9D,6LAAC;4CAAI,WAAU;sDACZ,kBAAkB,GAAG,CAAC,CAAC,wBACtB,6LAAC;oDAAkB,WAAU;;sEAC3B,6LAAC;4DACC,IAAI,CAAC,QAAQ,EAAE,SAAS;4DACxB,MAAK;4DACL,WAAU;4DACV,SAAS,gBAAgB,QAAQ,CAAC;4DAClC,UAAU,IAAM,oBAAoB;;;;;;sEAEtC,6LAAC;4DACC,SAAS,CAAC,QAAQ,EAAE,SAAS;4DAC7B,WAAU;sEAET,QAAQ,MAAM,CAAC,GAAG,WAAW,KAAK,QAAQ,KAAK,CAAC;;;;;;;mDAZ3C;;;;;;;;;;;;;;;;8CAoBhB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC,uKAAA,CAAA,kBAAe;oDAAC,MAAM,2KAAA,CAAA,QAAK;oDAAE,WAAU;;;;;;gDAAiB;;;;;;;sDAG3D,6LAAC;4CAAI,WAAU;sDACZ,QAAQ,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,oBACzB,6LAAC;oDAEC,SAAS,IAAM,cAAc;oDAC7B,WAAU;8DAET;mDAJI;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAajB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAM,WAAU;;sDACf,6LAAC;4CAAM,WAAU;sDACf,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,OAAM;wDAAM,WAAU;kEAAoG;;;;;;kEAG9H,6LAAC;wDAAG,OAAM;wDAAM,WAAU;kEAAoG;;;;;;kEAG9H,6LAAC;wDAAG,OAAM;wDAAM,WAAU;kEAAoG;;;;;;kEAG9H,6LAAC;wDAAG,OAAM;wDAAM,WAAU;kEAAoG;;;;;;;;;;;;;;;;;sDAKlI,6LAAC;4CAAM,WAAU;sDACd,kBAAkB,MAAM,GAAG,IAC1B,kBAAkB,GAAG,CAAC,CAAC,yBACrB,6LAAC;oDAAqB,WAAU;;sEAC9B,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAI,WAAU;;oEACZ,SAAS,OAAO,kBACf,6LAAC;wEACC,KAAK,SAAS,OAAO;wEACrB,KAAK,GAAG,SAAS,IAAI,CAAC,KAAK,CAAC;wEAC5B,WAAU;;;;;;kFAGd,6LAAC;wEAAI,WAAU;kFAA6C,SAAS,IAAI;;;;;;;;;;;;;;;;;sEAG7E,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAI,WAAU;;oEACZ,SAAS,WAAW;oEACpB,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,MAAM,GAAG,mBACvC,6LAAC;wEAAI,WAAU;kFACZ,SAAS,IAAI,CAAC,GAAG,CAAC,CAAC,oBAClB,6LAAC;gFAEC,WAAU;0FAET;+EAHI;;;;;;;;;;;;;;;;;;;;;sEAUjB,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAK,WAAU;;oEACb,SAAS,OAAO,KAAK,wBACpB,6LAAC;wEAAK,WAAU;kFAAoH;;;;;;oEAIrI,SAAS,OAAO,KAAK,4BACpB,6LAAC;wEAAK,WAAU;kFAAgH;;;;;;oEAIjI,SAAS,OAAO,KAAK,wBACpB,6LAAC;wEAAK,WAAU;kFAAwH;;;;;;oEAIzI,SAAS,OAAO,KAAK,8BACpB,6LAAC;wEAAK,WAAU;kFAA6G;;;;;;;;;;;;;;;;;sEAMnI,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEACC,MAAM,SAAS,GAAG;gEAClB,QAAO;gEACP,KAAI;gEACJ,WAAU;;kFAEV,6LAAC,uKAAA,CAAA,kBAAe;wEAAC,MAAM,2KAAA,CAAA,oBAAiB;wEAAE,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;mDA7DlE,SAAS,EAAE;;;;0EAoEtB,6LAAC;0DACC,cAAA,6LAAC;oDAAG,SAAS;oDAAG,WAAU;;sEACxB,6LAAC;4DAAE,WAAU;sEAAmC;;;;;;sEAChD,6LAAC;4DACC,SAAS;gEACP,cAAc;gEACd,mBAAmB,EAAE;4DACvB;4DACA,WAAU;sEACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAczB;GA7QwB;;QACP,qIAAA,CAAA,YAAS;;;KADF", "debugId": null}}]}