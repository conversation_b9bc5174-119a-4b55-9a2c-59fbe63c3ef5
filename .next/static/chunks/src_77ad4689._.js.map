{"version": 3, "sources": [], "sections": [{"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/admin/analytics/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { \n  faChartLine, \n  faUsers, \n  faRobot, \n  faShieldAlt,\n  faArrowLeft,\n  faCalendarAlt,\n  faExclamationTriangle\n} from '@fortawesome/free-solid-svg-icons';\nimport { mockDashboardAnalytics } from '@/data/mock-analytics';\n\nexport default function AnalyticsPage() {\n  const [timeRange, setTimeRange] = useState<'day' | 'week' | 'month'>('week');\n  const analytics = mockDashboardAnalytics;\n  \n  return (\n    <div className=\"space-y-8\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <Link\n            href=\"/admin\"\n            className=\"mb-4 inline-flex items-center text-sm font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300\"\n          >\n            <FontAwesomeIcon icon={faArrowLeft} className=\"mr-2 h-4 w-4\" />\n            Back to Admin Dashboard\n          </Link>\n          <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">Analytics Dashboard</h1>\n        </div>\n        \n        {/* Time range selector */}\n        <div className=\"flex items-center rounded-lg border border-gray-200 bg-white p-1 dark:border-gray-700 dark:bg-gray-800\">\n          <button\n            onClick={() => setTimeRange('day')}\n            className={`rounded px-3 py-1.5 text-sm font-medium ${\n              timeRange === 'day'\n                ? 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900/50 dark:text-indigo-300'\n                : 'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200'\n            }`}\n          >\n            Day\n          </button>\n          <button\n            onClick={() => setTimeRange('week')}\n            className={`rounded px-3 py-1.5 text-sm font-medium ${\n              timeRange === 'week'\n                ? 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900/50 dark:text-indigo-300'\n                : 'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200'\n            }`}\n          >\n            Week\n          </button>\n          <button\n            onClick={() => setTimeRange('month')}\n            className={`rounded px-3 py-1.5 text-sm font-medium ${\n              timeRange === 'month'\n                ? 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900/50 dark:text-indigo-300'\n                : 'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200'\n            }`}\n          >\n            Month\n          </button>\n        </div>\n      </div>\n\n      {/* Overview stats */}\n      <div className=\"grid gap-6 sm:grid-cols-2 lg:grid-cols-4\">\n        {/* Total users */}\n        <div className=\"rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-800 dark:bg-gray-900\">\n          <div className=\"flex items-center\">\n            <div className=\"mr-4 flex h-12 w-12 items-center justify-center rounded-full bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400\">\n              <FontAwesomeIcon icon={faUsers} className=\"h-6 w-6\" />\n            </div>\n            <div>\n              <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Total Users</p>\n              <h3 className=\"text-2xl font-bold text-gray-900 dark:text-white\">{analytics.totalUsers.toLocaleString()}</h3>\n            </div>\n          </div>\n          <div className=\"mt-4 flex items-center text-sm\">\n            <span className=\"font-medium text-green-600 dark:text-green-400\">\n              +{analytics.newUsers[timeRange]} new\n            </span>\n            <span className=\"ml-1 text-gray-500 dark:text-gray-400\">\n              in the last {timeRange === 'day' ? '24 hours' : timeRange === 'week' ? '7 days' : '30 days'}\n            </span>\n          </div>\n        </div>\n\n        {/* Active users */}\n        <div className=\"rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-800 dark:bg-gray-900\">\n          <div className=\"flex items-center\">\n            <div className=\"mr-4 flex h-12 w-12 items-center justify-center rounded-full bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400\">\n              <FontAwesomeIcon icon={faUsers} className=\"h-6 w-6\" />\n            </div>\n            <div>\n              <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Active Users</p>\n              <h3 className=\"text-2xl font-bold text-gray-900 dark:text-white\">{analytics.activeUsers[timeRange].toLocaleString()}</h3>\n            </div>\n          </div>\n          <div className=\"mt-4 flex items-center text-sm\">\n            <span className=\"font-medium text-gray-900 dark:text-white\">\n              {Math.round((analytics.activeUsers[timeRange] / analytics.totalUsers) * 100)}%\n            </span>\n            <span className=\"ml-1 text-gray-500 dark:text-gray-400\">\n              of total users\n            </span>\n          </div>\n        </div>\n\n        {/* Agent interactions */}\n        <div className=\"rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-800 dark:bg-gray-900\">\n          <div className=\"flex items-center\">\n            <div className=\"mr-4 flex h-12 w-12 items-center justify-center rounded-full bg-purple-100 text-purple-600 dark:bg-purple-900/30 dark:text-purple-400\">\n              <FontAwesomeIcon icon={faRobot} className=\"h-6 w-6\" />\n            </div>\n            <div>\n              <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Agent Interactions</p>\n              <h3 className=\"text-2xl font-bold text-gray-900 dark:text-white\">{analytics.totalAgentInteractions.toLocaleString()}</h3>\n            </div>\n          </div>\n          <div className=\"mt-4 flex items-center text-sm\">\n            <span className=\"font-medium text-gray-900 dark:text-white\">\n              {Math.round(analytics.totalAgentInteractions / analytics.activeUsers[timeRange])}\n            </span>\n            <span className=\"ml-1 text-gray-500 dark:text-gray-400\">\n              per active user\n            </span>\n          </div>\n        </div>\n\n        {/* Sensitivity alerts */}\n        <div className=\"rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-800 dark:bg-gray-900\">\n          <div className=\"flex items-center\">\n            <div className=\"mr-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100 text-red-600 dark:bg-red-900/30 dark:text-red-400\">\n              <FontAwesomeIcon icon={faExclamationTriangle} className=\"h-6 w-6\" />\n            </div>\n            <div>\n              <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Sensitivity Alerts</p>\n              <h3 className=\"text-2xl font-bold text-gray-900 dark:text-white\">{analytics.sensitivityAlerts.toLocaleString()}</h3>\n            </div>\n          </div>\n          <div className=\"mt-4 flex items-center text-sm\">\n            <span className={`font-medium ${\n              analytics.sensitivityAlerts > 100 ? 'text-red-600 dark:text-red-400' : 'text-yellow-600 dark:text-yellow-400'\n            }`}>\n              {analytics.sensitivityAlerts > 100 ? 'High' : 'Moderate'}\n            </span>\n            <span className=\"ml-1 text-gray-500 dark:text-gray-400\">\n              alert level\n            </span>\n          </div>\n        </div>\n      </div>\n\n      {/* Navigation tabs for different analytics sections */}\n      <div className=\"border-b border-gray-200 dark:border-gray-800\">\n        <nav className=\"-mb-px flex space-x-8\">\n          <button className=\"border-indigo-500 py-4 px-1 text-sm font-medium text-indigo-600 dark:border-indigo-400 dark:text-indigo-400 border-b-2\">\n            Overview\n          </button>\n          <button className=\"border-transparent py-4 px-1 text-sm font-medium text-gray-500 hover:border-gray-300 hover:text-gray-700 dark:text-gray-400 dark:hover:border-gray-700 dark:hover:text-gray-300 border-b-2\">\n            Platform Usage\n          </button>\n          <button className=\"border-transparent py-4 px-1 text-sm font-medium text-gray-500 hover:border-gray-300 hover:text-gray-700 dark:text-gray-400 dark:hover:border-gray-700 dark:hover:text-gray-300 border-b-2\">\n            Agent Usage\n          </button>\n          <button className=\"border-transparent py-4 px-1 text-sm font-medium text-gray-500 hover:border-gray-300 hover:text-gray-700 dark:text-gray-400 dark:hover:border-gray-700 dark:hover:text-gray-300 border-b-2\">\n            Sensitive Data\n          </button>\n          <button className=\"border-transparent py-4 px-1 text-sm font-medium text-gray-500 hover:border-gray-300 hover:text-gray-700 dark:text-gray-400 dark:hover:border-gray-700 dark:hover:text-gray-300 border-b-2\">\n            User Behavior\n          </button>\n        </nav>\n      </div>\n\n      {/* Placeholder for dashboard content - will be expanded in future iterations */}\n      <div className=\"rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-800 dark:bg-gray-900\">\n        <h2 className=\"mb-4 text-lg font-semibold text-gray-900 dark:text-white\">Analytics Overview</h2>\n        <p className=\"text-gray-600 dark:text-gray-400\">\n          This dashboard provides an overview of platform usage, agent interactions, and potential sensitive data concerns.\n          Use the tabs above to explore detailed analytics for specific areas.\n        </p>\n        <div className=\"mt-4 text-sm text-gray-500 dark:text-gray-400\">\n          <p>More detailed analytics sections will be implemented in future iterations.</p>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AASA;;;AAdA;;;;;;AAgBe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4B;IACrE,MAAM,YAAY,mIAAA,CAAA,yBAAsB;IAExC,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,6LAAC,uKAAA,CAAA,kBAAe;wCAAC,MAAM,2KAAA,CAAA,cAAW;wCAAE,WAAU;;;;;;oCAAiB;;;;;;;0CAGjE,6LAAC;gCAAG,WAAU;0CAAmD;;;;;;;;;;;;kCAInE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAC,wCAAwC,EAClD,cAAc,QACV,6EACA,iFACJ;0CACH;;;;;;0CAGD,6LAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAC,wCAAwC,EAClD,cAAc,SACV,6EACA,iFACJ;0CACH;;;;;;0CAGD,6LAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAC,wCAAwC,EAClD,cAAc,UACV,6EACA,iFACJ;0CACH;;;;;;;;;;;;;;;;;;0BAOL,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,uKAAA,CAAA,kBAAe;4CAAC,MAAM,2KAAA,CAAA,UAAO;4CAAE,WAAU;;;;;;;;;;;kDAE5C,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAuD;;;;;;0DACpE,6LAAC;gDAAG,WAAU;0DAAoD,UAAU,UAAU,CAAC,cAAc;;;;;;;;;;;;;;;;;;0CAGzG,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;;4CAAiD;4CAC7D,UAAU,QAAQ,CAAC,UAAU;4CAAC;;;;;;;kDAElC,6LAAC;wCAAK,WAAU;;4CAAwC;4CACzC,cAAc,QAAQ,aAAa,cAAc,SAAS,WAAW;;;;;;;;;;;;;;;;;;;kCAMxF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,uKAAA,CAAA,kBAAe;4CAAC,MAAM,2KAAA,CAAA,UAAO;4CAAE,WAAU;;;;;;;;;;;kDAE5C,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAuD;;;;;;0DACpE,6LAAC;gDAAG,WAAU;0DAAoD,UAAU,WAAW,CAAC,UAAU,CAAC,cAAc;;;;;;;;;;;;;;;;;;0CAGrH,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;;4CACb,KAAK,KAAK,CAAC,AAAC,UAAU,WAAW,CAAC,UAAU,GAAG,UAAU,UAAU,GAAI;4CAAK;;;;;;;kDAE/E,6LAAC;wCAAK,WAAU;kDAAwC;;;;;;;;;;;;;;;;;;kCAO5D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,uKAAA,CAAA,kBAAe;4CAAC,MAAM,2KAAA,CAAA,UAAO;4CAAE,WAAU;;;;;;;;;;;kDAE5C,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAuD;;;;;;0DACpE,6LAAC;gDAAG,WAAU;0DAAoD,UAAU,sBAAsB,CAAC,cAAc;;;;;;;;;;;;;;;;;;0CAGrH,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDACb,KAAK,KAAK,CAAC,UAAU,sBAAsB,GAAG,UAAU,WAAW,CAAC,UAAU;;;;;;kDAEjF,6LAAC;wCAAK,WAAU;kDAAwC;;;;;;;;;;;;;;;;;;kCAO5D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,uKAAA,CAAA,kBAAe;4CAAC,MAAM,2KAAA,CAAA,wBAAqB;4CAAE,WAAU;;;;;;;;;;;kDAE1D,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAuD;;;;;;0DACpE,6LAAC;gDAAG,WAAU;0DAAoD,UAAU,iBAAiB,CAAC,cAAc;;;;;;;;;;;;;;;;;;0CAGhH,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAW,CAAC,YAAY,EAC5B,UAAU,iBAAiB,GAAG,MAAM,mCAAmC,wCACvE;kDACC,UAAU,iBAAiB,GAAG,MAAM,SAAS;;;;;;kDAEhD,6LAAC;wCAAK,WAAU;kDAAwC;;;;;;;;;;;;;;;;;;;;;;;;0BAQ9D,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAO,WAAU;sCAAyH;;;;;;sCAG3I,6LAAC;4BAAO,WAAU;sCAA6L;;;;;;sCAG/M,6LAAC;4BAAO,WAAU;sCAA6L;;;;;;sCAG/M,6LAAC;4BAAO,WAAU;sCAA6L;;;;;;sCAG/M,6LAAC;4BAAO,WAAU;sCAA6L;;;;;;;;;;;;;;;;;0BAOnN,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2D;;;;;;kCACzE,6LAAC;wBAAE,WAAU;kCAAmC;;;;;;kCAIhD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;sCAAE;;;;;;;;;;;;;;;;;;;;;;;AAKb;GAjLwB;KAAA", "debugId": null}}]}