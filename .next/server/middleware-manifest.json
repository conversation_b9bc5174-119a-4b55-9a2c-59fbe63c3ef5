{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_94a4b977._.js", "server/edge/chunks/[root of the server]__860be4f0._.js", "server/edge/chunks/edge-wrapper_1985d09c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "aNvwYafJBiOw/PuYk0aET69PC+Z/PGqyr6WkLc+27S4=", "__NEXT_PREVIEW_MODE_ID": "0eccd51f84e660945d0129cd3e657891", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "8e104d2d2d9f2e4bc508fcae32696f1931703eed95417406720507b38853dacb", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "8a4e327d4f237d1d901497524606edc1de2592c680f1d3357cc6e4687e20c873"}}}, "sortedMiddleware": ["/"], "functions": {}}