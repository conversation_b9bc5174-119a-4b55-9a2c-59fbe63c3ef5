{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_94a4b977._.js", "server/edge/chunks/[root of the server]__860be4f0._.js", "server/edge/chunks/edge-wrapper_1985d09c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "aNvwYafJBiOw/PuYk0aET69PC+Z/PGqyr6WkLc+27S4=", "__NEXT_PREVIEW_MODE_ID": "26eb3b8cfd41d377c4db91b31af97a1e", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "6e4fbe699dc796b0e00b5544080d7c728bf21166555ace3de7f54b014616781f", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "4be9d1233fa949debbc39449aa97260aaeba0affeb351da812d15c4a61eb4143"}}}, "sortedMiddleware": ["/"], "functions": {}}