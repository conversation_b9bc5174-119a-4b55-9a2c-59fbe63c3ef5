{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_94a4b977._.js", "server/edge/chunks/[root of the server]__860be4f0._.js", "server/edge/chunks/edge-wrapper_1985d09c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "aNvwYafJBiOw/PuYk0aET69PC+Z/PGqyr6WkLc+27S4=", "__NEXT_PREVIEW_MODE_ID": "6db32c6c4831173723dff1f58e8a7393", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "abb1d210f5e92ca8556c6fb81bd6aa7ea518688f0b13da0353c75a247b294d82", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "c97caa847aa9986199b4f8ec98932132f193fdfcd16876818b46ba1a19913ed8"}}}, "sortedMiddleware": ["/"], "functions": {}}