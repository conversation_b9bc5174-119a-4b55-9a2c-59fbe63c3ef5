{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextResponse } from 'next/server';\nimport type { NextRequest } from 'next/server';\n\n// This function can be marked `async` if using `await` inside\nexport async function middleware(request: NextRequest) {\n  const { pathname } = request.nextUrl;\n\n  // Check if the pathname is a protected route\n  const isProtectedRoute =\n    pathname.startsWith('/browse') ||\n    pathname.startsWith('/agent') ||\n    pathname.startsWith('/favorites') ||\n    pathname.startsWith('/popular') ||\n    pathname.startsWith('/recent') ||\n    pathname.startsWith('/saved') ||\n    pathname.startsWith('/analytics') ||\n    pathname.startsWith('/users') ||\n    pathname.startsWith('/manage-agents') ||\n    pathname === '/';\n\n  // Check if the pathname is an auth route\n  const isAuthRoute =\n    pathname.startsWith('/auth/login') ||\n    pathname.startsWith('/auth/register') ||\n    pathname.startsWith('/auth/forgot-password') ||\n    pathname.startsWith('/auth/error');\n\n  // Check if user is logged in by looking for the user in localStorage\n  // Note: This is a simplified approach. In a real app, you'd use cookies or JWT tokens\n  const hasUser = request.cookies.has('user');\n\n  // If the user is not authenticated and the route is protected, redirect to login\n  if (!hasUser && isProtectedRoute) {\n    const url = new URL('/auth/login', request.url);\n    url.searchParams.set('callbackUrl', encodeURI(pathname));\n    return NextResponse.redirect(url);\n  }\n\n  // If the user is authenticated and trying to access an auth route, redirect to home\n  if (hasUser && isAuthRoute) {\n    return NextResponse.redirect(new URL('/', request.url));\n  }\n\n  // Otherwise, continue with the request\n  return NextResponse.next();\n}\n\n// See \"Matching Paths\" below to learn more\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - api (API routes)\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public folder\n     */\n    '/((?!api|_next/static|_next/image|favicon.ico|public).*)',\n  ],\n};\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAIO,eAAe,WAAW,OAAoB;IACnD,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,OAAO;IAEpC,6CAA6C;IAC7C,MAAM,mBACJ,SAAS,UAAU,CAAC,cACpB,SAAS,UAAU,CAAC,aACpB,SAAS,UAAU,CAAC,iBACpB,SAAS,UAAU,CAAC,eACpB,SAAS,UAAU,CAAC,cACpB,SAAS,UAAU,CAAC,aACpB,SAAS,UAAU,CAAC,iBACpB,SAAS,UAAU,CAAC,aACpB,SAAS,UAAU,CAAC,qBACpB,aAAa;IAEf,yCAAyC;IACzC,MAAM,cACJ,SAAS,UAAU,CAAC,kBACpB,SAAS,UAAU,CAAC,qBACpB,SAAS,UAAU,CAAC,4BACpB,SAAS,UAAU,CAAC;IAEtB,qEAAqE;IACrE,sFAAsF;IACtF,MAAM,UAAU,QAAQ,OAAO,CAAC,GAAG,CAAC;IAEpC,iFAAiF;IACjF,IAAI,CAAC,WAAW,kBAAkB;QAChC,MAAM,MAAM,IAAI,IAAI,eAAe,QAAQ,GAAG;QAC9C,IAAI,YAAY,CAAC,GAAG,CAAC,eAAe,UAAU;QAC9C,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;IAC/B;IAEA,oFAAoF;IACpF,IAAI,WAAW,aAAa;QAC1B,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,KAAK,QAAQ,GAAG;IACvD;IAEA,uCAAuC;IACvC,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAGO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;;KAOC,GACD;KACD;AACH"}}]}