{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/app/api/login/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { User } from '@/types/auth';\n\n// Mock user database - in a real app, this would be a database\nconst users: User[] = [\n  {\n    id: '1',\n    name: 'Admin User',\n    email: '<EMAIL>',\n    // Password: admin123\n    password: 'admin123',\n    role: 'admin',\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  },\n  {\n    id: '2',\n    name: 'Test User',\n    email: '<EMAIL>',\n    // Password: password123\n    password: 'password123',\n    role: 'user',\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  },\n];\n\nexport async function POST(request: NextRequest) {\n  console.log('Login API called');\n  try {\n    const { email, password } = await request.json();\n\n    // Validate input\n    if (!email || !password) {\n      return NextResponse.json(\n        { success: false, message: 'Email and password are required' },\n        { status: 400 }\n      );\n    }\n\n    // Find the user\n    console.log('Looking for user with email:', email);\n    const user = users.find((u) => u.email === email);\n\n    console.log('User found:', user ? 'Yes' : 'No');\n\n    if (!user) {\n      console.log('No user found with email:', email);\n      return NextResponse.json(\n        { success: false, message: 'Invalid email or password' },\n        { status: 401 }\n      );\n    }\n\n    // Check password\n    console.log('Checking password...');\n    console.log('Input password:', password);\n    console.log('Stored password:', user.password);\n    console.log('Password match:', user.password === password);\n\n    if (user.password !== password) {\n      console.log('Password does not match');\n      return NextResponse.json(\n        { success: false, message: 'Invalid email or password' },\n        { status: 401 }\n      );\n    }\n\n    console.log('Login successful');\n\n    // Return user without password\n    const { password: _, ...userWithoutPassword } = user;\n\n    return NextResponse.json({\n      success: true,\n      user: userWithoutPassword,\n    });\n  } catch (error) {\n    console.error('Login error:', error);\n    return NextResponse.json(\n      { success: false, message: 'An error occurred during login' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAGA,+DAA+D;AAC/D,MAAM,QAAgB;IACpB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,qBAAqB;QACrB,UAAU;QACV,MAAM;QACN,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,wBAAwB;QACxB,UAAU;QACV,MAAM;QACN,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;CACD;AAEM,eAAe,KAAK,OAAoB;IAC7C,QAAQ,GAAG,CAAC;IACZ,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,MAAM,QAAQ,IAAI;QAE9C,iBAAiB;QACjB,IAAI,CAAC,SAAS,CAAC,UAAU;YACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAAkC,GAC7D;gBAAE,QAAQ;YAAI;QAElB;QAEA,gBAAgB;QAChB,QAAQ,GAAG,CAAC,gCAAgC;QAC5C,MAAM,OAAO,MAAM,IAAI,CAAC,CAAC,IAAM,EAAE,KAAK,KAAK;QAE3C,QAAQ,GAAG,CAAC,eAAe,OAAO,QAAQ;QAE1C,IAAI,CAAC,MAAM;YACT,QAAQ,GAAG,CAAC,6BAA6B;YACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAA4B,GACvD;gBAAE,QAAQ;YAAI;QAElB;QAEA,iBAAiB;QACjB,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,mBAAmB;QAC/B,QAAQ,GAAG,CAAC,oBAAoB,KAAK,QAAQ;QAC7C,QAAQ,GAAG,CAAC,mBAAmB,KAAK,QAAQ,KAAK;QAEjD,IAAI,KAAK,QAAQ,KAAK,UAAU;YAC9B,QAAQ,GAAG,CAAC;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAA4B,GACvD;gBAAE,QAAQ;YAAI;QAElB;QAEA,QAAQ,GAAG,CAAC;QAEZ,+BAA+B;QAC/B,MAAM,EAAE,UAAU,CAAC,EAAE,GAAG,qBAAqB,GAAG;QAEhD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;QACR;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gBAAgB;QAC9B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,SAAS;QAAiC,GAC5D;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}