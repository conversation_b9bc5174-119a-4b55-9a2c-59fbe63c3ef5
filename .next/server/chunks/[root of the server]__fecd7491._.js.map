{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/api/auth/register/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport bcrypt from 'bcryptjs';\nimport { User } from '@/types/auth';\n\n// Mock user database - in a real app, this would be a database\n// This should be in a shared file with the NextAuth route\nconst users: User[] = [\n  {\n    id: '1',\n    name: 'Admin User',\n    email: '<EMAIL>',\n    // Password: admin123\n    password: '$2a$10$GQH.xZIBGI0yCJfLcAEd0OWKQoEvm/tL.rKFn9ug/BTvXcU.5Uwxa',\n    role: 'admin',\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  },\n  {\n    id: '2',\n    name: 'Test User',\n    email: '<EMAIL>',\n    // Password: password123\n    password: '$2a$10$GQH.xZIBGI0yCJfLcAEd0OdQiCJtGQjbZ9m4wy8hMqOJPSHBbhKIS',\n    role: 'user',\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  },\n];\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { name, email, password } = await request.json();\n    \n    // Validate input\n    if (!name || !email || !password) {\n      return NextResponse.json(\n        { message: 'Name, email, and password are required' },\n        { status: 400 }\n      );\n    }\n    \n    // Check if user already exists\n    const existingUser = users.find((user) => user.email === email);\n    if (existingUser) {\n      return NextResponse.json(\n        { message: 'User with this email already exists' },\n        { status: 409 }\n      );\n    }\n    \n    // Hash password\n    const hashedPassword = await bcrypt.hash(password, 10);\n    \n    // Create new user\n    const newUser: User = {\n      id: (users.length + 1).toString(),\n      name,\n      email,\n      password: hashedPassword,\n      role: 'user', // Default role for new users\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    };\n    \n    // In a real app, this would save to a database\n    users.push(newUser);\n    \n    // Return success without exposing the password\n    const { password: _, ...userWithoutPassword } = newUser;\n    return NextResponse.json(\n      { message: 'User registered successfully', user: userWithoutPassword },\n      { status: 201 }\n    );\n  } catch (error) {\n    console.error('Registration error:', error);\n    return NextResponse.json(\n      { message: 'An error occurred during registration' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGA,+DAA+D;AAC/D,0DAA0D;AAC1D,MAAM,QAAgB;IACpB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,qBAAqB;QACrB,UAAU;QACV,MAAM;QACN,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,wBAAwB;QACxB,UAAU;QACV,MAAM;QACN,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;CACD;AAEM,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEpD,iBAAiB;QACjB,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,UAAU;YAChC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;YAAyC,GACpD;gBAAE,QAAQ;YAAI;QAElB;QAEA,+BAA+B;QAC/B,MAAM,eAAe,MAAM,IAAI,CAAC,CAAC,OAAS,KAAK,KAAK,KAAK;QACzD,IAAI,cAAc;YAChB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;YAAsC,GACjD;gBAAE,QAAQ;YAAI;QAElB;QAEA,gBAAgB;QAChB,MAAM,iBAAiB,MAAM,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;QAEnD,kBAAkB;QAClB,MAAM,UAAgB;YACpB,IAAI,CAAC,MAAM,MAAM,GAAG,CAAC,EAAE,QAAQ;YAC/B;YACA;YACA,UAAU;YACV,MAAM;YACN,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;QACnC;QAEA,+CAA+C;QAC/C,MAAM,IAAI,CAAC;QAEX,+CAA+C;QAC/C,MAAM,EAAE,UAAU,CAAC,EAAE,GAAG,qBAAqB,GAAG;QAChD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAgC,MAAM;QAAoB,GACrE;YAAE,QAAQ;QAAI;IAElB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;QAAwC,GACnD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}