module.exports = {

"[project]/node_modules/react-organizational-chart/dist/index.module.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Tree": (()=>Ge),
    "TreeNode": (()=>qe)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
function r(e, r) {
    return r || (r = e.slice(0)), e.raw = r, e;
}
var n = function() {
    function e(e) {
        var r = this;
        this._insertTag = function(e) {
            r.container.insertBefore(e, 0 === r.tags.length ? r.insertionPoint ? r.insertionPoint.nextSibling : r.prepend ? r.container.firstChild : r.before : r.tags[r.tags.length - 1].nextSibling), r.tags.push(e);
        }, this.isSpeedy = void 0 === e.speedy ? "production" === ("TURBOPACK compile-time value", "development") : e.speedy, this.tags = [], this.ctr = 0, this.nonce = e.nonce, this.key = e.key, this.container = e.container, this.prepend = e.prepend, this.insertionPoint = e.insertionPoint, this.before = null;
    }
    var r = e.prototype;
    return r.hydrate = function(e) {
        e.forEach(this._insertTag);
    }, r.insert = function(e) {
        this.ctr % (this.isSpeedy ? 65e3 : 1) == 0 && this._insertTag(function(e) {
            var r = document.createElement("style");
            return r.setAttribute("data-emotion", e.key), void 0 !== e.nonce && r.setAttribute("nonce", e.nonce), r.appendChild(document.createTextNode("")), r.setAttribute("data-s", ""), r;
        }(this));
        var r = this.tags[this.tags.length - 1];
        if ("TURBOPACK compile-time truthy", 1) {
            var n = 64 === e.charCodeAt(0) && 105 === e.charCodeAt(1);
            n && this._alreadyInsertedOrderInsensitiveRule && console.error("You're attempting to insert the following rule:\n" + e + "\n\n`@import` rules must be before all other types of rules in a stylesheet but other rules have already been inserted. Please ensure that `@import` rules are before all other rules."), this._alreadyInsertedOrderInsensitiveRule = this._alreadyInsertedOrderInsensitiveRule || !n;
        }
        if (this.isSpeedy) {
            var t = function(e) {
                if (e.sheet) return e.sheet;
                for(var r = 0; r < document.styleSheets.length; r++)if (document.styleSheets[r].ownerNode === e) return document.styleSheets[r];
            }(r);
            try {
                t.insertRule(e, t.cssRules.length);
            } catch (r) {
                "production" === ("TURBOPACK compile-time value", "development") || /:(-moz-placeholder|-moz-focus-inner|-moz-focusring|-ms-input-placeholder|-moz-read-write|-moz-read-only|-ms-clear){/.test(e) || console.error('There was a problem inserting the following rule: "' + e + '"', r);
            }
        } else r.appendChild(document.createTextNode(e));
        this.ctr++;
    }, r.flush = function() {
        this.tags.forEach(function(e) {
            return e.parentNode && e.parentNode.removeChild(e);
        }), this.tags = [], this.ctr = 0, "production" !== ("TURBOPACK compile-time value", "development") && (this._alreadyInsertedOrderInsensitiveRule = !1);
    }, e;
}(), t = "-ms-", o = "-webkit-", a = Math.abs, i = String.fromCharCode, s = Object.assign;
function c(e) {
    return e.trim();
}
function l(e, r, n) {
    return e.replace(r, n);
}
function u(e, r) {
    return e.indexOf(r);
}
function d(e, r) {
    return 0 | e.charCodeAt(r);
}
function f(e, r, n) {
    return e.slice(r, n);
}
function p(e) {
    return e.length;
}
function h(e) {
    return e.length;
}
function v(e, r) {
    return r.push(e), e;
}
var m = 1, g = 1, y = 0, b = 0, w = 0, k = "";
function E(e, r, n, t, o, a, i) {
    return {
        value: e,
        root: r,
        parent: n,
        type: t,
        props: o,
        children: a,
        line: m,
        column: g,
        length: i,
        return: ""
    };
}
function x(e, r) {
    return s(E("", null, null, "", null, null, 0), e, {
        length: -e.length
    }, r);
}
function N() {
    return w = b > 0 ? d(k, --b) : 0, g--, 10 === w && (g = 1, m--), w;
}
function O() {
    return w = b < y ? d(k, b++) : 0, g++, 10 === w && (g = 1, m++), w;
}
function _() {
    return d(k, b);
}
function C() {
    return b;
}
function A(e, r) {
    return f(k, e, r);
}
function $(e) {
    switch(e){
        case 0:
        case 9:
        case 10:
        case 13:
        case 32:
            return 5;
        case 33:
        case 43:
        case 44:
        case 47:
        case 62:
        case 64:
        case 126:
        case 59:
        case 123:
        case 125:
            return 4;
        case 58:
            return 3;
        case 34:
        case 39:
        case 40:
        case 91:
            return 2;
        case 41:
        case 93:
            return 1;
    }
    return 0;
}
function S(e) {
    return m = g = 1, y = p(k = e), b = 0, [];
}
function D(e) {
    return k = "", e;
}
function V(e) {
    return c(A(b - 1, z(91 === e ? e + 2 : 40 === e ? e + 1 : e)));
}
function I(e) {
    for(; (w = _()) && w < 33;)O();
    return $(e) > 2 || $(w) > 3 ? "" : " ";
}
function R(e, r) {
    for(; --r && O() && !(w < 48 || w > 102 || w > 57 && w < 65 || w > 70 && w < 97););
    return A(e, C() + (r < 6 && 32 == _() && 32 == O()));
}
function z(e) {
    for(; O();)switch(w){
        case e:
            return b;
        case 34:
        case 39:
            34 !== e && 39 !== e && z(w);
            break;
        case 40:
            41 === e && z(e);
            break;
        case 92:
            O();
    }
    return b;
}
function j(e, r) {
    for(; O() && e + w !== 57 && (e + w !== 84 || 47 !== _()););
    return "/*" + A(r, b - 1) + "*" + i(47 === e ? e : O());
}
function P(e) {
    for(; !$(_());)O();
    return A(e, b);
}
function T(e) {
    return D(q("", null, null, null, [
        ""
    ], e = S(e), 0, [
        0
    ], e));
}
function q(e, r, n, t, o, a, s, c, d) {
    for(var f = 0, h = 0, m = s, g = 0, y = 0, b = 0, w = 1, k = 1, E = 1, x = 0, A = "", $ = o, S = a, D = t, z = A; k;)switch(b = x, x = O()){
        case 40:
            if (108 != b && 58 == z.charCodeAt(m - 1)) {
                -1 != u(z += l(V(x), "&", "&\f"), "&\f") && (E = -1);
                break;
            }
        case 34:
        case 39:
        case 91:
            z += V(x);
            break;
        case 9:
        case 10:
        case 13:
        case 32:
            z += I(b);
            break;
        case 92:
            z += R(C() - 1, 7);
            continue;
        case 47:
            switch(_()){
                case 42:
                case 47:
                    v(M(j(O(), C()), r, n), d);
                    break;
                default:
                    z += "/";
            }
            break;
        case 123 * w:
            c[f++] = p(z) * E;
        case 125 * w:
        case 59:
        case 0:
            switch(x){
                case 0:
                case 125:
                    k = 0;
                case 59 + h:
                    y > 0 && p(z) - m && v(y > 32 ? Y(z + ";", t, n, m - 1) : Y(l(z, " ", "") + ";", t, n, m - 2), d);
                    break;
                case 59:
                    z += ";";
                default:
                    if (v(D = G(z, r, n, f, h, o, c, A, $ = [], S = [], m), a), 123 === x) if (0 === h) q(z, r, D, D, $, a, m, c, S);
                    else switch(g){
                        case 100:
                        case 109:
                        case 115:
                            q(e, D, D, t && v(G(e, D, D, 0, 0, o, c, A, o, $ = [], m), S), o, S, m, c, t ? $ : S);
                            break;
                        default:
                            q(z, D, D, D, [
                                ""
                            ], S, 0, c, S);
                    }
            }
            f = h = y = 0, w = E = 1, A = z = "", m = s;
            break;
        case 58:
            m = 1 + p(z), y = b;
        default:
            if (w < 1) {
                if (123 == x) --w;
                else if (125 == x && 0 == w++ && 125 == N()) continue;
            }
            switch(z += i(x), x * w){
                case 38:
                    E = h > 0 ? 1 : (z += "\f", -1);
                    break;
                case 44:
                    c[f++] = (p(z) - 1) * E, E = 1;
                    break;
                case 64:
                    45 === _() && (z += V(O())), g = _(), h = m = p(A = z += P(C())), x++;
                    break;
                case 45:
                    45 === b && 2 == p(z) && (w = 0);
            }
    }
    return a;
}
function G(e, r, n, t, o, i, s, u, d, p, v) {
    for(var m = o - 1, g = 0 === o ? i : [
        ""
    ], y = h(g), b = 0, w = 0, k = 0; b < t; ++b)for(var x = 0, N = f(e, m + 1, m = a(w = s[b])), O = e; x < y; ++x)(O = c(w > 0 ? g[x] + " " + N : l(N, /&\f/g, g[x]))) && (d[k++] = O);
    return E(e, r, n, 0 === o ? "rule" : u, d, p, v);
}
function M(e, r, n) {
    return E(e, r, n, "comm", i(w), f(e, 2, -2), 0);
}
function Y(e, r, n, t) {
    return E(e, r, n, "decl", f(e, 0, t), f(e, t + 1, -1), t);
}
function W(e, r) {
    switch(function(e, r) {
        return (((r << 2 ^ d(e, 0)) << 2 ^ d(e, 1)) << 2 ^ d(e, 2)) << 2 ^ d(e, 3);
    }(e, r)){
        case 5103:
            return o + "print-" + e + e;
        case 5737:
        case 4201:
        case 3177:
        case 3433:
        case 1641:
        case 4457:
        case 2921:
        case 5572:
        case 6356:
        case 5844:
        case 3191:
        case 6645:
        case 3005:
        case 6391:
        case 5879:
        case 5623:
        case 6135:
        case 4599:
        case 4855:
        case 4215:
        case 6389:
        case 5109:
        case 5365:
        case 5621:
        case 3829:
            return o + e + e;
        case 5349:
        case 4246:
        case 4810:
        case 6968:
        case 2756:
            return o + e + "-moz-" + e + t + e + e;
        case 6828:
        case 4268:
            return o + e + t + e + e;
        case 6165:
            return o + e + t + "flex-" + e + e;
        case 5187:
            return o + e + l(e, /(\w+).+(:[^]+)/, "-webkit-box-$1$2-ms-flex-$1$2") + e;
        case 5443:
            return o + e + t + "flex-item-" + l(e, /flex-|-self/, "") + e;
        case 4675:
            return o + e + t + "flex-line-pack" + l(e, /align-content|flex-|-self/, "") + e;
        case 5548:
            return o + e + t + l(e, "shrink", "negative") + e;
        case 5292:
            return o + e + t + l(e, "basis", "preferred-size") + e;
        case 6060:
            return o + "box-" + l(e, "-grow", "") + o + e + t + l(e, "grow", "positive") + e;
        case 4554:
            return o + l(e, /([^-])(transform)/g, "$1-webkit-$2") + e;
        case 6187:
            return l(l(l(e, /(zoom-|grab)/, o + "$1"), /(image-set)/, o + "$1"), e, "") + e;
        case 5495:
        case 3959:
            return l(e, /(image-set\([^]*)/, o + "$1$`$1");
        case 4968:
            return l(l(e, /(.+:)(flex-)?(.*)/, "-webkit-box-pack:$3-ms-flex-pack:$3"), /s.+-b[^;]+/, "justify") + o + e + e;
        case 4095:
        case 3583:
        case 4068:
        case 2532:
            return l(e, /(.+)-inline(.+)/, o + "$1$2") + e;
        case 8116:
        case 7059:
        case 5753:
        case 5535:
        case 5445:
        case 5701:
        case 4933:
        case 4677:
        case 5533:
        case 5789:
        case 5021:
        case 4765:
            if (p(e) - 1 - r > 6) switch(d(e, r + 1)){
                case 109:
                    if (45 !== d(e, r + 4)) break;
                case 102:
                    return l(e, /(.+:)(.+)-([^]+)/, "$1-webkit-$2-$3$1-moz-" + (108 == d(e, r + 3) ? "$3" : "$2-$3")) + e;
                case 115:
                    return ~u(e, "stretch") ? W(l(e, "stretch", "fill-available"), r) + e : e;
            }
            break;
        case 4949:
            if (115 !== d(e, r + 1)) break;
        case 6444:
            switch(d(e, p(e) - 3 - (~u(e, "!important") && 10))){
                case 107:
                    return l(e, ":", ":" + o) + e;
                case 101:
                    return l(e, /(.+:)([^;!]+)(;|!.+)?/, "$1" + o + (45 === d(e, 14) ? "inline-" : "") + "box$3$1" + o + "$2$3$1" + t + "$2box$3") + e;
            }
            break;
        case 5936:
            switch(d(e, r + 11)){
                case 114:
                    return o + e + t + l(e, /[svh]\w+-[tblr]{2}/, "tb") + e;
                case 108:
                    return o + e + t + l(e, /[svh]\w+-[tblr]{2}/, "tb-rl") + e;
                case 45:
                    return o + e + t + l(e, /[svh]\w+-[tblr]{2}/, "lr") + e;
            }
            return o + e + t + e + e;
    }
    return e;
}
function L(e, r) {
    for(var n = "", t = h(e), o = 0; o < t; o++)n += r(e[o], o, e, r) || "";
    return n;
}
function U(e, r, n, t) {
    switch(e.type){
        case "@import":
        case "decl":
            return e.return = e.return || e.value;
        case "comm":
            return "";
        case "@keyframes":
            return e.return = e.value + "{" + L(e.children, t) + "}";
        case "rule":
            e.value = e.props.join(",");
    }
    return p(n = L(e.children, t)) ? e.return = e.value + "{" + n + "}" : "";
}
function B(e) {
    var r = Object.create(null);
    return function(n) {
        return void 0 === r[n] && (r[n] = e(n)), r[n];
    };
}
var F = function(e, r, n) {
    for(var t = 0, o = 0; t = o, o = _(), 38 === t && 12 === o && (r[n] = 1), !$(o);)O();
    return A(e, b);
}, H = new WeakMap, J = function(e) {
    if ("rule" === e.type && e.parent && !(e.length < 1)) {
        for(var r = e.value, n = e.parent, t = e.column === n.column && e.line === n.line; "rule" !== n.type;)if (!(n = n.parent)) return;
        if ((1 !== e.props.length || 58 === r.charCodeAt(0) || H.get(n)) && !t) {
            H.set(e, !0);
            for(var o = [], a = function(e, r) {
                return D(function(e, r) {
                    var n = -1, t = 44;
                    do {
                        switch($(t)){
                            case 0:
                                38 === t && 12 === _() && (r[n] = 1), e[n] += F(b - 1, r, n);
                                break;
                            case 2:
                                e[n] += V(t);
                                break;
                            case 4:
                                if (44 === t) {
                                    e[++n] = 58 === _() ? "&\f" : "", r[n] = e[n].length;
                                    break;
                                }
                            default:
                                e[n] += i(t);
                        }
                    }while (t = O())
                    return e;
                }(S(e), r));
            }(r, o), s = n.props, c = 0, l = 0; c < a.length; c++)for(var u = 0; u < s.length; u++, l++)e.props[l] = o[c] ? a[c].replace(/&\f/g, s[u]) : s[u] + " " + a[c];
        }
    }
}, K = function(e) {
    if ("decl" === e.type) {
        var r = e.value;
        108 === r.charCodeAt(0) && 98 === r.charCodeAt(2) && (e.return = "", e.value = "");
    }
}, Z = function(e) {
    return "comm" === e.type && e.children.indexOf("emotion-disable-server-rendering-unsafe-selector-warning-please-do-not-use-this-the-warning-exists-for-a-reason") > -1;
}, Q = function(e) {
    return 105 === e.type.charCodeAt(1) && 64 === e.type.charCodeAt(0);
}, X = function(e) {
    e.type = "", e.value = "", e.return = "", e.children = "", e.props = "";
}, ee = function(e, r, n) {
    Q(e) && (e.parent ? (console.error("`@import` rules can't be nested inside other rules. Please move it to the top level and put it before regular rules. Keep in mind that they can only be used within global styles."), X(e)) : function(e, r) {
        for(var n = e - 1; n >= 0; n--)if (!Q(r[n])) return !0;
        return !1;
    }(r, n) && (console.error("`@import` rules can't be after other rules. Please put your `@import` rules before your other rules."), X(e)));
}, re = [
    function(e, r, n, a) {
        if (e.length > -1 && !e.return) switch(e.type){
            case "decl":
                e.return = W(e.value, e.length);
                break;
            case "@keyframes":
                return L([
                    x(e, {
                        value: l(e.value, "@", "@" + o)
                    })
                ], a);
            case "rule":
                if (e.length) return function(e, r) {
                    return e.map(r).join("");
                }(e.props, function(r) {
                    switch(function(e, r) {
                        return (e = /(::plac\w+|:read-\w+)/.exec(e)) ? e[0] : e;
                    }(r)){
                        case ":read-only":
                        case ":read-write":
                            return L([
                                x(e, {
                                    props: [
                                        l(r, /:(read-\w+)/, ":-moz-$1")
                                    ]
                                })
                            ], a);
                        case "::placeholder":
                            return L([
                                x(e, {
                                    props: [
                                        l(r, /:(plac\w+)/, ":-webkit-input-$1")
                                    ]
                                }),
                                x(e, {
                                    props: [
                                        l(r, /:(plac\w+)/, ":-moz-$1")
                                    ]
                                }),
                                x(e, {
                                    props: [
                                        l(r, /:(plac\w+)/, t + "input-$1")
                                    ]
                                })
                            ], a);
                    }
                    return "";
                });
        }
    }
], ne = {
    animationIterationCount: 1,
    borderImageOutset: 1,
    borderImageSlice: 1,
    borderImageWidth: 1,
    boxFlex: 1,
    boxFlexGroup: 1,
    boxOrdinalGroup: 1,
    columnCount: 1,
    columns: 1,
    flex: 1,
    flexGrow: 1,
    flexPositive: 1,
    flexShrink: 1,
    flexNegative: 1,
    flexOrder: 1,
    gridRow: 1,
    gridRowEnd: 1,
    gridRowSpan: 1,
    gridRowStart: 1,
    gridColumn: 1,
    gridColumnEnd: 1,
    gridColumnSpan: 1,
    gridColumnStart: 1,
    msGridRow: 1,
    msGridRowSpan: 1,
    msGridColumn: 1,
    msGridColumnSpan: 1,
    fontWeight: 1,
    lineHeight: 1,
    opacity: 1,
    order: 1,
    orphans: 1,
    tabSize: 1,
    widows: 1,
    zIndex: 1,
    zoom: 1,
    WebkitLineClamp: 1,
    fillOpacity: 1,
    floodOpacity: 1,
    stopOpacity: 1,
    strokeDasharray: 1,
    strokeDashoffset: 1,
    strokeMiterlimit: 1,
    strokeOpacity: 1,
    strokeWidth: 1
}, te = "You have illegal escape sequence in your template literal, most likely inside content's property value.\nBecause you write your CSS inside a JavaScript string you actually have to do double escaping, so for example \"content: '\\00d7';\" should become \"content: '\\\\00d7';\".\nYou can read more about this here:\nhttps://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Template_literals#ES2018_revision_of_illegal_escape_sequences", oe = /[A-Z]|^ms/g, ae = /_EMO_([^_]+?)_([^]*?)_EMO_/g, ie = function(e) {
    return 45 === e.charCodeAt(1);
}, se = function(e) {
    return null != e && "boolean" != typeof e;
}, ce = B(function(e) {
    return ie(e) ? e : e.replace(oe, "-$&").toLowerCase();
}), le = function(e, r) {
    switch(e){
        case "animation":
        case "animationName":
            if ("string" == typeof r) return r.replace(ae, function(e, r, n) {
                return be = {
                    name: r,
                    styles: n,
                    next: be
                }, r;
            });
    }
    return 1 === ne[e] || ie(e) || "number" != typeof r || 0 === r ? r : r + "px";
};
if ("TURBOPACK compile-time truthy", 1) {
    var ue = /(var|attr|counters?|url|(((repeating-)?(linear|radial))|conic)-gradient)\(|(no-)?(open|close)-quote/, de = [
        "normal",
        "none",
        "initial",
        "inherit",
        "unset"
    ], fe = le, pe = /^-ms-/, he = /-(.)/g, ve = {};
    le = function(e, r) {
        if ("content" === e && ("string" != typeof r || -1 === de.indexOf(r) && !ue.test(r) && (r.charAt(0) !== r.charAt(r.length - 1) || '"' !== r.charAt(0) && "'" !== r.charAt(0)))) throw new Error("You seem to be using a value for 'content' without quotes, try replacing it with `content: '\"" + r + "\"'`");
        var n = fe(e, r);
        return "" === n || ie(e) || -1 === e.indexOf("-") || void 0 !== ve[e] || (ve[e] = !0, console.error("Using kebab-case for css properties in objects is not supported. Did you mean " + e.replace(pe, "ms-").replace(he, function(e, r) {
            return r.toUpperCase();
        }) + "?")), n;
    };
}
var me = "Component selectors can only be used in conjunction with @emotion/babel-plugin, the swc Emotion plugin, or another Emotion-aware compiler transform.";
function ge(e, r, n) {
    if (null == n) return "";
    if (void 0 !== n.__emotion_styles) {
        if ("production" !== ("TURBOPACK compile-time value", "development") && "NO_COMPONENT_SELECTOR" === n.toString()) throw new Error(me);
        return n;
    }
    switch(typeof n){
        case "boolean":
            return "";
        case "object":
            if (1 === n.anim) return be = {
                name: n.name,
                styles: n.styles,
                next: be
            }, n.name;
            if (void 0 !== n.styles) {
                var t = n.next;
                if (void 0 !== t) for(; void 0 !== t;)be = {
                    name: t.name,
                    styles: t.styles,
                    next: be
                }, t = t.next;
                var o = n.styles + ";";
                return "production" !== ("TURBOPACK compile-time value", "development") && void 0 !== n.map && (o += n.map), o;
            }
            return function(e, r, n) {
                var t = "";
                if (Array.isArray(n)) for(var o = 0; o < n.length; o++)t += ge(e, r, n[o]) + ";";
                else for(var a in n){
                    var i = n[a];
                    if ("object" != typeof i) null != r && void 0 !== r[i] ? t += a + "{" + r[i] + "}" : se(i) && (t += ce(a) + ":" + le(a, i) + ";");
                    else {
                        if ("NO_COMPONENT_SELECTOR" === a && "production" !== ("TURBOPACK compile-time value", "development")) throw new Error(me);
                        if (!Array.isArray(i) || "string" != typeof i[0] || null != r && void 0 !== r[i[0]]) {
                            var s = ge(e, r, i);
                            switch(a){
                                case "animation":
                                case "animationName":
                                    t += ce(a) + ":" + s + ";";
                                    break;
                                default:
                                    "production" !== ("TURBOPACK compile-time value", "development") && "undefined" === a && console.error("You have passed in falsy value as style object's key (can happen when in example you pass unexported component as computed key)."), t += a + "{" + s + "}";
                            }
                        } else for(var c = 0; c < i.length; c++)se(i[c]) && (t += ce(a) + ":" + le(a, i[c]) + ";");
                    }
                }
                return t;
            }(e, r, n);
        case "function":
            if (void 0 !== e) {
                var a = be, i = n(e);
                return be = a, ge(e, r, i);
            }
            "production" !== ("TURBOPACK compile-time value", "development") && console.error("Functions that are interpolated in css calls will be stringified.\nIf you want to have a css call based on props, create a function that returns a css call like this\nlet dynamicStyle = (props) => css`color: ${props.color}`\nIt can be called directly with props or interpolated in a styled call like this\nlet SomeComponent = styled('div')`${dynamicStyle}`");
            break;
        case "string":
            if ("TURBOPACK compile-time truthy", 1) {
                var s = [], c = n.replace(ae, function(e, r, n) {
                    var t = "animation" + s.length;
                    return s.push("const " + t + " = keyframes`" + n.replace(/^@keyframes animation-\w+/, "") + "`"), "${" + t + "}";
                });
                s.length && console.error("`keyframes` output got interpolated into plain string, please wrap it with `css`.\n\nInstead of doing this:\n\n" + [].concat(s, [
                    "`" + c + "`"
                ]).join("\n") + "\n\nYou should wrap it with `css` like this:\n\ncss`" + c + "`");
            }
    }
    if (null == r) return n;
    var l = r[n];
    return void 0 !== l ? l : n;
}
var ye, be, we = /label:\s*([^\s;\n{]+)\s*(;|$)/g;
"production" !== ("TURBOPACK compile-time value", "development") && (ye = /\/\*#\ssourceMappingURL=data:application\/json;\S+\s+\*\//g);
var ke = function(e, r, n) {
    if (1 === e.length && "object" == typeof e[0] && null !== e[0] && void 0 !== e[0].styles) return e[0];
    var t = !0, o = "";
    be = void 0;
    var a, i = e[0];
    null == i || void 0 === i.raw ? (t = !1, o += ge(n, r, i)) : ("production" !== ("TURBOPACK compile-time value", "development") && void 0 === i[0] && console.error(te), o += i[0]);
    for(var s = 1; s < e.length; s++)o += ge(n, r, e[s]), t && ("production" !== ("TURBOPACK compile-time value", "development") && void 0 === i[s] && console.error(te), o += i[s]);
    "production" !== ("TURBOPACK compile-time value", "development") && (o = o.replace(ye, function(e) {
        return a = e, "";
    })), we.lastIndex = 0;
    for(var c, l = ""; null !== (c = we.exec(o));)l += "-" + c[1];
    var u = function(e) {
        for(var r, n = 0, t = 0, o = e.length; o >= 4; ++t, o -= 4)r = 1540483477 * (65535 & (r = 255 & e.charCodeAt(t) | (255 & e.charCodeAt(++t)) << 8 | (255 & e.charCodeAt(++t)) << 16 | (255 & e.charCodeAt(++t)) << 24)) + (59797 * (r >>> 16) << 16), n = 1540483477 * (65535 & (r ^= r >>> 24)) + (59797 * (r >>> 16) << 16) ^ 1540483477 * (65535 & n) + (59797 * (n >>> 16) << 16);
        switch(o){
            case 3:
                n ^= (255 & e.charCodeAt(t + 2)) << 16;
            case 2:
                n ^= (255 & e.charCodeAt(t + 1)) << 8;
            case 1:
                n = 1540483477 * (65535 & (n ^= 255 & e.charCodeAt(t))) + (59797 * (n >>> 16) << 16);
        }
        return (((n = 1540483477 * (65535 & (n ^= n >>> 13)) + (59797 * (n >>> 16) << 16)) ^ n >>> 15) >>> 0).toString(36);
    }(o) + l;
    return ("TURBOPACK compile-time truthy", 1) ? {
        name: u,
        styles: o,
        map: a,
        next: be,
        toString: function() {
            return "You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop).";
        }
    } : ("TURBOPACK unreachable", undefined);
};
function Ee(e, r, n) {
    var t = "";
    return n.split(" ").forEach(function(n) {
        void 0 !== e[n] ? r.push(e[n] + ";") : t += n + " ";
    }), t;
}
var xe = function(e, r, n) {
    !function(e, r, n) {
        var t = e.key + "-" + r.name;
        !1 === n && void 0 === e.registered[t] && (e.registered[t] = r.styles);
    }(e, r, n);
    var t = e.key + "-" + r.name;
    if (void 0 === e.inserted[r.name]) {
        var o = r;
        do {
            e.insert(r === o ? "." + t : "", o, e.sheet, !0), o = o.next;
        }while (void 0 !== o)
    }
};
function Ne(e, r) {
    if (void 0 === e.inserted[r.name]) return e.insert("", r, e.sheet, !0);
}
function Oe(e, r, n) {
    var t = [], o = Ee(e, t, n);
    return t.length < 2 ? n : o + r(t);
}
var _e, Ce, Ae, $e, Se, De = function e(r) {
    for(var n = "", t = 0; t < r.length; t++){
        var o = r[t];
        if (null != o) {
            var a = void 0;
            switch(typeof o){
                case "boolean":
                    break;
                case "object":
                    if (Array.isArray(o)) a = e(o);
                    else for(var i in a = "", o)o[i] && i && (a && (a += " "), a += i);
                    break;
                default:
                    a = o;
            }
            a && (n && (n += " "), n += a);
        }
    }
    return n;
}, Ve = function(e) {
    var r = function(e) {
        var r = e.key;
        if ("production" !== ("TURBOPACK compile-time value", "development") && !r) throw new Error("You have to configure `key` for your cache. Please make sure it's unique (and not equal to 'css') as it's used for linking styles to your cache.\nIf multiple caches share the same key they might \"fight\" for each other's style elements.");
        if ("css" === r) {
            var t = document.querySelectorAll("style[data-emotion]:not([data-s])");
            Array.prototype.forEach.call(t, function(e) {
                -1 !== e.getAttribute("data-emotion").indexOf(" ") && (document.head.appendChild(e), e.setAttribute("data-s", ""));
            });
        }
        var o = e.stylisPlugins || re;
        if ("production" !== ("TURBOPACK compile-time value", "development") && /[^a-z-]/.test(r)) throw new Error('Emotion key must only contain lower case alphabetical characters and - but "' + r + '" was passed');
        var a, i, s = {}, c = [];
        a = e.container || document.head, Array.prototype.forEach.call(document.querySelectorAll('style[data-emotion^="' + r + ' "]'), function(e) {
            for(var r = e.getAttribute("data-emotion").split(" "), n = 1; n < r.length; n++)s[r[n]] = !0;
            c.push(e);
        });
        var l = [
            J,
            K
        ];
        "production" !== ("TURBOPACK compile-time value", "development") && l.push(function(e) {
            return function(r, n, t) {
                if ("rule" === r.type && !e.compat) {
                    var o = r.value.match(/(:first|:nth|:nth-last)-child/g);
                    if (o) {
                        for(var a = r.parent === t[0] ? t[0].children : t, i = a.length - 1; i >= 0; i--){
                            var s = a[i];
                            if (s.line < r.line) break;
                            if (s.column < r.column) {
                                if (Z(s)) return;
                                break;
                            }
                        }
                        o.forEach(function(e) {
                            console.error('The pseudo class "' + e + '" is potentially unsafe when doing server-side rendering. Try changing it to "' + e.split("-child")[0] + '-of-type".');
                        });
                    }
                }
            };
        }({
            get compat () {
                return v.compat;
            }
        }), ee);
        var u, d, f = [
            U,
            ("TURBOPACK compile-time truthy", 1) ? function(e) {
                e.root || (e.return ? u.insert(e.return) : e.value && "comm" !== e.type && u.insert(e.value + "{}"));
            } : ("TURBOPACK unreachable", undefined)
        ], p = function(e) {
            var r = h(e);
            return function(n, t, o, a) {
                for(var i = "", s = 0; s < r; s++)i += e[s](n, t, o, a) || "";
                return i;
            };
        }(l.concat(o, f));
        i = function(e, r, n, t) {
            u = n, "production" !== ("TURBOPACK compile-time value", "development") && void 0 !== r.map && (u = {
                insert: function(e) {
                    n.insert(e + r.map);
                }
            }), L(T(e ? e + "{" + r.styles + "}" : r.styles), p), t && (v.inserted[r.name] = !0);
        };
        var v = {
            key: r,
            sheet: new n({
                key: r,
                container: a,
                nonce: e.nonce,
                speedy: e.speedy,
                prepend: e.prepend,
                insertionPoint: e.insertionPoint
            }),
            nonce: e.nonce,
            inserted: s,
            registered: {},
            insert: i
        };
        return v.sheet.hydrate(c), v;
    }({
        key: "css"
    });
    r.sheet.speedy = function(e) {
        if ("production" !== ("TURBOPACK compile-time value", "development") && 0 !== this.ctr) throw new Error("speedy must be changed before any rules are inserted");
        this.isSpeedy = e;
    }, r.compat = !0;
    var t = function() {
        for(var e = arguments.length, n = new Array(e), t = 0; t < e; t++)n[t] = arguments[t];
        var o = ke(n, r.registered, void 0);
        return xe(r, o, !1), r.key + "-" + o.name;
    };
    return {
        css: t,
        cx: function() {
            for(var e = arguments.length, n = new Array(e), o = 0; o < e; o++)n[o] = arguments[o];
            return Oe(r.registered, t, De(n));
        },
        injectGlobal: function() {
            for(var e = arguments.length, n = new Array(e), t = 0; t < e; t++)n[t] = arguments[t];
            var o = ke(n, r.registered);
            Ne(r, o);
        },
        keyframes: function() {
            for(var e = arguments.length, n = new Array(e), t = 0; t < e; t++)n[t] = arguments[t];
            var o = ke(n, r.registered), a = "animation-" + o.name;
            return Ne(r, {
                name: o.name,
                styles: "@keyframes " + a + "{" + o.styles + "}"
            }), a;
        },
        hydrate: function(e) {
            e.forEach(function(e) {
                r.inserted[e] = !0;
            });
        },
        flush: function() {
            r.registered = {}, r.inserted = {}, r.sheet.flush();
        },
        sheet: r.sheet,
        cache: r,
        getRegisteredStyles: Ee.bind(null, r.registered),
        merge: Oe.bind(null, r.registered, t)
    };
}(), Ie = Ve.cx, Re = Ve.css, ze = Re(_e || (_e = r([
    "\n  content: '';\n  position: absolute;\n  top: 0;\n  height: var(--tree-line-height);\n  box-sizing: border-box;\n"
]))), je = Re(Ce || (Ce = r([
    "\n  display: flex;\n  padding-inline-start: 0;\n  margin: 0;\n  padding-top: var(--tree-line-height);\n  position: relative;\n\n  ::before {\n    ",
    ";\n    left: calc(50% - var(--tree-line-width) / 2);\n    width: 0;\n    border-left: var(--tree-line-width) var(--tree-node-line-style)\n      var(--tree-line-color);\n  }\n"
])), ze), Pe = Re(Ae || (Ae = r([
    "\n  flex: auto;\n  text-align: center;\n  list-style-type: none;\n  position: relative;\n  padding: var(--tree-line-height) var(--tree-node-padding) 0\n    var(--tree-node-padding);\n"
]))), Te = Re($e || ($e = r([
    "\n  ::before,\n  ::after {\n    ",
    ";\n    right: 50%;\n    width: 50%;\n    border-top: var(--tree-line-width) var(--tree-node-line-style)\n      var(--tree-line-color);\n  }\n  ::after {\n    left: 50%;\n    border-left: var(--tree-line-width) var(--tree-node-line-style)\n      var(--tree-line-color);\n  }\n\n  :only-of-type {\n    padding: 0;\n    ::after,\n    :before {\n      display: none;\n    }\n  }\n\n  :first-of-type {\n    ::before {\n      border: 0 none;\n    }\n    ::after {\n      border-radius: var(--tree-line-border-radius) 0 0 0;\n    }\n  }\n\n  :last-of-type {\n    ::before {\n      border-right: var(--tree-line-width) var(--tree-node-line-style)\n        var(--tree-line-color);\n      border-radius: 0 var(--tree-line-border-radius) 0 0;\n    }\n    ::after {\n      border: 0 none;\n    }\n  }\n"
])), ze);
function qe(r) {
    var n = r.children, t = r.label;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("li", {
        className: Ie(Pe, Te, r.className)
    }, t, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Children"].count(n) > 0 && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("ul", {
        className: je
    }, n));
}
function Ge(n) {
    var t = n.children, o = n.label, a = n.lineHeight, i = void 0 === a ? "20px" : a, s = n.lineWidth, c = void 0 === s ? "1px" : s, l = n.lineColor, u = void 0 === l ? "black" : l, d = n.nodePadding, f = void 0 === d ? "5px" : d, p = n.lineStyle, h = void 0 === p ? "solid" : p, v = n.lineBorderRadius, m = void 0 === v ? "5px" : v;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("ul", {
        className: Re(Se || (Se = r([
            "\n        padding-inline-start: 0;\n        margin: 0;\n        display: flex;\n\n        --line-height: ",
            ";\n        --line-width: ",
            ";\n        --line-color: ",
            ";\n        --line-border-radius: ",
            ";\n        --line-style: ",
            ";\n        --node-padding: ",
            ";\n\n        --tree-line-height: var(--line-height, 20px);\n        --tree-line-width: var(--line-width, 1px);\n        --tree-line-color: var(--line-color, black);\n        --tree-line-border-radius: var(--line-border-radius, 5px);\n        --tree-node-line-style: var(--line-style, solid);\n        --tree-node-padding: var(--node-padding, 5px);\n      "
        ])), i, c, u, m, h, f)
    }, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])(qe, {
        label: o
    }, t));
}
;
 //# sourceMappingURL=index.module.js.map
}}),

};

//# sourceMappingURL=node_modules_react-organizational-chart_dist_index_module_bf8cd484.js.map