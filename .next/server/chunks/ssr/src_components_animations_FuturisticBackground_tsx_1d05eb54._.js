module.exports = {

"[project]/src/components/animations/FuturisticBackground.tsx [app-rsc] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/src_components_animations_FuturisticBackground_tsx_115ed253._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/animations/FuturisticBackground.tsx [app-rsc] (ecmascript, next/dynamic entry)");
    });
});
}}),

};