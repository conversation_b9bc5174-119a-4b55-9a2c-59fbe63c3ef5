{"version": 3, "sources": [], "sections": [{"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/admin/analytics/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { \n  faChartLine, \n  faUsers, \n  faRobot, \n  faShieldAlt,\n  faArrowLeft,\n  faCalendarAlt,\n  faExclamationTriangle\n} from '@fortawesome/free-solid-svg-icons';\nimport { mockDashboardAnalytics } from '@/data/mock-analytics';\n\nexport default function AnalyticsPage() {\n  const [timeRange, setTimeRange] = useState<'day' | 'week' | 'month'>('week');\n  const analytics = mockDashboardAnalytics;\n  \n  return (\n    <div className=\"space-y-8\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <Link\n            href=\"/admin\"\n            className=\"mb-4 inline-flex items-center text-sm font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300\"\n          >\n            <FontAwesomeIcon icon={faArrowLeft} className=\"mr-2 h-4 w-4\" />\n            Back to Admin Dashboard\n          </Link>\n          <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">Analytics Dashboard</h1>\n        </div>\n        \n        {/* Time range selector */}\n        <div className=\"flex items-center rounded-lg border border-gray-200 bg-white p-1 dark:border-gray-700 dark:bg-gray-800\">\n          <button\n            onClick={() => setTimeRange('day')}\n            className={`rounded px-3 py-1.5 text-sm font-medium ${\n              timeRange === 'day'\n                ? 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900/50 dark:text-indigo-300'\n                : 'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200'\n            }`}\n          >\n            Day\n          </button>\n          <button\n            onClick={() => setTimeRange('week')}\n            className={`rounded px-3 py-1.5 text-sm font-medium ${\n              timeRange === 'week'\n                ? 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900/50 dark:text-indigo-300'\n                : 'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200'\n            }`}\n          >\n            Week\n          </button>\n          <button\n            onClick={() => setTimeRange('month')}\n            className={`rounded px-3 py-1.5 text-sm font-medium ${\n              timeRange === 'month'\n                ? 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900/50 dark:text-indigo-300'\n                : 'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200'\n            }`}\n          >\n            Month\n          </button>\n        </div>\n      </div>\n\n      {/* Overview stats */}\n      <div className=\"grid gap-6 sm:grid-cols-2 lg:grid-cols-4\">\n        {/* Total users */}\n        <div className=\"rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-800 dark:bg-gray-900\">\n          <div className=\"flex items-center\">\n            <div className=\"mr-4 flex h-12 w-12 items-center justify-center rounded-full bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400\">\n              <FontAwesomeIcon icon={faUsers} className=\"h-6 w-6\" />\n            </div>\n            <div>\n              <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Total Users</p>\n              <h3 className=\"text-2xl font-bold text-gray-900 dark:text-white\">{analytics.totalUsers.toLocaleString()}</h3>\n            </div>\n          </div>\n          <div className=\"mt-4 flex items-center text-sm\">\n            <span className=\"font-medium text-green-600 dark:text-green-400\">\n              +{analytics.newUsers[timeRange]} new\n            </span>\n            <span className=\"ml-1 text-gray-500 dark:text-gray-400\">\n              in the last {timeRange === 'day' ? '24 hours' : timeRange === 'week' ? '7 days' : '30 days'}\n            </span>\n          </div>\n        </div>\n\n        {/* Active users */}\n        <div className=\"rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-800 dark:bg-gray-900\">\n          <div className=\"flex items-center\">\n            <div className=\"mr-4 flex h-12 w-12 items-center justify-center rounded-full bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400\">\n              <FontAwesomeIcon icon={faUsers} className=\"h-6 w-6\" />\n            </div>\n            <div>\n              <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Active Users</p>\n              <h3 className=\"text-2xl font-bold text-gray-900 dark:text-white\">{analytics.activeUsers[timeRange].toLocaleString()}</h3>\n            </div>\n          </div>\n          <div className=\"mt-4 flex items-center text-sm\">\n            <span className=\"font-medium text-gray-900 dark:text-white\">\n              {Math.round((analytics.activeUsers[timeRange] / analytics.totalUsers) * 100)}%\n            </span>\n            <span className=\"ml-1 text-gray-500 dark:text-gray-400\">\n              of total users\n            </span>\n          </div>\n        </div>\n\n        {/* Agent interactions */}\n        <div className=\"rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-800 dark:bg-gray-900\">\n          <div className=\"flex items-center\">\n            <div className=\"mr-4 flex h-12 w-12 items-center justify-center rounded-full bg-purple-100 text-purple-600 dark:bg-purple-900/30 dark:text-purple-400\">\n              <FontAwesomeIcon icon={faRobot} className=\"h-6 w-6\" />\n            </div>\n            <div>\n              <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Agent Interactions</p>\n              <h3 className=\"text-2xl font-bold text-gray-900 dark:text-white\">{analytics.totalAgentInteractions.toLocaleString()}</h3>\n            </div>\n          </div>\n          <div className=\"mt-4 flex items-center text-sm\">\n            <span className=\"font-medium text-gray-900 dark:text-white\">\n              {Math.round(analytics.totalAgentInteractions / analytics.activeUsers[timeRange])}\n            </span>\n            <span className=\"ml-1 text-gray-500 dark:text-gray-400\">\n              per active user\n            </span>\n          </div>\n        </div>\n\n        {/* Sensitivity alerts */}\n        <div className=\"rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-800 dark:bg-gray-900\">\n          <div className=\"flex items-center\">\n            <div className=\"mr-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100 text-red-600 dark:bg-red-900/30 dark:text-red-400\">\n              <FontAwesomeIcon icon={faExclamationTriangle} className=\"h-6 w-6\" />\n            </div>\n            <div>\n              <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Sensitivity Alerts</p>\n              <h3 className=\"text-2xl font-bold text-gray-900 dark:text-white\">{analytics.sensitivityAlerts.toLocaleString()}</h3>\n            </div>\n          </div>\n          <div className=\"mt-4 flex items-center text-sm\">\n            <span className={`font-medium ${\n              analytics.sensitivityAlerts > 100 ? 'text-red-600 dark:text-red-400' : 'text-yellow-600 dark:text-yellow-400'\n            }`}>\n              {analytics.sensitivityAlerts > 100 ? 'High' : 'Moderate'}\n            </span>\n            <span className=\"ml-1 text-gray-500 dark:text-gray-400\">\n              alert level\n            </span>\n          </div>\n        </div>\n      </div>\n\n      {/* Navigation tabs for different analytics sections */}\n      <div className=\"border-b border-gray-200 dark:border-gray-800\">\n        <nav className=\"-mb-px flex space-x-8\">\n          <button className=\"border-indigo-500 py-4 px-1 text-sm font-medium text-indigo-600 dark:border-indigo-400 dark:text-indigo-400 border-b-2\">\n            Overview\n          </button>\n          <button className=\"border-transparent py-4 px-1 text-sm font-medium text-gray-500 hover:border-gray-300 hover:text-gray-700 dark:text-gray-400 dark:hover:border-gray-700 dark:hover:text-gray-300 border-b-2\">\n            Platform Usage\n          </button>\n          <button className=\"border-transparent py-4 px-1 text-sm font-medium text-gray-500 hover:border-gray-300 hover:text-gray-700 dark:text-gray-400 dark:hover:border-gray-700 dark:hover:text-gray-300 border-b-2\">\n            Agent Usage\n          </button>\n          <button className=\"border-transparent py-4 px-1 text-sm font-medium text-gray-500 hover:border-gray-300 hover:text-gray-700 dark:text-gray-400 dark:hover:border-gray-700 dark:hover:text-gray-300 border-b-2\">\n            Sensitive Data\n          </button>\n          <button className=\"border-transparent py-4 px-1 text-sm font-medium text-gray-500 hover:border-gray-300 hover:text-gray-700 dark:text-gray-400 dark:hover:border-gray-700 dark:hover:text-gray-300 border-b-2\">\n            User Behavior\n          </button>\n        </nav>\n      </div>\n\n      {/* Placeholder for dashboard content - will be expanded in future iterations */}\n      <div className=\"rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-800 dark:bg-gray-900\">\n        <h2 className=\"mb-4 text-lg font-semibold text-gray-900 dark:text-white\">Analytics Overview</h2>\n        <p className=\"text-gray-600 dark:text-gray-400\">\n          This dashboard provides an overview of platform usage, agent interactions, and potential sensitive data concerns.\n          Use the tabs above to explore detailed analytics for specific areas.\n        </p>\n        <div className=\"mt-4 text-sm text-gray-500 dark:text-gray-400\">\n          <p>More detailed analytics sections will be implemented in future iterations.</p>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AASA;AAdA;;;;;;;AAgBe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4B;IACrE,MAAM,YAAY,gIAAA,CAAA,yBAAsB;IAExC,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC,oKAAA,CAAA,kBAAe;wCAAC,MAAM,wKAAA,CAAA,cAAW;wCAAE,WAAU;;;;;;oCAAiB;;;;;;;0CAGjE,8OAAC;gCAAG,WAAU;0CAAmD;;;;;;;;;;;;kCAInE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAC,wCAAwC,EAClD,cAAc,QACV,6EACA,iFACJ;0CACH;;;;;;0CAGD,8OAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAC,wCAAwC,EAClD,cAAc,SACV,6EACA,iFACJ;0CACH;;;;;;0CAGD,8OAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAC,wCAAwC,EAClD,cAAc,UACV,6EACA,iFACJ;0CACH;;;;;;;;;;;;;;;;;;0BAOL,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oKAAA,CAAA,kBAAe;4CAAC,MAAM,wKAAA,CAAA,UAAO;4CAAE,WAAU;;;;;;;;;;;kDAE5C,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAuD;;;;;;0DACpE,8OAAC;gDAAG,WAAU;0DAAoD,UAAU,UAAU,CAAC,cAAc;;;;;;;;;;;;;;;;;;0CAGzG,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;;4CAAiD;4CAC7D,UAAU,QAAQ,CAAC,UAAU;4CAAC;;;;;;;kDAElC,8OAAC;wCAAK,WAAU;;4CAAwC;4CACzC,cAAc,QAAQ,aAAa,cAAc,SAAS,WAAW;;;;;;;;;;;;;;;;;;;kCAMxF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oKAAA,CAAA,kBAAe;4CAAC,MAAM,wKAAA,CAAA,UAAO;4CAAE,WAAU;;;;;;;;;;;kDAE5C,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAuD;;;;;;0DACpE,8OAAC;gDAAG,WAAU;0DAAoD,UAAU,WAAW,CAAC,UAAU,CAAC,cAAc;;;;;;;;;;;;;;;;;;0CAGrH,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;;4CACb,KAAK,KAAK,CAAC,AAAC,UAAU,WAAW,CAAC,UAAU,GAAG,UAAU,UAAU,GAAI;4CAAK;;;;;;;kDAE/E,8OAAC;wCAAK,WAAU;kDAAwC;;;;;;;;;;;;;;;;;;kCAO5D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oKAAA,CAAA,kBAAe;4CAAC,MAAM,wKAAA,CAAA,UAAO;4CAAE,WAAU;;;;;;;;;;;kDAE5C,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAuD;;;;;;0DACpE,8OAAC;gDAAG,WAAU;0DAAoD,UAAU,sBAAsB,CAAC,cAAc;;;;;;;;;;;;;;;;;;0CAGrH,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDACb,KAAK,KAAK,CAAC,UAAU,sBAAsB,GAAG,UAAU,WAAW,CAAC,UAAU;;;;;;kDAEjF,8OAAC;wCAAK,WAAU;kDAAwC;;;;;;;;;;;;;;;;;;kCAO5D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oKAAA,CAAA,kBAAe;4CAAC,MAAM,wKAAA,CAAA,wBAAqB;4CAAE,WAAU;;;;;;;;;;;kDAE1D,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAuD;;;;;;0DACpE,8OAAC;gDAAG,WAAU;0DAAoD,UAAU,iBAAiB,CAAC,cAAc;;;;;;;;;;;;;;;;;;0CAGhH,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAW,CAAC,YAAY,EAC5B,UAAU,iBAAiB,GAAG,MAAM,mCAAmC,wCACvE;kDACC,UAAU,iBAAiB,GAAG,MAAM,SAAS;;;;;;kDAEhD,8OAAC;wCAAK,WAAU;kDAAwC;;;;;;;;;;;;;;;;;;;;;;;;0BAQ9D,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAO,WAAU;sCAAyH;;;;;;sCAG3I,8OAAC;4BAAO,WAAU;sCAA6L;;;;;;sCAG/M,8OAAC;4BAAO,WAAU;sCAA6L;;;;;;sCAG/M,8OAAC;4BAAO,WAAU;sCAA6L;;;;;;sCAG/M,8OAAC;4BAAO,WAAU;sCAA6L;;;;;;;;;;;;;;;;;0BAOnN,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA2D;;;;;;kCACzE,8OAAC;wBAAE,WAAU;kCAAmC;;;;;;kCAIhD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;sCAAE;;;;;;;;;;;;;;;;;;;;;;;AAKb", "debugId": null}}]}