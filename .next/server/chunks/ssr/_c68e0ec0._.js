module.exports = {

"[project]/.next-internal/server/app/page/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[project]/src/app/favicon.ico.mjs { IMAGE => \"[project]/src/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/favicon.ico.mjs { IMAGE => \"[project]/src/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript)"));
}}),
"[project]/src/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/layout.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/src/lib/agents.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createMessage": (()=>createMessage),
    "getAgentById": (()=>getAgentById),
    "getAgentsByCategory": (()=>getAgentsByCategory),
    "getAgentsByIds": (()=>getAgentsByIds),
    "getAgentsByTag": (()=>getAgentsByTag),
    "getAllAgents": (()=>getAllAgents),
    "getAllCategories": (()=>getAllCategories),
    "getAllTags": (()=>getAllTags),
    "getFeaturedAgents": (()=>getFeaturedAgents),
    "getNewAgents": (()=>getNewAgents),
    "getRelatedAgents": (()=>getRelatedAgents),
    "getSessionById": (()=>getSessionById),
    "getSessionsForAgent": (()=>getSessionsForAgent),
    "searchAgents": (()=>searchAgents)
});
// Mock data for agents
const agents = [
    {
        id: '1',
        name: 'Data Analyst',
        description: 'Analyze data sets and generate insights with natural language queries.',
        longDescription: 'The Data Analyst agent helps you explore and understand your data through natural language. Ask questions about your data, request visualizations, or get statistical summaries without writing complex queries or code. This agent can work with various data formats including CSV, Excel, SQL databases, and more.',
        category: 'Analytics',
        capabilities: [
            'Natural language data queries',
            'Data visualization generation',
            'Statistical analysis',
            'Anomaly detection',
            'Trend identification',
            'Report generation'
        ],
        usageCount: 1245,
        isNew: false,
        isFeatured: true,
        createdAt: '2023-10-15',
        updatedAt: '2024-03-01',
        version: '2.3.0',
        creator: 'Data Science Team',
        avatarUrl: '/agents/data-analyst.svg',
        tags: [
            'data',
            'analytics',
            'visualization',
            'statistics'
        ],
        relatedAgentIds: [
            '5',
            '7',
            '8'
        ]
    },
    {
        id: '2',
        name: 'Code Assistant',
        description: 'Help with coding tasks, debugging, and code reviews across multiple languages.',
        longDescription: 'The Code Assistant agent is your programming partner. It can help write, debug, and optimize code in multiple languages including Python, JavaScript, Java, C++, and more. Ask for code examples, get help with debugging, or request code reviews. The agent can also explain complex code and suggest improvements.',
        category: 'Development',
        capabilities: [
            'Code generation',
            'Debugging assistance',
            'Code optimization',
            'Code explanation',
            'Multiple language support',
            'Best practices recommendations'
        ],
        usageCount: 3421,
        isNew: false,
        isFeatured: true,
        createdAt: '2023-08-10',
        updatedAt: '2024-02-15',
        version: '3.1.0',
        creator: 'Engineering Team',
        avatarUrl: '/agents/code-assistant.svg',
        tags: [
            'coding',
            'programming',
            'development',
            'debugging'
        ],
        relatedAgentIds: [
            '9',
            '10'
        ]
    },
    {
        id: '3',
        name: 'Research Companion',
        description: 'Find, summarize, and organize research papers and articles.',
        longDescription: 'The Research Companion agent helps you stay on top of the latest research in your field. It can find relevant papers, summarize key findings, and help you organize your research materials. Ask for papers on specific topics, get summaries of complex articles, or request literature reviews.',
        category: 'Research',
        capabilities: [
            'Research paper search',
            'Article summarization',
            'Literature review assistance',
            'Citation generation',
            'Research organization',
            'Key findings extraction'
        ],
        usageCount: 876,
        isNew: true,
        isFeatured: true,
        createdAt: '2024-01-20',
        updatedAt: '2024-03-10',
        version: '1.2.0',
        creator: 'Research & Development',
        avatarUrl: '/agents/research-companion.svg',
        tags: [
            'research',
            'papers',
            'academic',
            'literature'
        ],
        relatedAgentIds: [
            '11',
            '5'
        ]
    },
    {
        id: '4',
        name: 'Meeting Summarizer',
        description: 'Generate concise summaries and action items from meeting transcripts.',
        longDescription: 'The Meeting Summarizer agent helps you capture the important points from your meetings. Upload a transcript or recording, and the agent will identify key discussion points, decisions made, and action items assigned. It can also generate meeting minutes in various formats.',
        category: 'Productivity',
        capabilities: [
            'Meeting transcript analysis',
            'Key point extraction',
            'Action item identification',
            'Decision tracking',
            'Meeting minutes generation',
            'Follow-up reminder creation'
        ],
        usageCount: 2134,
        isNew: false,
        isFeatured: true,
        createdAt: '2023-11-05',
        updatedAt: '2024-02-28',
        version: '2.0.1',
        creator: 'Productivity Team',
        avatarUrl: '/agents/meeting-summarizer.svg',
        tags: [
            'meetings',
            'productivity',
            'transcription',
            'summaries'
        ],
        relatedAgentIds: [
            '5',
            '6',
            '12'
        ]
    },
    {
        id: '5',
        name: 'Document Analyzer',
        description: 'Extract key information from documents and generate summaries.',
        longDescription: 'The Document Analyzer agent helps you process and understand documents quickly. Upload any document (PDF, Word, etc.), and the agent will extract key information, generate summaries, and answer questions about the content. It can handle contracts, reports, articles, and more.',
        category: 'Productivity',
        capabilities: [
            'Document parsing',
            'Key information extraction',
            'Summary generation',
            'Question answering',
            'Multiple format support',
            'Entity recognition'
        ],
        usageCount: 567,
        isNew: false,
        isFeatured: false,
        createdAt: '2023-09-12',
        updatedAt: '2024-01-30',
        version: '1.5.2',
        creator: 'Content Team',
        avatarUrl: '/agents/document-analyzer.svg',
        tags: [
            'documents',
            'analysis',
            'extraction',
            'summaries'
        ],
        relatedAgentIds: [
            '1',
            '3',
            '4'
        ]
    },
    {
        id: '6',
        name: 'Presentation Creator',
        description: 'Generate professional presentations from outlines or topics.',
        longDescription: 'The Presentation Creator agent helps you build compelling presentations quickly. Provide a topic or outline, and the agent will generate slide content, suggest visuals, and help you structure your presentation for maximum impact. It can create presentations for various purposes including business, education, and sales.',
        category: 'Productivity',
        capabilities: [
            'Slide content generation',
            'Presentation structure suggestions',
            'Visual element recommendations',
            'Talking points creation',
            'Multiple template support',
            'Export to PowerPoint/Google Slides'
        ],
        usageCount: 321,
        isNew: true,
        isFeatured: false,
        createdAt: '2024-02-01',
        updatedAt: '2024-03-15',
        version: '1.0.0',
        creator: 'Marketing Team',
        avatarUrl: '/agents/presentation-creator.svg',
        tags: [
            'presentations',
            'slides',
            'design',
            'content'
        ],
        relatedAgentIds: [
            '4',
            '13'
        ]
    },
    {
        id: '7',
        name: 'Data Visualization Expert',
        description: 'Create beautiful and informative data visualizations from your datasets.',
        longDescription: 'The Data Visualization Expert agent helps you transform raw data into compelling visual stories. Upload your data and describe what you want to highlight, and the agent will generate appropriate charts, graphs, and interactive visualizations that make your data insights clear and impactful.',
        category: 'Analytics',
        capabilities: [
            'Chart and graph generation',
            'Interactive visualization creation',
            'Color palette optimization',
            'Data storytelling assistance',
            'Multiple export formats',
            'Accessibility considerations'
        ],
        usageCount: 892,
        isNew: false,
        isFeatured: false,
        createdAt: '2023-11-18',
        updatedAt: '2024-02-10',
        version: '1.8.0',
        creator: 'Data Science Team',
        avatarUrl: '/agents/data-analyst.svg',
        tags: [
            'visualization',
            'charts',
            'graphs',
            'data'
        ],
        relatedAgentIds: [
            '1',
            '8'
        ]
    },
    {
        id: '8',
        name: 'Predictive Analytics Agent',
        description: 'Forecast trends and make predictions based on historical data.',
        longDescription: 'The Predictive Analytics Agent uses machine learning algorithms to analyze your historical data and generate forecasts and predictions. It can identify patterns, trends, and anomalies to help you make data-driven decisions about future outcomes.',
        category: 'Analytics',
        capabilities: [
            'Time series forecasting',
            'Trend analysis',
            'Anomaly detection',
            'Predictive modeling',
            'Scenario planning',
            'Confidence interval calculation'
        ],
        usageCount: 754,
        isNew: true,
        isFeatured: false,
        createdAt: '2024-01-05',
        updatedAt: '2024-03-20',
        version: '1.2.1',
        creator: 'Data Science Team',
        avatarUrl: '/agents/data-analyst.svg',
        tags: [
            'predictions',
            'forecasting',
            'analytics',
            'trends'
        ],
        relatedAgentIds: [
            '1',
            '7'
        ]
    },
    {
        id: '9',
        name: 'API Documentation Generator',
        description: 'Automatically generate comprehensive API documentation from your code.',
        longDescription: 'The API Documentation Generator analyzes your API code and generates clear, comprehensive documentation. It can create reference docs, guides, examples, and interactive API explorers to help developers understand and use your APIs effectively.',
        category: 'Development',
        capabilities: [
            'API reference generation',
            'Code example creation',
            'Interactive API explorer',
            'Multiple format support',
            'Versioning assistance',
            'Consistency checking'
        ],
        usageCount: 623,
        isNew: false,
        isFeatured: false,
        createdAt: '2023-10-08',
        updatedAt: '2024-02-12',
        version: '2.1.0',
        creator: 'Engineering Team',
        avatarUrl: '/agents/code-assistant.svg',
        tags: [
            'documentation',
            'API',
            'development',
            'reference'
        ],
        relatedAgentIds: [
            '2',
            '10'
        ]
    },
    {
        id: '10',
        name: 'Code Reviewer',
        description: 'Get detailed code reviews with suggestions for improvements and best practices.',
        longDescription: 'The Code Reviewer agent analyzes your code for bugs, security issues, performance problems, and adherence to best practices. It provides detailed feedback with specific suggestions for improvements and explanations of potential issues.',
        category: 'Development',
        capabilities: [
            'Bug detection',
            'Security vulnerability scanning',
            'Performance optimization',
            'Best practice enforcement',
            'Code style consistency',
            'Refactoring suggestions'
        ],
        usageCount: 1876,
        isNew: false,
        isFeatured: false,
        createdAt: '2023-09-20',
        updatedAt: '2024-03-05',
        version: '2.4.0',
        creator: 'Engineering Team',
        avatarUrl: '/agents/code-assistant.svg',
        tags: [
            'code review',
            'quality',
            'security',
            'best practices'
        ],
        relatedAgentIds: [
            '2',
            '9'
        ]
    },
    {
        id: '11',
        name: 'Literature Review Assistant',
        description: 'Compile comprehensive literature reviews on any research topic.',
        longDescription: 'The Literature Review Assistant helps researchers compile thorough literature reviews by finding relevant papers, organizing them by themes, identifying key findings, and highlighting research gaps. It can save weeks of manual research work.',
        category: 'Research',
        capabilities: [
            'Research paper discovery',
            'Thematic organization',
            'Gap analysis',
            'Citation management',
            'Summary generation',
            'Trend identification'
        ],
        usageCount: 542,
        isNew: true,
        isFeatured: false,
        createdAt: '2024-02-15',
        updatedAt: '2024-03-18',
        version: '1.0.2',
        creator: 'Research & Development',
        avatarUrl: '/agents/research-companion.svg',
        tags: [
            'literature review',
            'research',
            'academic',
            'papers'
        ],
        relatedAgentIds: [
            '3'
        ]
    },
    {
        id: '12',
        name: 'Project Manager Assistant',
        description: 'Track projects, manage tasks, and coordinate team activities efficiently.',
        longDescription: 'The Project Manager Assistant helps you plan, track, and manage projects from start to finish. It can create project timelines, assign and track tasks, identify risks, generate status reports, and facilitate team communication to keep your projects on track.',
        category: 'Productivity',
        capabilities: [
            'Project planning',
            'Task management',
            'Timeline creation',
            'Risk assessment',
            'Status reporting',
            'Resource allocation'
        ],
        usageCount: 1432,
        isNew: false,
        isFeatured: false,
        createdAt: '2023-08-25',
        updatedAt: '2024-02-20',
        version: '2.2.0',
        creator: 'Productivity Team',
        avatarUrl: '/agents/meeting-summarizer.svg',
        tags: [
            'project management',
            'tasks',
            'planning',
            'coordination'
        ],
        relatedAgentIds: [
            '4',
            '13'
        ]
    },
    {
        id: '13',
        name: 'Content Creator',
        description: 'Generate high-quality content for blogs, social media, and marketing materials.',
        longDescription: 'The Content Creator agent helps you produce engaging content for various platforms. Provide a topic and target audience, and it will generate blog posts, social media updates, marketing copy, and more, tailored to your brand voice and content strategy.',
        category: 'Marketing',
        capabilities: [
            'Blog post generation',
            'Social media content creation',
            'Marketing copy writing',
            'SEO optimization',
            'Brand voice consistency',
            'Content strategy alignment'
        ],
        usageCount: 2187,
        isNew: false,
        isFeatured: false,
        createdAt: '2023-07-12',
        updatedAt: '2024-03-08',
        version: '3.0.1',
        creator: 'Marketing Team',
        avatarUrl: '/agents/presentation-creator.svg',
        tags: [
            'content',
            'writing',
            'marketing',
            'social media'
        ],
        relatedAgentIds: [
            '6',
            '12'
        ]
    }
];
// Mock data for sessions
const sessions = [
    {
        id: 'session1',
        agentId: '2',
        title: 'JavaScript Debugging Help',
        messages: [
            {
                id: 'msg1',
                content: 'Hello! How can I help you with coding today?',
                role: 'assistant',
                timestamp: Date.now() - 3600000
            },
            {
                id: 'msg2',
                content: 'I have a bug in my JavaScript code. The event listener is not working.',
                role: 'user',
                timestamp: Date.now() - 3500000
            },
            {
                id: 'msg3',
                content: 'Let\'s take a look. Can you share the code that\'s not working?',
                role: 'assistant',
                timestamp: Date.now() - 3400000
            }
        ],
        createdAt: Date.now() - 3600000,
        updatedAt: Date.now() - 3400000,
        isSaved: true
    },
    {
        id: 'session2',
        agentId: '1',
        title: 'Sales Data Analysis',
        messages: [
            {
                id: 'msg1',
                content: 'Welcome to Data Analyst. What data would you like to analyze today?',
                role: 'assistant',
                timestamp: Date.now() - 86400000
            },
            {
                id: 'msg2',
                content: 'I need to analyze our Q1 sales data to find trends.',
                role: 'user',
                timestamp: Date.now() - 86300000
            },
            {
                id: 'msg3',
                content: 'I can help with that. Do you have the sales data file you can upload?',
                role: 'assistant',
                timestamp: Date.now() - 86200000
            }
        ],
        createdAt: Date.now() - 86400000,
        updatedAt: Date.now() - 86200000,
        isSaved: true
    }
];
function getAllAgents() {
    return agents;
}
function getAgentById(id) {
    return agents.find((agent)=>agent.id === id);
}
function getFeaturedAgents() {
    return agents.filter((agent)=>agent.isFeatured);
}
function getNewAgents() {
    return agents.filter((agent)=>agent.isNew);
}
function getAgentsByCategory(category) {
    return agents.filter((agent)=>agent.category === category);
}
function getAgentsByTag(tag) {
    return agents.filter((agent)=>agent.tags?.includes(tag));
}
function getAllCategories() {
    const categories = new Set();
    agents.forEach((agent)=>categories.add(agent.category));
    return Array.from(categories).sort();
}
function getAllTags() {
    const tags = new Set();
    agents.forEach((agent)=>{
        agent.tags?.forEach((tag)=>tags.add(tag));
    });
    return Array.from(tags).sort();
}
function getRelatedAgents(agentId) {
    const agent = getAgentById(agentId);
    if (!agent || !agent.relatedAgentIds || agent.relatedAgentIds.length === 0) {
        return [];
    }
    return agent.relatedAgentIds.map((id)=>getAgentById(id)).filter((agent)=>agent !== undefined);
}
function getAgentsByIds(ids) {
    return ids.map((id)=>getAgentById(id)).filter((agent)=>agent !== undefined);
}
function searchAgents(query) {
    if (!query) return agents;
    const lowercaseQuery = query.toLowerCase();
    return agents.filter((agent)=>agent.name.toLowerCase().includes(lowercaseQuery) || agent.description.toLowerCase().includes(lowercaseQuery) || agent.category.toLowerCase().includes(lowercaseQuery) || agent.tags?.some((tag)=>tag.toLowerCase().includes(lowercaseQuery)) || agent.capabilities.some((capability)=>capability.toLowerCase().includes(lowercaseQuery)));
}
function getSessionsForAgent(agentId) {
    return sessions.filter((session)=>session.agentId === agentId);
}
function getSessionById(sessionId) {
    return sessions.find((session)=>session.id === sessionId);
}
function createMessage(content, role) {
    return {
        id: `msg_${Date.now()}`,
        content,
        role,
        timestamp: Date.now()
    };
}
}}),
"[project]/src/data/learning-resources.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getArticles": (()=>getArticles),
    "getBlogPosts": (()=>getBlogPosts),
    "getCombinedLearningResources": (()=>getCombinedLearningResources),
    "getFeaturedResources": (()=>getFeaturedResources),
    "getPopularResources": (()=>getPopularResources),
    "getRecentResources": (()=>getRecentResources),
    "getResourceById": (()=>getResourceById),
    "getVideos": (()=>getVideos),
    "learningResources": (()=>learningResources)
});
const learningResources = [
    // Videos
    {
        id: 'video-1',
        title: 'Introduction to AI Agents',
        description: 'Learn the basics of AI agents and how they can help you in your daily tasks.',
        type: 'video',
        thumbnail: 'https://placehold.co/640x360.png?text=Introduction+to+AI+Agents',
        content: 'This video provides a comprehensive introduction to AI agents, covering their capabilities, limitations, and practical applications.',
        url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        tags: [
            'beginner',
            'introduction',
            'ai-basics'
        ],
        author: 'Dr. Sarah Johnson',
        publishedAt: new Date('2023-10-15')
    },
    {
        id: 'video-2',
        title: 'Advanced AI Agent Techniques',
        description: 'Dive deeper into AI agent capabilities with advanced techniques and strategies.',
        type: 'video',
        thumbnail: 'https://placehold.co/640x360.png?text=Advanced+AI+Agent+Techniques',
        content: 'This advanced tutorial explores sophisticated AI agent techniques, including prompt engineering, context management, and multi-agent systems.',
        url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        tags: [
            'advanced',
            'techniques',
            'prompt-engineering'
        ],
        author: 'Prof. Michael Chen',
        publishedAt: new Date('2023-11-05')
    },
    {
        id: 'video-3',
        title: 'AI Agents for Data Analysis',
        description: 'Learn how to use AI agents to analyze and visualize complex datasets.',
        type: 'video',
        thumbnail: 'https://placehold.co/640x360.png?text=AI+Agents+for+Data+Analysis',
        content: 'This tutorial demonstrates how to leverage AI agents for data analysis tasks, from data cleaning to visualization and interpretation.',
        url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        tags: [
            'data-analysis',
            'visualization',
            'practical'
        ],
        author: 'Emma Rodriguez',
        publishedAt: new Date('2023-12-10')
    },
    // Articles
    {
        id: 'article-1',
        title: 'The Future of AI Agents in Enterprise',
        description: 'An in-depth look at how AI agents are transforming enterprise operations and decision-making.',
        type: 'article',
        thumbnail: 'https://placehold.co/800x400.png?text=Future+of+AI+Agents',
        content: `
# The Future of AI Agents in Enterprise

Artificial Intelligence (AI) agents are rapidly transforming how enterprises operate, making processes more efficient and enabling new capabilities that were previously impossible. This article explores the current state and future potential of AI agents in enterprise settings.

## Current Applications

Today, enterprises are using AI agents for:

- **Customer Service**: Handling routine inquiries and providing 24/7 support
- **Data Analysis**: Processing large datasets to extract actionable insights
- **Process Automation**: Streamlining workflows and reducing manual tasks
- **Decision Support**: Providing recommendations based on complex data analysis

## Future Directions

Looking ahead, we can expect AI agents to evolve in several key ways:

### 1. Increased Autonomy

Future AI agents will operate with greater independence, making decisions and taking actions with minimal human oversight. This will free up human workers to focus on more creative and strategic tasks.

### 2. Multi-Agent Systems

Rather than single agents working in isolation, we'll see sophisticated networks of specialized agents collaborating to solve complex problems. These multi-agent systems will be able to tackle challenges that would be impossible for a single agent.

### 3. Enhanced Personalization

AI agents will become increasingly adept at understanding individual user preferences and adapting their behavior accordingly. This will enable highly personalized experiences for employees and customers alike.

### 4. Seamless Integration

The distinction between AI agents and traditional software will blur, with AI capabilities being embedded throughout enterprise systems rather than existing as standalone tools.

## Challenges and Considerations

Despite their promise, enterprises must navigate several challenges when implementing AI agents:

- **Data Privacy**: Ensuring that AI agents handle sensitive information appropriately
- **Transparency**: Making AI decision-making processes understandable to humans
- **Skills Gap**: Training employees to work effectively alongside AI agents
- **Ethical Considerations**: Addressing questions about automation and job displacement

## Conclusion

AI agents represent a transformative technology for enterprises, offering unprecedented opportunities for efficiency, innovation, and growth. Organizations that thoughtfully integrate these tools into their operations will gain significant competitive advantages in the years ahead.
    `,
        tags: [
            'enterprise',
            'future-trends',
            'business'
        ],
        author: 'Dr. James Wilson',
        publishedAt: new Date('2023-09-20')
    },
    {
        id: 'article-2',
        title: 'Ethical Considerations for AI Agent Deployment',
        description: 'Exploring the ethical dimensions of deploying AI agents in various contexts.',
        type: 'article',
        thumbnail: 'https://placehold.co/800x400.png?text=AI+Ethics',
        content: `
# Ethical Considerations for AI Agent Deployment

As AI agents become increasingly integrated into our daily lives and business operations, it's crucial to consider the ethical implications of their deployment. This article examines key ethical considerations that should guide the development and implementation of AI agent systems.

## Transparency and Explainability

One of the fundamental ethical requirements for AI agents is transparency. Users should understand:

- When they are interacting with an AI rather than a human
- The general capabilities and limitations of the AI system
- How the AI makes decisions or recommendations

Explainability is equally important—the ability to provide understandable explanations for why an AI agent took a particular action or made a specific recommendation.

## Privacy and Data Protection

AI agents often require access to significant amounts of data to function effectively. Organizations must:

- Be transparent about what data is collected and how it's used
- Implement robust security measures to protect sensitive information
- Comply with relevant regulations like GDPR or CCPA
- Minimize data collection to what's necessary for the agent to function

## Fairness and Bias Mitigation

AI agents can inadvertently perpetuate or amplify existing biases if not carefully designed and monitored:

- Training data should be diverse and representative
- Systems should be regularly audited for biased outcomes
- Developers should implement bias detection and mitigation techniques
- Organizations should establish clear standards for fairness

## Accountability and Oversight

Clear lines of accountability are essential when deploying AI agents:

- Organizations should designate specific individuals responsible for AI systems
- Regular auditing and monitoring should be implemented
- Feedback mechanisms should allow users to report concerns
- Processes should exist to address harmful or unintended consequences

## Human Autonomy and Agency

AI agents should enhance human capabilities rather than diminish human agency:

- Users should maintain meaningful control over important decisions
- Systems should be designed to complement human judgment, not replace it
- People should be able to override AI recommendations when appropriate
- The division of labor between humans and AI should be thoughtfully considered

## Conclusion

Ethical deployment of AI agents requires ongoing attention and commitment. By prioritizing transparency, privacy, fairness, accountability, and human agency, organizations can harness the benefits of AI while minimizing potential harms. As these technologies continue to evolve, our ethical frameworks must evolve alongside them, ensuring that AI agents serve human values and well-being.
    `,
        tags: [
            'ethics',
            'responsible-ai',
            'governance'
        ],
        author: 'Prof. Elena Martinez',
        publishedAt: new Date('2023-10-05')
    },
    // Blog Posts
    {
        id: 'blog-1',
        title: 'How We Built Our First AI Agent',
        description: 'A behind-the-scenes look at our journey developing our first AI agent.',
        type: 'blog',
        thumbnail: 'https://placehold.co/800x400.png?text=Building+AI+Agents',
        content: `
# How We Built Our First AI Agent: Lessons Learned

When we set out to build our first AI agent six months ago, we had no idea how challenging—and rewarding—the journey would be. In this post, I'll share our experience, including the obstacles we faced and the insights we gained along the way.

## The Initial Vision

Our goal was straightforward: create an AI agent that could help our customer support team handle routine inquiries, freeing them to focus on more complex issues. We envisioned a system that could:

- Answer frequently asked questions
- Help users troubleshoot common problems
- Collect necessary information before escalating to a human agent
- Learn and improve over time

Simple enough, right? Not quite.

## Challenges We Encountered

### 1. Defining the Scope

Our first challenge was scope creep. As we brainstormed capabilities, our simple support agent began transforming into an all-purpose assistant that would do everything from technical support to sales to product recommendations.

**Lesson learned**: Start narrow and expand later. We eventually refocused on technical support for a specific product line, which gave us a manageable scope.

### 2. Data Quality Issues

We initially trained our agent on our support documentation and past ticket logs. However, we quickly discovered inconsistencies in how our team had resolved similar issues in the past, leading to confused responses from our agent.

**Lesson learned**: Clean and standardize your training data before implementation. We ended up creating a curated dataset of best-practice responses.

### 3. Integration Complexities

Connecting our agent to existing systems—our knowledge base, CRM, and ticketing system—proved more difficult than anticipated.

**Lesson learned**: Plan your integration strategy early and thoroughly. Consider building a middleware layer if your systems don't have robust APIs.

### 4. User Acceptance

Some team members were hesitant to adopt the agent, fearing it might eventually replace them.

**Lesson learned**: Involve end-users from the beginning and emphasize how the agent will enhance their work rather than replace it.

## What Worked Well

Despite the challenges, several approaches proved successful:

### 1. Iterative Development

Rather than aiming for perfection from the start, we released early versions internally and gathered feedback. This allowed us to identify and address issues quickly.

### 2. Human-in-the-Loop Design

We designed our agent to collaborate with human agents rather than operate independently. This improved performance and helped with team acceptance.

### 3. Clear Success Metrics

We established specific metrics to evaluate our agent's performance, including resolution rate, customer satisfaction, and time savings for human agents.

### 4. Continuous Learning

We implemented a feedback loop where human agents could flag problematic responses, helping our system improve over time.

## Results and Next Steps

Six months in, our AI agent now successfully handles about 40% of initial customer inquiries, reducing wait times and allowing our support team to focus on more complex issues.

Our next steps include:

- Expanding to additional product lines
- Implementing more sophisticated natural language understanding
- Adding proactive support capabilities
- Exploring voice interface options

## Conclusion

Building an effective AI agent is more challenging than it might initially appear, but the benefits can be substantial. By starting with a focused scope, prioritizing data quality, planning integrations carefully, and involving end-users throughout the process, you can create an agent that genuinely enhances your team's capabilities.

Have you built an AI agent for your organization? I'd love to hear about your experience in the comments!
    `,
        tags: [
            'case-study',
            'development',
            'lessons-learned'
        ],
        author: 'Alex Thompson',
        publishedAt: new Date('2023-11-15')
    },
    {
        id: 'blog-2',
        title: 'Monthly AI Agent Updates - January 2024',
        description: 'The latest updates and improvements to our AI agent platform.',
        type: 'blog',
        thumbnail: 'https://placehold.co/800x400.png?text=January+Updates',
        content: `
# Monthly AI Agent Updates - January 2024

Welcome to our first monthly update of 2024! We've been hard at work improving our AI agent platform, and we're excited to share the latest enhancements, bug fixes, and upcoming features.

## New Features

### 1. Enhanced Context Management

Our agents can now maintain context more effectively across longer conversations. This means they can refer back to information mentioned earlier in the discussion without requiring users to repeat themselves.

### 2. Multi-Modal Capabilities

Agents can now process and respond to both text and images. This is particularly useful for troubleshooting scenarios where users can share screenshots of errors or problems they're experiencing.

### 3. Custom Agent Creation UI

We've launched a new user interface for creating and customizing agents without coding. This drag-and-drop interface allows you to:

- Define conversation flows
- Create custom knowledge bases
- Set up integration with your existing tools
- Customize the agent's tone and personality

## Improvements

### 1. Performance Optimization

We've significantly improved response times across the platform:

- 40% faster initial response time
- 25% reduction in token usage
- Improved handling of concurrent requests

### 2. Better Error Handling

Agents now recover more gracefully from misunderstandings and provide clearer guidance when they need additional information.

### 3. Enhanced Analytics

The analytics dashboard now provides deeper insights into:

- Common user queries and pain points
- Success rates for different types of requests
- Patterns in escalations to human agents
- User satisfaction metrics

## Bug Fixes

- Fixed an issue where agents occasionally lost context after API errors
- Resolved a problem with webhook integrations timing out
- Fixed formatting issues in exported conversation logs
- Addressed authentication issues with certain SSO providers

## Coming Soon

We're excited about several features currently in development:

### 1. Agent Collaboration

Soon, multiple specialized agents will be able to collaborate on complex requests, each handling the aspects they're best suited for.

### 2. Scheduled Actions

Agents will be able to perform actions at scheduled times, such as sending follow-ups or checking on the status of issues.

### 3. Voice Interface

We're working on a voice interface that will allow users to interact with agents through spoken conversation.

## Community Spotlight

We want to highlight some impressive implementations from our community:

- **HealthTech Solutions** created an agent that helps patients understand their medication schedules and potential side effects
- **GlobalLogistics** built an agent that provides real-time shipping updates and resolves delivery issues
- **EduLearn** developed a tutoring agent that adapts to different learning styles

## Feedback Request

We're considering several directions for our Q2 roadmap and would love your input. Please take our quick survey to help prioritize upcoming features: [Survey Link]

## Conclusion

Thank you for being part of our community! As always, we welcome your feedback and suggestions. You can reach our <NAME_EMAIL> or through the feedback form in your dashboard.

Happy agent building!

The AI Agent Platform Team
    `,
        tags: [
            'updates',
            'new-features',
            'roadmap'
        ],
        author: 'The AI Hub Team',
        publishedAt: new Date('2024-01-10')
    }
];
const getVideos = ()=>learningResources.filter((resource)=>resource.type === 'video');
const getArticles = ()=>learningResources.filter((resource)=>resource.type === 'article');
const getBlogPosts = ()=>learningResources.filter((resource)=>resource.type === 'blog');
const getResourceById = (id)=>learningResources.find((resource)=>resource.id === id);
const getFeaturedResources = (count = 3)=>{
    // In a real app, you might have a 'featured' flag or use other criteria
    // Here we'll just return the most recent resources
    return [
        ...learningResources
    ].sort((a, b)=>b.publishedAt.getTime() - a.publishedAt.getTime()).slice(0, count);
};
const getRecentResources = (count = 4)=>{
    return [
        ...learningResources
    ].sort((a, b)=>b.publishedAt.getTime() - a.publishedAt.getTime()).slice(0, count);
};
const getPopularResources = (count = 4)=>{
    // In a real app, this would be based on view counts or user engagement
    // For now, we'll shuffle the array and take the first few items
    const shuffled = [
        ...learningResources
    ].sort(()=>0.5 - Math.random());
    return shuffled.slice(0, count);
};
const getCombinedLearningResources = (resources)=>{
    return resources.map((resource)=>({
            ...resource,
            // Add a URL based on the resource type
            viewUrl: resource.type === 'video' ? `/learning/videos/${resource.id}` : resource.type === 'article' ? `/learning/articles/${resource.id}` : `/learning/blog/${resource.id}`
        }));
};
}}),
"[project]/src/data/resources.ts [app-rsc] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "resources": (()=>combinedResources)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$resources$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/data/resources.ts [app-rsc] (ecmascript) <locals>");
;
// New resources from the AI Presentation Series Summary PDF
const newResources = [
    // Productivity Tools
    {
        id: 'perplexity-ai',
        name: 'Perplexity AI',
        description: 'An AI-powered search engine with a chatbot interface that understands and responds to user queries using GPT-3.5.',
        url: 'https://www.perplexity.ai/',
        category: 'productivity',
        tags: [
            'search-engine',
            'ai-assistant',
            'research'
        ],
        pricing: 'freemium',
        logoUrl: 'https://www.perplexity.ai/favicon.ico'
    },
    {
        id: 'claude-ai',
        name: 'Claude AI',
        description: 'Analyzes and suggests improvements for very long content, similar to ChatGPT but with enhanced capabilities for handling lengthy documents.',
        url: 'https://claude.ai/',
        category: 'productivity',
        tags: [
            'ai-assistant',
            'content-generation',
            'document-analysis'
        ],
        pricing: 'freemium',
        logoUrl: 'https://claude.ai/favicon.ico'
    },
    {
        id: 'fathom-ai',
        name: 'Fathom AI',
        description: 'Zoom app that records, transcribes, and highlights key moments from calls, making meeting follow-up more efficient.',
        url: 'https://fathom.video/',
        category: 'productivity',
        tags: [
            'meeting-assistant',
            'transcription',
            'video-conferencing'
        ],
        pricing: 'freemium',
        logoUrl: 'https://fathom.video/favicon.ico'
    },
    {
        id: 'plaud-ai',
        name: 'Plaud.ai',
        description: 'AI tool for note-taking and transcription that helps capture and organize information from meetings and conversations.',
        url: 'https://www.plaud.ai/',
        category: 'productivity',
        tags: [
            'note-taking',
            'transcription',
            'meeting-assistant'
        ],
        pricing: 'freemium',
        logoUrl: 'https://www.plaud.ai/favicon.ico'
    },
    {
        id: 'whisper',
        name: 'Whisper',
        description: 'Converts audio to text and vice versa using AI, providing accurate transcription for various languages and accents.',
        url: 'https://openai.com/index/whisper/',
        category: 'productivity',
        tags: [
            'transcription',
            'audio-processing',
            'speech-to-text'
        ],
        pricing: 'freemium',
        logoUrl: 'https://openai.com/favicon.ico'
    },
    {
        id: 'notebooklm',
        name: 'NotebookLM',
        description: 'Tool for data management and note-taking that uses AI to help organize and retrieve information efficiently.',
        url: 'https://notebooklm.google.com',
        category: 'productivity',
        tags: [
            'note-taking',
            'knowledge-management',
            'ai-organization'
        ],
        pricing: 'free',
        logoUrl: 'https://notebooklm.google.com/favicon.ico'
    },
    {
        id: 'deepl',
        name: 'DeepL',
        description: 'Translation tool for efficient and accurate translations between multiple languages, powered by advanced AI.',
        url: 'https://www.deepl.com',
        category: 'productivity',
        tags: [
            'translation',
            'language-processing',
            'communication'
        ],
        pricing: 'freemium',
        logoUrl: 'https://www.deepl.com/favicon.ico'
    },
    // Design Tools
    {
        id: 'midjourney',
        name: 'Midjourney',
        description: 'Generates images from descriptive language, similar to DALL-E and Stable Diffusion, with a focus on artistic quality.',
        url: 'https://www.midjourney.com/',
        category: 'design',
        tags: [
            'image-generation',
            'ai-art',
            'creative-tools'
        ],
        pricing: 'paid',
        logoUrl: 'https://www.midjourney.com/favicon.ico'
    },
    {
        id: 'bing-images',
        name: 'Bing Image Creator',
        description: 'Provides tools for creating images from text descriptions, potentially offering more features than Midjourney.',
        url: 'https://www.bing.com/images/create',
        category: 'design',
        tags: [
            'image-generation',
            'ai-art',
            'creative-tools'
        ],
        pricing: 'free',
        logoUrl: 'https://www.bing.com/favicon.ico'
    },
    {
        id: 'meta-imagine',
        name: 'Meta Imagine',
        description: 'Uses models to generate images from textual prompts provided by the user, created by Meta (Facebook).',
        url: 'https://imagine.meta.com',
        category: 'design',
        tags: [
            'image-generation',
            'ai-art',
            'creative-tools'
        ],
        pricing: 'free',
        logoUrl: 'https://imagine.meta.com/favicon.ico'
    },
    {
        id: 'designer-microsoft',
        name: 'Microsoft Designer',
        description: 'Microsoft\'s answer to Canva, focusing on design solutions with AI-powered features for creating professional graphics.',
        url: 'https://designer.microsoft.com/',
        category: 'design',
        tags: [
            'graphic-design',
            'presentation',
            'marketing-materials'
        ],
        pricing: 'freemium',
        logoUrl: 'https://designer.microsoft.com/favicon.ico'
    },
    {
        id: 'runway',
        name: 'Runway',
        description: 'A creative platform for video production and editing with AI-powered tools for visual effects and content creation.',
        url: 'https://app.runwayml.com/',
        category: 'design',
        tags: [
            'video-editing',
            'visual-effects',
            'content-creation'
        ],
        pricing: 'freemium',
        logoUrl: 'https://app.runwayml.com/favicon.ico'
    },
    {
        id: 'clipdrop',
        name: 'Clipdrop',
        description: 'Developed by Stability AI, for various image and video editing tasks with AI-powered features.',
        url: 'https://clipdrop.co/',
        category: 'design',
        tags: [
            'image-editing',
            'video-editing',
            'content-creation'
        ],
        pricing: 'freemium',
        logoUrl: 'https://clipdrop.co/favicon.ico'
    },
    {
        id: 'interior-ai',
        name: 'Interior AI',
        description: 'Interior design tool for generating and visualizing room layouts using AI to create realistic interior designs.',
        url: 'https://interiorai.com/',
        category: 'design',
        tags: [
            'interior-design',
            'visualization',
            'architecture'
        ],
        pricing: 'freemium',
        logoUrl: 'https://interiorai.com/favicon.ico'
    },
    {
        id: 'meshy-ai',
        name: 'Meshy.ai',
        description: 'For 3D modeling, used in architecture and design to create and manipulate 3D objects with AI assistance.',
        url: 'https://www.meshy.ai',
        category: 'design',
        tags: [
            '3d-modeling',
            'architecture',
            'design'
        ],
        pricing: 'freemium',
        logoUrl: 'https://www.meshy.ai/favicon.ico'
    },
    {
        id: 'mnml-ai',
        name: 'MNML AI',
        description: 'Architecture design assistant that helps create minimalist architectural designs with AI guidance.',
        url: 'https://mnml.ai',
        category: 'design',
        tags: [
            'architecture',
            'design',
            'minimalism'
        ],
        pricing: 'freemium',
        logoUrl: 'https://mnml.ai/favicon.ico'
    },
    {
        id: 'ulama-tech',
        name: 'Ulama.tech',
        description: 'For architectural design, specifically for structure and planning with AI-powered tools.',
        url: 'http://ulama.tech',
        category: 'design',
        tags: [
            'architecture',
            'structural-design',
            'planning'
        ],
        pricing: 'freemium',
        logoUrl: 'http://ulama.tech/favicon.ico'
    },
    {
        id: 'weshop-ai',
        name: 'WeShop',
        description: 'Produce high-quality product images inexpensively and quickly using AI-generated visuals.',
        url: 'https://www.weshop.ai/',
        category: 'design',
        tags: [
            'product-photography',
            'e-commerce',
            'marketing'
        ],
        pricing: 'paid',
        logoUrl: 'https://www.weshop.ai/favicon.ico'
    },
    {
        id: 'botika',
        name: 'Botika',
        description: 'Helps fashion retailers save on photo costs and boost sales using AI-generated models for product visualization.',
        url: 'https://botika.io/',
        category: 'design',
        tags: [
            'fashion',
            'e-commerce',
            'product-visualization'
        ],
        pricing: 'paid',
        logoUrl: 'https://botika.io/favicon.ico'
    },
    {
        id: 'flux-ai',
        name: 'Flux AI',
        description: 'Enables creative image generation and animation with AI-powered tools for designers and artists.',
        url: 'https://flux-ai.io/',
        category: 'design',
        tags: [
            'image-generation',
            'animation',
            'creative-tools'
        ],
        pricing: 'freemium',
        logoUrl: 'https://flux-ai.io/favicon.ico'
    },
    // Development Tools
    {
        id: '10web',
        name: '10Web',
        description: 'An AI-Powered WordPress Platform for website development and management with automated features.',
        url: 'https://10web.io/',
        category: 'development',
        tags: [
            'wordpress',
            'website-builder',
            'automation'
        ],
        pricing: 'paid',
        logoUrl: 'https://10web.io/favicon.ico'
    },
    {
        id: 'framer',
        name: 'Framer',
        description: 'A tool for building interactive websites and web applications with a focus on design and user experience.',
        url: 'https://framer.com/',
        category: 'development',
        tags: [
            'website-builder',
            'prototyping',
            'design'
        ],
        pricing: 'freemium',
        logoUrl: 'https://framer.com/favicon.ico'
    },
    {
        id: 'github-copilot',
        name: 'GitHub Copilot',
        description: 'AI pair programmer that assists in code completion and suggestions within code editors, powered by OpenAI Codex.',
        url: 'https://github.com/features/copilot',
        category: 'development',
        tags: [
            'coding-assistant',
            'pair-programming',
            'code-completion'
        ],
        pricing: 'paid',
        logoUrl: 'https://github.com/favicon.ico'
    },
    {
        id: 'github-spark',
        name: 'GitHub Spark',
        description: 'AI tool for building web applications using natural language, aiming to lower the barrier to software development.',
        url: 'https://github.com/features',
        category: 'development',
        tags: [
            'web-development',
            'no-code',
            'ai-coding'
        ],
        pricing: 'paid',
        logoUrl: 'https://github.com/favicon.ico'
    },
    {
        id: 'langchain',
        name: 'LangChain',
        description: 'Builds AI-powered applications by connecting large language models with external data sources and tools.',
        url: 'https://www.langchain.com/',
        category: 'development',
        tags: [
            'llm-framework',
            'ai-development',
            'integration'
        ],
        pricing: 'free',
        logoUrl: 'https://www.langchain.com/favicon.ico'
    },
    // Communication
    {
        id: 'heygen',
        name: 'HeyGen',
        description: 'Produces studio quality videos with AI-generated avatars and voices for professional communication.',
        url: 'https://www.heygen.com',
        category: 'communication',
        tags: [
            'video-creation',
            'avatars',
            'presentation'
        ],
        pricing: 'paid',
        logoUrl: 'https://www.heygen.com/favicon.ico'
    },
    {
        id: 'beautiful-ai',
        name: 'Beautiful.ai',
        description: 'Presentation tool that simplifies the creation of professional presentations using AI to handle design elements.',
        url: 'http://beautiful.ai',
        category: 'communication',
        tags: [
            'presentation',
            'slides',
            'design'
        ],
        pricing: 'freemium',
        logoUrl: 'http://beautiful.ai/favicon.ico'
    },
    {
        id: 'gamma-app',
        name: 'Gamma.app',
        description: 'Creates engaging presentations by transforming ideas into visually appealing slides with AI assistance.',
        url: 'https://gamma.app/',
        category: 'communication',
        tags: [
            'presentation',
            'slides',
            'design'
        ],
        pricing: 'freemium',
        logoUrl: 'https://gamma.app/favicon.ico'
    },
    {
        id: 'decktopus',
        name: 'Decktopus',
        description: 'An AI-powered tool that assists in creating presentation starting points with professional templates and designs.',
        url: 'https://decktopus.com',
        category: 'communication',
        tags: [
            'presentation',
            'slides',
            'design'
        ],
        pricing: 'freemium',
        logoUrl: 'https://decktopus.com/favicon.ico'
    },
    {
        id: 'opus',
        name: 'Opus',
        description: 'Transforms long videos into short clips with a single click using generative AI for more effective communication.',
        url: 'https://www.opus.pro/',
        category: 'communication',
        tags: [
            'video-editing',
            'content-creation',
            'summarization'
        ],
        pricing: 'paid',
        logoUrl: 'https://www.opus.pro/favicon.ico'
    },
    {
        id: 'vapi',
        name: 'VAPI',
        description: 'Builds and optimizes voice agents for customer service and communication applications.',
        url: 'https://vapi.ai/',
        category: 'communication',
        tags: [
            'voice-agents',
            'customer-service',
            'automation'
        ],
        pricing: 'paid',
        logoUrl: 'https://vapi.ai/favicon.ico'
    },
    {
        id: 'sora',
        name: 'Sora',
        description: 'Turn text instructions into detailed video scenes for communication and presentation purposes.',
        url: 'https://openai.com/sora',
        category: 'communication',
        tags: [
            'video-generation',
            'content-creation',
            'presentation'
        ],
        pricing: 'unreleased',
        logoUrl: 'https://openai.com/favicon.ico'
    },
    // Collaboration
    {
        id: 'rancelab',
        name: 'RanceLab',
        description: 'Integrates WhatsApp with other platforms for better team collaboration and customer communication.',
        url: 'https://www.rancelab.com/',
        category: 'collaboration',
        tags: [
            'whatsapp-integration',
            'communication',
            'customer-service'
        ],
        pricing: 'paid',
        logoUrl: 'https://www.rancelab.com/favicon.ico'
    },
    {
        id: 'lawgeex',
        name: 'LawGeex',
        description: 'Legal automation platform that uses AI to review contracts and facilitate legal collaboration.',
        url: 'https://www.lawgeex.com/',
        category: 'collaboration',
        tags: [
            'legal',
            'contract-review',
            'automation'
        ],
        pricing: 'paid',
        logoUrl: 'https://www.lawgeex.com/favicon.ico'
    },
    // Analytics & Data
    {
        id: 'browse-ai',
        name: 'BrowseAI',
        description: 'Facilitates data extraction and monitoring from websites for easy data acquisition and analysis.',
        url: 'https://www.browse.ai/',
        category: 'analytics',
        tags: [
            'data-extraction',
            'web-scraping',
            'monitoring'
        ],
        pricing: 'freemium',
        logoUrl: 'https://www.browse.ai/favicon.ico'
    },
    {
        id: 'relevance-ai',
        name: 'Relevance AI',
        description: 'Platform providing AI-driven insights and analytics to enhance business decision-making.',
        url: 'https://relevanceai.com/',
        category: 'analytics',
        tags: [
            'data-analysis',
            'insights',
            'business-intelligence'
        ],
        pricing: 'freemium',
        logoUrl: 'https://relevanceai.com/favicon.ico'
    },
    // Project Management
    {
        id: 'make-com',
        name: 'Make.com',
        description: 'Automation platform for streamlining workflows and processes using AI to connect apps and automate tasks.',
        url: 'https://make.com',
        category: 'project-management',
        tags: [
            'automation',
            'workflow',
            'integration'
        ],
        pricing: 'freemium',
        logoUrl: 'https://make.com/favicon.ico'
    },
    {
        id: 'zapier-central',
        name: 'Zapier Central',
        description: 'Automating tasks and workflows using AI-powered integrations between different applications and services.',
        url: 'https://zapier.com/central',
        category: 'project-management',
        tags: [
            'automation',
            'workflow',
            'integration'
        ],
        pricing: 'freemium',
        logoUrl: 'https://zapier.com/favicon.ico'
    },
    {
        id: 'agents-ai',
        name: 'Agents.ai',
        description: 'Professional network of AI agents for business automation and task management.',
        url: 'https://agents.ai/',
        category: 'project-management',
        tags: [
            'automation',
            'ai-agents',
            'task-management'
        ],
        pricing: 'paid',
        logoUrl: 'https://agents.ai/favicon.ico'
    },
    {
        id: 'napkin-ai',
        name: 'Napkin.ai',
        description: 'Useful for generating content, proofreading, and ideation feedback for project planning and documentation.',
        url: 'http://napkin.ai',
        category: 'project-management',
        tags: [
            'content-generation',
            'ideation',
            'documentation'
        ],
        pricing: 'freemium',
        logoUrl: 'http://napkin.ai/favicon.ico'
    }
];
// Combine existing resources with new resources, avoiding duplicates
const combinedResources = [
    ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$resources$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["resources"],
    ...newResources.filter((newResource)=>!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$resources$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["resources"].some((existingResource)=>existingResource.id === newResource.id || existingResource.name.toLowerCase() === newResource.name.toLowerCase()))
];
;
}}),
"[project]/src/data/resources.ts [app-rsc] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$resources$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/data/resources.ts [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$resources$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/data/resources.ts [app-rsc] (ecmascript) <locals>");
}}),
"[project]/src/data/combined-resources.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getCombinedPopularContent": (()=>getCombinedPopularContent),
    "getCombinedRecentContent": (()=>getCombinedRecentContent),
    "getPopularAITools": (()=>getPopularAITools),
    "getRecentAITools": (()=>getRecentAITools)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$learning$2d$resources$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/data/learning-resources.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$resources$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/data/resources.ts [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$resources$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/data/resources.ts [app-rsc] (ecmascript) <locals>");
;
;
const getRecentAITools = (count = 4)=>{
    // In a real app, these would be sorted by date added
    // For now, we'll just take a random selection
    const shuffled = [
        ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$resources$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["resources"]
    ].sort(()=>0.5 - Math.random());
    const selected = shuffled.slice(0, count);
    return selected.map((resource)=>({
            id: resource.id,
            title: resource.name,
            description: resource.description,
            type: 'resource',
            thumbnail: resource.logoUrl,
            url: `/learning/resources/${resource.category}`,
            category: resource.category,
            tags: resource.tags || [],
            // Simulate a recent date
            publishedAt: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000)
        }));
};
const getPopularAITools = (count = 4)=>{
    // In a real app, these would be sorted by popularity
    // For now, we'll just take a different random selection
    const shuffled = [
        ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$resources$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["resources"]
    ].sort(()=>0.5 - Math.random());
    const selected = shuffled.slice(0, count);
    return selected.map((resource)=>({
            id: resource.id,
            title: resource.name,
            description: resource.description,
            type: 'resource',
            thumbnail: resource.logoUrl,
            url: `/learning/resources/${resource.category}`,
            category: resource.category,
            tags: resource.tags || []
        }));
};
const getCombinedRecentContent = (count = 8)=>{
    const learningContent = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$learning$2d$resources$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCombinedLearningResources"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$learning$2d$resources$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getRecentResources"])(count / 2)).map((resource)=>({
            id: resource.id,
            title: resource.title,
            description: resource.description,
            type: resource.type,
            thumbnail: resource.thumbnail,
            url: resource.viewUrl,
            tags: resource.tags,
            publishedAt: resource.publishedAt,
            author: resource.author
        }));
    const aiTools = getRecentAITools(count / 2);
    // Combine and sort by date
    return [
        ...learningContent,
        ...aiTools
    ].sort((a, b)=>{
        if (!a.publishedAt) return 1;
        if (!b.publishedAt) return -1;
        return b.publishedAt.getTime() - a.publishedAt.getTime();
    }).slice(0, count);
};
const getCombinedPopularContent = (count = 8)=>{
    const learningContent = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$learning$2d$resources$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCombinedLearningResources"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$learning$2d$resources$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getPopularResources"])(count / 2)).map((resource)=>({
            id: resource.id,
            title: resource.title,
            description: resource.description,
            type: resource.type,
            thumbnail: resource.thumbnail,
            url: resource.viewUrl,
            tags: resource.tags,
            publishedAt: resource.publishedAt,
            author: resource.author
        }));
    const aiTools = getPopularAITools(count / 2);
    // Combine and shuffle
    return [
        ...learningContent,
        ...aiTools
    ].sort(()=>0.5 - Math.random()).slice(0, count);
};
}}),
"[project]/src/components/animations/AnimatedBackgroundWrapper.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/components/animations/AnimatedBackgroundWrapper.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/animations/AnimatedBackgroundWrapper.tsx <module evaluation>", "default");
}}),
"[project]/src/components/animations/AnimatedBackgroundWrapper.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/components/animations/AnimatedBackgroundWrapper.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/animations/AnimatedBackgroundWrapper.tsx", "default");
}}),
"[project]/src/components/animations/AnimatedBackgroundWrapper.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$animations$2f$AnimatedBackgroundWrapper$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/components/animations/AnimatedBackgroundWrapper.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$animations$2f$AnimatedBackgroundWrapper$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/src/components/animations/AnimatedBackgroundWrapper.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$animations$2f$AnimatedBackgroundWrapper$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/src/app/page.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>Home)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$react$2d$fontawesome$2f$index$2e$es$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@fortawesome/react-fontawesome/index.es.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@fortawesome/free-solid-svg-icons/index.mjs [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$agents$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/agents.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$combined$2d$resources$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/data/combined-resources.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$animations$2f$AnimatedBackgroundWrapper$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/animations/AnimatedBackgroundWrapper.tsx [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
function Home() {
    // Get agents from our data utility
    const featuredAgents = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$agents$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getFeaturedAgents"])();
    const allAgents = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$agents$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getAllAgents"])();
    // For recently used, we'll simulate by taking the first 2 agents
    // In a real app, this would come from user history
    const recentAgents = allAgents.slice(0, 2);
    // Get new agents
    const newAgents = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$agents$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getNewAgents"])();
    // Get learning content
    const recentLearningContent = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$combined$2d$resources$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCombinedRecentContent"])(4);
    const popularLearningContent = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$combined$2d$resources$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCombinedPopularContent"])(4);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "space-y-10",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$animations$2f$AnimatedBackgroundWrapper$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                fileName: "[project]/src/app/page.tsx",
                lineNumber: 34,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "absolute top-0 left-0 right-0 -z-10 overflow-hidden",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "h-64 w-64 rounded-full bg-blue-500 opacity-10 blur-3xl absolute -top-20 -left-20"
                    }, void 0, false, {
                        fileName: "[project]/src/app/page.tsx",
                        lineNumber: 38,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "h-64 w-64 rounded-full bg-purple-500 opacity-10 blur-3xl absolute top-10 left-40"
                    }, void 0, false, {
                        fileName: "[project]/src/app/page.tsx",
                        lineNumber: 39,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "h-64 w-64 rounded-full bg-pink-500 opacity-10 blur-3xl absolute -top-10 right-20"
                    }, void 0, false, {
                        fileName: "[project]/src/app/page.tsx",
                        lineNumber: 40,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/page.tsx",
                lineNumber: 37,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                className: "rounded-lg bg-gradient-to-r from-blue-600 to-indigo-700 p-8 text-white",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "mx-auto max-w-4xl",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                            className: "mb-4 text-3xl font-bold md:text-4xl",
                            children: "Welcome to our AI Hub"
                        }, void 0, false, {
                            fileName: "[project]/src/app/page.tsx",
                            lineNumber: 45,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "mb-6 text-lg md:text-xl",
                            children: "Discover and interact with our collection of AI agents designed to help you work more efficiently & Learn about AI tools and services in our learning hub."
                        }, void 0, false, {
                            fileName: "[project]/src/app/page.tsx",
                            lineNumber: 46,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex flex-wrap gap-4",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                                    href: "/browse",
                                    className: "rounded-md bg-white px-4 py-2 font-medium text-blue-700 hover:bg-blue-50",
                                    children: "Browse All Agents"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/page.tsx",
                                    lineNumber: 50,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                                    href: "/favorites",
                                    className: "rounded-md bg-blue-800 px-4 py-2 font-medium text-white hover:bg-blue-900",
                                    children: "View Favorites"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/page.tsx",
                                    lineNumber: 56,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/page.tsx",
                            lineNumber: 49,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 44,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/page.tsx",
                lineNumber: 43,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mb-6 flex items-center justify-between",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                className: "text-2xl font-bold",
                                children: "Featured Agents"
                            }, void 0, false, {
                                fileName: "[project]/src/app/page.tsx",
                                lineNumber: 69,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                                href: "/browse",
                                className: "text-blue-600 hover:underline dark:text-blue-400",
                                children: "View all"
                            }, void 0, false, {
                                fileName: "[project]/src/app/page.tsx",
                                lineNumber: 70,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/page.tsx",
                        lineNumber: 68,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4",
                        children: featuredAgents.map((agent)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "rounded-lg border border-gray-200 bg-white p-6 shadow-sm transition-shadow hover:shadow-md dark:border-gray-800 dark:bg-gray-900",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "mb-4 flex items-center justify-between",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "rounded-full bg-gray-100 px-3 py-1 text-xs font-medium text-gray-800 dark:bg-gray-800 dark:text-gray-200",
                                                children: agent.category
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/page.tsx",
                                                lineNumber: 81,
                                                columnNumber: 17
                                            }, this),
                                            agent.isNew && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "rounded-full bg-green-100 px-3 py-1 text-xs font-medium text-green-800 dark:bg-green-900/30 dark:text-green-400",
                                                children: "New"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/page.tsx",
                                                lineNumber: 85,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/page.tsx",
                                        lineNumber: 80,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                        className: "mb-2 text-xl font-bold",
                                        children: agent.name
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/page.tsx",
                                        lineNumber: 90,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "mb-4 text-sm text-gray-600 dark:text-gray-400",
                                        children: agent.description
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/page.tsx",
                                        lineNumber: 91,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center justify-between",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "text-xs text-gray-500 dark:text-gray-500",
                                                children: [
                                                    agent.usageCount.toLocaleString(),
                                                    " uses"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/page.tsx",
                                                lineNumber: 93,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                                                href: `/agent/${agent.id}`,
                                                className: "rounded-md bg-blue-600 px-3 py-1.5 text-sm font-medium text-white hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800",
                                                children: "View Agent"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/page.tsx",
                                                lineNumber: 96,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/page.tsx",
                                        lineNumber: 92,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, agent.id, true, {
                                fileName: "[project]/src/app/page.tsx",
                                lineNumber: 76,
                                columnNumber: 13
                            }, this))
                    }, void 0, false, {
                        fileName: "[project]/src/app/page.tsx",
                        lineNumber: 74,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/page.tsx",
                lineNumber: 67,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "grid grid-cols-1 gap-10 lg:grid-cols-2",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "mb-6 flex items-center justify-between",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                        className: "text-2xl font-bold",
                                        children: "Recently Used"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/page.tsx",
                                        lineNumber: 113,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                                        href: "/recent",
                                        className: "text-blue-600 hover:underline dark:text-blue-400",
                                        children: "View all"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/page.tsx",
                                        lineNumber: 114,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/page.tsx",
                                lineNumber: 112,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "space-y-4",
                                children: recentAgents.map((agent)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "rounded-lg border border-gray-200 bg-white p-4 shadow-sm transition-shadow hover:shadow-md dark:border-gray-800 dark:bg-gray-900",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center justify-between",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                            className: "font-bold",
                                                            children: agent.name
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/page.tsx",
                                                            lineNumber: 126,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "text-sm text-gray-600 dark:text-gray-400",
                                                            children: agent.description
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/page.tsx",
                                                            lineNumber: 127,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/page.tsx",
                                                    lineNumber: 125,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                                                    href: `/agent/${agent.id}`,
                                                    className: "rounded-md bg-blue-600 px-3 py-1.5 text-sm font-medium text-white hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800",
                                                    children: "View Agent"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/page.tsx",
                                                    lineNumber: 129,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/page.tsx",
                                            lineNumber: 124,
                                            columnNumber: 17
                                        }, this)
                                    }, agent.id, false, {
                                        fileName: "[project]/src/app/page.tsx",
                                        lineNumber: 120,
                                        columnNumber: 15
                                    }, this))
                            }, void 0, false, {
                                fileName: "[project]/src/app/page.tsx",
                                lineNumber: 118,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/page.tsx",
                        lineNumber: 111,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "mb-6 flex items-center justify-between",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                        className: "text-2xl font-bold",
                                        children: "New Agents"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/page.tsx",
                                        lineNumber: 144,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                                        href: "/new-releases",
                                        className: "text-blue-600 hover:underline dark:text-blue-400",
                                        children: "View all"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/page.tsx",
                                        lineNumber: 145,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/page.tsx",
                                lineNumber: 143,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "space-y-4",
                                children: newAgents.map((agent)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "rounded-lg border border-gray-200 bg-white p-4 shadow-sm transition-shadow hover:shadow-md dark:border-gray-800 dark:bg-gray-900",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center justify-between",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "mb-1 flex items-center gap-2",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                                    className: "font-bold",
                                                                    children: agent.name
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/page.tsx",
                                                                    lineNumber: 158,
                                                                    columnNumber: 23
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    className: "rounded-full bg-green-100 px-2 py-0.5 text-xs font-medium text-green-800 dark:bg-green-900/30 dark:text-green-400",
                                                                    children: "New"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/page.tsx",
                                                                    lineNumber: 159,
                                                                    columnNumber: 23
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/page.tsx",
                                                            lineNumber: 157,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "text-sm text-gray-600 dark:text-gray-400",
                                                            children: agent.description
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/page.tsx",
                                                            lineNumber: 163,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/page.tsx",
                                                    lineNumber: 156,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                                                    href: `/agent/${agent.id}`,
                                                    className: "rounded-md bg-blue-600 px-3 py-1.5 text-sm font-medium text-white hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800",
                                                    children: "View Agent"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/page.tsx",
                                                    lineNumber: 165,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/page.tsx",
                                            lineNumber: 155,
                                            columnNumber: 17
                                        }, this)
                                    }, agent.id, false, {
                                        fileName: "[project]/src/app/page.tsx",
                                        lineNumber: 151,
                                        columnNumber: 15
                                    }, this))
                            }, void 0, false, {
                                fileName: "[project]/src/app/page.tsx",
                                lineNumber: 149,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/page.tsx",
                        lineNumber: 142,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/page.tsx",
                lineNumber: 109,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                className: "mt-12 rounded-lg bg-gradient-to-r from-indigo-100 to-purple-100 p-6 dark:from-indigo-950/30 dark:to-purple-950/30 w-full",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "mx-auto max-w-7xl",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "mb-6 flex items-center",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$react$2d$fontawesome$2f$index$2e$es$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["FontAwesomeIcon"], {
                                    icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["faGraduationCap"],
                                    className: "mr-3 h-6 w-6 text-indigo-600 dark:text-indigo-400"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/page.tsx",
                                    lineNumber: 183,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                    className: "text-2xl font-bold",
                                    children: "Learning Hub"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/page.tsx",
                                    lineNumber: 184,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/page.tsx",
                            lineNumber: 182,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "mb-8",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "mb-4 flex items-center justify-between",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                            className: "text-lg font-semibold",
                                            children: "Recent Content"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/page.tsx",
                                            lineNumber: 190,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                                            href: "/learning",
                                            className: "flex items-center text-sm font-medium text-indigo-600 hover:text-indigo-800 dark:text-indigo-400 dark:hover:text-indigo-300",
                                            children: [
                                                "Explore all",
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$react$2d$fontawesome$2f$index$2e$es$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["FontAwesomeIcon"], {
                                                    icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["faArrowRight"],
                                                    className: "ml-1 h-3 w-3"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/page.tsx",
                                                    lineNumber: 193,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/page.tsx",
                                            lineNumber: 191,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/page.tsx",
                                    lineNumber: 189,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "grid gap-4 sm:grid-cols-2 lg:grid-cols-4",
                                    children: recentLearningContent.map((item)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                                            href: item.url,
                                            className: "group overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm transition hover:shadow-md dark:border-gray-800 dark:bg-gray-900",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "relative h-36 overflow-hidden",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                                            src: item.thumbnail || `https://via.placeholder.com/400x200?text=${encodeURIComponent(item.title)}`,
                                                            alt: item.title,
                                                            className: "h-full w-full object-cover transition duration-300 group-hover:scale-105"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/page.tsx",
                                                            lineNumber: 204,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-3",
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "flex items-center",
                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    className: "rounded bg-white/20 px-2 py-1 text-xs font-medium text-white backdrop-blur-sm",
                                                                    children: [
                                                                        item.type === 'video' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Fragment"], {
                                                                            children: [
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$react$2d$fontawesome$2f$index$2e$es$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["FontAwesomeIcon"], {
                                                                                    icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["faVideo"],
                                                                                    className: "mr-1 h-3 w-3"
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/app/page.tsx",
                                                                                    lineNumber: 214,
                                                                                    columnNumber: 31
                                                                                }, this),
                                                                                "Video"
                                                                            ]
                                                                        }, void 0, true),
                                                                        item.type === 'article' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Fragment"], {
                                                                            children: [
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$react$2d$fontawesome$2f$index$2e$es$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["FontAwesomeIcon"], {
                                                                                    icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["faNewspaper"],
                                                                                    className: "mr-1 h-3 w-3"
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/app/page.tsx",
                                                                                    lineNumber: 220,
                                                                                    columnNumber: 31
                                                                                }, this),
                                                                                "Article"
                                                                            ]
                                                                        }, void 0, true),
                                                                        item.type === 'blog' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Fragment"], {
                                                                            children: [
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$react$2d$fontawesome$2f$index$2e$es$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["FontAwesomeIcon"], {
                                                                                    icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["faBlog"],
                                                                                    className: "mr-1 h-3 w-3"
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/app/page.tsx",
                                                                                    lineNumber: 226,
                                                                                    columnNumber: 31
                                                                                }, this),
                                                                                "Blog"
                                                                            ]
                                                                        }, void 0, true),
                                                                        item.type === 'resource' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Fragment"], {
                                                                            children: [
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$react$2d$fontawesome$2f$index$2e$es$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["FontAwesomeIcon"], {
                                                                                    icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["faToolbox"],
                                                                                    className: "mr-1 h-3 w-3"
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/app/page.tsx",
                                                                                    lineNumber: 232,
                                                                                    columnNumber: 31
                                                                                }, this),
                                                                                "Resource"
                                                                            ]
                                                                        }, void 0, true)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/page.tsx",
                                                                    lineNumber: 211,
                                                                    columnNumber: 25
                                                                }, this)
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/page.tsx",
                                                                lineNumber: 210,
                                                                columnNumber: 23
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/page.tsx",
                                                            lineNumber: 209,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/page.tsx",
                                                    lineNumber: 203,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "p-4",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                            className: "mb-1 font-medium text-gray-900 line-clamp-1 group-hover:text-indigo-600 dark:text-white dark:group-hover:text-indigo-400",
                                                            children: item.title
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/page.tsx",
                                                            lineNumber: 241,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "text-sm text-gray-600 line-clamp-2 dark:text-gray-400",
                                                            children: item.description
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/page.tsx",
                                                            lineNumber: 244,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/page.tsx",
                                                    lineNumber: 240,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, item.id, true, {
                                            fileName: "[project]/src/app/page.tsx",
                                            lineNumber: 198,
                                            columnNumber: 17
                                        }, this))
                                }, void 0, false, {
                                    fileName: "[project]/src/app/page.tsx",
                                    lineNumber: 196,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/page.tsx",
                            lineNumber: 188,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "mb-4 flex items-center justify-between",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                            className: "text-lg font-semibold",
                                            children: "Popular Resources"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/page.tsx",
                                            lineNumber: 256,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                                            href: "/learning/resources",
                                            className: "flex items-center text-sm font-medium text-indigo-600 hover:text-indigo-800 dark:text-indigo-400 dark:hover:text-indigo-300",
                                            children: [
                                                "View all resources",
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$react$2d$fontawesome$2f$index$2e$es$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["FontAwesomeIcon"], {
                                                    icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["faArrowRight"],
                                                    className: "ml-1 h-3 w-3"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/page.tsx",
                                                    lineNumber: 259,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/page.tsx",
                                            lineNumber: 257,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/page.tsx",
                                    lineNumber: 255,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "grid gap-4 sm:grid-cols-2 lg:grid-cols-4",
                                    children: popularLearningContent.map((item)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                                            href: item.url,
                                            className: "group overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm transition hover:shadow-md dark:border-gray-800 dark:bg-gray-900",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "p-4",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "mb-3 flex items-center",
                                                        children: [
                                                            item.type === 'video' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$react$2d$fontawesome$2f$index$2e$es$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["FontAwesomeIcon"], {
                                                                icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["faVideo"],
                                                                className: "mr-2 h-4 w-4 text-blue-500"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/page.tsx",
                                                                lineNumber: 272,
                                                                columnNumber: 25
                                                            }, this),
                                                            item.type === 'article' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$react$2d$fontawesome$2f$index$2e$es$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["FontAwesomeIcon"], {
                                                                icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["faNewspaper"],
                                                                className: "mr-2 h-4 w-4 text-green-500"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/page.tsx",
                                                                lineNumber: 275,
                                                                columnNumber: 25
                                                            }, this),
                                                            item.type === 'blog' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$react$2d$fontawesome$2f$index$2e$es$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["FontAwesomeIcon"], {
                                                                icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["faBlog"],
                                                                className: "mr-2 h-4 w-4 text-purple-500"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/page.tsx",
                                                                lineNumber: 278,
                                                                columnNumber: 25
                                                            }, this),
                                                            item.type === 'resource' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$react$2d$fontawesome$2f$index$2e$es$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["FontAwesomeIcon"], {
                                                                icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["faToolbox"],
                                                                className: "mr-2 h-4 w-4 text-orange-500"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/page.tsx",
                                                                lineNumber: 281,
                                                                columnNumber: 25
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "text-sm font-medium text-gray-700 dark:text-gray-300",
                                                                children: item.type.charAt(0).toUpperCase() + item.type.slice(1)
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/page.tsx",
                                                                lineNumber: 283,
                                                                columnNumber: 23
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/page.tsx",
                                                        lineNumber: 270,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                        className: "mb-1 font-medium text-gray-900 line-clamp-1 group-hover:text-indigo-600 dark:text-white dark:group-hover:text-indigo-400",
                                                        children: item.title
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/page.tsx",
                                                        lineNumber: 287,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "text-sm text-gray-600 line-clamp-2 dark:text-gray-400",
                                                        children: item.description
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/page.tsx",
                                                        lineNumber: 290,
                                                        columnNumber: 21
                                                    }, this),
                                                    item.tags && item.tags.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "mt-3 flex flex-wrap gap-1",
                                                        children: item.tags.slice(0, 3).map((tag)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "inline-flex items-center rounded-full bg-gray-100 px-2 py-0.5 text-xs font-medium text-gray-800 dark:bg-gray-800 dark:text-gray-200",
                                                                children: tag
                                                            }, tag, false, {
                                                                fileName: "[project]/src/app/page.tsx",
                                                                lineNumber: 296,
                                                                columnNumber: 27
                                                            }, this))
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/page.tsx",
                                                        lineNumber: 294,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/page.tsx",
                                                lineNumber: 269,
                                                columnNumber: 19
                                            }, this)
                                        }, item.id, false, {
                                            fileName: "[project]/src/app/page.tsx",
                                            lineNumber: 264,
                                            columnNumber: 17
                                        }, this))
                                }, void 0, false, {
                                    fileName: "[project]/src/app/page.tsx",
                                    lineNumber: 262,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/page.tsx",
                            lineNumber: 254,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 181,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/page.tsx",
                lineNumber: 180,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/page.tsx",
        lineNumber: 32,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/app/page.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/page.tsx [app-rsc] (ecmascript)"));
}}),

};

//# sourceMappingURL=_c68e0ec0._.js.map