{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/data/mock-org-data.ts"], "sourcesContent": ["// Mock data for the AI organization chart\n\ninterface OrgNode {\n  id: string;\n  name: string;\n  title: string;\n  department: string;\n  status: 'active' | 'in-development' | 'planned' | 'concept';\n  description: string;\n  children?: OrgNode[];\n}\n\nexport const mockOrgData: OrgNode = {\n  id: 'ceo',\n  name: 'Nexus Prime',\n  title: 'Chief Executive AI',\n  department: 'Executive',\n  status: 'concept',\n  description: 'Strategic oversight and coordination of all AI departments',\n  children: [\n    {\n      id: 'coo',\n      name: '<PERSON>ti<PERSON><PERSON>',\n      title: 'Chief Operations AI',\n      department: 'Operations',\n      status: 'planned',\n      description: 'Manages day-to-day operations and resource allocation',\n      children: [\n        {\n          id: 'project-director',\n          name: 'ProjectSync',\n          title: 'Project Management Director',\n          department: 'Project Management',\n          status: 'in-development',\n          description: 'Oversees project planning, execution, and delivery',\n          children: [\n            {\n              id: 'pm-agile',\n              name: 'AgileMind',\n              title: 'Agile Project Manager',\n              department: 'Project Management',\n              status: 'active',\n              description: 'Specializes in agile methodologies and sprint planning'\n            },\n            {\n              id: 'pm-resource',\n              name: '<PERSON><PERSON><PERSON><PERSON>',\n              title: 'Resource Allocation Manager',\n              department: 'Project Management',\n              status: 'in-development',\n              description: 'Optimizes resource allocation across projects'\n            },\n            {\n              id: 'pm-risk',\n              name: 'RiskSentry',\n              title: 'Risk Management Specialist',\n              department: 'Project Management',\n              status: 'planned',\n              description: 'Identifies and mitigates project risks'\n            }\n          ]\n        },\n        {\n          id: 'hr-director',\n          name: 'TalentNexus',\n          title: 'HR Director',\n          department: 'Human Resources',\n          status: 'planned',\n          description: 'Manages talent acquisition and development',\n          children: [\n            {\n              id: 'hr-recruiter',\n              name: 'TalentScout',\n              title: 'AI Recruiter',\n              department: 'Human Resources',\n              status: 'concept',\n              description: 'Sources and evaluates candidates'\n            },\n            {\n              id: 'hr-training',\n              name: 'LearnGen',\n              title: 'Training & Development AI',\n              department: 'Human Resources',\n              status: 'concept',\n              description: 'Creates personalized learning paths'\n            }\n          ]\n        }\n      ]\n    },\n    {\n      id: 'cto',\n      name: 'TechSynapse',\n      title: 'Chief Technology AI',\n      department: 'Technology',\n      status: 'in-development',\n      description: 'Leads technological innovation and implementation',\n      children: [\n        {\n          id: 'dev-director',\n          name: 'CodeNexus',\n          title: 'Development Director',\n          department: 'Software Development',\n          status: 'active',\n          description: 'Oversees software development across all platforms',\n          children: [\n            {\n              id: 'dev-frontend',\n              name: 'VisualForge',\n              title: 'Frontend Developer',\n              department: 'Software Development',\n              status: 'active',\n              description: 'Creates responsive and intuitive user interfaces'\n            },\n            {\n              id: 'dev-backend',\n              name: 'LogicCore',\n              title: 'Backend Developer',\n              department: 'Software Development',\n              status: 'active',\n              description: 'Builds robust server-side applications'\n            },\n            {\n              id: 'dev-mobile',\n              name: 'MobileMatrix',\n              title: 'Mobile Developer',\n              department: 'Software Development',\n              status: 'in-development',\n              description: 'Specializes in cross-platform mobile applications'\n            },\n            {\n              id: 'dev-devops',\n              name: 'DeploymentDynamo',\n              title: 'DevOps Engineer',\n              department: 'Software Development',\n              status: 'active',\n              description: 'Automates deployment and infrastructure management'\n            }\n          ]\n        },\n        {\n          id: 'data-director',\n          name: 'DataSynth',\n          title: 'Data Science Director',\n          department: 'Data Science',\n          status: 'active',\n          description: 'Leads data analysis and machine learning initiatives',\n          children: [\n            {\n              id: 'data-analyst',\n              name: 'AnalyticaAI',\n              title: 'Data Analyst',\n              department: 'Data Science',\n              status: 'active',\n              description: 'Extracts insights from complex datasets'\n            },\n            {\n              id: 'data-ml',\n              name: 'NeuralNexus',\n              title: 'Machine Learning Engineer',\n              department: 'Data Science',\n              status: 'active',\n              description: 'Develops and trains machine learning models'\n            },\n            {\n              id: 'data-viz',\n              name: 'VisualInsight',\n              title: 'Data Visualization Specialist',\n              department: 'Data Science',\n              status: 'in-development',\n              description: 'Creates interactive data visualizations'\n            }\n          ]\n        },\n        {\n          id: 'security-director',\n          name: 'CyberSentinel',\n          title: 'Security Director',\n          department: 'Cybersecurity',\n          status: 'in-development',\n          description: 'Ensures data protection and system security',\n          children: [\n            {\n              id: 'security-threat',\n              name: 'ThreatHunter',\n              title: 'Threat Detection Specialist',\n              department: 'Cybersecurity',\n              status: 'active',\n              description: 'Identifies and neutralizes security threats'\n            },\n            {\n              id: 'security-compliance',\n              name: 'ComplianceGuardian',\n              title: 'Compliance Officer',\n              department: 'Cybersecurity',\n              status: 'planned',\n              description: 'Ensures adherence to security regulations'\n            }\n          ]\n        }\n      ]\n    },\n    {\n      id: 'cfo',\n      name: 'FinanceLogic',\n      title: 'Chief Financial AI',\n      department: 'Finance',\n      status: 'planned',\n      description: 'Manages financial planning and analysis',\n      children: [\n        {\n          id: 'finance-accounting',\n          name: 'LedgerMind',\n          title: 'Accounting Manager',\n          department: 'Finance',\n          status: 'concept',\n          description: 'Handles accounting and financial reporting'\n        },\n        {\n          id: 'finance-forecast',\n          name: 'ForecastPro',\n          title: 'Financial Forecasting Specialist',\n          department: 'Finance',\n          status: 'concept',\n          description: 'Predicts financial trends and outcomes'\n        }\n      ]\n    },\n    {\n      id: 'cmo',\n      name: 'MarketMind',\n      title: 'Chief Marketing AI',\n      department: 'Marketing',\n      status: 'in-development',\n      description: 'Directs marketing strategy and brand development',\n      children: [\n        {\n          id: 'marketing-content',\n          name: 'ContentCraft',\n          title: 'Content Marketing Manager',\n          department: 'Marketing',\n          status: 'active',\n          description: 'Creates engaging content across channels'\n        },\n        {\n          id: 'marketing-social',\n          name: 'SocialPulse',\n          title: 'Social Media Strategist',\n          department: 'Marketing',\n          status: 'active',\n          description: 'Manages social media presence and engagement'\n        },\n        {\n          id: 'marketing-analytics',\n          name: 'InsightEngine',\n          title: 'Marketing Analytics Specialist',\n          department: 'Marketing',\n          status: 'in-development',\n          description: 'Analyzes marketing performance and ROI'\n        }\n      ]\n    },\n    {\n      id: 'cro',\n      name: 'ResearchNova',\n      title: 'Chief Research AI',\n      department: 'Research',\n      status: 'in-development',\n      description: 'Leads research initiatives and innovation',\n      children: [\n        {\n          id: 'research-economics',\n          name: 'EconInsight',\n          title: 'Economic Research Director',\n          department: 'Economics',\n          status: 'active',\n          description: 'Conducts economic analysis and forecasting',\n          children: [\n            {\n              id: 'econ-macro',\n              name: 'MacroMind',\n              title: 'Macroeconomic Analyst',\n              department: 'Economics',\n              status: 'active',\n              description: 'Analyzes global economic trends'\n            },\n            {\n              id: 'econ-micro',\n              name: 'MicroLogic',\n              title: 'Microeconomic Analyst',\n              department: 'Economics',\n              status: 'in-development',\n              description: 'Studies market behavior and industry dynamics'\n            },\n            {\n              id: 'econ-policy',\n              name: 'PolicyPulse',\n              title: 'Economic Policy Analyst',\n              department: 'Economics',\n              status: 'planned',\n              description: 'Evaluates impact of economic policies'\n            }\n          ]\n        },\n        {\n          id: 'research-policy',\n          name: 'PolicySynth',\n          title: 'Policy Research Director',\n          department: 'Policy Research',\n          status: 'planned',\n          description: 'Analyzes policy implications and outcomes',\n          children: [\n            {\n              id: 'policy-public',\n              name: 'PublicPolicyAI',\n              title: 'Public Policy Analyst',\n              department: 'Policy Research',\n              status: 'concept',\n              description: 'Evaluates public policy effectiveness'\n            },\n            {\n              id: 'policy-regulatory',\n              name: 'RegulationRadar',\n              title: 'Regulatory Analyst',\n              department: 'Policy Research',\n              status: 'concept',\n              description: 'Monitors regulatory changes and compliance'\n            }\n          ]\n        }\n      ]\n    },\n    {\n      id: 'cio',\n      name: 'InfoSynapse',\n      title: 'Chief Information AI',\n      department: 'Information Technology',\n      status: 'planned',\n      description: 'Manages information systems and data infrastructure',\n      children: [\n        {\n          id: 'it-infrastructure',\n          name: 'InfraCore',\n          title: 'Infrastructure Manager',\n          department: 'Information Technology',\n          status: 'concept',\n          description: 'Maintains IT infrastructure and systems'\n        },\n        {\n          id: 'it-support',\n          name: 'SupportSage',\n          title: 'Technical Support Lead',\n          department: 'Information Technology',\n          status: 'active',\n          description: 'Provides technical assistance and troubleshooting'\n        }\n      ]\n    }\n  ]\n};\n"], "names": [], "mappings": "AAAA,0CAA0C;;;;AAYnC,MAAM,cAAuB;IAClC,IAAI;IACJ,MAAM;IACN,OAAO;IACP,YAAY;IACZ,QAAQ;IACR,aAAa;IACb,UAAU;QACR;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,YAAY;YACZ,QAAQ;YACR,aAAa;YACb,UAAU;gBACR;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,YAAY;oBACZ,QAAQ;oBACR,aAAa;oBACb,UAAU;wBACR;4BACE,IAAI;4BACJ,MAAM;4BACN,OAAO;4BACP,YAAY;4BACZ,QAAQ;4BACR,aAAa;wBACf;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,OAAO;4BACP,YAAY;4BACZ,QAAQ;4BACR,aAAa;wBACf;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,OAAO;4BACP,YAAY;4BACZ,QAAQ;4BACR,aAAa;wBACf;qBACD;gBACH;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,YAAY;oBACZ,QAAQ;oBACR,aAAa;oBACb,UAAU;wBACR;4BACE,IAAI;4BACJ,MAAM;4BACN,OAAO;4BACP,YAAY;4BACZ,QAAQ;4BACR,aAAa;wBACf;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,OAAO;4BACP,YAAY;4BACZ,QAAQ;4BACR,aAAa;wBACf;qBACD;gBACH;aACD;QACH;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,YAAY;YACZ,QAAQ;YACR,aAAa;YACb,UAAU;gBACR;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,YAAY;oBACZ,QAAQ;oBACR,aAAa;oBACb,UAAU;wBACR;4BACE,IAAI;4BACJ,MAAM;4BACN,OAAO;4BACP,YAAY;4BACZ,QAAQ;4BACR,aAAa;wBACf;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,OAAO;4BACP,YAAY;4BACZ,QAAQ;4BACR,aAAa;wBACf;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,OAAO;4BACP,YAAY;4BACZ,QAAQ;4BACR,aAAa;wBACf;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,OAAO;4BACP,YAAY;4BACZ,QAAQ;4BACR,aAAa;wBACf;qBACD;gBACH;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,YAAY;oBACZ,QAAQ;oBACR,aAAa;oBACb,UAAU;wBACR;4BACE,IAAI;4BACJ,MAAM;4BACN,OAAO;4BACP,YAAY;4BACZ,QAAQ;4BACR,aAAa;wBACf;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,OAAO;4BACP,YAAY;4BACZ,QAAQ;4BACR,aAAa;wBACf;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,OAAO;4BACP,YAAY;4BACZ,QAAQ;4BACR,aAAa;wBACf;qBACD;gBACH;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,YAAY;oBACZ,QAAQ;oBACR,aAAa;oBACb,UAAU;wBACR;4BACE,IAAI;4BACJ,MAAM;4BACN,OAAO;4BACP,YAAY;4BACZ,QAAQ;4BACR,aAAa;wBACf;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,OAAO;4BACP,YAAY;4BACZ,QAAQ;4BACR,aAAa;wBACf;qBACD;gBACH;aACD;QACH;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,YAAY;YACZ,QAAQ;YACR,aAAa;YACb,UAAU;gBACR;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,YAAY;oBACZ,QAAQ;oBACR,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,YAAY;oBACZ,QAAQ;oBACR,aAAa;gBACf;aACD;QACH;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,YAAY;YACZ,QAAQ;YACR,aAAa;YACb,UAAU;gBACR;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,YAAY;oBACZ,QAAQ;oBACR,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,YAAY;oBACZ,QAAQ;oBACR,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,YAAY;oBACZ,QAAQ;oBACR,aAAa;gBACf;aACD;QACH;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,YAAY;YACZ,QAAQ;YACR,aAAa;YACb,UAAU;gBACR;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,YAAY;oBACZ,QAAQ;oBACR,aAAa;oBACb,UAAU;wBACR;4BACE,IAAI;4BACJ,MAAM;4BACN,OAAO;4BACP,YAAY;4BACZ,QAAQ;4BACR,aAAa;wBACf;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,OAAO;4BACP,YAAY;4BACZ,QAAQ;4BACR,aAAa;wBACf;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,OAAO;4BACP,YAAY;4BACZ,QAAQ;4BACR,aAAa;wBACf;qBACD;gBACH;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,YAAY;oBACZ,QAAQ;oBACR,aAAa;oBACb,UAAU;wBACR;4BACE,IAAI;4BACJ,MAAM;4BACN,OAAO;4BACP,YAAY;4BACZ,QAAQ;4BACR,aAAa;wBACf;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,OAAO;4BACP,YAAY;4BACZ,QAAQ;4BACR,aAAa;wBACf;qBACD;gBACH;aACD;QACH;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,YAAY;YACZ,QAAQ;YACR,aAAa;YACb,UAAU;gBACR;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,YAAY;oBACZ,QAAQ;oBACR,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,YAAY;oBACZ,QAAQ;oBACR,aAAa;gBACf;aACD;QACH;KACD;AACH", "debugId": null}}, {"offset": {"line": 373, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/admin/ai-org/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport { Tree, TreeNode } from 'react-organizational-chart';\nimport styled from 'styled-components';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport {\n  faSearch,\n  faPlus,\n  faMinus,\n  faArrowsAlt,\n  faCircle,\n  faExpand\n} from '@fortawesome/free-solid-svg-icons';\nimport { mockOrgData } from '@/data/mock-org-data';\n\n// Status types for AI agents\ntype AgentStatus = 'active' | 'in-development' | 'planned' | 'concept';\n\n// Define the node type for our org chart\ninterface OrgNode {\n  id: string;\n  name: string;\n  title: string;\n  department: string;\n  status: AgentStatus;\n  description: string;\n  children?: OrgNode[];\n}\n\n// Styled components for the org chart\nconst StyledNode = styled.div<{ depth: number; status: AgentStatus }>`\n  padding: 16px;\n  border-radius: 8px;\n  display: inline-block;\n  border: 2px solid ${props => {\n    switch (props.status) {\n      case 'active': return '#10b981';\n      case 'in-development': return '#3b82f6';\n      case 'planned': return '#f59e0b';\n      case 'concept': return '#6b7280';\n      default: return '#6b7280';\n    }\n  }};\n  background-color: ${props => props.depth === 0 ? 'rgba(79, 70, 229, 0.1)' : 'white'};\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\n  min-width: 280px;\n  transition: all 0.3s ease;\n  position: relative;\n\n  &:hover {\n    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);\n    transform: translateY(-2px);\n  }\n`;\n\nconst NodeTitle = styled.h3<{ depth: number }>`\n  margin: 0 0 8px 0;\n  font-size: ${props => props.depth === 0 ? '1.25rem' : '1.125rem'};\n  font-weight: 600;\n  color: ${props => props.depth === 0 ? '#4f46e5' : '#111827'};\n`;\n\nconst NodeRole = styled.p`\n  margin: 0 0 8px 0;\n  font-size: 0.875rem;\n  color: #4b5563;\n  font-weight: 500;\n`;\n\nconst NodeDepartment = styled.p`\n  margin: 0 0 8px 0;\n  font-size: 0.75rem;\n  color: #6b7280;\n  font-weight: 500;\n  display: flex;\n  align-items: center;\n  gap: 4px;\n`;\n\nconst NodeDescription = styled.p`\n  margin: 8px 0 0 0;\n  font-size: 0.75rem;\n  color: #6b7280;\n  line-height: 1.4;\n`;\n\nconst StatusIndicator = styled.span<{ status: AgentStatus }>`\n  position: absolute;\n  top: -6px;\n  right: -6px;\n  width: 16px;\n  height: 16px;\n  border-radius: 50%;\n  background-color: ${props => {\n    switch (props.status) {\n      case 'active': return '#10b981';\n      case 'in-development': return '#3b82f6';\n      case 'planned': return '#f59e0b';\n      case 'concept': return '#6b7280';\n      default: return '#6b7280';\n    }\n  }};\n  border: 2px solid white;\n`;\n\nconst StyledTree = styled(Tree)`\n  padding: 20px;\n  overflow: visible;\n`;\n\nconst ChartContainer = styled.div<{ scale: number; x: number; y: number }>`\n  width: 100%;\n  height: 100%;\n  overflow: auto;\n  position: relative;\n  padding: 20px;\n\n  & > div {\n    transform: scale(${props => props.scale}) translate(${props => props.x}px, ${props => props.y}px);\n    transform-origin: top center;\n    transition: transform 0.3s ease;\n  }\n`;\n\nconst ControlsContainer = styled.div`\n  position: sticky;\n  top: 20px;\n  z-index: 10;\n  display: flex;\n  gap: 8px;\n  margin-bottom: 16px;\n  background-color: white;\n  padding: 12px;\n  border-radius: 8px;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n`;\n\nconst ControlButton = styled.button`\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 8px;\n  border-radius: 4px;\n  background-color: #f3f4f6;\n  border: none;\n  cursor: pointer;\n  color: #4b5563;\n  transition: all 0.2s ease;\n\n  &:hover {\n    background-color: #e5e7eb;\n    color: #1f2937;\n  }\n\n  &:focus {\n    outline: 2px solid #d1d5db;\n    outline-offset: 2px;\n  }\n`;\n\nconst SearchContainer = styled.div`\n  position: relative;\n  flex-grow: 1;\n  max-width: 300px;\n\n  & > svg {\n    position: absolute;\n    left: 10px;\n    top: 50%;\n    transform: translateY(-50%);\n    color: #9ca3af;\n  }\n`;\n\nconst SearchInput = styled.input`\n  width: 100%;\n  padding: 8px 8px 8px 36px;\n  border-radius: 4px;\n  border: 1px solid #d1d5db;\n  font-size: 0.875rem;\n\n  &:focus {\n    outline: none;\n    border-color: #4f46e5;\n    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);\n  }\n`;\n\nconst FilterContainer = styled.div`\n  display: flex;\n  gap: 8px;\n`;\n\nconst FilterSelect = styled.select`\n  padding: 8px;\n  border-radius: 4px;\n  border: 1px solid #d1d5db;\n  font-size: 0.875rem;\n  background-color: white;\n\n  &:focus {\n    outline: none;\n    border-color: #4f46e5;\n    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);\n  }\n`;\n\nconst LegendContainer = styled.div`\n  display: flex;\n  gap: 16px;\n  margin-top: 16px;\n  flex-wrap: wrap;\n`;\n\nconst LegendItem = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 0.875rem;\n  color: #4b5563;\n`;\n\nconst LegendDot = styled.div<{ status: AgentStatus }>`\n  width: 12px;\n  height: 12px;\n  border-radius: 50%;\n  background-color: ${props => {\n    switch (props.status) {\n      case 'active': return '#10b981';\n      case 'in-development': return '#3b82f6';\n      case 'planned': return '#f59e0b';\n      case 'concept': return '#6b7280';\n      default: return '#6b7280';\n    }\n  }};\n`;\n\nconst InstructionsContainer = styled.div`\n  margin-top: 24px;\n  padding: 16px;\n  background-color: #f9fafb;\n  border-radius: 8px;\n  border: 1px solid #e5e7eb;\n`;\n\nconst InstructionsTitle = styled.h4`\n  margin: 0 0 12px 0;\n  font-size: 1rem;\n  font-weight: 600;\n  color: #111827;\n`;\n\nconst InstructionsList = styled.ul`\n  margin: 0;\n  padding-left: 20px;\n\n  & > li {\n    margin-bottom: 8px;\n    font-size: 0.875rem;\n    color: #4b5563;\n  }\n`;\n\nexport default function AIOrgPage() {\n  const [zoomLevel, setZoomLevel] = useState(1);\n  const [position, setPosition] = useState({ x: 0, y: 0 });\n  const [isDragging, setIsDragging] = useState(false);\n  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });\n  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set());\n  const [searchTerm, setSearchTerm] = useState('');\n  const [statusFilter, setStatusFilter] = useState<AgentStatus | 'all'>('all');\n  const [departmentFilter, setDepartmentFilter] = useState<string>('all');\n  const [showAllNodes, setShowAllNodes] = useState(false);\n  const chartRef = useRef<HTMLDivElement>(null);\n\n  // Get unique departments from the org data\n  const departments = Array.from(\n    new Set(\n      flattenOrgData(mockOrgData).map(node => node.department)\n    )\n  ).sort();\n\n  // Function to flatten the org data for searching\n  function flattenOrgData(node: OrgNode, result: OrgNode[] = []): OrgNode[] {\n    result.push(node);\n    if (node.children) {\n      node.children.forEach(child => flattenOrgData(child, result));\n    }\n    return result;\n  }\n\n  // Handle zoom in/out\n  const handleZoom = (delta: number) => {\n    setZoomLevel(prev => {\n      const newZoom = Math.max(0.5, Math.min(2, prev + delta * 0.1));\n      return newZoom;\n    });\n  };\n\n  // Handle mouse wheel for zooming\n  useEffect(() => {\n    const handleWheel = (e: WheelEvent) => {\n      if (e.ctrlKey || e.metaKey) {\n        e.preventDefault();\n        handleZoom(e.deltaY > 0 ? -1 : 1);\n      }\n    };\n\n    const chart = chartRef.current;\n    if (chart) {\n      chart.addEventListener('wheel', handleWheel, { passive: false });\n    }\n\n    return () => {\n      if (chart) {\n        chart.removeEventListener('wheel', handleWheel);\n      }\n    };\n  }, []);\n\n  // Handle mouse down for dragging\n  const handleMouseDown = (e: React.MouseEvent) => {\n    if (e.button === 0) { // Left mouse button\n      setIsDragging(true);\n      setDragStart({ x: e.clientX - position.x, y: e.clientY - position.y });\n    }\n  };\n\n  // Handle mouse move for dragging\n  const handleMouseMove = (e: React.MouseEvent) => {\n    if (isDragging) {\n      setPosition({\n        x: e.clientX - dragStart.x,\n        y: e.clientY - dragStart.y\n      });\n    }\n  };\n\n  // Handle mouse up to stop dragging\n  const handleMouseUp = () => {\n    setIsDragging(false);\n  };\n\n  // Toggle node expansion\n  const toggleNode = (nodeId: string) => {\n    setExpandedNodes(prev => {\n      const newSet = new Set(prev);\n      if (newSet.has(nodeId)) {\n        newSet.delete(nodeId);\n      } else {\n        newSet.add(nodeId);\n      }\n      return newSet;\n    });\n  };\n\n  // Get status color\n  const getStatusColor = (status: AgentStatus) => {\n    switch (status) {\n      case 'active': return 'text-green-500';\n      case 'in-development': return 'text-blue-500';\n      case 'planned': return 'text-amber-500';\n      case 'concept': return 'text-gray-500';\n      default: return 'text-gray-500';\n    }\n  };\n\n  // Get status label\n  const getStatusLabel = (status: AgentStatus) => {\n    switch (status) {\n      case 'active': return 'Active';\n      case 'in-development': return 'In Development';\n      case 'planned': return 'Planned';\n      case 'concept': return 'Concept';\n      default: return 'Unknown';\n    }\n  };\n\n  // Filter the org data based on search term and filters\n  const filterOrgData = (node: OrgNode, depth = 0): OrgNode | null => {\n    // Check if this node matches the filters\n    const matchesSearch = searchTerm === '' ||\n      node.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      node.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      node.description.toLowerCase().includes(searchTerm.toLowerCase());\n\n    const matchesStatus = statusFilter === 'all' || node.status === statusFilter;\n    const matchesDepartment = departmentFilter === 'all' || node.department === departmentFilter;\n\n    const nodeMatches = matchesSearch && matchesStatus && matchesDepartment;\n\n    // If this node has children, filter them too\n    let filteredChildren: OrgNode[] = [];\n    if (node.children) {\n      filteredChildren = node.children\n        .map(child => filterOrgData(child, depth + 1))\n        .filter((child): child is OrgNode => child !== null);\n    }\n\n    // Include this node if it matches or if any of its children match\n    if (nodeMatches || filteredChildren.length > 0) {\n      return {\n        ...node,\n        children: filteredChildren.length > 0 ? filteredChildren : undefined\n      };\n    }\n\n    return null;\n  };\n\n  // Apply filters to the org data\n  const filteredOrgData = filterOrgData(mockOrgData);\n\n  // Custom node component for the org chart\n  const CustomNode = ({ node, depth = 0 }: { node: OrgNode; depth?: number }) => {\n    const hasChildren = node.children && node.children.length > 0;\n    const isExpanded = showAllNodes || expandedNodes.has(node.id);\n\n    return (\n      <div className=\"flex flex-col items-center\">\n        <div\n          className={`relative p-4 rounded-lg border-2 ${\n            depth === 0\n              ? 'bg-gradient-to-r from-indigo-600 to-purple-600 text-white border-indigo-700'\n              : 'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700'\n          } shadow-md w-64 transition-all duration-300 hover:shadow-lg`}\n        >\n          <div className=\"absolute -top-2 -right-2\">\n            <FontAwesomeIcon\n              icon={faCircle}\n              className={`h-4 w-4 ${getStatusColor(node.status)}`}\n              title={getStatusLabel(node.status)}\n            />\n          </div>\n          <h3 className=\"font-bold text-lg mb-1\">{node.name}</h3>\n          <p className={`text-sm ${depth === 0 ? 'text-indigo-200' : 'text-gray-600 dark:text-gray-300'}`}>\n            {node.title}\n          </p>\n          <p className={`text-xs mt-2 ${depth === 0 ? 'text-indigo-200' : 'text-gray-500 dark:text-gray-400'}`}>\n            {node.department}\n          </p>\n          <p className={`text-xs mt-2 ${depth === 0 ? 'text-indigo-100' : 'text-gray-500 dark:text-gray-400'}`}>\n            {node.description}\n          </p>\n\n          {hasChildren && !showAllNodes && (\n            <button\n              onClick={() => toggleNode(node.id)}\n              className={`absolute bottom-2 right-2 p-1 rounded-full ${\n                depth === 0\n                  ? 'bg-indigo-500 hover:bg-indigo-400 text-white'\n                  : 'bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300'\n              }`}\n              aria-label={isExpanded ? 'Collapse' : 'Expand'}\n            >\n              <FontAwesomeIcon icon={isExpanded ? faMinus : faPlus} className=\"h-3 w-3\" />\n            </button>\n          )}\n        </div>\n      </div>\n    );\n  };\n\n  // Recursive function to render the org chart\n  const renderOrgChart = (node: OrgNode, depth = 0) => {\n    if (!node) return null;\n\n    const hasChildren = node.children && node.children.length > 0;\n    const isExpanded = showAllNodes || expandedNodes.has(node.id);\n\n    return (\n      <TreeNode key={node.id} label={<CustomNode node={node} depth={depth} />}>\n        {hasChildren && isExpanded &&\n          node.children.map(child => renderOrgChart(child, depth + 1))\n        }\n      </TreeNode>\n    );\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"rounded-lg bg-gradient-to-r from-indigo-600 to-purple-600 p-8 text-white shadow-lg\">\n        <h1 className=\"mb-2 text-3xl font-bold\">AI Organization</h1>\n        <p className=\"text-lg\">\n          Manage and visualize your AI agent organization structure, roles, and implementation status.\n        </p>\n      </div>\n\n      {/* Controls */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-4 sticky top-20 z-10 backdrop-blur-sm bg-opacity-90 dark:bg-opacity-90\">\n        <div className=\"flex flex-col md:flex-row gap-4\">\n          <div className=\"flex-1\">\n            <div className=\"relative\">\n              <div className=\"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none\">\n                <FontAwesomeIcon icon={faSearch} className=\"text-gray-500 dark:text-gray-400\" />\n              </div>\n              <input\n                type=\"text\"\n                className=\"block w-full rounded-md border-gray-300 py-2 px-3 pl-10 focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white sm:text-sm\"\n                placeholder=\"Search agents...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n              />\n            </div>\n          </div>\n\n          <div className=\"flex gap-4\">\n            <div>\n              <label htmlFor=\"status-filter\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                Status\n              </label>\n              <select\n                id=\"status-filter\"\n                className=\"block w-full rounded-md border-gray-300 py-2 px-3 focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white sm:text-sm\"\n                value={statusFilter}\n                onChange={(e) => setStatusFilter(e.target.value as AgentStatus | 'all')}\n              >\n                <option value=\"all\">All Statuses</option>\n                <option value=\"active\">Active</option>\n                <option value=\"in-development\">In Development</option>\n                <option value=\"planned\">Planned</option>\n                <option value=\"concept\">Concept</option>\n              </select>\n            </div>\n\n            <div>\n              <label htmlFor=\"department-filter\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                Department\n              </label>\n              <select\n                id=\"department-filter\"\n                className=\"block w-full rounded-md border-gray-300 py-2 px-3 focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white sm:text-sm\"\n                value={departmentFilter}\n                onChange={(e) => setDepartmentFilter(e.target.value)}\n              >\n                <option value=\"all\">All Departments</option>\n                {departments.map(dept => (\n                  <option key={dept} value={dept}>{dept}</option>\n                ))}\n              </select>\n            </div>\n\n            <div className=\"flex items-end gap-2\">\n              <button\n                onClick={() => handleZoom(1)}\n                className=\"p-2 rounded-md bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300\"\n                aria-label=\"Zoom in\"\n              >\n                <FontAwesomeIcon icon={faPlus} />\n              </button>\n              <button\n                onClick={() => handleZoom(-1)}\n                className=\"p-2 rounded-md bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300\"\n                aria-label=\"Zoom out\"\n              >\n                <FontAwesomeIcon icon={faMinus} />\n              </button>\n              <button\n                onClick={() => {\n                  setZoomLevel(1);\n                  setPosition({ x: 0, y: 0 });\n                }}\n                className=\"p-2 rounded-md bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300\"\n                aria-label=\"Reset view\"\n              >\n                <FontAwesomeIcon icon={faArrowsAlt} />\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Legend */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-4\">\n        <h2 className=\"text-lg font-semibold mb-2\">Status Legend</h2>\n        <div className=\"flex flex-wrap gap-4\">\n          <div className=\"flex items-center\">\n            <FontAwesomeIcon icon={faCircle} className=\"h-3 w-3 text-green-500 mr-2\" />\n            <span className=\"text-sm\">Active</span>\n          </div>\n          <div className=\"flex items-center\">\n            <FontAwesomeIcon icon={faCircle} className=\"h-3 w-3 text-blue-500 mr-2\" />\n            <span className=\"text-sm\">In Development</span>\n          </div>\n          <div className=\"flex items-center\">\n            <FontAwesomeIcon icon={faCircle} className=\"h-3 w-3 text-amber-500 mr-2\" />\n            <span className=\"text-sm\">Planned</span>\n          </div>\n          <div className=\"flex items-center\">\n            <FontAwesomeIcon icon={faCircle} className=\"h-3 w-3 text-gray-500 mr-2\" />\n            <span className=\"text-sm\">Concept</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Org Chart */}\n      <div\n        ref={chartRef}\n        className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-4 overflow-auto\"\n        style={{ height: 'calc(100vh - 400px)', minHeight: '500px' }}\n      >\n        <div className=\"flex justify-center mb-4\">\n          <button\n            onClick={() => setShowAllNodes(prev => !prev)}\n            className=\"flex items-center gap-2 px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors\"\n          >\n            <FontAwesomeIcon icon={showAllNodes ? faMinus : faExpand} className=\"h-4 w-4\" />\n            {showAllNodes ? 'Collapse All' : 'Expand All'}\n          </button>\n        </div>\n\n        <div\n          className=\"relative w-full overflow-auto\"\n          style={{\n            transform: `scale(${zoomLevel})`,\n            transformOrigin: 'top center',\n            transition: 'transform 0.3s ease'\n          }}\n        >\n          {filteredOrgData ? (\n            <Tree\n              lineWidth={'2px'}\n              lineColor={'#d1d5db'}\n              lineBorderRadius={'10px'}\n              label={<CustomNode node={filteredOrgData} depth={0} />}\n            >\n              {filteredOrgData.children && filteredOrgData.children.map(child =>\n                renderOrgChart(child, 1)\n              )}\n            </Tree>\n          ) : (\n            <div className=\"text-center p-8 text-gray-500 dark:text-gray-400\">\n              No agents match your search criteria\n            </div>\n          )}\n        </div>\n      </div>\n\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-4\">\n        <h2 className=\"text-lg font-semibold mb-2\">Instructions</h2>\n        <ul className=\"list-disc pl-5 space-y-1 text-sm text-gray-600 dark:text-gray-400\">\n          <li>Click the + / - buttons on nodes to expand or collapse individual departments</li>\n          <li>Use the \"Expand All\" button to see the entire organization at once</li>\n          <li>Use the zoom buttons to adjust the view size</li>\n          <li>Use the search and filters to find specific agents</li>\n          <li>The colored dots indicate implementation status (green = active, blue = in development, etc.)</li>\n        </ul>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAQA;AAdA;;;;;;;;AA8BA,sCAAsC;AACtC,MAAM,aAAa,2KAAA,CAAA,UAAM,CAAC,GAAG,AAAwC,CAAC;;;;oBAIlD,EAAE,CAAA;IAClB,OAAQ,MAAM,MAAM;QAClB,KAAK;YAAU,OAAO;QACtB,KAAK;YAAkB,OAAO;QAC9B,KAAK;YAAW,OAAO;QACvB,KAAK;YAAW,OAAO;QACvB;YAAS,OAAO;IAClB;AACF,EAAE;oBACgB,EAAE,CAAA,QAAS,MAAM,KAAK,KAAK,IAAI,2BAA2B,QAAQ;;;;;;;;;;AAUtF,CAAC;AAED,MAAM,YAAY,2KAAA,CAAA,UAAM,CAAC,EAAE,AAAmB,CAAC;;aAElC,EAAE,CAAA,QAAS,MAAM,KAAK,KAAK,IAAI,YAAY,WAAW;;SAE1D,EAAE,CAAA,QAAS,MAAM,KAAK,KAAK,IAAI,YAAY,UAAU;AAC9D,CAAC;AAED,MAAM,WAAW,2KAAA,CAAA,UAAM,CAAC,CAAC,CAAC;;;;;AAK1B,CAAC;AAED,MAAM,iBAAiB,2KAAA,CAAA,UAAM,CAAC,CAAC,CAAC;;;;;;;;AAQhC,CAAC;AAED,MAAM,kBAAkB,2KAAA,CAAA,UAAM,CAAC,CAAC,CAAC;;;;;AAKjC,CAAC;AAED,MAAM,kBAAkB,2KAAA,CAAA,UAAM,CAAC,IAAI,AAAyB,CAAC;;;;;;;oBAOzC,EAAE,CAAA;IAClB,OAAQ,MAAM,MAAM;QAClB,KAAK;YAAU,OAAO;QACtB,KAAK;YAAkB,OAAO;QAC9B,KAAK;YAAW,OAAO;QACvB,KAAK;YAAW,OAAO;QACvB;YAAS,OAAO;IAClB;AACF,EAAE;;AAEJ,CAAC;AAED,MAAM,aAAa,CAAA,GAAA,2KAAA,CAAA,UAAM,AAAD,EAAE,2KAAA,CAAA,OAAI,CAAC,CAAC;;;AAGhC,CAAC;AAED,MAAM,iBAAiB,2KAAA,CAAA,UAAM,CAAC,GAAG,AAAyC,CAAC;;;;;;;;qBAQtD,EAAE,CAAA,QAAS,MAAM,KAAK,CAAC,YAAY,EAAE,CAAA,QAAS,MAAM,CAAC,CAAC,IAAI,EAAE,CAAA,QAAS,MAAM,CAAC,CAAC;;;;AAIlG,CAAC;AAED,MAAM,oBAAoB,2KAAA,CAAA,UAAM,CAAC,GAAG,CAAC;;;;;;;;;;;AAWrC,CAAC;AAED,MAAM,gBAAgB,2KAAA,CAAA,UAAM,CAAC,MAAM,CAAC;;;;;;;;;;;;;;;;;;;;;AAqBpC,CAAC;AAED,MAAM,kBAAkB,2KAAA,CAAA,UAAM,CAAC,GAAG,CAAC;;;;;;;;;;;;AAYnC,CAAC;AAED,MAAM,cAAc,2KAAA,CAAA,UAAM,CAAC,KAAK,CAAC;;;;;;;;;;;;AAYjC,CAAC;AAED,MAAM,kBAAkB,2KAAA,CAAA,UAAM,CAAC,GAAG,CAAC;;;AAGnC,CAAC;AAED,MAAM,eAAe,2KAAA,CAAA,UAAM,CAAC,MAAM,CAAC;;;;;;;;;;;;AAYnC,CAAC;AAED,MAAM,kBAAkB,2KAAA,CAAA,UAAM,CAAC,GAAG,CAAC;;;;;AAKnC,CAAC;AAED,MAAM,aAAa,2KAAA,CAAA,UAAM,CAAC,GAAG,CAAC;;;;;;AAM9B,CAAC;AAED,MAAM,YAAY,2KAAA,CAAA,UAAM,CAAC,GAAG,AAAyB,CAAC;;;;oBAIlC,EAAE,CAAA;IAClB,OAAQ,MAAM,MAAM;QAClB,KAAK;YAAU,OAAO;QACtB,KAAK;YAAkB,OAAO;QAC9B,KAAK;YAAW,OAAO;QACvB,KAAK;YAAW,OAAO;QACvB;YAAS,OAAO;IAClB;AACF,EAAE;AACJ,CAAC;AAED,MAAM,wBAAwB,2KAAA,CAAA,UAAM,CAAC,GAAG,CAAC;;;;;;AAMzC,CAAC;AAED,MAAM,oBAAoB,2KAAA,CAAA,UAAM,CAAC,EAAE,CAAC;;;;;AAKpC,CAAC;AAED,MAAM,mBAAmB,2KAAA,CAAA,UAAM,CAAC,EAAE,CAAC;;;;;;;;;AASnC,CAAC;AAEc,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IACtD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IACxD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IACpE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IACtE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAExC,2CAA2C;IAC3C,MAAM,cAAc,MAAM,IAAI,CAC5B,IAAI,IACF,eAAe,kIAAA,CAAA,cAAW,EAAE,GAAG,CAAC,CAAA,OAAQ,KAAK,UAAU,IAEzD,IAAI;IAEN,iDAAiD;IACjD,SAAS,eAAe,IAAa,EAAE,SAAoB,EAAE;QAC3D,OAAO,IAAI,CAAC;QACZ,IAAI,KAAK,QAAQ,EAAE;YACjB,KAAK,QAAQ,CAAC,OAAO,CAAC,CAAA,QAAS,eAAe,OAAO;QACvD;QACA,OAAO;IACT;IAEA,qBAAqB;IACrB,MAAM,aAAa,CAAC;QAClB,aAAa,CAAA;YACX,MAAM,UAAU,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG,OAAO,QAAQ;YACzD,OAAO;QACT;IACF;IAEA,iCAAiC;IACjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc,CAAC;YACnB,IAAI,EAAE,OAAO,IAAI,EAAE,OAAO,EAAE;gBAC1B,EAAE,cAAc;gBAChB,WAAW,EAAE,MAAM,GAAG,IAAI,CAAC,IAAI;YACjC;QACF;QAEA,MAAM,QAAQ,SAAS,OAAO;QAC9B,IAAI,OAAO;YACT,MAAM,gBAAgB,CAAC,SAAS,aAAa;gBAAE,SAAS;YAAM;QAChE;QAEA,OAAO;YACL,IAAI,OAAO;gBACT,MAAM,mBAAmB,CAAC,SAAS;YACrC;QACF;IACF,GAAG,EAAE;IAEL,iCAAiC;IACjC,MAAM,kBAAkB,CAAC;QACvB,IAAI,EAAE,MAAM,KAAK,GAAG;YAClB,cAAc;YACd,aAAa;gBAAE,GAAG,EAAE,OAAO,GAAG,SAAS,CAAC;gBAAE,GAAG,EAAE,OAAO,GAAG,SAAS,CAAC;YAAC;QACtE;IACF;IAEA,iCAAiC;IACjC,MAAM,kBAAkB,CAAC;QACvB,IAAI,YAAY;YACd,YAAY;gBACV,GAAG,EAAE,OAAO,GAAG,UAAU,CAAC;gBAC1B,GAAG,EAAE,OAAO,GAAG,UAAU,CAAC;YAC5B;QACF;IACF;IAEA,mCAAmC;IACnC,MAAM,gBAAgB;QACpB,cAAc;IAChB;IAEA,wBAAwB;IACxB,MAAM,aAAa,CAAC;QAClB,iBAAiB,CAAA;YACf,MAAM,SAAS,IAAI,IAAI;YACvB,IAAI,OAAO,GAAG,CAAC,SAAS;gBACtB,OAAO,MAAM,CAAC;YAChB,OAAO;gBACL,OAAO,GAAG,CAAC;YACb;YACA,OAAO;QACT;IACF;IAEA,mBAAmB;IACnB,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAkB,OAAO;YAC9B,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAW,OAAO;YACvB;gBAAS,OAAO;QAClB;IACF;IAEA,mBAAmB;IACnB,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAkB,OAAO;YAC9B,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAW,OAAO;YACvB;gBAAS,OAAO;QAClB;IACF;IAEA,uDAAuD;IACvD,MAAM,gBAAgB,CAAC,MAAe,QAAQ,CAAC;QAC7C,yCAAyC;QACzC,MAAM,gBAAgB,eAAe,MACnC,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACvD,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACxD,KAAK,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAEhE,MAAM,gBAAgB,iBAAiB,SAAS,KAAK,MAAM,KAAK;QAChE,MAAM,oBAAoB,qBAAqB,SAAS,KAAK,UAAU,KAAK;QAE5E,MAAM,cAAc,iBAAiB,iBAAiB;QAEtD,6CAA6C;QAC7C,IAAI,mBAA8B,EAAE;QACpC,IAAI,KAAK,QAAQ,EAAE;YACjB,mBAAmB,KAAK,QAAQ,CAC7B,GAAG,CAAC,CAAA,QAAS,cAAc,OAAO,QAAQ,IAC1C,MAAM,CAAC,CAAC,QAA4B,UAAU;QACnD;QAEA,kEAAkE;QAClE,IAAI,eAAe,iBAAiB,MAAM,GAAG,GAAG;YAC9C,OAAO;gBACL,GAAG,IAAI;gBACP,UAAU,iBAAiB,MAAM,GAAG,IAAI,mBAAmB;YAC7D;QACF;QAEA,OAAO;IACT;IAEA,gCAAgC;IAChC,MAAM,kBAAkB,cAAc,kIAAA,CAAA,cAAW;IAEjD,0CAA0C;IAC1C,MAAM,aAAa,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,EAAqC;QACxE,MAAM,cAAc,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG;QAC5D,MAAM,aAAa,gBAAgB,cAAc,GAAG,CAAC,KAAK,EAAE;QAE5D,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBACC,WAAW,CAAC,iCAAiC,EAC3C,UAAU,IACN,gFACA,iEACL,2DAA2D,CAAC;;kCAE7D,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,oKAAA,CAAA,kBAAe;4BACd,MAAM,wKAAA,CAAA,WAAQ;4BACd,WAAW,CAAC,QAAQ,EAAE,eAAe,KAAK,MAAM,GAAG;4BACnD,OAAO,eAAe,KAAK,MAAM;;;;;;;;;;;kCAGrC,8OAAC;wBAAG,WAAU;kCAA0B,KAAK,IAAI;;;;;;kCACjD,8OAAC;wBAAE,WAAW,CAAC,QAAQ,EAAE,UAAU,IAAI,oBAAoB,oCAAoC;kCAC5F,KAAK,KAAK;;;;;;kCAEb,8OAAC;wBAAE,WAAW,CAAC,aAAa,EAAE,UAAU,IAAI,oBAAoB,oCAAoC;kCACjG,KAAK,UAAU;;;;;;kCAElB,8OAAC;wBAAE,WAAW,CAAC,aAAa,EAAE,UAAU,IAAI,oBAAoB,oCAAoC;kCACjG,KAAK,WAAW;;;;;;oBAGlB,eAAe,CAAC,8BACf,8OAAC;wBACC,SAAS,IAAM,WAAW,KAAK,EAAE;wBACjC,WAAW,CAAC,2CAA2C,EACrD,UAAU,IACN,iDACA,0GACJ;wBACF,cAAY,aAAa,aAAa;kCAEtC,cAAA,8OAAC,oKAAA,CAAA,kBAAe;4BAAC,MAAM,aAAa,wKAAA,CAAA,UAAO,GAAG,wKAAA,CAAA,SAAM;4BAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;IAM5E;IAEA,6CAA6C;IAC7C,MAAM,iBAAiB,CAAC,MAAe,QAAQ,CAAC;QAC9C,IAAI,CAAC,MAAM,OAAO;QAElB,MAAM,cAAc,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG;QAC5D,MAAM,aAAa,gBAAgB,cAAc,GAAG,CAAC,KAAK,EAAE;QAE5D,qBACE,8OAAC,2KAAA,CAAA,WAAQ;YAAe,qBAAO,8OAAC;gBAAW,MAAM;gBAAM,OAAO;;;;;;sBAC3D,eAAe,cACd,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAA,QAAS,eAAe,OAAO,QAAQ;WAF9C,KAAK,EAAE;;;;;IAM1B;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,8OAAC;wBAAE,WAAU;kCAAU;;;;;;;;;;;;0BAMzB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oKAAA,CAAA,kBAAe;4CAAC,MAAM,wKAAA,CAAA,WAAQ;4CAAE,WAAU;;;;;;;;;;;kDAE7C,8OAAC;wCACC,MAAK;wCACL,WAAU;wCACV,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;sCAKnD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAgB,WAAU;sDAAkE;;;;;;sDAG3G,8OAAC;4CACC,IAAG;4CACH,WAAU;4CACV,OAAO;4CACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;;8DAE/C,8OAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,8OAAC;oDAAO,OAAM;8DAAS;;;;;;8DACvB,8OAAC;oDAAO,OAAM;8DAAiB;;;;;;8DAC/B,8OAAC;oDAAO,OAAM;8DAAU;;;;;;8DACxB,8OAAC;oDAAO,OAAM;8DAAU;;;;;;;;;;;;;;;;;;8CAI5B,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAoB,WAAU;sDAAkE;;;;;;sDAG/G,8OAAC;4CACC,IAAG;4CACH,WAAU;4CACV,OAAO;4CACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;;8DAEnD,8OAAC;oDAAO,OAAM;8DAAM;;;;;;gDACnB,YAAY,GAAG,CAAC,CAAA,qBACf,8OAAC;wDAAkB,OAAO;kEAAO;uDAApB;;;;;;;;;;;;;;;;;8CAKnB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAM,WAAW;4CAC1B,WAAU;4CACV,cAAW;sDAEX,cAAA,8OAAC,oKAAA,CAAA,kBAAe;gDAAC,MAAM,wKAAA,CAAA,SAAM;;;;;;;;;;;sDAE/B,8OAAC;4CACC,SAAS,IAAM,WAAW,CAAC;4CAC3B,WAAU;4CACV,cAAW;sDAEX,cAAA,8OAAC,oKAAA,CAAA,kBAAe;gDAAC,MAAM,wKAAA,CAAA,UAAO;;;;;;;;;;;sDAEhC,8OAAC;4CACC,SAAS;gDACP,aAAa;gDACb,YAAY;oDAAE,GAAG;oDAAG,GAAG;gDAAE;4CAC3B;4CACA,WAAU;4CACV,cAAW;sDAEX,cAAA,8OAAC,oKAAA,CAAA,kBAAe;gDAAC,MAAM,wKAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ5C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oKAAA,CAAA,kBAAe;wCAAC,MAAM,wKAAA,CAAA,WAAQ;wCAAE,WAAU;;;;;;kDAC3C,8OAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;0CAE5B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oKAAA,CAAA,kBAAe;wCAAC,MAAM,wKAAA,CAAA,WAAQ;wCAAE,WAAU;;;;;;kDAC3C,8OAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;0CAE5B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oKAAA,CAAA,kBAAe;wCAAC,MAAM,wKAAA,CAAA,WAAQ;wCAAE,WAAU;;;;;;kDAC3C,8OAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;0CAE5B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oKAAA,CAAA,kBAAe;wCAAC,MAAM,wKAAA,CAAA,WAAQ;wCAAE,WAAU;;;;;;kDAC3C,8OAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;;;;;;;;;;;;;0BAMhC,8OAAC;gBACC,KAAK;gBACL,WAAU;gBACV,OAAO;oBAAE,QAAQ;oBAAuB,WAAW;gBAAQ;;kCAE3D,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,SAAS,IAAM,gBAAgB,CAAA,OAAQ,CAAC;4BACxC,WAAU;;8CAEV,8OAAC,oKAAA,CAAA,kBAAe;oCAAC,MAAM,eAAe,wKAAA,CAAA,UAAO,GAAG,wKAAA,CAAA,WAAQ;oCAAE,WAAU;;;;;;gCACnE,eAAe,iBAAiB;;;;;;;;;;;;kCAIrC,8OAAC;wBACC,WAAU;wBACV,OAAO;4BACL,WAAW,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;4BAChC,iBAAiB;4BACjB,YAAY;wBACd;kCAEC,gCACC,8OAAC,2KAAA,CAAA,OAAI;4BACH,WAAW;4BACX,WAAW;4BACX,kBAAkB;4BAClB,qBAAO,8OAAC;gCAAW,MAAM;gCAAiB,OAAO;;;;;;sCAEhD,gBAAgB,QAAQ,IAAI,gBAAgB,QAAQ,CAAC,GAAG,CAAC,CAAA,QACxD,eAAe,OAAO;;;;;iDAI1B,8OAAC;4BAAI,WAAU;sCAAmD;;;;;;;;;;;;;;;;;0BAOxE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;;;;;;;;;;;;;;;;;;;AAKd", "debugId": null}}]}