module.exports = {

"[project]/src/lib/agents.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createMessage": (()=>createMessage),
    "getAgentById": (()=>getAgentById),
    "getAgentsByCategory": (()=>getAgentsByCategory),
    "getAgentsByIds": (()=>getAgentsByIds),
    "getAgentsByTag": (()=>getAgentsByTag),
    "getAllAgents": (()=>getAllAgents),
    "getAllCategories": (()=>getAllCategories),
    "getAllTags": (()=>getAllTags),
    "getFeaturedAgents": (()=>getFeaturedAgents),
    "getNewAgents": (()=>getNewAgents),
    "getRelatedAgents": (()=>getRelatedAgents),
    "getSessionById": (()=>getSessionById),
    "getSessionsForAgent": (()=>getSessionsForAgent),
    "searchAgents": (()=>searchAgents)
});
// Mock data for agents
const agents = [
    {
        id: '1',
        name: 'Data Analyst',
        description: 'Analyze data sets and generate insights with natural language queries.',
        longDescription: 'The Data Analyst agent helps you explore and understand your data through natural language. Ask questions about your data, request visualizations, or get statistical summaries without writing complex queries or code. This agent can work with various data formats including CSV, Excel, SQL databases, and more.',
        category: 'Analytics',
        capabilities: [
            'Natural language data queries',
            'Data visualization generation',
            'Statistical analysis',
            'Anomaly detection',
            'Trend identification',
            'Report generation'
        ],
        usageCount: 1245,
        isNew: false,
        isFeatured: true,
        createdAt: '2023-10-15',
        updatedAt: '2024-03-01',
        version: '2.3.0',
        creator: 'Data Science Team',
        avatarUrl: '/agents/data-analyst.svg',
        tags: [
            'data',
            'analytics',
            'visualization',
            'statistics'
        ],
        relatedAgentIds: [
            '5',
            '7',
            '8'
        ]
    },
    {
        id: '2',
        name: 'Code Assistant',
        description: 'Help with coding tasks, debugging, and code reviews across multiple languages.',
        longDescription: 'The Code Assistant agent is your programming partner. It can help write, debug, and optimize code in multiple languages including Python, JavaScript, Java, C++, and more. Ask for code examples, get help with debugging, or request code reviews. The agent can also explain complex code and suggest improvements.',
        category: 'Development',
        capabilities: [
            'Code generation',
            'Debugging assistance',
            'Code optimization',
            'Code explanation',
            'Multiple language support',
            'Best practices recommendations'
        ],
        usageCount: 3421,
        isNew: false,
        isFeatured: true,
        createdAt: '2023-08-10',
        updatedAt: '2024-02-15',
        version: '3.1.0',
        creator: 'Engineering Team',
        avatarUrl: '/agents/code-assistant.svg',
        tags: [
            'coding',
            'programming',
            'development',
            'debugging'
        ],
        relatedAgentIds: [
            '9',
            '10'
        ]
    },
    {
        id: '3',
        name: 'Research Companion',
        description: 'Find, summarize, and organize research papers and articles.',
        longDescription: 'The Research Companion agent helps you stay on top of the latest research in your field. It can find relevant papers, summarize key findings, and help you organize your research materials. Ask for papers on specific topics, get summaries of complex articles, or request literature reviews.',
        category: 'Research',
        capabilities: [
            'Research paper search',
            'Article summarization',
            'Literature review assistance',
            'Citation generation',
            'Research organization',
            'Key findings extraction'
        ],
        usageCount: 876,
        isNew: true,
        isFeatured: true,
        createdAt: '2024-01-20',
        updatedAt: '2024-03-10',
        version: '1.2.0',
        creator: 'Research & Development',
        avatarUrl: '/agents/research-companion.svg',
        tags: [
            'research',
            'papers',
            'academic',
            'literature'
        ],
        relatedAgentIds: [
            '11',
            '5'
        ]
    },
    {
        id: '4',
        name: 'Meeting Summarizer',
        description: 'Generate concise summaries and action items from meeting transcripts.',
        longDescription: 'The Meeting Summarizer agent helps you capture the important points from your meetings. Upload a transcript or recording, and the agent will identify key discussion points, decisions made, and action items assigned. It can also generate meeting minutes in various formats.',
        category: 'Productivity',
        capabilities: [
            'Meeting transcript analysis',
            'Key point extraction',
            'Action item identification',
            'Decision tracking',
            'Meeting minutes generation',
            'Follow-up reminder creation'
        ],
        usageCount: 2134,
        isNew: false,
        isFeatured: true,
        createdAt: '2023-11-05',
        updatedAt: '2024-02-28',
        version: '2.0.1',
        creator: 'Productivity Team',
        avatarUrl: '/agents/meeting-summarizer.svg',
        tags: [
            'meetings',
            'productivity',
            'transcription',
            'summaries'
        ],
        relatedAgentIds: [
            '5',
            '6',
            '12'
        ]
    },
    {
        id: '5',
        name: 'Document Analyzer',
        description: 'Extract key information from documents and generate summaries.',
        longDescription: 'The Document Analyzer agent helps you process and understand documents quickly. Upload any document (PDF, Word, etc.), and the agent will extract key information, generate summaries, and answer questions about the content. It can handle contracts, reports, articles, and more.',
        category: 'Productivity',
        capabilities: [
            'Document parsing',
            'Key information extraction',
            'Summary generation',
            'Question answering',
            'Multiple format support',
            'Entity recognition'
        ],
        usageCount: 567,
        isNew: false,
        isFeatured: false,
        createdAt: '2023-09-12',
        updatedAt: '2024-01-30',
        version: '1.5.2',
        creator: 'Content Team',
        avatarUrl: '/agents/document-analyzer.svg',
        tags: [
            'documents',
            'analysis',
            'extraction',
            'summaries'
        ],
        relatedAgentIds: [
            '1',
            '3',
            '4'
        ]
    },
    {
        id: '6',
        name: 'Presentation Creator',
        description: 'Generate professional presentations from outlines or topics.',
        longDescription: 'The Presentation Creator agent helps you build compelling presentations quickly. Provide a topic or outline, and the agent will generate slide content, suggest visuals, and help you structure your presentation for maximum impact. It can create presentations for various purposes including business, education, and sales.',
        category: 'Productivity',
        capabilities: [
            'Slide content generation',
            'Presentation structure suggestions',
            'Visual element recommendations',
            'Talking points creation',
            'Multiple template support',
            'Export to PowerPoint/Google Slides'
        ],
        usageCount: 321,
        isNew: true,
        isFeatured: false,
        createdAt: '2024-02-01',
        updatedAt: '2024-03-15',
        version: '1.0.0',
        creator: 'Marketing Team',
        avatarUrl: '/agents/presentation-creator.svg',
        tags: [
            'presentations',
            'slides',
            'design',
            'content'
        ],
        relatedAgentIds: [
            '4',
            '13'
        ]
    },
    {
        id: '7',
        name: 'Data Visualization Expert',
        description: 'Create beautiful and informative data visualizations from your datasets.',
        longDescription: 'The Data Visualization Expert agent helps you transform raw data into compelling visual stories. Upload your data and describe what you want to highlight, and the agent will generate appropriate charts, graphs, and interactive visualizations that make your data insights clear and impactful.',
        category: 'Analytics',
        capabilities: [
            'Chart and graph generation',
            'Interactive visualization creation',
            'Color palette optimization',
            'Data storytelling assistance',
            'Multiple export formats',
            'Accessibility considerations'
        ],
        usageCount: 892,
        isNew: false,
        isFeatured: false,
        createdAt: '2023-11-18',
        updatedAt: '2024-02-10',
        version: '1.8.0',
        creator: 'Data Science Team',
        avatarUrl: '/agents/data-analyst.svg',
        tags: [
            'visualization',
            'charts',
            'graphs',
            'data'
        ],
        relatedAgentIds: [
            '1',
            '8'
        ]
    },
    {
        id: '8',
        name: 'Predictive Analytics Agent',
        description: 'Forecast trends and make predictions based on historical data.',
        longDescription: 'The Predictive Analytics Agent uses machine learning algorithms to analyze your historical data and generate forecasts and predictions. It can identify patterns, trends, and anomalies to help you make data-driven decisions about future outcomes.',
        category: 'Analytics',
        capabilities: [
            'Time series forecasting',
            'Trend analysis',
            'Anomaly detection',
            'Predictive modeling',
            'Scenario planning',
            'Confidence interval calculation'
        ],
        usageCount: 754,
        isNew: true,
        isFeatured: false,
        createdAt: '2024-01-05',
        updatedAt: '2024-03-20',
        version: '1.2.1',
        creator: 'Data Science Team',
        avatarUrl: '/agents/data-analyst.svg',
        tags: [
            'predictions',
            'forecasting',
            'analytics',
            'trends'
        ],
        relatedAgentIds: [
            '1',
            '7'
        ]
    },
    {
        id: '9',
        name: 'API Documentation Generator',
        description: 'Automatically generate comprehensive API documentation from your code.',
        longDescription: 'The API Documentation Generator analyzes your API code and generates clear, comprehensive documentation. It can create reference docs, guides, examples, and interactive API explorers to help developers understand and use your APIs effectively.',
        category: 'Development',
        capabilities: [
            'API reference generation',
            'Code example creation',
            'Interactive API explorer',
            'Multiple format support',
            'Versioning assistance',
            'Consistency checking'
        ],
        usageCount: 623,
        isNew: false,
        isFeatured: false,
        createdAt: '2023-10-08',
        updatedAt: '2024-02-12',
        version: '2.1.0',
        creator: 'Engineering Team',
        avatarUrl: '/agents/code-assistant.svg',
        tags: [
            'documentation',
            'API',
            'development',
            'reference'
        ],
        relatedAgentIds: [
            '2',
            '10'
        ]
    },
    {
        id: '10',
        name: 'Code Reviewer',
        description: 'Get detailed code reviews with suggestions for improvements and best practices.',
        longDescription: 'The Code Reviewer agent analyzes your code for bugs, security issues, performance problems, and adherence to best practices. It provides detailed feedback with specific suggestions for improvements and explanations of potential issues.',
        category: 'Development',
        capabilities: [
            'Bug detection',
            'Security vulnerability scanning',
            'Performance optimization',
            'Best practice enforcement',
            'Code style consistency',
            'Refactoring suggestions'
        ],
        usageCount: 1876,
        isNew: false,
        isFeatured: false,
        createdAt: '2023-09-20',
        updatedAt: '2024-03-05',
        version: '2.4.0',
        creator: 'Engineering Team',
        avatarUrl: '/agents/code-assistant.svg',
        tags: [
            'code review',
            'quality',
            'security',
            'best practices'
        ],
        relatedAgentIds: [
            '2',
            '9'
        ]
    },
    {
        id: '11',
        name: 'Literature Review Assistant',
        description: 'Compile comprehensive literature reviews on any research topic.',
        longDescription: 'The Literature Review Assistant helps researchers compile thorough literature reviews by finding relevant papers, organizing them by themes, identifying key findings, and highlighting research gaps. It can save weeks of manual research work.',
        category: 'Research',
        capabilities: [
            'Research paper discovery',
            'Thematic organization',
            'Gap analysis',
            'Citation management',
            'Summary generation',
            'Trend identification'
        ],
        usageCount: 542,
        isNew: true,
        isFeatured: false,
        createdAt: '2024-02-15',
        updatedAt: '2024-03-18',
        version: '1.0.2',
        creator: 'Research & Development',
        avatarUrl: '/agents/research-companion.svg',
        tags: [
            'literature review',
            'research',
            'academic',
            'papers'
        ],
        relatedAgentIds: [
            '3'
        ]
    },
    {
        id: '12',
        name: 'Project Manager Assistant',
        description: 'Track projects, manage tasks, and coordinate team activities efficiently.',
        longDescription: 'The Project Manager Assistant helps you plan, track, and manage projects from start to finish. It can create project timelines, assign and track tasks, identify risks, generate status reports, and facilitate team communication to keep your projects on track.',
        category: 'Productivity',
        capabilities: [
            'Project planning',
            'Task management',
            'Timeline creation',
            'Risk assessment',
            'Status reporting',
            'Resource allocation'
        ],
        usageCount: 1432,
        isNew: false,
        isFeatured: false,
        createdAt: '2023-08-25',
        updatedAt: '2024-02-20',
        version: '2.2.0',
        creator: 'Productivity Team',
        avatarUrl: '/agents/meeting-summarizer.svg',
        tags: [
            'project management',
            'tasks',
            'planning',
            'coordination'
        ],
        relatedAgentIds: [
            '4',
            '13'
        ]
    },
    {
        id: '13',
        name: 'Content Creator',
        description: 'Generate high-quality content for blogs, social media, and marketing materials.',
        longDescription: 'The Content Creator agent helps you produce engaging content for various platforms. Provide a topic and target audience, and it will generate blog posts, social media updates, marketing copy, and more, tailored to your brand voice and content strategy.',
        category: 'Marketing',
        capabilities: [
            'Blog post generation',
            'Social media content creation',
            'Marketing copy writing',
            'SEO optimization',
            'Brand voice consistency',
            'Content strategy alignment'
        ],
        usageCount: 2187,
        isNew: false,
        isFeatured: false,
        createdAt: '2023-07-12',
        updatedAt: '2024-03-08',
        version: '3.0.1',
        creator: 'Marketing Team',
        avatarUrl: '/agents/presentation-creator.svg',
        tags: [
            'content',
            'writing',
            'marketing',
            'social media'
        ],
        relatedAgentIds: [
            '6',
            '12'
        ]
    }
];
// Mock data for sessions
const sessions = [
    {
        id: 'session1',
        agentId: '2',
        title: 'JavaScript Debugging Help',
        messages: [
            {
                id: 'msg1',
                content: 'Hello! How can I help you with coding today?',
                role: 'assistant',
                timestamp: Date.now() - 3600000
            },
            {
                id: 'msg2',
                content: 'I have a bug in my JavaScript code. The event listener is not working.',
                role: 'user',
                timestamp: Date.now() - 3500000
            },
            {
                id: 'msg3',
                content: 'Let\'s take a look. Can you share the code that\'s not working?',
                role: 'assistant',
                timestamp: Date.now() - 3400000
            }
        ],
        createdAt: Date.now() - 3600000,
        updatedAt: Date.now() - 3400000,
        isSaved: true
    },
    {
        id: 'session2',
        agentId: '1',
        title: 'Sales Data Analysis',
        messages: [
            {
                id: 'msg1',
                content: 'Welcome to Data Analyst. What data would you like to analyze today?',
                role: 'assistant',
                timestamp: Date.now() - 86400000
            },
            {
                id: 'msg2',
                content: 'I need to analyze our Q1 sales data to find trends.',
                role: 'user',
                timestamp: Date.now() - 86300000
            },
            {
                id: 'msg3',
                content: 'I can help with that. Do you have the sales data file you can upload?',
                role: 'assistant',
                timestamp: Date.now() - 86200000
            }
        ],
        createdAt: Date.now() - 86400000,
        updatedAt: Date.now() - 86200000,
        isSaved: true
    }
];
function getAllAgents() {
    return agents;
}
function getAgentById(id) {
    return agents.find((agent)=>agent.id === id);
}
function getFeaturedAgents() {
    return agents.filter((agent)=>agent.isFeatured);
}
function getNewAgents() {
    return agents.filter((agent)=>agent.isNew);
}
function getAgentsByCategory(category) {
    return agents.filter((agent)=>agent.category === category);
}
function getAgentsByTag(tag) {
    return agents.filter((agent)=>agent.tags?.includes(tag));
}
function getAllCategories() {
    const categories = new Set();
    agents.forEach((agent)=>categories.add(agent.category));
    return Array.from(categories).sort();
}
function getAllTags() {
    const tags = new Set();
    agents.forEach((agent)=>{
        agent.tags?.forEach((tag)=>tags.add(tag));
    });
    return Array.from(tags).sort();
}
function getRelatedAgents(agentId) {
    const agent = getAgentById(agentId);
    if (!agent || !agent.relatedAgentIds || agent.relatedAgentIds.length === 0) {
        return [];
    }
    return agent.relatedAgentIds.map((id)=>getAgentById(id)).filter((agent)=>agent !== undefined);
}
function getAgentsByIds(ids) {
    return ids.map((id)=>getAgentById(id)).filter((agent)=>agent !== undefined);
}
function searchAgents(query) {
    if (!query) return agents;
    const lowercaseQuery = query.toLowerCase();
    return agents.filter((agent)=>agent.name.toLowerCase().includes(lowercaseQuery) || agent.description.toLowerCase().includes(lowercaseQuery) || agent.category.toLowerCase().includes(lowercaseQuery) || agent.tags?.some((tag)=>tag.toLowerCase().includes(lowercaseQuery)) || agent.capabilities.some((capability)=>capability.toLowerCase().includes(lowercaseQuery)));
}
function getSessionsForAgent(agentId) {
    return sessions.filter((session)=>session.agentId === agentId);
}
function getSessionById(sessionId) {
    return sessions.find((session)=>session.id === sessionId);
}
function createMessage(content, role) {
    return {
        id: `msg_${Date.now()}`,
        content,
        role,
        timestamp: Date.now()
    };
}
}}),
"[project]/src/lib/metrics.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "generateAgentMetrics": (()=>generateAgentMetrics),
    "generateTopQueries": (()=>generateTopQueries),
    "generateUsageByDay": (()=>generateUsageByDay),
    "generateUsageByTime": (()=>generateUsageByTime),
    "generateUsageMetric": (()=>generateUsageMetric),
    "generateUsageOverTime": (()=>generateUsageOverTime),
    "generateUserFeedback": (()=>generateUserFeedback),
    "getAllAgentMetrics": (()=>getAllAgentMetrics),
    "getCategoryDistribution": (()=>getCategoryDistribution),
    "getPlatformUsageOverTime": (()=>getPlatformUsageOverTime),
    "getPopularAgents": (()=>getPopularAgents),
    "getTopAgentsByMetric": (()=>getTopAgentsByMetric)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$agents$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/agents.ts [app-ssr] (ecmascript)");
'use client';
;
// Generate random number between min and max
const randomNumber = (min, max)=>{
    return Math.floor(Math.random() * (max - min + 1)) + min;
};
// Generate random decimal between min and max with specified precision
const randomDecimal = (min, max, precision = 2)=>{
    const value = Math.random() * (max - min) + min;
    return Number(value.toFixed(precision));
};
const generateUsageMetric = (agentId, popularity)=>{
    // Scale usage based on popularity (0-100)
    const baseUses = popularity * 100;
    const variability = baseUses * 0.2; // 20% variability
    return {
        agentId,
        totalUses: randomNumber(baseUses - variability, baseUses + variability),
        averageRating: randomDecimal(3.5, 4.9, 1),
        completionRate: randomDecimal(0.75, 0.98, 2),
        averageSessionDuration: randomNumber(60, 600),
        popularityScore: popularity
    };
};
const generateUsageOverTime = (baseUses, days)=>{
    const data = [];
    const today = new Date();
    for(let i = days - 1; i >= 0; i--){
        const date = new Date(today);
        date.setDate(date.getDate() - i);
        // Create some patterns in the data
        let modifier = 1;
        // Weekend dip
        const dayOfWeek = date.getDay();
        if (dayOfWeek === 0 || dayOfWeek === 6) {
            modifier = 0.7; // Less usage on weekends
        }
        // Gradual growth trend
        const trendFactor = 1 + i / days * 0.5;
        // Random daily fluctuation
        const fluctuation = randomDecimal(0.8, 1.2);
        const dailyUses = Math.round(baseUses / days * modifier * fluctuation * trendFactor);
        data.push({
            date: date.toISOString().split('T')[0],
            count: dailyUses
        });
    }
    return data;
};
const generateUserFeedback = ()=>{
    // Most agents should have good ratings
    const baseDistribution = [
        {
            rating: 1,
            weight: 5
        },
        {
            rating: 2,
            weight: 10
        },
        {
            rating: 3,
            weight: 20
        },
        {
            rating: 4,
            weight: 35
        },
        {
            rating: 5,
            weight: 30
        }
    ];
    const totalWeight = baseDistribution.reduce((sum, item)=>sum + item.weight, 0);
    const totalRatings = randomNumber(50, 500);
    return baseDistribution.map((item)=>{
        const percentage = item.weight / totalWeight;
        return {
            rating: item.rating,
            count: Math.round(totalRatings * percentage)
        };
    });
};
const generateUsageByTime = ()=>{
    const data = [];
    for(let hour = 0; hour < 24; hour++){
        // Create a realistic distribution with peak during work hours
        let baseFactor = 1;
        // Early morning (low usage)
        if (hour >= 0 && hour < 6) {
            baseFactor = 0.2;
        } else if (hour >= 6 && hour < 9) {
            baseFactor = 0.5 + (hour - 6) * 0.25;
        } else if (hour >= 9 && hour < 17) {
            baseFactor = 1.0;
        } else if (hour >= 17 && hour < 22) {
            baseFactor = 1.0 - (hour - 17) * 0.15;
        } else {
            baseFactor = 0.3;
        }
        // Add some randomness
        const factor = baseFactor * randomDecimal(0.8, 1.2);
        data.push({
            hour,
            count: Math.round(100 * factor)
        });
    }
    return data;
};
const generateUsageByDay = ()=>{
    const days = [
        'Monday',
        'Tuesday',
        'Wednesday',
        'Thursday',
        'Friday',
        'Saturday',
        'Sunday'
    ];
    return days.map((day)=>{
        // Weekdays have higher usage than weekends
        const isWeekend = day === 'Saturday' || day === 'Sunday';
        const baseFactor = isWeekend ? 0.6 : 1.0;
        // Wednesday and Thursday slightly higher
        const dayFactor = day === 'Wednesday' || day === 'Thursday' ? 1.1 : 1.0;
        // Add some randomness
        const factor = baseFactor * dayFactor * randomDecimal(0.9, 1.1);
        return {
            day,
            count: Math.round(100 * factor)
        };
    });
};
const generateTopQueries = (agentId)=>{
    const dataQueries = [
        'How to analyze sales data',
        'Generate monthly report',
        'Forecast Q3 revenue',
        'Compare year-over-year growth',
        'Find outliers in dataset',
        'Visualize customer demographics',
        'Calculate profit margins'
    ];
    const codeQueries = [
        'Debug React useEffect',
        'Optimize SQL query',
        'Convert JSON to CSV',
        'Fix memory leak',
        'Write unit test for API',
        'Create Docker container',
        'Implement authentication'
    ];
    const researchQueries = [
        'Summarize latest AI papers',
        'Find studies on climate change',
        'Compare research methodologies',
        'Generate literature review',
        'Extract key findings',
        'Organize research notes',
        'Find citation for paper'
    ];
    const meetingQueries = [
        'Summarize team meeting',
        'Extract action items',
        'Generate meeting minutes',
        'Identify key decisions',
        'Create follow-up tasks',
        'Analyze meeting sentiment',
        'Schedule follow-up meeting'
    ];
    const documentQueries = [
        'Extract data from PDF',
        'Summarize legal document',
        'Find key contract terms',
        'Compare document versions',
        'Generate document outline',
        'Extract contact information',
        'Identify document type'
    ];
    const presentationQueries = [
        'Create sales pitch deck',
        'Design executive summary',
        'Generate presentation outline',
        'Create data visualizations',
        'Improve slide design',
        'Add speaker notes',
        'Create product demo slides'
    ];
    // Select appropriate queries based on agent ID
    let queries = [];
    switch(agentId){
        case '1':
            queries = dataQueries;
            break;
        case '2':
            queries = codeQueries;
            break;
        case '3':
            queries = researchQueries;
            break;
        case '4':
            queries = meetingQueries;
            break;
        case '5':
            queries = documentQueries;
            break;
        case '6':
            queries = presentationQueries;
            break;
        default:
            // Mix of all queries for other agents
            queries = [
                ...dataQueries.slice(0, 2),
                ...codeQueries.slice(0, 2),
                ...researchQueries.slice(0, 2),
                ...meetingQueries.slice(0, 1)
            ];
    }
    // Randomize counts
    return queries.map((query)=>({
            query,
            count: randomNumber(10, 100)
        })).sort((a, b)=>b.count - a.count) // Sort by count descending
    .slice(0, 5); // Take top 5
};
const generateAgentMetrics = (agentId)=>{
    const agent = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$agents$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getAllAgents"])().find((a)=>a.id === agentId);
    if (!agent) {
        throw new Error(`Agent with ID ${agentId} not found`);
    }
    // Base popularity on usage count
    const popularity = Math.min(100, agent.usageCount / 50);
    const usageMetrics = generateUsageMetric(agentId, popularity);
    return {
        id: agent.id,
        name: agent.name,
        usageMetrics,
        usageOverTime: generateUsageOverTime(usageMetrics.totalUses, 30),
        userFeedback: generateUserFeedback(),
        usageByTime: generateUsageByTime(),
        usageByDay: generateUsageByDay(),
        topQueries: generateTopQueries(agentId)
    };
};
const getAllAgentMetrics = ()=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$agents$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getAllAgents"])().map((agent)=>generateAgentMetrics(agent.id));
};
const getTopAgentsByMetric = (metric, limit = 5)=>{
    const allMetrics = getAllAgentMetrics();
    return allMetrics.map((agentMetric)=>({
            agentId: agentMetric.id,
            metric,
            value: agentMetric.usageMetrics[metric]
        })).sort((a, b)=>b.value - a.value).slice(0, limit);
};
const getCategoryDistribution = ()=>{
    const agents = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$agents$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getAllAgents"])();
    const categories = {};
    // Count agents by category
    agents.forEach((agent)=>{
        if (categories[agent.category]) {
            categories[agent.category]++;
        } else {
            categories[agent.category] = 1;
        }
    });
    // Calculate total
    const total = Object.values(categories).reduce((sum, count)=>sum + count, 0);
    // Convert to array with percentages
    return Object.entries(categories).map(([category, count])=>({
            category,
            count,
            percentage: count / total * 100
        })).sort((a, b)=>b.count - a.count); // Sort by count descending
};
const getPlatformUsageOverTime = (days)=>{
    const allMetrics = getAllAgentMetrics();
    const totalUses = allMetrics.reduce((sum, agent)=>sum + agent.usageMetrics.totalUses, 0);
    return generateUsageOverTime(totalUses, days);
};
const getPopularAgents = (limit = 5)=>{
    const allMetrics = getAllAgentMetrics();
    return allMetrics.sort((a, b)=>b.usageMetrics.popularityScore - a.usageMetrics.popularityScore).slice(0, limit);
};
}}),
"[project]/src/app/popular/components/UsageChart.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>UsageChart)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
'use client';
;
function UsageChart({ data, title, height = 200 }) {
    // Find the maximum value for scaling
    const maxValue = Math.max(...data.map((item)=>item.count));
    // Calculate the width of each bar based on the number of data points
    const barWidth = 100 / data.length;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                className: "mb-4 text-lg font-semibold text-gray-900 dark:text-white",
                children: title
            }, void 0, false, {
                fileName: "[project]/src/app/popular/components/UsageChart.tsx",
                lineNumber: 21,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                style: {
                    height: `${height}px`
                },
                className: "relative w-full",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "absolute bottom-0 left-0 top-0 flex flex-col justify-between text-xs text-gray-500 dark:text-gray-400",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: "100%"
                            }, void 0, false, {
                                fileName: "[project]/src/app/popular/components/UsageChart.tsx",
                                lineNumber: 26,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: "75%"
                            }, void 0, false, {
                                fileName: "[project]/src/app/popular/components/UsageChart.tsx",
                                lineNumber: 27,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: "50%"
                            }, void 0, false, {
                                fileName: "[project]/src/app/popular/components/UsageChart.tsx",
                                lineNumber: 28,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: "25%"
                            }, void 0, false, {
                                fileName: "[project]/src/app/popular/components/UsageChart.tsx",
                                lineNumber: 29,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: "0%"
                            }, void 0, false, {
                                fileName: "[project]/src/app/popular/components/UsageChart.tsx",
                                lineNumber: 30,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/popular/components/UsageChart.tsx",
                        lineNumber: 25,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "absolute bottom-0 left-6 right-0 top-0",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "absolute bottom-0 left-0 right-0 top-0 flex flex-col justify-between",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "h-px w-full bg-gray-200 dark:bg-gray-700"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/popular/components/UsageChart.tsx",
                                        lineNumber: 37,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "h-px w-full bg-gray-200 dark:bg-gray-700"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/popular/components/UsageChart.tsx",
                                        lineNumber: 38,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "h-px w-full bg-gray-200 dark:bg-gray-700"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/popular/components/UsageChart.tsx",
                                        lineNumber: 39,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "h-px w-full bg-gray-200 dark:bg-gray-700"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/popular/components/UsageChart.tsx",
                                        lineNumber: 40,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "h-px w-full bg-gray-200 dark:bg-gray-700"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/popular/components/UsageChart.tsx",
                                        lineNumber: 41,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/popular/components/UsageChart.tsx",
                                lineNumber: 36,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "absolute bottom-0 left-0 right-0 flex h-full items-end",
                                children: data.map((item, index)=>{
                                    const height = item.count / maxValue * 100;
                                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "group flex flex-col items-center",
                                        style: {
                                            width: `${barWidth}%`
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "w-4/5 rounded-t bg-blue-500 transition-all group-hover:bg-blue-600 dark:bg-blue-600 dark:group-hover:bg-blue-700",
                                                style: {
                                                    height: `${height}%`
                                                }
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/popular/components/UsageChart.tsx",
                                                lineNumber: 55,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "invisible absolute bottom-full mb-2 rounded bg-gray-800 p-2 text-xs text-white opacity-0 transition-opacity group-hover:visible group-hover:opacity-100 dark:bg-gray-700",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "font-semibold",
                                                        children: new Date(item.date).toLocaleDateString()
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/popular/components/UsageChart.tsx",
                                                        lineNumber: 62,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        children: [
                                                            item.count,
                                                            " uses"
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/popular/components/UsageChart.tsx",
                                                        lineNumber: 63,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/popular/components/UsageChart.tsx",
                                                lineNumber: 61,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, index, true, {
                                        fileName: "[project]/src/app/popular/components/UsageChart.tsx",
                                        lineNumber: 50,
                                        columnNumber: 17
                                    }, this);
                                })
                            }, void 0, false, {
                                fileName: "[project]/src/app/popular/components/UsageChart.tsx",
                                lineNumber: 45,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/popular/components/UsageChart.tsx",
                        lineNumber: 34,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/popular/components/UsageChart.tsx",
                lineNumber: 23,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mt-2 flex justify-between text-xs text-gray-500 dark:text-gray-400",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        children: new Date(data[0].date).toLocaleDateString()
                    }, void 0, false, {
                        fileName: "[project]/src/app/popular/components/UsageChart.tsx",
                        lineNumber: 74,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        children: new Date(data[Math.floor(data.length / 2)].date).toLocaleDateString()
                    }, void 0, false, {
                        fileName: "[project]/src/app/popular/components/UsageChart.tsx",
                        lineNumber: 75,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        children: new Date(data[data.length - 1].date).toLocaleDateString()
                    }, void 0, false, {
                        fileName: "[project]/src/app/popular/components/UsageChart.tsx",
                        lineNumber: 76,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/popular/components/UsageChart.tsx",
                lineNumber: 73,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/popular/components/UsageChart.tsx",
        lineNumber: 20,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/app/popular/components/RatingDistribution.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>RatingDistribution)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
'use client';
;
function RatingDistribution({ data, title }) {
    // Calculate total ratings
    const totalRatings = data.reduce((sum, item)=>sum + item.count, 0);
    // Calculate average rating
    const weightedSum = data.reduce((sum, item)=>sum + item.rating * item.count, 0);
    const averageRating = totalRatings > 0 ? (weightedSum / totalRatings).toFixed(1) : '0.0';
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mb-4 flex items-center justify-between",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        className: "text-lg font-semibold text-gray-900 dark:text-white",
                        children: title
                    }, void 0, false, {
                        fileName: "[project]/src/app/popular/components/RatingDistribution.tsx",
                        lineNumber: 22,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "mr-1 text-xl font-bold text-yellow-500",
                                children: averageRating
                            }, void 0, false, {
                                fileName: "[project]/src/app/popular/components/RatingDistribution.tsx",
                                lineNumber: 24,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-sm text-gray-500 dark:text-gray-400",
                                children: "/ 5"
                            }, void 0, false, {
                                fileName: "[project]/src/app/popular/components/RatingDistribution.tsx",
                                lineNumber: 25,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/popular/components/RatingDistribution.tsx",
                        lineNumber: 23,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/popular/components/RatingDistribution.tsx",
                lineNumber: 21,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "space-y-3",
                children: [
                    5,
                    4,
                    3,
                    2,
                    1
                ].map((rating)=>{
                    const ratingData = data.find((item)=>item.rating === rating);
                    const count = ratingData ? ratingData.count : 0;
                    const percentage = totalRatings > 0 ? count / totalRatings * 100 : 0;
                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "mr-2 w-3 text-sm font-medium text-gray-700 dark:text-gray-300",
                                children: rating
                            }, void 0, false, {
                                fileName: "[project]/src/app/popular/components/RatingDistribution.tsx",
                                lineNumber: 37,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "relative h-4 flex-1 rounded-full bg-gray-200 dark:bg-gray-700",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "absolute left-0 top-0 h-full rounded-full bg-yellow-500",
                                    style: {
                                        width: `${percentage}%`
                                    }
                                }, void 0, false, {
                                    fileName: "[project]/src/app/popular/components/RatingDistribution.tsx",
                                    lineNumber: 41,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/popular/components/RatingDistribution.tsx",
                                lineNumber: 40,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "ml-2 w-12 text-right text-xs text-gray-500 dark:text-gray-400",
                                children: [
                                    percentage.toFixed(1),
                                    "%"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/popular/components/RatingDistribution.tsx",
                                lineNumber: 46,
                                columnNumber: 15
                            }, this)
                        ]
                    }, rating, true, {
                        fileName: "[project]/src/app/popular/components/RatingDistribution.tsx",
                        lineNumber: 36,
                        columnNumber: 13
                    }, this);
                })
            }, void 0, false, {
                fileName: "[project]/src/app/popular/components/RatingDistribution.tsx",
                lineNumber: 29,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mt-4 text-center text-sm text-gray-500 dark:text-gray-400",
                children: [
                    "Based on ",
                    totalRatings,
                    " ratings"
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/popular/components/RatingDistribution.tsx",
                lineNumber: 54,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/popular/components/RatingDistribution.tsx",
        lineNumber: 20,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/app/popular/components/TopQueries.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>TopQueries)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
'use client';
;
function TopQueries({ data, title }) {
    // Find the maximum count for scaling
    const maxCount = Math.max(...data.map((item)=>item.count));
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                className: "mb-4 text-lg font-semibold text-gray-900 dark:text-white",
                children: title
            }, void 0, false, {
                fileName: "[project]/src/app/popular/components/TopQueries.tsx",
                lineNumber: 17,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "space-y-3",
                children: data.map((item, index)=>{
                    const percentage = item.count / maxCount * 100;
                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "space-y-1",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center justify-between",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-sm font-medium text-gray-700 dark:text-gray-300",
                                        children: item.query
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/popular/components/TopQueries.tsx",
                                        lineNumber: 26,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-xs text-gray-500 dark:text-gray-400",
                                        children: [
                                            item.count,
                                            " queries"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/popular/components/TopQueries.tsx",
                                        lineNumber: 29,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/popular/components/TopQueries.tsx",
                                lineNumber: 25,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "relative h-2 w-full rounded-full bg-gray-200 dark:bg-gray-700",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "absolute left-0 top-0 h-full rounded-full bg-blue-500 dark:bg-blue-600",
                                    style: {
                                        width: `${percentage}%`
                                    }
                                }, void 0, false, {
                                    fileName: "[project]/src/app/popular/components/TopQueries.tsx",
                                    lineNumber: 34,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/popular/components/TopQueries.tsx",
                                lineNumber: 33,
                                columnNumber: 15
                            }, this)
                        ]
                    }, index, true, {
                        fileName: "[project]/src/app/popular/components/TopQueries.tsx",
                        lineNumber: 24,
                        columnNumber: 13
                    }, this);
                })
            }, void 0, false, {
                fileName: "[project]/src/app/popular/components/TopQueries.tsx",
                lineNumber: 19,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/popular/components/TopQueries.tsx",
        lineNumber: 16,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/app/popular/components/UsageByTimeChart.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>UsageByTimeChart)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
'use client';
;
function UsageByTimeChart({ data, title }) {
    // Find the maximum value for scaling
    const maxValue = Math.max(...data.map((item)=>item.count));
    // Format hour for display
    const formatHour = (hour)=>{
        if (hour === 0) return '12 AM';
        if (hour === 12) return '12 PM';
        return hour < 12 ? `${hour} AM` : `${hour - 12} PM`;
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                className: "mb-4 text-lg font-semibold text-gray-900 dark:text-white",
                children: title
            }, void 0, false, {
                fileName: "[project]/src/app/popular/components/UsageByTimeChart.tsx",
                lineNumber: 24,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "h-48 w-full",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "relative h-full w-full",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "absolute bottom-0 left-0 right-0 top-0 flex flex-col justify-between",
                            children: [
                                0,
                                1,
                                2,
                                3,
                                4
                            ].map((_, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "h-px w-full bg-gray-200 dark:bg-gray-700"
                                }, index, false, {
                                    fileName: "[project]/src/app/popular/components/UsageByTimeChart.tsx",
                                    lineNumber: 31,
                                    columnNumber: 15
                                }, this))
                        }, void 0, false, {
                            fileName: "[project]/src/app/popular/components/UsageByTimeChart.tsx",
                            lineNumber: 29,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "absolute bottom-0 left-0 right-0 flex h-full items-end",
                            children: data.map((item, index)=>{
                                const height = item.count / maxValue * 100;
                                // Determine color based on time of day
                                let colorClass = 'bg-blue-500 group-hover:bg-blue-600 dark:bg-blue-600 dark:group-hover:bg-blue-700';
                                // Work hours (9 AM - 5 PM)
                                if (item.hour >= 9 && item.hour < 17) {
                                    colorClass = 'bg-green-500 group-hover:bg-green-600 dark:bg-green-600 dark:group-hover:bg-green-700';
                                } else if (item.hour >= 17 && item.hour < 22) {
                                    colorClass = 'bg-purple-500 group-hover:bg-purple-600 dark:bg-purple-600 dark:group-hover:bg-purple-700';
                                } else if (item.hour >= 22 || item.hour < 6) {
                                    colorClass = 'bg-indigo-500 group-hover:bg-indigo-600 dark:bg-indigo-600 dark:group-hover:bg-indigo-700';
                                }
                                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "group flex flex-1 flex-col items-center",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: `w-4/5 rounded-t transition-all ${colorClass}`,
                                            style: {
                                                height: `${height}%`
                                            }
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/popular/components/UsageByTimeChart.tsx",
                                            lineNumber: 61,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "invisible absolute bottom-full mb-2 rounded bg-gray-800 p-2 text-xs text-white opacity-0 transition-opacity group-hover:visible group-hover:opacity-100 dark:bg-gray-700",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "font-semibold",
                                                    children: formatHour(item.hour)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/popular/components/UsageByTimeChart.tsx",
                                                    lineNumber: 68,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    children: [
                                                        item.count,
                                                        " uses"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/popular/components/UsageByTimeChart.tsx",
                                                    lineNumber: 69,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/popular/components/UsageByTimeChart.tsx",
                                            lineNumber: 67,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, index, true, {
                                    fileName: "[project]/src/app/popular/components/UsageByTimeChart.tsx",
                                    lineNumber: 57,
                                    columnNumber: 17
                                }, this);
                            })
                        }, void 0, false, {
                            fileName: "[project]/src/app/popular/components/UsageByTimeChart.tsx",
                            lineNumber: 36,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/popular/components/UsageByTimeChart.tsx",
                    lineNumber: 27,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/popular/components/UsageByTimeChart.tsx",
                lineNumber: 26,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mt-2 flex justify-between text-xs text-gray-500 dark:text-gray-400",
                children: [
                    0,
                    6,
                    12,
                    18,
                    23
                ].map((hour)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        children: formatHour(hour)
                    }, hour, false, {
                        fileName: "[project]/src/app/popular/components/UsageByTimeChart.tsx",
                        lineNumber: 81,
                        columnNumber: 11
                    }, this))
            }, void 0, false, {
                fileName: "[project]/src/app/popular/components/UsageByTimeChart.tsx",
                lineNumber: 79,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/popular/components/UsageByTimeChart.tsx",
        lineNumber: 23,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/app/popular/components/UsageByDayChart.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>UsageByDayChart)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
'use client';
;
function UsageByDayChart({ data, title }) {
    // Find the maximum value for scaling
    const maxValue = Math.max(...data.map((item)=>item.count));
    // Sort days in correct order
    const daysOrder = [
        'Monday',
        'Tuesday',
        'Wednesday',
        'Thursday',
        'Friday',
        'Saturday',
        'Sunday'
    ];
    const sortedData = [
        ...data
    ].sort((a, b)=>daysOrder.indexOf(a.day) - daysOrder.indexOf(b.day));
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                className: "mb-4 text-lg font-semibold text-gray-900 dark:text-white",
                children: title
            }, void 0, false, {
                fileName: "[project]/src/app/popular/components/UsageByDayChart.tsx",
                lineNumber: 23,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "h-48 w-full",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "relative h-full w-full",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "absolute bottom-0 left-0 right-0 top-0 flex flex-col justify-between",
                            children: [
                                0,
                                1,
                                2,
                                3,
                                4
                            ].map((_, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "h-px w-full bg-gray-200 dark:bg-gray-700"
                                }, index, false, {
                                    fileName: "[project]/src/app/popular/components/UsageByDayChart.tsx",
                                    lineNumber: 30,
                                    columnNumber: 15
                                }, this))
                        }, void 0, false, {
                            fileName: "[project]/src/app/popular/components/UsageByDayChart.tsx",
                            lineNumber: 28,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "absolute bottom-0 left-0 right-0 flex h-full items-end justify-between",
                            children: sortedData.map((item, index)=>{
                                const height = item.count / maxValue * 100;
                                // Weekend days get a different color
                                const isWeekend = item.day === 'Saturday' || item.day === 'Sunday';
                                const colorClass = isWeekend ? 'bg-purple-500 group-hover:bg-purple-600 dark:bg-purple-600 dark:group-hover:bg-purple-700' : 'bg-blue-500 group-hover:bg-blue-600 dark:bg-blue-600 dark:group-hover:bg-blue-700';
                                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "group flex w-10 flex-col items-center",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: `w-8 rounded-t transition-all ${colorClass}`,
                                            style: {
                                                height: `${height}%`
                                            }
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/popular/components/UsageByDayChart.tsx",
                                            lineNumber: 50,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "invisible absolute bottom-full mb-2 rounded bg-gray-800 p-2 text-xs text-white opacity-0 transition-opacity group-hover:visible group-hover:opacity-100 dark:bg-gray-700",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "font-semibold",
                                                    children: item.day
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/popular/components/UsageByDayChart.tsx",
                                                    lineNumber: 57,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    children: [
                                                        item.count,
                                                        " uses"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/popular/components/UsageByDayChart.tsx",
                                                    lineNumber: 58,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/popular/components/UsageByDayChart.tsx",
                                            lineNumber: 56,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, index, true, {
                                    fileName: "[project]/src/app/popular/components/UsageByDayChart.tsx",
                                    lineNumber: 46,
                                    columnNumber: 17
                                }, this);
                            })
                        }, void 0, false, {
                            fileName: "[project]/src/app/popular/components/UsageByDayChart.tsx",
                            lineNumber: 35,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/popular/components/UsageByDayChart.tsx",
                    lineNumber: 26,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/popular/components/UsageByDayChart.tsx",
                lineNumber: 25,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mt-2 flex justify-between text-xs text-gray-500 dark:text-gray-400",
                children: sortedData.map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        children: item.day.substring(0, 3)
                    }, index, false, {
                        fileName: "[project]/src/app/popular/components/UsageByDayChart.tsx",
                        lineNumber: 70,
                        columnNumber: 11
                    }, this))
            }, void 0, false, {
                fileName: "[project]/src/app/popular/components/UsageByDayChart.tsx",
                lineNumber: 68,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/popular/components/UsageByDayChart.tsx",
        lineNumber: 22,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/app/popular/components/MetricCard.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>MetricCard)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
'use client';
;
function MetricCard({ title, value, change, icon }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex items-center justify-between",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-sm font-medium text-gray-500 dark:text-gray-400",
                            children: title
                        }, void 0, false, {
                            fileName: "[project]/src/app/popular/components/MetricCard.tsx",
                            lineNumber: 17,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                            className: "mt-1 text-2xl font-bold text-gray-900 dark:text-white",
                            children: value
                        }, void 0, false, {
                            fileName: "[project]/src/app/popular/components/MetricCard.tsx",
                            lineNumber: 18,
                            columnNumber: 11
                        }, this),
                        change !== undefined && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: `mt-1 flex items-center text-sm ${change >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`,
                            children: [
                                change >= 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                    xmlns: "http://www.w3.org/2000/svg",
                                    width: "24",
                                    height: "24",
                                    viewBox: "0 0 24 24",
                                    fill: "none",
                                    stroke: "currentColor",
                                    strokeWidth: "2",
                                    strokeLinecap: "round",
                                    strokeLinejoin: "round",
                                    className: "mr-1 h-3 w-3",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("polyline", {
                                        points: "18 15 12 9 6 15"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/popular/components/MetricCard.tsx",
                                        lineNumber: 39,
                                        columnNumber: 19
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/popular/components/MetricCard.tsx",
                                    lineNumber: 27,
                                    columnNumber: 17
                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                    xmlns: "http://www.w3.org/2000/svg",
                                    width: "24",
                                    height: "24",
                                    viewBox: "0 0 24 24",
                                    fill: "none",
                                    stroke: "currentColor",
                                    strokeWidth: "2",
                                    strokeLinecap: "round",
                                    strokeLinejoin: "round",
                                    className: "mr-1 h-3 w-3",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("polyline", {
                                        points: "6 9 12 15 18 9"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/popular/components/MetricCard.tsx",
                                        lineNumber: 54,
                                        columnNumber: 19
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/popular/components/MetricCard.tsx",
                                    lineNumber: 42,
                                    columnNumber: 17
                                }, this),
                                Math.abs(change),
                                "% from last period"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/popular/components/MetricCard.tsx",
                            lineNumber: 21,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/popular/components/MetricCard.tsx",
                    lineNumber: 16,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "rounded-full bg-blue-100 p-3 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400",
                    children: icon
                }, void 0, false, {
                    fileName: "[project]/src/app/popular/components/MetricCard.tsx",
                    lineNumber: 62,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/app/popular/components/MetricCard.tsx",
            lineNumber: 15,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/popular/components/MetricCard.tsx",
        lineNumber: 14,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/lib/events.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "EVENTS": (()=>EVENTS),
    "addCustomEventListener": (()=>addCustomEventListener),
    "dispatchCustomEvent": (()=>dispatchCustomEvent)
});
'use client';
const EVENTS = {
    FAVORITES_UPDATED: 'favorites-updated',
    RECENTLY_VIEWED_UPDATED: 'recently-viewed-updated'
};
function dispatchCustomEvent(eventName, detail) {
    if ("TURBOPACK compile-time truthy", 1) return;
    "TURBOPACK unreachable";
    const event = undefined;
}
function addCustomEventListener(eventName, callback) {
    if ("TURBOPACK compile-time truthy", 1) return ()=>{};
    "TURBOPACK unreachable";
    const eventListener = undefined;
}
}}),
"[project]/src/lib/userPreferences.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "addToRecentlyViewed": (()=>addToRecentlyViewed),
    "getDarkMode": (()=>getDarkMode),
    "getDefaultView": (()=>getDefaultView),
    "getPageSize": (()=>getPageSize),
    "getRecentlyViewedAgentIds": (()=>getRecentlyViewedAgentIds),
    "getUserPreferences": (()=>getUserPreferences),
    "isAgentFavorited": (()=>isAgentFavorited),
    "saveUserPreferences": (()=>saveUserPreferences),
    "toggleDarkMode": (()=>toggleDarkMode),
    "toggleFavoriteAgent": (()=>toggleFavoriteAgent),
    "updateDefaultView": (()=>updateDefaultView),
    "updatePageSize": (()=>updatePageSize)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$events$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/events.ts [app-ssr] (ecmascript)");
'use client';
;
// Default user preferences
const defaultPreferences = {
    favoriteAgentIds: [],
    recentlyViewedAgentIds: [],
    darkMode: false,
    defaultView: 'grid',
    pageSize: 12
};
function getUserPreferences(userId) {
    if ("TURBOPACK compile-time truthy", 1) {
        return defaultPreferences;
    }
    "TURBOPACK unreachable";
    // If userId is provided, use it to get user-specific preferences
    const storageKey = undefined;
    const storedPreferences = undefined;
}
function saveUserPreferences(preferences, userId) {
    if ("TURBOPACK compile-time truthy", 1) {
        return;
    }
    "TURBOPACK unreachable";
    // If userId is provided, use it to save user-specific preferences
    const storageKey = undefined;
}
function toggleFavoriteAgent(agentId, userId) {
    const preferences = getUserPreferences(userId);
    const isFavorite = preferences.favoriteAgentIds.includes(agentId);
    if (isFavorite) {
        preferences.favoriteAgentIds = preferences.favoriteAgentIds.filter((id)=>id !== agentId);
    } else {
        preferences.favoriteAgentIds.push(agentId);
    }
    saveUserPreferences(preferences, userId);
    // Dispatch event to notify other components
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$events$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["dispatchCustomEvent"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$events$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EVENTS"].FAVORITES_UPDATED, {
        agentId,
        isFavorite: !isFavorite,
        userId
    });
    return !isFavorite; // Return the new favorite status
}
function isAgentFavorited(agentId, userId) {
    const preferences = getUserPreferences(userId);
    return preferences.favoriteAgentIds.includes(agentId);
}
function addToRecentlyViewed(agentId, userId) {
    const preferences = getUserPreferences(userId);
    // Remove if already exists to avoid duplicates
    preferences.recentlyViewedAgentIds = preferences.recentlyViewedAgentIds.filter((id)=>id !== agentId);
    // Add to the beginning of the array
    preferences.recentlyViewedAgentIds.unshift(agentId);
    // Keep only the 10 most recent
    if (preferences.recentlyViewedAgentIds.length > 10) {
        preferences.recentlyViewedAgentIds = preferences.recentlyViewedAgentIds.slice(0, 10);
    }
    saveUserPreferences(preferences, userId);
    // Dispatch event to notify other components
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$events$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["dispatchCustomEvent"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$events$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EVENTS"].RECENTLY_VIEWED_UPDATED, {
        agentId,
        recentlyViewedIds: preferences.recentlyViewedAgentIds,
        userId
    });
}
function getRecentlyViewedAgentIds(userId) {
    const preferences = getUserPreferences(userId);
    return preferences.recentlyViewedAgentIds;
}
function updatePageSize(pageSize, userId) {
    const preferences = getUserPreferences(userId);
    preferences.pageSize = pageSize;
    saveUserPreferences(preferences, userId);
}
function getPageSize(userId) {
    const preferences = getUserPreferences(userId);
    return preferences.pageSize;
}
function updateDefaultView(view, userId) {
    const preferences = getUserPreferences(userId);
    preferences.defaultView = view;
    saveUserPreferences(preferences, userId);
}
function getDefaultView(userId) {
    const preferences = getUserPreferences(userId);
    return preferences.defaultView;
}
function toggleDarkMode(userId) {
    const preferences = getUserPreferences(userId);
    preferences.darkMode = !preferences.darkMode;
    saveUserPreferences(preferences, userId);
    return preferences.darkMode;
}
function getDarkMode(userId) {
    const preferences = getUserPreferences(userId);
    return preferences.darkMode;
}
}}),
"[project]/src/app/popular/components/PopularAgentCard.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>PopularAgentCard)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$userPreferences$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/userPreferences.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$events$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/events.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
;
function PopularAgentCard({ agent, metrics, rank }) {
    const [isFavorite, setIsFavorite] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // Load favorite status
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if ("TURBOPACK compile-time truthy", 1) return;
        "TURBOPACK unreachable";
        const loadFavoriteStatus = undefined;
        // Listen for favorites updates
        const removeListener = undefined;
    }, [
        agent.id
    ]);
    // Handle favorite toggle
    const handleFavoriteToggle = (e)=>{
        e.preventDefault();
        e.stopPropagation();
        const newStatus = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$userPreferences$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toggleFavoriteAgent"])(agent.id);
        setIsFavorite(newStatus);
    };
    // Handle agent click to track recently viewed
    const handleAgentClick = ()=>{
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$userPreferences$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["addToRecentlyViewed"])(agent.id);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex rounded-lg border border-gray-200 bg-white p-4 shadow-sm transition-shadow hover:shadow-md dark:border-gray-800 dark:bg-gray-900",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mr-4 flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-blue-100 text-xl font-bold text-blue-600 dark:bg-blue-900/30 dark:text-blue-400",
                children: [
                    "#",
                    rank
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/popular/components/PopularAgentCard.tsx",
                lineNumber: 59,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex flex-1 flex-col",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mb-2 flex items-start justify-between",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center",
                                children: [
                                    agent.avatarUrl && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "mr-3 h-10 w-10 flex-shrink-0 overflow-hidden rounded-full bg-gray-100 dark:bg-gray-800",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                            src: agent.avatarUrl,
                                            alt: `${agent.name} icon`,
                                            width: 40,
                                            height: 40,
                                            className: "h-full w-full object-cover"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/popular/components/PopularAgentCard.tsx",
                                            lineNumber: 68,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/popular/components/PopularAgentCard.tsx",
                                        lineNumber: 67,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                className: "font-bold text-gray-900 dark:text-white",
                                                children: agent.name
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/popular/components/PopularAgentCard.tsx",
                                                lineNumber: 78,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-sm text-gray-500 dark:text-gray-400",
                                                children: agent.category
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/popular/components/PopularAgentCard.tsx",
                                                lineNumber: 79,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/popular/components/PopularAgentCard.tsx",
                                        lineNumber: 77,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/popular/components/PopularAgentCard.tsx",
                                lineNumber: 65,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: handleFavoriteToggle,
                                className: "rounded-full p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-600 dark:hover:bg-gray-800 dark:hover:text-gray-300",
                                "aria-label": isFavorite ? 'Remove from favorites' : 'Add to favorites',
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                    xmlns: "http://www.w3.org/2000/svg",
                                    width: "24",
                                    height: "24",
                                    viewBox: "0 0 24 24",
                                    fill: isFavorite ? 'currentColor' : 'none',
                                    stroke: "currentColor",
                                    strokeWidth: "2",
                                    strokeLinecap: "round",
                                    strokeLinejoin: "round",
                                    className: `h-5 w-5 ${isFavorite ? 'text-yellow-400' : ''}`,
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                        d: "M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/popular/components/PopularAgentCard.tsx",
                                        lineNumber: 100,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/popular/components/PopularAgentCard.tsx",
                                    lineNumber: 88,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/popular/components/PopularAgentCard.tsx",
                                lineNumber: 83,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/popular/components/PopularAgentCard.tsx",
                        lineNumber: 64,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mb-3 grid grid-cols-3 gap-2 text-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-xs text-gray-500 dark:text-gray-400",
                                        children: "Uses"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/popular/components/PopularAgentCard.tsx",
                                        lineNumber: 107,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "font-semibold text-gray-900 dark:text-white",
                                        children: metrics.totalUses.toLocaleString()
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/popular/components/PopularAgentCard.tsx",
                                        lineNumber: 108,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/popular/components/PopularAgentCard.tsx",
                                lineNumber: 106,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-xs text-gray-500 dark:text-gray-400",
                                        children: "Rating"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/popular/components/PopularAgentCard.tsx",
                                        lineNumber: 111,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "font-semibold text-gray-900 dark:text-white",
                                        children: [
                                            metrics.averageRating.toFixed(1),
                                            "/5"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/popular/components/PopularAgentCard.tsx",
                                        lineNumber: 112,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/popular/components/PopularAgentCard.tsx",
                                lineNumber: 110,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-xs text-gray-500 dark:text-gray-400",
                                        children: "Completion"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/popular/components/PopularAgentCard.tsx",
                                        lineNumber: 115,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "font-semibold text-gray-900 dark:text-white",
                                        children: [
                                            (metrics.completionRate * 100).toFixed(0),
                                            "%"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/popular/components/PopularAgentCard.tsx",
                                        lineNumber: 116,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/popular/components/PopularAgentCard.tsx",
                                lineNumber: 114,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/popular/components/PopularAgentCard.tsx",
                        lineNumber: 105,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mt-auto flex items-center justify-between",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-sm text-gray-500 dark:text-gray-400",
                                children: [
                                    "Popularity Score: ",
                                    metrics.popularityScore.toFixed(0),
                                    "/100"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/popular/components/PopularAgentCard.tsx",
                                lineNumber: 121,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                href: `/agent/${agent.id}`,
                                className: "rounded-md bg-blue-600 px-3 py-1 text-sm font-medium text-white hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800",
                                onClick: handleAgentClick,
                                children: "View Agent"
                            }, void 0, false, {
                                fileName: "[project]/src/app/popular/components/PopularAgentCard.tsx",
                                lineNumber: 124,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/popular/components/PopularAgentCard.tsx",
                        lineNumber: 120,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/popular/components/PopularAgentCard.tsx",
                lineNumber: 63,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/popular/components/PopularAgentCard.tsx",
        lineNumber: 58,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/app/popular/components/CategoryDistributionChart.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>CategoryDistributionChart)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
'use client';
;
function CategoryDistributionChart({ data, title }) {
    // Generate colors for each category
    const colors = [
        'bg-blue-500 dark:bg-blue-600',
        'bg-green-500 dark:bg-green-600',
        'bg-purple-500 dark:bg-purple-600',
        'bg-yellow-500 dark:bg-yellow-600',
        'bg-red-500 dark:bg-red-600',
        'bg-indigo-500 dark:bg-indigo-600',
        'bg-pink-500 dark:bg-pink-600',
        'bg-teal-500 dark:bg-teal-600'
    ];
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                className: "mb-4 text-lg font-semibold text-gray-900 dark:text-white",
                children: title
            }, void 0, false, {
                fileName: "[project]/src/app/popular/components/CategoryDistributionChart.tsx",
                lineNumber: 26,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mb-4 h-48 w-full",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "relative h-full w-full",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 transform",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "h-32 w-32 rounded-full border-8 border-gray-100 dark:border-gray-800"
                            }, void 0, false, {
                                fileName: "[project]/src/app/popular/components/CategoryDistributionChart.tsx",
                                lineNumber: 31,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/popular/components/CategoryDistributionChart.tsx",
                            lineNumber: 30,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                            viewBox: "0 0 100 100",
                            className: "h-full w-full",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                                    cx: "50",
                                    cy: "50",
                                    r: "40",
                                    fill: "transparent",
                                    stroke: "#e5e7eb",
                                    strokeWidth: "20"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/popular/components/CategoryDistributionChart.tsx",
                                    lineNumber: 36,
                                    columnNumber: 13
                                }, this),
                                data.map((category, index)=>{
                                    // Calculate the segment's start and end angles
                                    const totalPercentage = data.reduce((sum, item)=>sum + item.percentage, 0);
                                    const startPercentage = data.slice(0, index).reduce((sum, item)=>sum + item.percentage, 0);
                                    const endPercentage = startPercentage + category.percentage;
                                    const startAngle = startPercentage / totalPercentage * 360;
                                    const endAngle = endPercentage / totalPercentage * 360;
                                    // Convert angles to radians
                                    const startRad = (startAngle - 90) * (Math.PI / 180);
                                    const endRad = (endAngle - 90) * (Math.PI / 180);
                                    // Calculate the SVG arc path
                                    const x1 = 50 + 40 * Math.cos(startRad);
                                    const y1 = 50 + 40 * Math.sin(startRad);
                                    const x2 = 50 + 40 * Math.cos(endRad);
                                    const y2 = 50 + 40 * Math.sin(endRad);
                                    // Determine if the arc should be drawn the long way around
                                    const largeArcFlag = endAngle - startAngle <= 180 ? "0" : "1";
                                    // Create the SVG path
                                    const d = `M 50 50 L ${x1} ${y1} A 40 40 0 ${largeArcFlag} 1 ${x2} ${y2} Z`;
                                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                        d: d,
                                        fill: colors[index % colors.length].split(' ')[0],
                                        className: "hover:opacity-80"
                                    }, index, false, {
                                        fileName: "[project]/src/app/popular/components/CategoryDistributionChart.tsx",
                                        lineNumber: 64,
                                        columnNumber: 17
                                    }, this);
                                })
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/popular/components/CategoryDistributionChart.tsx",
                            lineNumber: 35,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/popular/components/CategoryDistributionChart.tsx",
                    lineNumber: 29,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/popular/components/CategoryDistributionChart.tsx",
                lineNumber: 28,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "space-y-2",
                children: data.map((category, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center justify-between",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: `mr-2 h-3 w-3 rounded-full ${colors[index % colors.length].split(' ')[0]}`
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/popular/components/CategoryDistributionChart.tsx",
                                        lineNumber: 80,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-sm font-medium text-gray-700 dark:text-gray-300",
                                        children: category.category
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/popular/components/CategoryDistributionChart.tsx",
                                        lineNumber: 81,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/popular/components/CategoryDistributionChart.tsx",
                                lineNumber: 79,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-sm text-gray-500 dark:text-gray-400",
                                children: [
                                    category.percentage.toFixed(1),
                                    "% (",
                                    category.count,
                                    ")"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/popular/components/CategoryDistributionChart.tsx",
                                lineNumber: 85,
                                columnNumber: 13
                            }, this)
                        ]
                    }, index, true, {
                        fileName: "[project]/src/app/popular/components/CategoryDistributionChart.tsx",
                        lineNumber: 78,
                        columnNumber: 11
                    }, this))
            }, void 0, false, {
                fileName: "[project]/src/app/popular/components/CategoryDistributionChart.tsx",
                lineNumber: 76,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/popular/components/CategoryDistributionChart.tsx",
        lineNumber: 25,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/app/popular/page.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>PopularPage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$metrics$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/metrics.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$agents$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/agents.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$popular$2f$components$2f$UsageChart$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/popular/components/UsageChart.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$popular$2f$components$2f$RatingDistribution$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/popular/components/RatingDistribution.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$popular$2f$components$2f$TopQueries$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/popular/components/TopQueries.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$popular$2f$components$2f$UsageByTimeChart$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/popular/components/UsageByTimeChart.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$popular$2f$components$2f$UsageByDayChart$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/popular/components/UsageByDayChart.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$popular$2f$components$2f$MetricCard$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/popular/components/MetricCard.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$popular$2f$components$2f$PopularAgentCard$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/popular/components/PopularAgentCard.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$popular$2f$components$2f$CategoryDistributionChart$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/popular/components/CategoryDistributionChart.tsx [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
;
;
;
;
;
;
;
function PopularPage() {
    const [timeRange, setTimeRange] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('month');
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    // Load data
    const [popularAgents, setPopularAgents] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$metrics$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getPopularAgents"])(5));
    const [platformUsage, setPlatformUsage] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$metrics$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getPlatformUsageOverTime"])(30));
    const [categoryDistribution, setCategoryDistribution] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$metrics$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getCategoryDistribution"])());
    // Set loading state
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        setIsLoading(false);
    }, []);
    // Handle time range change
    const handleTimeRangeChange = (range)=>{
        setTimeRange(range);
        // Update platform usage data based on time range
        let days = 30;
        switch(range){
            case 'day':
                days = 1;
                break;
            case 'week':
                days = 7;
                break;
            case 'month':
                days = 30;
                break;
            case 'year':
                days = 365;
                break;
        }
        setPlatformUsage((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$metrics$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getPlatformUsageOverTime"])(days));
    };
    // Calculate total platform metrics
    const totalAgents = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$agents$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getAllAgents"])().length;
    const totalUses = popularAgents.reduce((sum, agent)=>sum + agent.usageMetrics.totalUses, 0);
    const averageRating = popularAgents.reduce((sum, agent)=>sum + agent.usageMetrics.averageRating, 0) / popularAgents.length;
    const averageCompletionRate = popularAgents.reduce((sum, agent)=>sum + agent.usageMetrics.completionRate, 0) / popularAgents.length;
    // Render loading state
    if (isLoading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "container mx-auto px-4 py-8",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                    className: "mb-6 text-2xl font-bold text-gray-900 dark:text-white md:text-3xl",
                    children: "Popular Agents"
                }, void 0, false, {
                    fileName: "[project]/src/app/popular/page.tsx",
                    lineNumber: 69,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex h-64 items-center justify-center",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-blue-600"
                    }, void 0, false, {
                        fileName: "[project]/src/app/popular/page.tsx",
                        lineNumber: 71,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/popular/page.tsx",
                    lineNumber: 70,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/app/popular/page.tsx",
            lineNumber: 68,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "container mx-auto px-4 py-8",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mb-6 flex flex-col justify-between gap-4 sm:flex-row sm:items-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                        className: "text-2xl font-bold text-gray-900 dark:text-white md:text-3xl",
                        children: "Popular Agents"
                    }, void 0, false, {
                        fileName: "[project]/src/app/popular/page.tsx",
                        lineNumber: 80,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center space-x-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "text-sm text-gray-500 dark:text-gray-400",
                                children: "Time Range:"
                            }, void 0, false, {
                                fileName: "[project]/src/app/popular/page.tsx",
                                lineNumber: 83,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "rounded-md border border-gray-300 dark:border-gray-600",
                                children: [
                                    'day',
                                    'week',
                                    'month',
                                    'year'
                                ].map((range)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        onClick: ()=>handleTimeRangeChange(range),
                                        className: `px-3 py-1.5 text-sm font-medium ${timeRange === range ? 'bg-blue-600 text-white dark:bg-blue-700' : 'bg-white text-gray-700 hover:bg-gray-50 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700'}`,
                                        children: range.charAt(0).toUpperCase() + range.slice(1)
                                    }, range, false, {
                                        fileName: "[project]/src/app/popular/page.tsx",
                                        lineNumber: 86,
                                        columnNumber: 15
                                    }, this))
                            }, void 0, false, {
                                fileName: "[project]/src/app/popular/page.tsx",
                                lineNumber: 84,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/popular/page.tsx",
                        lineNumber: 82,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/popular/page.tsx",
                lineNumber: 79,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mb-8 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$popular$2f$components$2f$MetricCard$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        title: "Total Agents",
                        value: totalAgents,
                        change: 8.5,
                        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                            xmlns: "http://www.w3.org/2000/svg",
                            width: "24",
                            height: "24",
                            viewBox: "0 0 24 24",
                            fill: "none",
                            stroke: "currentColor",
                            strokeWidth: "2",
                            strokeLinecap: "round",
                            strokeLinejoin: "round",
                            className: "h-6 w-6",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                    d: "M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/popular/page.tsx",
                                    lineNumber: 121,
                                    columnNumber: 15
                                }, void 0),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                                    cx: "9",
                                    cy: "7",
                                    r: "4"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/popular/page.tsx",
                                    lineNumber: 122,
                                    columnNumber: 15
                                }, void 0),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                    d: "M22 21v-2a4 4 0 0 0-3-3.87"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/popular/page.tsx",
                                    lineNumber: 123,
                                    columnNumber: 15
                                }, void 0),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                    d: "M16 3.13a4 4 0 0 1 0 7.75"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/popular/page.tsx",
                                    lineNumber: 124,
                                    columnNumber: 15
                                }, void 0)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/popular/page.tsx",
                            lineNumber: 109,
                            columnNumber: 13
                        }, void 0)
                    }, void 0, false, {
                        fileName: "[project]/src/app/popular/page.tsx",
                        lineNumber: 104,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$popular$2f$components$2f$MetricCard$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        title: "Total Uses",
                        value: totalUses.toLocaleString(),
                        change: 12.3,
                        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                            xmlns: "http://www.w3.org/2000/svg",
                            width: "24",
                            height: "24",
                            viewBox: "0 0 24 24",
                            fill: "none",
                            stroke: "currentColor",
                            strokeWidth: "2",
                            strokeLinecap: "round",
                            strokeLinejoin: "round",
                            className: "h-6 w-6",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                    d: "M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/popular/page.tsx",
                                    lineNumber: 146,
                                    columnNumber: 15
                                }, void 0),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                    d: "M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/popular/page.tsx",
                                    lineNumber: 147,
                                    columnNumber: 15
                                }, void 0)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/popular/page.tsx",
                            lineNumber: 134,
                            columnNumber: 13
                        }, void 0)
                    }, void 0, false, {
                        fileName: "[project]/src/app/popular/page.tsx",
                        lineNumber: 129,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$popular$2f$components$2f$MetricCard$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        title: "Average Rating",
                        value: `${averageRating.toFixed(1)}/5`,
                        change: 3.2,
                        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                            xmlns: "http://www.w3.org/2000/svg",
                            width: "24",
                            height: "24",
                            viewBox: "0 0 24 24",
                            fill: "none",
                            stroke: "currentColor",
                            strokeWidth: "2",
                            strokeLinecap: "round",
                            strokeLinejoin: "round",
                            className: "h-6 w-6",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("polygon", {
                                points: "12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"
                            }, void 0, false, {
                                fileName: "[project]/src/app/popular/page.tsx",
                                lineNumber: 169,
                                columnNumber: 15
                            }, void 0)
                        }, void 0, false, {
                            fileName: "[project]/src/app/popular/page.tsx",
                            lineNumber: 157,
                            columnNumber: 13
                        }, void 0)
                    }, void 0, false, {
                        fileName: "[project]/src/app/popular/page.tsx",
                        lineNumber: 152,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$popular$2f$components$2f$MetricCard$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        title: "Completion Rate",
                        value: `${(averageCompletionRate * 100).toFixed(0)}%`,
                        change: 1.8,
                        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                            xmlns: "http://www.w3.org/2000/svg",
                            width: "24",
                            height: "24",
                            viewBox: "0 0 24 24",
                            fill: "none",
                            stroke: "currentColor",
                            strokeWidth: "2",
                            strokeLinecap: "round",
                            strokeLinejoin: "round",
                            className: "h-6 w-6",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("polyline", {
                                points: "20 6 9 17 4 12"
                            }, void 0, false, {
                                fileName: "[project]/src/app/popular/page.tsx",
                                lineNumber: 191,
                                columnNumber: 15
                            }, void 0)
                        }, void 0, false, {
                            fileName: "[project]/src/app/popular/page.tsx",
                            lineNumber: 179,
                            columnNumber: 13
                        }, void 0)
                    }, void 0, false, {
                        fileName: "[project]/src/app/popular/page.tsx",
                        lineNumber: 174,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/popular/page.tsx",
                lineNumber: 103,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mb-8",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$popular$2f$components$2f$UsageChart$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                    data: platformUsage,
                    title: `Platform Usage (${timeRange.charAt(0).toUpperCase() + timeRange.slice(1)})`,
                    height: 300
                }, void 0, false, {
                    fileName: "[project]/src/app/popular/page.tsx",
                    lineNumber: 199,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/popular/page.tsx",
                lineNumber: 198,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                className: "mb-4 text-xl font-bold text-gray-900 dark:text-white",
                children: "Top 5 Agents"
            }, void 0, false, {
                fileName: "[project]/src/app/popular/page.tsx",
                lineNumber: 207,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mb-8 grid grid-cols-1 gap-4",
                children: popularAgents.map((agent, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$popular$2f$components$2f$PopularAgentCard$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        agent: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$agents$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getAllAgents"])().find((a)=>a.id === agent.id),
                        metrics: agent.usageMetrics,
                        rank: index + 1
                    }, agent.id, false, {
                        fileName: "[project]/src/app/popular/page.tsx",
                        lineNumber: 210,
                        columnNumber: 11
                    }, this))
            }, void 0, false, {
                fileName: "[project]/src/app/popular/page.tsx",
                lineNumber: 208,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                className: "mb-4 text-xl font-bold text-gray-900 dark:text-white",
                children: "Usage Patterns"
            }, void 0, false, {
                fileName: "[project]/src/app/popular/page.tsx",
                lineNumber: 220,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mb-8 grid grid-cols-1 gap-6 md:grid-cols-2",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$popular$2f$components$2f$UsageByTimeChart$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        data: popularAgents[0].usageByTime,
                        title: "Usage by Time of Day"
                    }, void 0, false, {
                        fileName: "[project]/src/app/popular/page.tsx",
                        lineNumber: 222,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$popular$2f$components$2f$UsageByDayChart$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        data: popularAgents[0].usageByDay,
                        title: "Usage by Day of Week"
                    }, void 0, false, {
                        fileName: "[project]/src/app/popular/page.tsx",
                        lineNumber: 227,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/popular/page.tsx",
                lineNumber: 221,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "grid grid-cols-1 gap-6 md:grid-cols-3",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$popular$2f$components$2f$CategoryDistributionChart$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        data: categoryDistribution,
                        title: "Agent Categories"
                    }, void 0, false, {
                        fileName: "[project]/src/app/popular/page.tsx",
                        lineNumber: 235,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$popular$2f$components$2f$RatingDistribution$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        data: popularAgents[0].userFeedback,
                        title: "User Ratings"
                    }, void 0, false, {
                        fileName: "[project]/src/app/popular/page.tsx",
                        lineNumber: 240,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$popular$2f$components$2f$TopQueries$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        data: popularAgents[0].topQueries,
                        title: "Top Queries"
                    }, void 0, false, {
                        fileName: "[project]/src/app/popular/page.tsx",
                        lineNumber: 245,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/popular/page.tsx",
                lineNumber: 234,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/popular/page.tsx",
        lineNumber: 78,
        columnNumber: 5
    }, this);
}
}}),

};

//# sourceMappingURL=src_60b0f0ef._.js.map