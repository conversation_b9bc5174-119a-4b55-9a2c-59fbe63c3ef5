{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/auth/layout.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Image from 'next/image';\nimport Link from 'next/link';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { faRobot } from '@fortawesome/free-solid-svg-icons';\n\nexport default function AuthLayout({\n  children,\n}: {\n  children: React.ReactNode;\n}) {\n  return (\n    <div className=\"flex min-h-screen flex-col bg-gray-50 dark:bg-gray-900\">\n      <div className=\"container mx-auto flex flex-1 items-center justify-center px-4 py-12 sm:px-6 lg:px-8\">\n        <div className=\"w-full max-w-md space-y-8\">\n          <div className=\"text-center\">\n            <Link href=\"/\" className=\"inline-block\">\n              <div className=\"flex items-center justify-center\">\n                <div className=\"mr-2 flex h-10 w-10 items-center justify-center rounded-full bg-blue-600 text-white\">\n                  <FontAwesomeIcon icon={faRobot} className=\"h-6 w-6\" />\n                </div>\n                <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">AI Agent Hub</h1>\n              </div>\n            </Link>\n            <p className=\"mt-2 text-sm text-gray-600 dark:text-gray-400\">\n              Your central platform for AI agent discovery and interaction\n            </p>\n          </div>\n          \n          <div className=\"rounded-lg border border-gray-200 bg-white p-8 shadow-md dark:border-gray-700 dark:bg-gray-800\">\n            {children}\n          </div>\n          \n          <div className=\"text-center text-sm text-gray-500 dark:text-gray-400\">\n            <p>\n              &copy; {new Date().getFullYear()} AI Agent Hub. All rights reserved.\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAIA;AACA;AACA;AANA;;;;;AAQe,SAAS,WAAW,EACjC,QAAQ,EAGT;IACC,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CACvB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oKAAA,CAAA,kBAAe;gDAAC,MAAM,wKAAA,CAAA,UAAO;gDAAE,WAAU;;;;;;;;;;;sDAE5C,8OAAC;4CAAG,WAAU;sDAAmD;;;;;;;;;;;;;;;;;0CAGrE,8OAAC;gCAAE,WAAU;0CAAgD;;;;;;;;;;;;kCAK/D,8OAAC;wBAAI,WAAU;kCACZ;;;;;;kCAGH,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;;gCAAE;gCACO,IAAI,OAAO,WAAW;gCAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO/C", "debugId": null}}]}