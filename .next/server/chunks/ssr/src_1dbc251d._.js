module.exports = {

"[project]/src/data/resources-original.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "resourceCategories": (()=>resourceCategories),
    "resources": (()=>resources)
});
const resourceCategories = [
    {
        id: 'productivity',
        name: 'Productivity Tools',
        description: 'Tools to enhance personal and team productivity, including task management, note-taking, and time tracking.',
        icon: 'bolt'
    },
    {
        id: 'project-management',
        name: 'Project Management',
        description: 'Software and platforms for planning, executing, and monitoring projects of all sizes.',
        icon: 'tasks'
    },
    {
        id: 'design',
        name: 'Design Tools',
        description: 'Resources for UI/UX design, graphic design, prototyping, and visual collaboration.',
        icon: 'palette'
    },
    {
        id: 'development',
        name: 'Development Tools',
        description: 'Tools and services for software development, coding, testing, and deployment.',
        icon: 'code'
    },
    {
        id: 'research',
        name: 'Research Tools',
        description: 'Resources for market research, user research, academic research, and data collection.',
        icon: 'magnifying-glass-chart'
    },
    {
        id: 'analytics',
        name: 'Analytics & Data',
        description: 'Tools for data analysis, visualization, business intelligence, and reporting.',
        icon: 'chart-line'
    },
    {
        id: 'communication',
        name: 'Communication',
        description: 'Platforms for team communication, client meetings, presentations, and email management.',
        icon: 'comments'
    },
    {
        id: 'collaboration',
        name: 'Collaboration',
        description: 'Tools that facilitate teamwork, knowledge sharing, and cross-functional collaboration.',
        icon: 'users-gear'
    }
];
const resources = [
    {
        id: 'notion',
        name: 'Notion',
        description: 'All-in-one workspace for notes, tasks, wikis, and databases.',
        url: 'https://www.notion.so',
        category: 'productivity',
        tags: [
            'note-taking',
            'project-management',
            'wiki'
        ],
        pricing: 'freemium',
        logoUrl: 'https://www.notion.so/images/favicon.ico'
    },
    {
        id: 'figma',
        name: 'Figma',
        description: 'Collaborative interface design tool for teams.',
        url: 'https://www.figma.com',
        category: 'design',
        tags: [
            'ui-design',
            'prototyping',
            'collaboration'
        ],
        pricing: 'freemium',
        logoUrl: 'https://static.figma.com/app/icon/1/favicon.png'
    },
    {
        id: 'vscode',
        name: 'Visual Studio Code',
        description: 'Free, open-source code editor with powerful development features.',
        url: 'https://code.visualstudio.com',
        category: 'development',
        tags: [
            'code-editor',
            'debugging',
            'extensions'
        ],
        pricing: 'free',
        logoUrl: 'https://code.visualstudio.com/favicon.ico'
    },
    {
        id: 'slack',
        name: 'Slack',
        description: 'Channel-based messaging platform for teams and workplaces.',
        url: 'https://slack.com',
        category: 'communication',
        tags: [
            'messaging',
            'team-communication',
            'integrations'
        ],
        pricing: 'freemium',
        logoUrl: 'https://a.slack-edge.com/80588/marketing/img/meta/favicon-32.png'
    },
    {
        id: 'trello',
        name: 'Trello',
        description: 'Visual tool for organizing work with boards, lists, and cards.',
        url: 'https://trello.com',
        category: 'project-management',
        tags: [
            'kanban',
            'task-management',
            'collaboration'
        ],
        pricing: 'freemium',
        logoUrl: 'https://a.trellocdn.com/prgb/dist/images/ios/apple-touch-icon-152x152-precomposed.0307bc39ec6c9ff499c8.png'
    },
    {
        id: 'google-analytics',
        name: 'Google Analytics',
        description: 'Web analytics service that tracks and reports website traffic.',
        url: 'https://analytics.google.com',
        category: 'analytics',
        tags: [
            'web-analytics',
            'reporting',
            'user-behavior'
        ],
        pricing: 'freemium',
        logoUrl: 'https://www.google.com/analytics/images/ga_icon_256.png'
    },
    {
        id: 'miro',
        name: 'Miro',
        description: 'Online collaborative whiteboard platform for teams.',
        url: 'https://miro.com',
        category: 'collaboration',
        tags: [
            'whiteboard',
            'brainstorming',
            'visual-collaboration'
        ],
        pricing: 'freemium',
        logoUrl: 'https://miro.com/static/images/favicon/apple-touch-icon.png'
    },
    {
        id: 'google-scholar',
        name: 'Google Scholar',
        description: 'Search engine for academic literature and research papers.',
        url: 'https://scholar.google.com',
        category: 'research',
        tags: [
            'academic-research',
            'citations',
            'literature-search'
        ],
        pricing: 'free',
        logoUrl: 'https://scholar.google.com/favicon.ico'
    }
];
}}),
"[project]/src/data/resources.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getCategoryById": (()=>getCategoryById),
    "getResourcesByCategory": (()=>getResourcesByCategory),
    "resources": (()=>combinedResources)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$resources$2d$original$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/data/resources-original.ts [app-ssr] (ecmascript)");
;
// New resources from the AI Presentation Series Summary PDF
const newResources = [
    // Productivity Tools
    {
        id: 'perplexity-ai',
        name: 'Perplexity AI',
        description: 'An AI-powered search engine with a chatbot interface that understands and responds to user queries using GPT-3.5.',
        url: 'https://www.perplexity.ai/',
        category: 'productivity',
        tags: [
            'search-engine',
            'ai-assistant',
            'research'
        ],
        pricing: 'freemium',
        logoUrl: 'https://www.perplexity.ai/favicon.ico'
    },
    {
        id: 'claude-ai',
        name: 'Claude AI',
        description: 'Analyzes and suggests improvements for very long content, similar to ChatGPT but with enhanced capabilities for handling lengthy documents.',
        url: 'https://claude.ai/',
        category: 'productivity',
        tags: [
            'ai-assistant',
            'content-generation',
            'document-analysis'
        ],
        pricing: 'freemium',
        logoUrl: 'https://claude.ai/favicon.ico'
    },
    {
        id: 'fathom-ai',
        name: 'Fathom AI',
        description: 'Zoom app that records, transcribes, and highlights key moments from calls, making meeting follow-up more efficient.',
        url: 'https://fathom.video/',
        category: 'productivity',
        tags: [
            'meeting-assistant',
            'transcription',
            'video-conferencing'
        ],
        pricing: 'freemium',
        logoUrl: 'https://fathom.video/favicon.ico'
    },
    {
        id: 'plaud-ai',
        name: 'Plaud.ai',
        description: 'AI tool for note-taking and transcription that helps capture and organize information from meetings and conversations.',
        url: 'https://www.plaud.ai/',
        category: 'productivity',
        tags: [
            'note-taking',
            'transcription',
            'meeting-assistant'
        ],
        pricing: 'freemium',
        logoUrl: 'https://www.plaud.ai/favicon.ico'
    },
    {
        id: 'whisper',
        name: 'Whisper',
        description: 'Converts audio to text and vice versa using AI, providing accurate transcription for various languages and accents.',
        url: 'https://openai.com/index/whisper/',
        category: 'productivity',
        tags: [
            'transcription',
            'audio-processing',
            'speech-to-text'
        ],
        pricing: 'freemium',
        logoUrl: 'https://openai.com/favicon.ico'
    },
    {
        id: 'notebooklm',
        name: 'NotebookLM',
        description: 'Tool for data management and note-taking that uses AI to help organize and retrieve information efficiently.',
        url: 'https://notebooklm.google.com',
        category: 'productivity',
        tags: [
            'note-taking',
            'knowledge-management',
            'ai-organization'
        ],
        pricing: 'free',
        logoUrl: 'https://notebooklm.google.com/favicon.ico'
    },
    {
        id: 'deepl',
        name: 'DeepL',
        description: 'Translation tool for efficient and accurate translations between multiple languages, powered by advanced AI.',
        url: 'https://www.deepl.com',
        category: 'productivity',
        tags: [
            'translation',
            'language-processing',
            'communication'
        ],
        pricing: 'freemium',
        logoUrl: 'https://www.deepl.com/favicon.ico'
    },
    // Design Tools
    {
        id: 'midjourney',
        name: 'Midjourney',
        description: 'Generates images from descriptive language, similar to DALL-E and Stable Diffusion, with a focus on artistic quality.',
        url: 'https://www.midjourney.com/',
        category: 'design',
        tags: [
            'image-generation',
            'ai-art',
            'creative-tools'
        ],
        pricing: 'paid',
        logoUrl: 'https://www.midjourney.com/favicon.ico'
    },
    {
        id: 'bing-images',
        name: 'Bing Image Creator',
        description: 'Provides tools for creating images from text descriptions, potentially offering more features than Midjourney.',
        url: 'https://www.bing.com/images/create',
        category: 'design',
        tags: [
            'image-generation',
            'ai-art',
            'creative-tools'
        ],
        pricing: 'free',
        logoUrl: 'https://www.bing.com/favicon.ico'
    },
    {
        id: 'meta-imagine',
        name: 'Meta Imagine',
        description: 'Uses models to generate images from textual prompts provided by the user, created by Meta (Facebook).',
        url: 'https://imagine.meta.com',
        category: 'design',
        tags: [
            'image-generation',
            'ai-art',
            'creative-tools'
        ],
        pricing: 'free',
        logoUrl: 'https://imagine.meta.com/favicon.ico'
    },
    {
        id: 'designer-microsoft',
        name: 'Microsoft Designer',
        description: 'Microsoft\'s answer to Canva, focusing on design solutions with AI-powered features for creating professional graphics.',
        url: 'https://designer.microsoft.com/',
        category: 'design',
        tags: [
            'graphic-design',
            'presentation',
            'marketing-materials'
        ],
        pricing: 'freemium',
        logoUrl: 'https://designer.microsoft.com/favicon.ico'
    },
    {
        id: 'runway',
        name: 'Runway',
        description: 'A creative platform for video production and editing with AI-powered tools for visual effects and content creation.',
        url: 'https://app.runwayml.com/',
        category: 'design',
        tags: [
            'video-editing',
            'visual-effects',
            'content-creation'
        ],
        pricing: 'freemium',
        logoUrl: 'https://app.runwayml.com/favicon.ico'
    },
    {
        id: 'clipdrop',
        name: 'Clipdrop',
        description: 'Developed by Stability AI, for various image and video editing tasks with AI-powered features.',
        url: 'https://clipdrop.co/',
        category: 'design',
        tags: [
            'image-editing',
            'video-editing',
            'content-creation'
        ],
        pricing: 'freemium',
        logoUrl: 'https://clipdrop.co/favicon.ico'
    },
    {
        id: 'interior-ai',
        name: 'Interior AI',
        description: 'Interior design tool for generating and visualizing room layouts using AI to create realistic interior designs.',
        url: 'https://interiorai.com/',
        category: 'design',
        tags: [
            'interior-design',
            'visualization',
            'architecture'
        ],
        pricing: 'freemium',
        logoUrl: 'https://interiorai.com/favicon.ico'
    },
    {
        id: 'meshy-ai',
        name: 'Meshy.ai',
        description: 'For 3D modeling, used in architecture and design to create and manipulate 3D objects with AI assistance.',
        url: 'https://www.meshy.ai',
        category: 'design',
        tags: [
            '3d-modeling',
            'architecture',
            'design'
        ],
        pricing: 'freemium',
        logoUrl: 'https://www.meshy.ai/favicon.ico'
    },
    {
        id: 'mnml-ai',
        name: 'MNML AI',
        description: 'Architecture design assistant that helps create minimalist architectural designs with AI guidance.',
        url: 'https://mnml.ai',
        category: 'design',
        tags: [
            'architecture',
            'design',
            'minimalism'
        ],
        pricing: 'freemium',
        logoUrl: 'https://mnml.ai/favicon.ico'
    },
    {
        id: 'ulama-tech',
        name: 'Ulama.tech',
        description: 'For architectural design, specifically for structure and planning with AI-powered tools.',
        url: 'http://ulama.tech',
        category: 'design',
        tags: [
            'architecture',
            'structural-design',
            'planning'
        ],
        pricing: 'freemium',
        logoUrl: 'http://ulama.tech/favicon.ico'
    },
    {
        id: 'weshop-ai',
        name: 'WeShop',
        description: 'Produce high-quality product images inexpensively and quickly using AI-generated visuals.',
        url: 'https://www.weshop.ai/',
        category: 'design',
        tags: [
            'product-photography',
            'e-commerce',
            'marketing'
        ],
        pricing: 'paid',
        logoUrl: 'https://www.weshop.ai/favicon.ico'
    },
    {
        id: 'botika',
        name: 'Botika',
        description: 'Helps fashion retailers save on photo costs and boost sales using AI-generated models for product visualization.',
        url: 'https://botika.io/',
        category: 'design',
        tags: [
            'fashion',
            'e-commerce',
            'product-visualization'
        ],
        pricing: 'paid',
        logoUrl: 'https://botika.io/favicon.ico'
    },
    {
        id: 'flux-ai',
        name: 'Flux AI',
        description: 'Enables creative image generation and animation with AI-powered tools for designers and artists.',
        url: 'https://flux-ai.io/',
        category: 'design',
        tags: [
            'image-generation',
            'animation',
            'creative-tools'
        ],
        pricing: 'freemium',
        logoUrl: 'https://flux-ai.io/favicon.ico'
    },
    // Development Tools
    {
        id: '10web',
        name: '10Web',
        description: 'An AI-Powered WordPress Platform for website development and management with automated features.',
        url: 'https://10web.io/',
        category: 'development',
        tags: [
            'wordpress',
            'website-builder',
            'automation'
        ],
        pricing: 'paid',
        logoUrl: 'https://10web.io/favicon.ico'
    },
    {
        id: 'framer',
        name: 'Framer',
        description: 'A tool for building interactive websites and web applications with a focus on design and user experience.',
        url: 'https://framer.com/',
        category: 'development',
        tags: [
            'website-builder',
            'prototyping',
            'design'
        ],
        pricing: 'freemium',
        logoUrl: 'https://framer.com/favicon.ico'
    },
    {
        id: 'github-copilot',
        name: 'GitHub Copilot',
        description: 'AI pair programmer that assists in code completion and suggestions within code editors, powered by OpenAI Codex.',
        url: 'https://github.com/features/copilot',
        category: 'development',
        tags: [
            'coding-assistant',
            'pair-programming',
            'code-completion'
        ],
        pricing: 'paid',
        logoUrl: 'https://github.com/favicon.ico'
    },
    {
        id: 'github-spark',
        name: 'GitHub Spark',
        description: 'AI tool for building web applications using natural language, aiming to lower the barrier to software development.',
        url: 'https://github.com/features',
        category: 'development',
        tags: [
            'web-development',
            'no-code',
            'ai-coding'
        ],
        pricing: 'paid',
        logoUrl: 'https://github.com/favicon.ico'
    },
    {
        id: 'langchain',
        name: 'LangChain',
        description: 'Builds AI-powered applications by connecting large language models with external data sources and tools.',
        url: 'https://www.langchain.com/',
        category: 'development',
        tags: [
            'llm-framework',
            'ai-development',
            'integration'
        ],
        pricing: 'free',
        logoUrl: 'https://www.langchain.com/favicon.ico'
    },
    // Communication
    {
        id: 'heygen',
        name: 'HeyGen',
        description: 'Produces studio quality videos with AI-generated avatars and voices for professional communication.',
        url: 'https://www.heygen.com',
        category: 'communication',
        tags: [
            'video-creation',
            'avatars',
            'presentation'
        ],
        pricing: 'paid',
        logoUrl: 'https://www.heygen.com/favicon.ico'
    },
    {
        id: 'beautiful-ai',
        name: 'Beautiful.ai',
        description: 'Presentation tool that simplifies the creation of professional presentations using AI to handle design elements.',
        url: 'http://beautiful.ai',
        category: 'communication',
        tags: [
            'presentation',
            'slides',
            'design'
        ],
        pricing: 'freemium',
        logoUrl: 'http://beautiful.ai/favicon.ico'
    },
    {
        id: 'gamma-app',
        name: 'Gamma.app',
        description: 'Creates engaging presentations by transforming ideas into visually appealing slides with AI assistance.',
        url: 'https://gamma.app/',
        category: 'communication',
        tags: [
            'presentation',
            'slides',
            'design'
        ],
        pricing: 'freemium',
        logoUrl: 'https://gamma.app/favicon.ico'
    },
    {
        id: 'decktopus',
        name: 'Decktopus',
        description: 'An AI-powered tool that assists in creating presentation starting points with professional templates and designs.',
        url: 'https://decktopus.com',
        category: 'communication',
        tags: [
            'presentation',
            'slides',
            'design'
        ],
        pricing: 'freemium',
        logoUrl: 'https://decktopus.com/favicon.ico'
    },
    {
        id: 'opus',
        name: 'Opus',
        description: 'Transforms long videos into short clips with a single click using generative AI for more effective communication.',
        url: 'https://www.opus.pro/',
        category: 'communication',
        tags: [
            'video-editing',
            'content-creation',
            'summarization'
        ],
        pricing: 'paid',
        logoUrl: 'https://www.opus.pro/favicon.ico'
    },
    {
        id: 'vapi',
        name: 'VAPI',
        description: 'Builds and optimizes voice agents for customer service and communication applications.',
        url: 'https://vapi.ai/',
        category: 'communication',
        tags: [
            'voice-agents',
            'customer-service',
            'automation'
        ],
        pricing: 'paid',
        logoUrl: 'https://vapi.ai/favicon.ico'
    },
    {
        id: 'sora',
        name: 'Sora',
        description: 'Turn text instructions into detailed video scenes for communication and presentation purposes.',
        url: 'https://openai.com/sora',
        category: 'communication',
        tags: [
            'video-generation',
            'content-creation',
            'presentation'
        ],
        pricing: 'paid',
        logoUrl: 'https://openai.com/favicon.ico'
    },
    // Collaboration
    {
        id: 'rancelab',
        name: 'RanceLab',
        description: 'Integrates WhatsApp with other platforms for better team collaboration and customer communication.',
        url: 'https://www.rancelab.com/',
        category: 'collaboration',
        tags: [
            'whatsapp-integration',
            'communication',
            'customer-service'
        ],
        pricing: 'paid',
        logoUrl: 'https://www.rancelab.com/favicon.ico'
    },
    {
        id: 'lawgeex',
        name: 'LawGeex',
        description: 'Legal automation platform that uses AI to review contracts and facilitate legal collaboration.',
        url: 'https://www.lawgeex.com/',
        category: 'collaboration',
        tags: [
            'legal',
            'contract-review',
            'automation'
        ],
        pricing: 'paid',
        logoUrl: 'https://www.lawgeex.com/favicon.ico'
    },
    // Analytics & Data
    {
        id: 'browse-ai',
        name: 'BrowseAI',
        description: 'Facilitates data extraction and monitoring from websites for easy data acquisition and analysis.',
        url: 'https://www.browse.ai/',
        category: 'analytics',
        tags: [
            'data-extraction',
            'web-scraping',
            'monitoring'
        ],
        pricing: 'freemium',
        logoUrl: 'https://www.browse.ai/favicon.ico'
    },
    {
        id: 'relevance-ai',
        name: 'Relevance AI',
        description: 'Platform providing AI-driven insights and analytics to enhance business decision-making.',
        url: 'https://relevanceai.com/',
        category: 'analytics',
        tags: [
            'data-analysis',
            'insights',
            'business-intelligence'
        ],
        pricing: 'freemium',
        logoUrl: 'https://relevanceai.com/favicon.ico'
    },
    // Project Management
    {
        id: 'make-com',
        name: 'Make.com',
        description: 'Automation platform for streamlining workflows and processes using AI to connect apps and automate tasks.',
        url: 'https://make.com',
        category: 'project-management',
        tags: [
            'automation',
            'workflow',
            'integration'
        ],
        pricing: 'freemium',
        logoUrl: 'https://make.com/favicon.ico'
    },
    {
        id: 'zapier-central',
        name: 'Zapier Central',
        description: 'Automating tasks and workflows using AI-powered integrations between different applications and services.',
        url: 'https://zapier.com/central',
        category: 'project-management',
        tags: [
            'automation',
            'workflow',
            'integration'
        ],
        pricing: 'freemium',
        logoUrl: 'https://zapier.com/favicon.ico'
    },
    {
        id: 'agents-ai',
        name: 'Agents.ai',
        description: 'Professional network of AI agents for business automation and task management.',
        url: 'https://agents.ai/',
        category: 'project-management',
        tags: [
            'automation',
            'ai-agents',
            'task-management'
        ],
        pricing: 'paid',
        logoUrl: 'https://agents.ai/favicon.ico'
    },
    {
        id: 'napkin-ai',
        name: 'Napkin.ai',
        description: 'Useful for generating content, proofreading, and ideation feedback for project planning and documentation.',
        url: 'http://napkin.ai',
        category: 'project-management',
        tags: [
            'content-generation',
            'ideation',
            'documentation'
        ],
        pricing: 'freemium',
        logoUrl: 'http://napkin.ai/favicon.ico'
    }
];
// Combine existing resources with new resources, avoiding duplicates
const combinedResources = [
    ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$resources$2d$original$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["resources"],
    ...newResources.filter((newResource)=>!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$resources$2d$original$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["resources"].some((existingResource)=>existingResource.id === newResource.id || existingResource.name.toLowerCase() === newResource.name.toLowerCase()))
];
;
function getResourcesByCategory(categoryId) {
    return combinedResources.filter((resource)=>resource.category === categoryId);
}
function getCategoryById(categoryId) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$resources$2d$original$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["resourceCategories"].find((category)=>category.id === categoryId);
}
}}),
"[project]/src/data/resources.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$resources$2d$original$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/data/resources-original.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$resources$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/data/resources.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/app/learning/resources/[category]/page.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>CategoryPage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$react$2d$fontawesome$2f$index$2e$es$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@fortawesome/react-fontawesome/index.es.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@fortawesome/free-solid-svg-icons/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$resources$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/data/resources.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$resources$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/data/resources.ts [app-ssr] (ecmascript) <locals>");
'use client';
;
;
;
;
;
;
;
// Map category IDs to FontAwesome icons
const categoryIcons = {
    'productivity': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["faBolt"],
    'project-management': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["faTasks"],
    'design': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["faPalette"],
    'development': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["faCode"],
    'research': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["faMagnifyingGlassChart"],
    'analytics': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["faChartLine"],
    'communication': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["faComments"],
    'collaboration': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["faUsersGear"]
};
function CategoryPage() {
    const params = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useParams"])();
    const categoryId = params.category;
    const category = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$resources$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getCategoryById"])(categoryId);
    const resources = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$resources$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getResourcesByCategory"])(categoryId);
    const [searchTerm, setSearchTerm] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('');
    const [selectedPricing, setSelectedPricing] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    // Get all unique tags from resources in this category
    const allTags = Array.from(new Set(resources.flatMap((resource)=>resource.tags || []))).sort();
    // Get all unique pricing options from resources in this category
    const allPricingOptions = Array.from(new Set(resources.map((resource)=>resource.pricing))).filter(Boolean);
    // Filter resources based on search term and pricing filter
    const filteredResources = resources.filter((resource)=>{
        const matchesSearch = resource.name.toLowerCase().includes(searchTerm.toLowerCase()) || resource.description.toLowerCase().includes(searchTerm.toLowerCase()) || resource.tags && resource.tags.some((tag)=>tag.toLowerCase().includes(searchTerm.toLowerCase()));
        const matchesPricing = selectedPricing.length === 0 || resource.pricing && selectedPricing.includes(resource.pricing);
        return matchesSearch && matchesPricing;
    });
    // Toggle pricing filter
    const togglePricingFilter = (pricing)=>{
        if (selectedPricing.includes(pricing)) {
            setSelectedPricing(selectedPricing.filter((p)=>p !== pricing));
        } else {
            setSelectedPricing([
                ...selectedPricing,
                pricing
            ]);
        }
    };
    if (!category) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex flex-col items-center justify-center py-12 text-center",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                    className: "mb-4 text-2xl font-bold",
                    children: "Category Not Found"
                }, void 0, false, {
                    fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                    lineNumber: 81,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "mb-6 text-gray-600 dark:text-gray-400",
                    children: "The resource category you're looking for doesn't exist."
                }, void 0, false, {
                    fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                    lineNumber: 82,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                    href: "/learning/resources",
                    className: "flex items-center rounded-md bg-teal-600 px-4 py-2 text-white hover:bg-teal-700",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$react$2d$fontawesome$2f$index$2e$es$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FontAwesomeIcon"], {
                            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["faArrowLeft"],
                            className: "mr-2 h-4 w-4"
                        }, void 0, false, {
                            fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                            lineNumber: 87,
                            columnNumber: 11
                        }, this),
                        "Back to Resources"
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                    lineNumber: 83,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
            lineNumber: 80,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "space-y-8",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "rounded-lg bg-gradient-to-r from-teal-600 to-emerald-700 p-8 text-white shadow-lg",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        href: "/learning/resources",
                        className: "mb-4 inline-flex items-center rounded-md bg-white bg-opacity-20 px-3 py-1 text-sm font-medium text-white backdrop-blur-sm transition hover:bg-opacity-30",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$react$2d$fontawesome$2f$index$2e$es$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FontAwesomeIcon"], {
                                icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["faArrowLeft"],
                                className: "mr-2 h-3 w-3"
                            }, void 0, false, {
                                fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                lineNumber: 101,
                                columnNumber: 11
                            }, this),
                            "Back to Resources"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                        lineNumber: 97,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "mr-4 flex h-16 w-16 items-center justify-center rounded-full bg-white bg-opacity-20 backdrop-blur-sm",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$react$2d$fontawesome$2f$index$2e$es$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FontAwesomeIcon"], {
                                    icon: categoryIcons[category.id],
                                    className: "h-8 w-8"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                    lineNumber: 107,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                lineNumber: 106,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                        className: "mb-2 text-3xl font-bold",
                                        children: category.name
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                        lineNumber: 110,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-lg",
                                        children: category.description
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                        lineNumber: 111,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                lineNumber: 109,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                        lineNumber: 105,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                lineNumber: 96,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex flex-col space-y-4 md:flex-row md:space-y-0 md:space-x-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "w-full md:w-1/4",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "rounded-lg border border-gray-200 bg-white p-4 shadow-sm dark:border-gray-800 dark:bg-gray-900",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                    className: "mb-4 font-semibold",
                                    children: "Search & Filter"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                    lineNumber: 120,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "mb-4",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                            htmlFor: "search",
                                            className: "mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300",
                                            children: "Search"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                            lineNumber: 124,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "relative",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$react$2d$fontawesome$2f$index$2e$es$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FontAwesomeIcon"], {
                                                        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["faSearch"],
                                                        className: "h-4 w-4 text-gray-400"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                        lineNumber: 129,
                                                        columnNumber: 19
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                    lineNumber: 128,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                    type: "text",
                                                    id: "search",
                                                    className: "block w-full rounded-md border-gray-300 pl-10 focus:border-teal-500 focus:ring-teal-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white sm:text-sm",
                                                    placeholder: "Search resources...",
                                                    value: searchTerm,
                                                    onChange: (e)=>setSearchTerm(e.target.value)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                    lineNumber: 131,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                            lineNumber: 127,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                    lineNumber: 123,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "mb-4",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                            className: "mb-2 flex items-center text-sm font-medium text-gray-700 dark:text-gray-300",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$react$2d$fontawesome$2f$index$2e$es$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FontAwesomeIcon"], {
                                                    icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["faFilter"],
                                                    className: "mr-2 h-3 w-3"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                    lineNumber: 145,
                                                    columnNumber: 17
                                                }, this),
                                                "Pricing"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                            lineNumber: 144,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "space-y-2",
                                            children: allPricingOptions.map((pricing)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex items-center",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                            id: `pricing-${pricing}`,
                                                            type: "checkbox",
                                                            className: "h-4 w-4 rounded border-gray-300 text-teal-600 focus:ring-teal-500 dark:border-gray-700 dark:bg-gray-800",
                                                            checked: selectedPricing.includes(pricing),
                                                            onChange: ()=>togglePricingFilter(pricing)
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                            lineNumber: 151,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                            htmlFor: `pricing-${pricing}`,
                                                            className: "ml-2 text-sm text-gray-700 dark:text-gray-300",
                                                            children: pricing.charAt(0).toUpperCase() + pricing.slice(1)
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                            lineNumber: 158,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, pricing, true, {
                                                    fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                    lineNumber: 150,
                                                    columnNumber: 19
                                                }, this))
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                            lineNumber: 148,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                    lineNumber: 143,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                            className: "mb-2 flex items-center text-sm font-medium text-gray-700 dark:text-gray-300",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$react$2d$fontawesome$2f$index$2e$es$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FontAwesomeIcon"], {
                                                    icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["faTag"],
                                                    className: "mr-2 h-3 w-3"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                    lineNumber: 172,
                                                    columnNumber: 17
                                                }, this),
                                                "Popular Tags"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                            lineNumber: 171,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex flex-wrap gap-2",
                                            children: allTags.slice(0, 10).map((tag)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    onClick: ()=>setSearchTerm(tag),
                                                    className: "inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700",
                                                    children: tag
                                                }, tag, false, {
                                                    fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                    lineNumber: 177,
                                                    columnNumber: 19
                                                }, this))
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                            lineNumber: 175,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                    lineNumber: 170,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                            lineNumber: 119,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                        lineNumber: 118,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "w-full md:w-3/4",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "rounded-lg border border-gray-200 bg-white shadow-sm dark:border-gray-800 dark:bg-gray-900",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "overflow-x-auto",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("table", {
                                    className: "min-w-full divide-y divide-gray-200 dark:divide-gray-800",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("thead", {
                                            className: "bg-gray-50 dark:bg-gray-800",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                        scope: "col",
                                                        className: "px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400",
                                                        children: "Name"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                        lineNumber: 197,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                        scope: "col",
                                                        className: "px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400",
                                                        children: "Description"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                        lineNumber: 200,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                        scope: "col",
                                                        className: "px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400",
                                                        children: "Pricing"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                        lineNumber: 203,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                        scope: "col",
                                                        className: "px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400",
                                                        children: "Link"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                        lineNumber: 206,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                lineNumber: 196,
                                                columnNumber: 19
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                            lineNumber: 195,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("tbody", {
                                            className: "divide-y divide-gray-200 bg-white dark:divide-gray-800 dark:bg-gray-900",
                                            children: filteredResources.length > 0 ? filteredResources.map((resource)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                                    className: "hover:bg-gray-50 dark:hover:bg-gray-800",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                            className: "whitespace-nowrap px-6 py-4",
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "flex items-center",
                                                                children: [
                                                                    resource.logoUrl && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                                                        src: resource.logoUrl,
                                                                        alt: `${resource.name} logo`,
                                                                        className: "mr-3 h-8 w-8 rounded-full object-contain"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                                        lineNumber: 218,
                                                                        columnNumber: 31
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: "font-medium text-gray-900 dark:text-white",
                                                                        children: resource.name
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                                        lineNumber: 224,
                                                                        columnNumber: 29
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                                lineNumber: 216,
                                                                columnNumber: 27
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                            lineNumber: 215,
                                                            columnNumber: 25
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                            className: "px-6 py-4",
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "text-sm text-gray-600 dark:text-gray-400",
                                                                children: [
                                                                    resource.description,
                                                                    resource.tags && resource.tags.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: "mt-2 flex flex-wrap gap-1",
                                                                        children: resource.tags.map((tag)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                className: "inline-flex items-center rounded-full bg-teal-100 px-2 py-0.5 text-xs font-medium text-teal-800 dark:bg-teal-900/30 dark:text-teal-300",
                                                                                children: tag
                                                                            }, tag, false, {
                                                                                fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                                                lineNumber: 233,
                                                                                columnNumber: 35
                                                                            }, this))
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                                        lineNumber: 231,
                                                                        columnNumber: 31
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                                lineNumber: 228,
                                                                columnNumber: 27
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                            lineNumber: 227,
                                                            columnNumber: 25
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                            className: "whitespace-nowrap px-6 py-4",
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "inline-flex rounded-full px-2 text-xs font-semibold leading-5",
                                                                children: [
                                                                    resource.pricing === 'free' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: "rounded-full bg-green-100 px-2 py-0.5 text-xs font-medium text-green-800 dark:bg-green-900/30 dark:text-green-300",
                                                                        children: "Free"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                                        lineNumber: 247,
                                                                        columnNumber: 31
                                                                    }, this),
                                                                    resource.pricing === 'freemium' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: "rounded-full bg-blue-100 px-2 py-0.5 text-xs font-medium text-blue-800 dark:bg-blue-900/30 dark:text-blue-300",
                                                                        children: "Freemium"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                                        lineNumber: 252,
                                                                        columnNumber: 31
                                                                    }, this),
                                                                    resource.pricing === 'paid' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: "rounded-full bg-purple-100 px-2 py-0.5 text-xs font-medium text-purple-800 dark:bg-purple-900/30 dark:text-purple-300",
                                                                        children: "Paid"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                                        lineNumber: 257,
                                                                        columnNumber: 31
                                                                    }, this),
                                                                    resource.pricing === 'enterprise' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: "rounded-full bg-gray-100 px-2 py-0.5 text-xs font-medium text-gray-800 dark:bg-gray-700 dark:text-gray-300",
                                                                        children: "Enterprise"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                                        lineNumber: 262,
                                                                        columnNumber: 31
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                                lineNumber: 245,
                                                                columnNumber: 27
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                            lineNumber: 244,
                                                            columnNumber: 25
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                            className: "whitespace-nowrap px-6 py-4 text-sm font-medium",
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                                                href: resource.url,
                                                                target: "_blank",
                                                                rel: "noopener noreferrer",
                                                                className: "text-teal-600 hover:text-teal-900 dark:text-teal-400 dark:hover:text-teal-300",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$react$2d$fontawesome$2f$index$2e$es$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FontAwesomeIcon"], {
                                                                        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["faExternalLinkAlt"],
                                                                        className: "mr-1 h-3 w-3"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                                        lineNumber: 275,
                                                                        columnNumber: 29
                                                                    }, this),
                                                                    "Visit"
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                                lineNumber: 269,
                                                                columnNumber: 27
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                            lineNumber: 268,
                                                            columnNumber: 25
                                                        }, this)
                                                    ]
                                                }, resource.id, true, {
                                                    fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                    lineNumber: 214,
                                                    columnNumber: 23
                                                }, this)) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                    colSpan: 4,
                                                    className: "px-6 py-10 text-center",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "text-gray-500 dark:text-gray-400",
                                                            children: "No resources found matching your criteria."
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                            lineNumber: 284,
                                                            columnNumber: 25
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                            onClick: ()=>{
                                                                setSearchTerm('');
                                                                setSelectedPricing([]);
                                                            },
                                                            className: "mt-2 text-sm font-medium text-teal-600 hover:text-teal-500 dark:text-teal-400",
                                                            children: "Clear filters"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                            lineNumber: 285,
                                                            columnNumber: 25
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                    lineNumber: 283,
                                                    columnNumber: 23
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                lineNumber: 282,
                                                columnNumber: 21
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                            lineNumber: 211,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                    lineNumber: 194,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                lineNumber: 193,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                            lineNumber: 192,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                        lineNumber: 191,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                lineNumber: 116,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
        lineNumber: 95,
        columnNumber: 5
    }, this);
}
}}),

};

//# sourceMappingURL=src_1dbc251d._.js.map