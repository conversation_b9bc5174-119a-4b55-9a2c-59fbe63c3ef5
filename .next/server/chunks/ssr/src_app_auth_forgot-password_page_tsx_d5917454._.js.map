{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/app/auth/forgot-password/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { faEnvelope, faSpinner, faCheckCircle } from '@fortawesome/free-solid-svg-icons';\n\nexport default function ForgotPasswordPage() {\n  const [email, setEmail] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [isSubmitted, setIsSubmitted] = useState(false);\n  const [error, setError] = useState('');\n  \n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setError('');\n    \n    if (!email) {\n      setError('Email is required');\n      return;\n    }\n    \n    setIsLoading(true);\n    \n    // Simulate API call\n    setTimeout(() => {\n      // In a real app, this would be an API call to send a password reset email\n      setIsLoading(false);\n      setIsSubmitted(true);\n    }, 1500);\n  };\n  \n  if (isSubmitted) {\n    return (\n      <div className=\"space-y-6\">\n        <div className=\"text-center\">\n          <div className=\"mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-green-100 dark:bg-green-900/30\">\n            <FontAwesomeIcon icon={faCheckCircle} className=\"h-6 w-6 text-green-600 dark:text-green-400\" />\n          </div>\n          <h2 className=\"mt-3 text-xl font-bold text-gray-900 dark:text-white\">Check your email</h2>\n          <p className=\"mt-2 text-sm text-gray-600 dark:text-gray-400\">\n            We've sent a password reset link to <span className=\"font-medium\">{email}</span>\n          </p>\n          <p className=\"mt-2 text-sm text-gray-500 dark:text-gray-400\">\n            If you don't see it, check your spam folder.\n          </p>\n        </div>\n        \n        <div className=\"flex flex-col space-y-4\">\n          <Link\n            href=\"/auth/login\"\n            className=\"inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:bg-blue-700 dark:hover:bg-blue-800\"\n          >\n            Return to sign in\n          </Link>\n          \n          <button\n            onClick={() => setIsSubmitted(false)}\n            className=\"inline-flex justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700\"\n          >\n            Try another email\n          </button>\n        </div>\n      </div>\n    );\n  }\n  \n  return (\n    <div className=\"space-y-6\">\n      <div className=\"text-center\">\n        <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">Forgot your password?</h2>\n        <p className=\"mt-2 text-sm text-gray-600 dark:text-gray-400\">\n          Enter your email address and we'll send you a link to reset your password.\n        </p>\n      </div>\n      \n      {error && (\n        <div className=\"rounded-md bg-red-50 p-4 dark:bg-red-900/30\">\n          <div className=\"flex\">\n            <div className=\"flex-shrink-0\">\n              <svg className=\"h-5 w-5 text-red-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\" aria-hidden=\"true\">\n                <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z\" clipRule=\"evenodd\" />\n              </svg>\n            </div>\n            <div className=\"ml-3\">\n              <h3 className=\"text-sm font-medium text-red-800 dark:text-red-200\">Error</h3>\n              <div className=\"mt-2 text-sm text-red-700 dark:text-red-300\">\n                <p>{error}</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n      \n      <form className=\"space-y-6\" onSubmit={handleSubmit}>\n        <div>\n          <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n            Email address\n          </label>\n          <div className=\"relative mt-1 rounded-md shadow-sm\">\n            <div className=\"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3\">\n              <FontAwesomeIcon icon={faEnvelope} className=\"h-5 w-5 text-gray-400\" />\n            </div>\n            <input\n              id=\"email\"\n              name=\"email\"\n              type=\"email\"\n              autoComplete=\"email\"\n              required\n              value={email}\n              onChange={(e) => setEmail(e.target.value)}\n              className=\"block w-full rounded-md border-gray-300 pl-10 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400\"\n              placeholder=\"<EMAIL>\"\n            />\n          </div>\n        </div>\n        \n        <div>\n          <button\n            type=\"submit\"\n            disabled={isLoading}\n            className=\"flex w-full justify-center rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-75 dark:bg-blue-700 dark:hover:bg-blue-800\"\n          >\n            {isLoading ? (\n              <>\n                <FontAwesomeIcon icon={faSpinner} className=\"mr-2 h-4 w-4 animate-spin\" />\n                Sending reset link...\n              </>\n            ) : (\n              'Send reset link'\n            )}\n          </button>\n        </div>\n      </form>\n      \n      <div className=\"text-center\">\n        <Link href=\"/auth/login\" className=\"text-sm font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300\">\n          Return to sign in\n        </Link>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,SAAS;QAET,IAAI,CAAC,OAAO;YACV,SAAS;YACT;QACF;QAEA,aAAa;QAEb,oBAAoB;QACpB,WAAW;YACT,0EAA0E;YAC1E,aAAa;YACb,eAAe;QACjB,GAAG;IACL;IAEA,IAAI,aAAa;QACf,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,oKAAA,CAAA,kBAAe;gCAAC,MAAM,wKAAA,CAAA,gBAAa;gCAAE,WAAU;;;;;;;;;;;sCAElD,8OAAC;4BAAG,WAAU;sCAAuD;;;;;;sCACrE,8OAAC;4BAAE,WAAU;;gCAAgD;8CACvB,8OAAC;oCAAK,WAAU;8CAAe;;;;;;;;;;;;sCAErE,8OAAC;4BAAE,WAAU;sCAAgD;;;;;;;;;;;;8BAK/D,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;sCAID,8OAAC;4BACC,SAAS,IAAM,eAAe;4BAC9B,WAAU;sCACX;;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAmD;;;;;;kCACjE,8OAAC;wBAAE,WAAU;kCAAgD;;;;;;;;;;;;YAK9D,uBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;gCAAuB,SAAQ;gCAAY,MAAK;gCAAe,eAAY;0CACxF,cAAA,8OAAC;oCAAK,UAAS;oCAAU,GAAE;oCAAgN,UAAS;;;;;;;;;;;;;;;;sCAGxP,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAqD;;;;;;8CACnE,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;kDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOd,8OAAC;gBAAK,WAAU;gBAAY,UAAU;;kCACpC,8OAAC;;0CACC,8OAAC;gCAAM,SAAQ;gCAAQ,WAAU;0CAA6D;;;;;;0CAG9F,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oKAAA,CAAA,kBAAe;4CAAC,MAAM,wKAAA,CAAA,aAAU;4CAAE,WAAU;;;;;;;;;;;kDAE/C,8OAAC;wCACC,IAAG;wCACH,MAAK;wCACL,MAAK;wCACL,cAAa;wCACb,QAAQ;wCACR,OAAO;wCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wCACxC,WAAU;wCACV,aAAY;;;;;;;;;;;;;;;;;;kCAKlB,8OAAC;kCACC,cAAA,8OAAC;4BACC,MAAK;4BACL,UAAU;4BACV,WAAU;sCAET,0BACC;;kDACE,8OAAC,oKAAA,CAAA,kBAAe;wCAAC,MAAM,wKAAA,CAAA,YAAS;wCAAE,WAAU;;;;;;oCAA8B;;+CAI5E;;;;;;;;;;;;;;;;;0BAMR,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oBAAC,MAAK;oBAAc,WAAU;8BAAoG;;;;;;;;;;;;;;;;;AAM/I", "debugId": null}}]}