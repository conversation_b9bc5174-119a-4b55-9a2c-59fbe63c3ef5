{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/data/mock-users.ts"], "sourcesContent": ["import { User, UserRole, UserStatus } from '@/types/user';\n\n// Helper function to generate default permissions based on role\nconst getDefaultPermissions = (role: UserRole) => {\n  switch (role) {\n    case 'admin':\n      return {\n        canCreateAgents: true,\n        canEditAgents: true,\n        canDeleteAgents: true,\n        canManageUsers: true,\n        canAccessAdmin: true,\n        canAccessAnalytics: true,\n        canManageContent: true,\n        canApproveContent: true\n      };\n    case 'manager':\n      return {\n        canCreateAgents: true,\n        canEditAgents: true,\n        canDeleteAgents: false,\n        canManageUsers: false,\n        canAccessAdmin: true,\n        canAccessAnalytics: true,\n        canManageContent: true,\n        canApproveContent: true\n      };\n    case 'user':\n      return {\n        canCreateAgents: false,\n        canEditAgents: false,\n        canDeleteAgents: false,\n        canManageUsers: false,\n        canAccessAdmin: false,\n        canAccessAnalytics: false,\n        canManageContent: false,\n        canApproveContent: false\n      };\n    case 'guest':\n      return {\n        canCreateAgents: false,\n        canEditAgents: false,\n        canDeleteAgents: false,\n        canManageUsers: false,\n        canAccessAdmin: false,\n        canAccessAnalytics: false,\n        canManageContent: false,\n        canApproveContent: false\n      };\n  }\n};\n\n// Mock users data\nexport const mockUsers: User[] = [\n  {\n    id: 'user-001',\n    email: '<EMAIL>',\n    username: 'admin',\n    role: 'admin',\n    status: 'active',\n    permissions: getDefaultPermissions('admin'),\n    profile: {\n      firstName: 'Admin',\n      lastName: 'User',\n      jobTitle: 'System Administrator',\n      department: 'IT',\n      location: 'New York',\n      bio: 'System administrator with full access to all platform features.',\n      avatarUrl: 'https://randomuser.me/api/portraits/men/1.jpg',\n      phoneNumber: '+****************'\n    },\n    settings: {\n      theme: 'system',\n      emailNotifications: true,\n      twoFactorEnabled: true,\n      sidebarCollapsed: false,\n      language: 'en-US'\n    },\n    activity: {\n      lastLogin: new Date('2023-05-15T08:30:00'),\n      lastActive: new Date('2023-05-15T16:45:00'),\n      totalLogins: 247,\n      totalSessions: 312,\n      averageSessionDuration: 3600, // 1 hour\n      favoriteAgents: ['1', '3', '5'],\n      favoriteSection: 'admin'\n    },\n    createdAt: new Date('2022-01-01T00:00:00'),\n    updatedAt: new Date('2023-04-15T14:30:00'),\n    lastPasswordChange: new Date('2023-03-01T00:00:00')\n  },\n  {\n    id: 'user-002',\n    email: '<EMAIL>',\n    username: 'manager',\n    role: 'manager',\n    status: 'active',\n    permissions: getDefaultPermissions('manager'),\n    profile: {\n      firstName: 'Sarah',\n      lastName: 'Johnson',\n      jobTitle: 'Project Manager',\n      department: 'Operations',\n      location: 'Chicago',\n      bio: 'Project manager overseeing AI implementation projects.',\n      avatarUrl: 'https://randomuser.me/api/portraits/women/2.jpg',\n      phoneNumber: '+****************'\n    },\n    settings: {\n      theme: 'light',\n      emailNotifications: true,\n      twoFactorEnabled: false,\n      sidebarCollapsed: true,\n      language: 'en-US'\n    },\n    activity: {\n      lastLogin: new Date('2023-05-14T09:15:00'),\n      lastActive: new Date('2023-05-15T17:30:00'),\n      totalLogins: 183,\n      totalSessions: 245,\n      averageSessionDuration: 2700, // 45 minutes\n      favoriteAgents: ['2', '4'],\n      favoriteSection: 'browse'\n    },\n    createdAt: new Date('2022-02-15T00:00:00'),\n    updatedAt: new Date('2023-04-10T11:20:00'),\n    lastPasswordChange: new Date('2023-02-15T00:00:00')\n  },\n  {\n    id: 'user-003',\n    email: '<EMAIL>',\n    username: 'johnsmith',\n    role: 'user',\n    status: 'active',\n    permissions: getDefaultPermissions('user'),\n    profile: {\n      firstName: 'John',\n      lastName: 'Smith',\n      jobTitle: 'Data Analyst',\n      department: 'Analytics',\n      location: 'Boston',\n      bio: 'Data analyst using AI tools to enhance data processing workflows.',\n      avatarUrl: 'https://randomuser.me/api/portraits/men/3.jpg'\n    },\n    settings: {\n      theme: 'dark',\n      emailNotifications: false,\n      twoFactorEnabled: false,\n      sidebarCollapsed: false,\n      language: 'en-US'\n    },\n    activity: {\n      lastLogin: new Date('2023-05-15T10:00:00'),\n      lastActive: new Date('2023-05-15T15:30:00'),\n      totalLogins: 97,\n      totalSessions: 142,\n      averageSessionDuration: 1800, // 30 minutes\n      favoriteAgents: ['1', '3'],\n      favoriteSection: 'learning'\n    },\n    createdAt: new Date('2022-03-10T00:00:00'),\n    updatedAt: new Date('2023-03-25T09:45:00'),\n    lastPasswordChange: new Date('2023-01-20T00:00:00')\n  },\n  {\n    id: 'user-004',\n    email: '<EMAIL>',\n    username: 'emilyd',\n    role: 'user',\n    status: 'active',\n    permissions: getDefaultPermissions('user'),\n    profile: {\n      firstName: 'Emily',\n      lastName: 'Davis',\n      jobTitle: 'Content Strategist',\n      department: 'Marketing',\n      location: 'San Francisco',\n      bio: 'Content strategist exploring AI tools for content creation and optimization.',\n      avatarUrl: 'https://randomuser.me/api/portraits/women/4.jpg',\n      phoneNumber: '+****************'\n    },\n    settings: {\n      theme: 'light',\n      emailNotifications: true,\n      twoFactorEnabled: false,\n      sidebarCollapsed: true,\n      language: 'en-US'\n    },\n    activity: {\n      lastLogin: new Date('2023-05-14T14:20:00'),\n      lastActive: new Date('2023-05-15T11:45:00'),\n      totalLogins: 76,\n      totalSessions: 104,\n      averageSessionDuration: 2100, // 35 minutes\n      favoriteAgents: ['4', '5'],\n      favoriteSection: 'learning_resources'\n    },\n    createdAt: new Date('2022-04-05T00:00:00'),\n    updatedAt: new Date('2023-03-15T16:30:00'),\n    lastPasswordChange: new Date('2023-01-10T00:00:00')\n  },\n  {\n    id: 'user-005',\n    email: '<EMAIL>',\n    username: 'michaelb',\n    role: 'user',\n    status: 'inactive',\n    permissions: getDefaultPermissions('user'),\n    profile: {\n      firstName: 'Michael',\n      lastName: 'Brown',\n      jobTitle: 'Research Scientist',\n      department: 'R&D',\n      location: 'Seattle',\n      bio: 'Research scientist working on AI applications in healthcare.',\n      avatarUrl: 'https://randomuser.me/api/portraits/men/5.jpg'\n    },\n    settings: {\n      theme: 'system',\n      emailNotifications: false,\n      twoFactorEnabled: false,\n      sidebarCollapsed: false,\n      language: 'en-US'\n    },\n    activity: {\n      lastLogin: new Date('2023-04-20T09:30:00'),\n      lastActive: new Date('2023-04-20T14:15:00'),\n      totalLogins: 42,\n      totalSessions: 67,\n      averageSessionDuration: 3300, // 55 minutes\n      favoriteAgents: ['2'],\n      favoriteSection: 'agent_detail'\n    },\n    createdAt: new Date('2022-05-12T00:00:00'),\n    updatedAt: new Date('2023-04-20T14:15:00'),\n    lastPasswordChange: new Date('2022-12-05T00:00:00')\n  },\n  {\n    id: 'user-006',\n    email: '<EMAIL>',\n    username: 'jenniferw',\n    role: 'user',\n    status: 'pending',\n    permissions: getDefaultPermissions('user'),\n    profile: {\n      firstName: 'Jennifer',\n      lastName: 'Wilson',\n      jobTitle: 'UX Designer',\n      department: 'Design',\n      location: 'Austin',\n      avatarUrl: 'https://randomuser.me/api/portraits/women/6.jpg'\n    },\n    settings: {\n      theme: 'light',\n      emailNotifications: true,\n      twoFactorEnabled: false,\n      sidebarCollapsed: false,\n      language: 'en-US'\n    },\n    activity: {\n      lastLogin: new Date('2023-05-10T11:00:00'),\n      lastActive: new Date('2023-05-10T11:30:00'),\n      totalLogins: 2,\n      totalSessions: 2,\n      averageSessionDuration: 1800, // 30 minutes\n      favoriteAgents: [],\n      favoriteSection: 'home'\n    },\n    createdAt: new Date('2023-05-10T00:00:00'),\n    updatedAt: new Date('2023-05-10T11:30:00'),\n    lastPasswordChange: new Date('2023-05-10T00:00:00')\n  },\n  {\n    id: 'user-007',\n    email: '<EMAIL>',\n    username: 'robertt',\n    role: 'user',\n    status: 'suspended',\n    permissions: getDefaultPermissions('user'),\n    profile: {\n      firstName: 'Robert',\n      lastName: 'Taylor',\n      jobTitle: 'Sales Representative',\n      department: 'Sales',\n      location: 'Denver',\n      avatarUrl: 'https://randomuser.me/api/portraits/men/7.jpg',\n      phoneNumber: '+****************'\n    },\n    settings: {\n      theme: 'dark',\n      emailNotifications: false,\n      twoFactorEnabled: false,\n      sidebarCollapsed: true,\n      language: 'en-US'\n    },\n    activity: {\n      lastLogin: new Date('2023-03-15T13:45:00'),\n      lastActive: new Date('2023-03-15T15:30:00'),\n      totalLogins: 28,\n      totalSessions: 35,\n      averageSessionDuration: 1500, // 25 minutes\n      favoriteAgents: ['1'],\n      favoriteSection: 'browse'\n    },\n    createdAt: new Date('2022-06-20T00:00:00'),\n    updatedAt: new Date('2023-03-20T10:00:00'),\n    lastPasswordChange: new Date('2022-11-15T00:00:00')\n  },\n  {\n    id: 'user-008',\n    email: '<EMAIL>',\n    username: 'guest',\n    role: 'guest',\n    status: 'active',\n    permissions: getDefaultPermissions('guest'),\n    profile: {\n      firstName: 'Guest',\n      lastName: 'User',\n      jobTitle: 'External Consultant',\n      department: 'External',\n      location: 'Remote',\n      bio: 'Guest account with limited access for demonstration purposes.',\n      avatarUrl: 'https://randomuser.me/api/portraits/lego/1.jpg'\n    },\n    settings: {\n      theme: 'light',\n      emailNotifications: false,\n      twoFactorEnabled: false,\n      sidebarCollapsed: false,\n      language: 'en-US'\n    },\n    activity: {\n      lastLogin: new Date('2023-05-14T10:30:00'),\n      lastActive: new Date('2023-05-14T11:15:00'),\n      totalLogins: 5,\n      totalSessions: 5,\n      averageSessionDuration: 900, // 15 minutes\n      favoriteAgents: [],\n      favoriteSection: 'browse'\n    },\n    createdAt: new Date('2023-01-01T00:00:00'),\n    updatedAt: new Date('2023-05-14T11:15:00'),\n    lastPasswordChange: new Date('2023-01-01T00:00:00')\n  }\n];\n\n// Helper functions for user management\nexport const getUserById = (id: string): User | undefined => {\n  return mockUsers.find(user => user.id === id);\n};\n\nexport const getUsersByRole = (role: UserRole): User[] => {\n  return mockUsers.filter(user => user.role === role);\n};\n\nexport const getUsersByStatus = (status: UserStatus): User[] => {\n  return mockUsers.filter(user => user.status === status);\n};\n\nexport const getActiveUsers = (): User[] => {\n  return mockUsers.filter(user => user.status === 'active');\n};\n\nexport const searchUsers = (query: string): User[] => {\n  const lowercaseQuery = query.toLowerCase();\n  return mockUsers.filter(user => \n    user.username.toLowerCase().includes(lowercaseQuery) ||\n    user.email.toLowerCase().includes(lowercaseQuery) ||\n    user.profile.firstName.toLowerCase().includes(lowercaseQuery) ||\n    user.profile.lastName.toLowerCase().includes(lowercaseQuery) ||\n    (user.profile.jobTitle && user.profile.jobTitle.toLowerCase().includes(lowercaseQuery)) ||\n    (user.profile.department && user.profile.department.toLowerCase().includes(lowercaseQuery))\n  );\n};\n"], "names": [], "mappings": ";;;;;;;;AAEA,gEAAgE;AAChE,MAAM,wBAAwB,CAAC;IAC7B,OAAQ;QACN,KAAK;YACH,OAAO;gBACL,iBAAiB;gBACjB,eAAe;gBACf,iBAAiB;gBACjB,gBAAgB;gBAChB,gBAAgB;gBAChB,oBAAoB;gBACpB,kBAAkB;gBAClB,mBAAmB;YACrB;QACF,KAAK;YACH,OAAO;gBACL,iBAAiB;gBACjB,eAAe;gBACf,iBAAiB;gBACjB,gBAAgB;gBAChB,gBAAgB;gBAChB,oBAAoB;gBACpB,kBAAkB;gBAClB,mBAAmB;YACrB;QACF,KAAK;YACH,OAAO;gBACL,iBAAiB;gBACjB,eAAe;gBACf,iBAAiB;gBACjB,gBAAgB;gBAChB,gBAAgB;gBAChB,oBAAoB;gBACpB,kBAAkB;gBAClB,mBAAmB;YACrB;QACF,KAAK;YACH,OAAO;gBACL,iBAAiB;gBACjB,eAAe;gBACf,iBAAiB;gBACjB,gBAAgB;gBAChB,gBAAgB;gBAChB,oBAAoB;gBACpB,kBAAkB;gBAClB,mBAAmB;YACrB;IACJ;AACF;AAGO,MAAM,YAAoB;IAC/B;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,MAAM;QACN,QAAQ;QACR,aAAa,sBAAsB;QACnC,SAAS;YACP,WAAW;YACX,UAAU;YACV,UAAU;YACV,YAAY;YACZ,UAAU;YACV,KAAK;YACL,WAAW;YACX,aAAa;QACf;QACA,UAAU;YACR,OAAO;YACP,oBAAoB;YACpB,kBAAkB;YAClB,kBAAkB;YAClB,UAAU;QACZ;QACA,UAAU;YACR,WAAW,IAAI,KAAK;YACpB,YAAY,IAAI,KAAK;YACrB,aAAa;YACb,eAAe;YACf,wBAAwB;YACxB,gBAAgB;gBAAC;gBAAK;gBAAK;aAAI;YAC/B,iBAAiB;QACnB;QACA,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,oBAAoB,IAAI,KAAK;IAC/B;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,MAAM;QACN,QAAQ;QACR,aAAa,sBAAsB;QACnC,SAAS;YACP,WAAW;YACX,UAAU;YACV,UAAU;YACV,YAAY;YACZ,UAAU;YACV,KAAK;YACL,WAAW;YACX,aAAa;QACf;QACA,UAAU;YACR,OAAO;YACP,oBAAoB;YACpB,kBAAkB;YAClB,kBAAkB;YAClB,UAAU;QACZ;QACA,UAAU;YACR,WAAW,IAAI,KAAK;YACpB,YAAY,IAAI,KAAK;YACrB,aAAa;YACb,eAAe;YACf,wBAAwB;YACxB,gBAAgB;gBAAC;gBAAK;aAAI;YAC1B,iBAAiB;QACnB;QACA,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,oBAAoB,IAAI,KAAK;IAC/B;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,MAAM;QACN,QAAQ;QACR,aAAa,sBAAsB;QACnC,SAAS;YACP,WAAW;YACX,UAAU;YACV,UAAU;YACV,YAAY;YACZ,UAAU;YACV,KAAK;YACL,WAAW;QACb;QACA,UAAU;YACR,OAAO;YACP,oBAAoB;YACpB,kBAAkB;YAClB,kBAAkB;YAClB,UAAU;QACZ;QACA,UAAU;YACR,WAAW,IAAI,KAAK;YACpB,YAAY,IAAI,KAAK;YACrB,aAAa;YACb,eAAe;YACf,wBAAwB;YACxB,gBAAgB;gBAAC;gBAAK;aAAI;YAC1B,iBAAiB;QACnB;QACA,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,oBAAoB,IAAI,KAAK;IAC/B;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,MAAM;QACN,QAAQ;QACR,aAAa,sBAAsB;QACnC,SAAS;YACP,WAAW;YACX,UAAU;YACV,UAAU;YACV,YAAY;YACZ,UAAU;YACV,KAAK;YACL,WAAW;YACX,aAAa;QACf;QACA,UAAU;YACR,OAAO;YACP,oBAAoB;YACpB,kBAAkB;YAClB,kBAAkB;YAClB,UAAU;QACZ;QACA,UAAU;YACR,WAAW,IAAI,KAAK;YACpB,YAAY,IAAI,KAAK;YACrB,aAAa;YACb,eAAe;YACf,wBAAwB;YACxB,gBAAgB;gBAAC;gBAAK;aAAI;YAC1B,iBAAiB;QACnB;QACA,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,oBAAoB,IAAI,KAAK;IAC/B;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,MAAM;QACN,QAAQ;QACR,aAAa,sBAAsB;QACnC,SAAS;YACP,WAAW;YACX,UAAU;YACV,UAAU;YACV,YAAY;YACZ,UAAU;YACV,KAAK;YACL,WAAW;QACb;QACA,UAAU;YACR,OAAO;YACP,oBAAoB;YACpB,kBAAkB;YAClB,kBAAkB;YAClB,UAAU;QACZ;QACA,UAAU;YACR,WAAW,IAAI,KAAK;YACpB,YAAY,IAAI,KAAK;YACrB,aAAa;YACb,eAAe;YACf,wBAAwB;YACxB,gBAAgB;gBAAC;aAAI;YACrB,iBAAiB;QACnB;QACA,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,oBAAoB,IAAI,KAAK;IAC/B;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,MAAM;QACN,QAAQ;QACR,aAAa,sBAAsB;QACnC,SAAS;YACP,WAAW;YACX,UAAU;YACV,UAAU;YACV,YAAY;YACZ,UAAU;YACV,WAAW;QACb;QACA,UAAU;YACR,OAAO;YACP,oBAAoB;YACpB,kBAAkB;YAClB,kBAAkB;YAClB,UAAU;QACZ;QACA,UAAU;YACR,WAAW,IAAI,KAAK;YACpB,YAAY,IAAI,KAAK;YACrB,aAAa;YACb,eAAe;YACf,wBAAwB;YACxB,gBAAgB,EAAE;YAClB,iBAAiB;QACnB;QACA,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,oBAAoB,IAAI,KAAK;IAC/B;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,MAAM;QACN,QAAQ;QACR,aAAa,sBAAsB;QACnC,SAAS;YACP,WAAW;YACX,UAAU;YACV,UAAU;YACV,YAAY;YACZ,UAAU;YACV,WAAW;YACX,aAAa;QACf;QACA,UAAU;YACR,OAAO;YACP,oBAAoB;YACpB,kBAAkB;YAClB,kBAAkB;YAClB,UAAU;QACZ;QACA,UAAU;YACR,WAAW,IAAI,KAAK;YACpB,YAAY,IAAI,KAAK;YACrB,aAAa;YACb,eAAe;YACf,wBAAwB;YACxB,gBAAgB;gBAAC;aAAI;YACrB,iBAAiB;QACnB;QACA,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,oBAAoB,IAAI,KAAK;IAC/B;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,MAAM;QACN,QAAQ;QACR,aAAa,sBAAsB;QACnC,SAAS;YACP,WAAW;YACX,UAAU;YACV,UAAU;YACV,YAAY;YACZ,UAAU;YACV,KAAK;YACL,WAAW;QACb;QACA,UAAU;YACR,OAAO;YACP,oBAAoB;YACpB,kBAAkB;YAClB,kBAAkB;YAClB,UAAU;QACZ;QACA,UAAU;YACR,WAAW,IAAI,KAAK;YACpB,YAAY,IAAI,KAAK;YACrB,aAAa;YACb,eAAe;YACf,wBAAwB;YACxB,gBAAgB,EAAE;YAClB,iBAAiB;QACnB;QACA,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,oBAAoB,IAAI,KAAK;IAC/B;CACD;AAGM,MAAM,cAAc,CAAC;IAC1B,OAAO,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;AAC5C;AAEO,MAAM,iBAAiB,CAAC;IAC7B,OAAO,UAAU,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK;AAChD;AAEO,MAAM,mBAAmB,CAAC;IAC/B,OAAO,UAAU,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK;AAClD;AAEO,MAAM,iBAAiB;IAC5B,OAAO,UAAU,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK;AAClD;AAEO,MAAM,cAAc,CAAC;IAC1B,MAAM,iBAAiB,MAAM,WAAW;IACxC,OAAO,UAAU,MAAM,CAAC,CAAA,OACtB,KAAK,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,mBACrC,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,mBAClC,KAAK,OAAO,CAAC,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,mBAC9C,KAAK,OAAO,CAAC,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,mBAC5C,KAAK,OAAO,CAAC,QAAQ,IAAI,KAAK,OAAO,CAAC,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,mBACtE,KAAK,OAAO,CAAC,UAAU,IAAI,KAAK,OAAO,CAAC,UAAU,CAAC,WAAW,GAAG,QAAQ,CAAC;AAE/E", "debugId": null}}, {"offset": {"line": 395, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/admin/users/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { usePara<PERSON>, useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport {\n  faArrowLeft,\n  faUser,\n  faSave,\n  faTrash,\n  faLock,\n  faUnlock,\n  faEnvelope,\n  faIdCard,\n  faBuilding,\n  faMapMarkerAlt,\n  faPhone,\n  faShieldAlt\n} from '@fortawesome/free-solid-svg-icons';\nimport { getUserById } from '@/data/mock-users';\nimport { User, UserRole, UserStatus } from '@/types/user';\n\nexport default function UserDetailsPage() {\n  const params = useParams();\n  const router = useRouter();\n  const userId = params.id as string;\n\n  const [user, setUser] = useState<User | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [activeTab, setActiveTab] = useState<'profile' | 'security' | 'activity'>('profile');\n\n  // Load user data\n  useEffect(() => {\n    if (userId) {\n      const userData = getUserById(userId);\n      if (userData) {\n        setUser(userData);\n      }\n      setIsLoading(false);\n    }\n  }, [userId]);\n\n  // Handle form submission\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    // In a real app, this would save the user data to the database\n    alert('User data saved successfully!');\n  };\n\n  // Handle user deletion\n  const handleDelete = () => {\n    // In a real app, this would delete the user from the database\n    const confirmed = window.confirm('Are you sure you want to delete this user? This action cannot be undone.');\n    if (confirmed) {\n      alert('User deleted successfully!');\n      router.push('/admin/users');\n    }\n  };\n\n  // Handle user status change\n  const handleStatusChange = (newStatus: UserStatus) => {\n    if (!user) return;\n\n    // In a real app, this would update the user status in the database\n    setUser({\n      ...user,\n      status: newStatus\n    });\n\n    alert(`User status changed to ${newStatus}`);\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center justify-center py-12\">\n        <div className=\"text-center\">\n          <div className=\"mb-4 h-12 w-12 animate-spin rounded-full border-b-2 border-t-2 border-indigo-600\"></div>\n          <p className=\"text-gray-600 dark:text-gray-400\">Loading user data...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!user) {\n    return (\n      <div className=\"flex flex-col items-center justify-center py-12 text-center\">\n        <h2 className=\"mb-4 text-2xl font-bold\">User Not Found</h2>\n        <p className=\"mb-6 text-gray-600 dark:text-gray-400\">The user you're looking for doesn't exist or has been removed.</p>\n        <Link\n          href=\"/admin/users\"\n          className=\"flex items-center rounded-md bg-indigo-600 px-4 py-2 text-white hover:bg-indigo-700\"\n        >\n          <FontAwesomeIcon icon={faArrowLeft} className=\"mr-2 h-4 w-4\" />\n          Back to Users\n        </Link>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-8\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <Link\n            href=\"/admin/users\"\n            className=\"mb-4 inline-flex items-center text-sm font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300\"\n          >\n            <FontAwesomeIcon icon={faArrowLeft} className=\"mr-2 h-4 w-4\" />\n            Back to Users\n          </Link>\n          <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n            {user.profile.firstName} {user.profile.lastName}\n          </h1>\n          <p className=\"text-sm text-gray-500 dark:text-gray-400\">\n            User ID: {user.id}\n          </p>\n        </div>\n\n        <div className=\"flex space-x-3\">\n          {user.status === 'active' ? (\n            <button\n              onClick={() => handleStatusChange('suspended')}\n              className=\"inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700\"\n            >\n              <FontAwesomeIcon icon={faLock} className=\"mr-2 h-4 w-4\" />\n              Suspend User\n            </button>\n          ) : (\n            <button\n              onClick={() => handleStatusChange('active')}\n              className=\"inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700\"\n            >\n              <FontAwesomeIcon icon={faUnlock} className=\"mr-2 h-4 w-4\" />\n              Activate User\n            </button>\n          )}\n\n          <button\n            onClick={handleDelete}\n            className=\"inline-flex items-center rounded-md border border-red-300 bg-white px-4 py-2 text-sm font-medium text-red-700 shadow-sm hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:border-red-700 dark:bg-gray-800 dark:text-red-400 dark:hover:bg-red-900/20\"\n          >\n            <FontAwesomeIcon icon={faTrash} className=\"mr-2 h-4 w-4\" />\n            Delete User\n          </button>\n\n          <button\n            form=\"user-form\"\n            type=\"submit\"\n            className=\"inline-flex items-center rounded-md bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:bg-indigo-500 dark:hover:bg-indigo-600\"\n          >\n            <FontAwesomeIcon icon={faSave} className=\"mr-2 h-4 w-4\" />\n            Save Changes\n          </button>\n        </div>\n      </div>\n\n      {/* User info card */}\n      <div className=\"overflow-hidden rounded-lg bg-white shadow dark:bg-gray-900\">\n        <div className=\"flex items-center space-x-6 border-b border-gray-200 px-6 py-4 dark:border-gray-800\">\n          <div className=\"h-24 w-24 flex-shrink-0\">\n            <img\n              className=\"h-24 w-24 rounded-full\"\n              src={user.profile.avatarUrl || `https://ui-avatars.com/api/?name=${user.profile.firstName}+${user.profile.lastName}&background=random`}\n              alt={`${user.profile.firstName} ${user.profile.lastName}`}\n            />\n          </div>\n          <div>\n            <h2 className=\"text-xl font-bold text-gray-900 dark:text-white\">\n              {user.profile.firstName} {user.profile.lastName}\n            </h2>\n            <p className=\"text-sm text-gray-500 dark:text-gray-400\">\n              {user.profile.jobTitle}{user.profile.department ? `, ${user.profile.department}` : ''}\n            </p>\n            <div className=\"mt-2 flex items-center space-x-2\">\n              <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${\n                user.status === 'active'\n                  ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'\n                  : user.status === 'pending'\n                    ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300'\n                    : user.status === 'suspended'\n                      ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'\n                      : 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300'\n              }`}>\n                {user.status.charAt(0).toUpperCase() + user.status.slice(1)}\n              </span>\n              <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${\n                user.role === 'admin'\n                  ? 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300'\n                  : user.role === 'manager'\n                    ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'\n                    : user.role === 'user'\n                      ? 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-300'\n                      : 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300'\n              }`}>\n                {user.role.charAt(0).toUpperCase() + user.role.slice(1)}\n              </span>\n            </div>\n          </div>\n        </div>\n\n        {/* Tabs */}\n        <div className=\"border-b border-gray-200 dark:border-gray-800\">\n          <nav className=\"-mb-px flex\">\n            <button\n              className={`w-1/3 border-b-2 py-4 px-1 text-center text-sm font-medium ${\n                activeTab === 'profile'\n                  ? 'border-indigo-500 text-indigo-600 dark:border-indigo-400 dark:text-indigo-400'\n                  : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 dark:text-gray-400 dark:hover:border-gray-700 dark:hover:text-gray-300'\n              }`}\n              onClick={() => setActiveTab('profile')}\n            >\n              Profile\n            </button>\n            <button\n              className={`w-1/3 border-b-2 py-4 px-1 text-center text-sm font-medium ${\n                activeTab === 'security'\n                  ? 'border-indigo-500 text-indigo-600 dark:border-indigo-400 dark:text-indigo-400'\n                  : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 dark:text-gray-400 dark:hover:border-gray-700 dark:hover:text-gray-300'\n              }`}\n              onClick={() => setActiveTab('security')}\n            >\n              Security & Permissions\n            </button>\n            <button\n              className={`w-1/3 border-b-2 py-4 px-1 text-center text-sm font-medium ${\n                activeTab === 'activity'\n                  ? 'border-indigo-500 text-indigo-600 dark:border-indigo-400 dark:text-indigo-400'\n                  : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 dark:text-gray-400 dark:hover:border-gray-700 dark:hover:text-gray-300'\n              }`}\n              onClick={() => setActiveTab('activity')}\n            >\n              Activity\n            </button>\n          </nav>\n        </div>\n\n        {/* Tab content */}\n        <div className=\"p-6\">\n          {/* Profile Tab */}\n          {activeTab === 'profile' && (\n            <form id=\"user-form\" onSubmit={handleSubmit} className=\"space-y-6\">\n              <div className=\"grid gap-6 md:grid-cols-2\">\n                {/* Basic Information */}\n                <div className=\"space-y-6\">\n                  <div>\n                    <h3 className=\"text-lg font-medium text-gray-900 dark:text-white\">Basic Information</h3>\n                    <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">Update the user's profile information.</p>\n                  </div>\n\n                  <div className=\"grid gap-6\">\n                    <div className=\"grid grid-cols-2 gap-6\">\n                      <div>\n                        <label htmlFor=\"firstName\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                          First Name\n                        </label>\n                        <input\n                          type=\"text\"\n                          name=\"firstName\"\n                          id=\"firstName\"\n                          defaultValue={user.profile.firstName}\n                          className=\"mt-1 block w-full rounded-md border-gray-300 py-2 px-3 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white sm:text-sm\"\n                        />\n                      </div>\n                      <div>\n                        <label htmlFor=\"lastName\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                          Last Name\n                        </label>\n                        <input\n                          type=\"text\"\n                          name=\"lastName\"\n                          id=\"lastName\"\n                          defaultValue={user.profile.lastName}\n                          className=\"mt-1 block w-full rounded-md border-gray-300 py-2 px-3 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white sm:text-sm\"\n                        />\n                      </div>\n                    </div>\n\n                    <div>\n                      <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                        Email\n                      </label>\n                      <div className=\"mt-1 flex rounded-md shadow-sm\">\n                        <span className=\"inline-flex items-center rounded-l-md border border-r-0 border-gray-300 bg-gray-50 px-3 text-gray-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 sm:text-sm\">\n                          <FontAwesomeIcon icon={faEnvelope} className=\"h-4 w-4\" />\n                        </span>\n                        <input\n                          type=\"email\"\n                          name=\"email\"\n                          id=\"email\"\n                          defaultValue={user.email}\n                          className=\"block w-full flex-1 rounded-none rounded-r-md border-gray-300 py-2 px-3 focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white sm:text-sm\"\n                        />\n                      </div>\n                    </div>\n\n                    <div>\n                      <label htmlFor=\"username\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                        Username\n                      </label>\n                      <div className=\"mt-1 flex rounded-md shadow-sm\">\n                        <span className=\"inline-flex items-center rounded-l-md border border-r-0 border-gray-300 bg-gray-50 px-3 text-gray-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 sm:text-sm\">\n                          <FontAwesomeIcon icon={faUser} className=\"h-4 w-4\" />\n                        </span>\n                        <input\n                          type=\"text\"\n                          name=\"username\"\n                          id=\"username\"\n                          defaultValue={user.username}\n                          className=\"block w-full flex-1 rounded-none rounded-r-md border-gray-300 py-2 px-3 focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white sm:text-sm\"\n                        />\n                      </div>\n                    </div>\n\n                    <div className=\"grid grid-cols-2 gap-6\">\n                      <div>\n                        <label htmlFor=\"role\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                          Role\n                        </label>\n                        <select\n                          id=\"role\"\n                          name=\"role\"\n                          defaultValue={user.role}\n                          className=\"mt-1 block w-full rounded-md border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white sm:text-sm\"\n                        >\n                          <option value=\"admin\">Admin</option>\n                          <option value=\"manager\">Manager</option>\n                          <option value=\"user\">User</option>\n                          <option value=\"guest\">Guest</option>\n                        </select>\n                      </div>\n                      <div>\n                        <label htmlFor=\"status\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                          Status\n                        </label>\n                        <select\n                          id=\"status\"\n                          name=\"status\"\n                          defaultValue={user.status}\n                          className=\"mt-1 block w-full rounded-md border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white sm:text-sm\"\n                        >\n                          <option value=\"active\">Active</option>\n                          <option value=\"inactive\">Inactive</option>\n                          <option value=\"pending\">Pending</option>\n                          <option value=\"suspended\">Suspended</option>\n                        </select>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Professional Information */}\n                <div className=\"space-y-6\">\n                  <div>\n                    <h3 className=\"text-lg font-medium text-gray-900 dark:text-white\">Professional Information</h3>\n                    <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">Update the user's job and location details.</p>\n                  </div>\n\n                  <div className=\"grid gap-6\">\n                    <div>\n                      <label htmlFor=\"jobTitle\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                        Job Title\n                      </label>\n                      <div className=\"mt-1 flex rounded-md shadow-sm\">\n                        <span className=\"inline-flex items-center rounded-l-md border border-r-0 border-gray-300 bg-gray-50 px-3 text-gray-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 sm:text-sm\">\n                          <FontAwesomeIcon icon={faIdCard} className=\"h-4 w-4\" />\n                        </span>\n                        <input\n                          type=\"text\"\n                          name=\"jobTitle\"\n                          id=\"jobTitle\"\n                          defaultValue={user.profile.jobTitle || ''}\n                          className=\"block w-full flex-1 rounded-none rounded-r-md border-gray-300 py-2 px-3 focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white sm:text-sm\"\n                        />\n                      </div>\n                    </div>\n\n                    <div>\n                      <label htmlFor=\"department\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                        Department\n                      </label>\n                      <div className=\"mt-1 flex rounded-md shadow-sm\">\n                        <span className=\"inline-flex items-center rounded-l-md border border-r-0 border-gray-300 bg-gray-50 px-3 text-gray-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 sm:text-sm\">\n                          <FontAwesomeIcon icon={faBuilding} className=\"h-4 w-4\" />\n                        </span>\n                        <input\n                          type=\"text\"\n                          name=\"department\"\n                          id=\"department\"\n                          defaultValue={user.profile.department || ''}\n                          className=\"block w-full flex-1 rounded-none rounded-r-md border-gray-300 py-2 px-3 focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white sm:text-sm\"\n                        />\n                      </div>\n                    </div>\n\n                    <div>\n                      <label htmlFor=\"location\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                        Location\n                      </label>\n                      <div className=\"mt-1 flex rounded-md shadow-sm\">\n                        <span className=\"inline-flex items-center rounded-l-md border border-r-0 border-gray-300 bg-gray-50 px-3 text-gray-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 sm:text-sm\">\n                          <FontAwesomeIcon icon={faMapMarkerAlt} className=\"h-4 w-4\" />\n                        </span>\n                        <input\n                          type=\"text\"\n                          name=\"location\"\n                          id=\"location\"\n                          defaultValue={user.profile.location || ''}\n                          className=\"block w-full flex-1 rounded-none rounded-r-md border-gray-300 py-2 px-3 focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white sm:text-sm\"\n                        />\n                      </div>\n                    </div>\n\n                    <div>\n                      <label htmlFor=\"phoneNumber\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                        Phone Number\n                      </label>\n                      <div className=\"mt-1 flex rounded-md shadow-sm\">\n                        <span className=\"inline-flex items-center rounded-l-md border border-r-0 border-gray-300 bg-gray-50 px-3 text-gray-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 sm:text-sm\">\n                          <FontAwesomeIcon icon={faPhone} className=\"h-4 w-4\" />\n                        </span>\n                        <input\n                          type=\"text\"\n                          name=\"phoneNumber\"\n                          id=\"phoneNumber\"\n                          defaultValue={user.profile.phoneNumber || ''}\n                          className=\"block w-full flex-1 rounded-none rounded-r-md border-gray-300 py-2 px-3 focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white sm:text-sm\"\n                        />\n                      </div>\n                    </div>\n\n                    <div>\n                      <label htmlFor=\"bio\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                        Bio\n                      </label>\n                      <div className=\"mt-1\">\n                        <textarea\n                          id=\"bio\"\n                          name=\"bio\"\n                          rows={3}\n                          defaultValue={user.profile.bio || ''}\n                          className=\"block w-full rounded-md border-gray-300 py-2 px-3 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white sm:text-sm\"\n                        />\n                      </div>\n                      <p className=\"mt-2 text-sm text-gray-500 dark:text-gray-400\">Brief description of the user's role and expertise.</p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </form>\n          )}\n\n          {activeTab === 'security' && (\n            <div className=\"space-y-6\">\n              {/* Permissions */}\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-white\">Permissions</h3>\n                <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">Configure what actions this user can perform.</p>\n\n                <div className=\"mt-6 space-y-6\">\n                  <div className=\"relative flex items-start\">\n                    <div className=\"flex h-5 items-center\">\n                      <input\n                        id=\"canCreateAgents\"\n                        name=\"canCreateAgents\"\n                        type=\"checkbox\"\n                        defaultChecked={user.permissions.canCreateAgents}\n                        className=\"h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800\"\n                      />\n                    </div>\n                    <div className=\"ml-3 text-sm\">\n                      <label htmlFor=\"canCreateAgents\" className=\"font-medium text-gray-700 dark:text-gray-300\">\n                        Create Agents\n                      </label>\n                      <p className=\"text-gray-500 dark:text-gray-400\">Allow user to create new AI agents.</p>\n                    </div>\n                  </div>\n\n                  <div className=\"relative flex items-start\">\n                    <div className=\"flex h-5 items-center\">\n                      <input\n                        id=\"canEditAgents\"\n                        name=\"canEditAgents\"\n                        type=\"checkbox\"\n                        defaultChecked={user.permissions.canEditAgents}\n                        className=\"h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800\"\n                      />\n                    </div>\n                    <div className=\"ml-3 text-sm\">\n                      <label htmlFor=\"canEditAgents\" className=\"font-medium text-gray-700 dark:text-gray-300\">\n                        Edit Agents\n                      </label>\n                      <p className=\"text-gray-500 dark:text-gray-400\">Allow user to modify existing AI agents.</p>\n                    </div>\n                  </div>\n\n                  <div className=\"relative flex items-start\">\n                    <div className=\"flex h-5 items-center\">\n                      <input\n                        id=\"canDeleteAgents\"\n                        name=\"canDeleteAgents\"\n                        type=\"checkbox\"\n                        defaultChecked={user.permissions.canDeleteAgents}\n                        className=\"h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800\"\n                      />\n                    </div>\n                    <div className=\"ml-3 text-sm\">\n                      <label htmlFor=\"canDeleteAgents\" className=\"font-medium text-gray-700 dark:text-gray-300\">\n                        Delete Agents\n                      </label>\n                      <p className=\"text-gray-500 dark:text-gray-400\">Allow user to delete AI agents.</p>\n                    </div>\n                  </div>\n\n                  <div className=\"relative flex items-start\">\n                    <div className=\"flex h-5 items-center\">\n                      <input\n                        id=\"canManageUsers\"\n                        name=\"canManageUsers\"\n                        type=\"checkbox\"\n                        defaultChecked={user.permissions.canManageUsers}\n                        className=\"h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800\"\n                      />\n                    </div>\n                    <div className=\"ml-3 text-sm\">\n                      <label htmlFor=\"canManageUsers\" className=\"font-medium text-gray-700 dark:text-gray-300\">\n                        Manage Users\n                      </label>\n                      <p className=\"text-gray-500 dark:text-gray-400\">Allow user to create, edit, and delete other users.</p>\n                    </div>\n                  </div>\n\n                  <div className=\"relative flex items-start\">\n                    <div className=\"flex h-5 items-center\">\n                      <input\n                        id=\"canAccessAdmin\"\n                        name=\"canAccessAdmin\"\n                        type=\"checkbox\"\n                        defaultChecked={user.permissions.canAccessAdmin}\n                        className=\"h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800\"\n                      />\n                    </div>\n                    <div className=\"ml-3 text-sm\">\n                      <label htmlFor=\"canAccessAdmin\" className=\"font-medium text-gray-700 dark:text-gray-300\">\n                        Access Admin Panel\n                      </label>\n                      <p className=\"text-gray-500 dark:text-gray-400\">Allow user to access the admin panel and settings.</p>\n                    </div>\n                  </div>\n\n                  <div className=\"relative flex items-start\">\n                    <div className=\"flex h-5 items-center\">\n                      <input\n                        id=\"canAccessAnalytics\"\n                        name=\"canAccessAnalytics\"\n                        type=\"checkbox\"\n                        defaultChecked={user.permissions.canAccessAnalytics}\n                        className=\"h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800\"\n                      />\n                    </div>\n                    <div className=\"ml-3 text-sm\">\n                      <label htmlFor=\"canAccessAnalytics\" className=\"font-medium text-gray-700 dark:text-gray-300\">\n                        Access Analytics\n                      </label>\n                      <p className=\"text-gray-500 dark:text-gray-400\">Allow user to view analytics and reports.</p>\n                    </div>\n                  </div>\n\n                  <div className=\"relative flex items-start\">\n                    <div className=\"flex h-5 items-center\">\n                      <input\n                        id=\"canManageContent\"\n                        name=\"canManageContent\"\n                        type=\"checkbox\"\n                        defaultChecked={user.permissions.canManageContent}\n                        className=\"h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800\"\n                      />\n                    </div>\n                    <div className=\"ml-3 text-sm\">\n                      <label htmlFor=\"canManageContent\" className=\"font-medium text-gray-700 dark:text-gray-300\">\n                        Manage Content\n                      </label>\n                      <p className=\"text-gray-500 dark:text-gray-400\">Allow user to create and edit content in the learning section.</p>\n                    </div>\n                  </div>\n\n                  <div className=\"relative flex items-start\">\n                    <div className=\"flex h-5 items-center\">\n                      <input\n                        id=\"canApproveContent\"\n                        name=\"canApproveContent\"\n                        type=\"checkbox\"\n                        defaultChecked={user.permissions.canApproveContent}\n                        className=\"h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800\"\n                      />\n                    </div>\n                    <div className=\"ml-3 text-sm\">\n                      <label htmlFor=\"canApproveContent\" className=\"font-medium text-gray-700 dark:text-gray-300\">\n                        Approve Content\n                      </label>\n                      <p className=\"text-gray-500 dark:text-gray-400\">Allow user to approve or reject content submitted by others.</p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Security Settings */}\n              <div className=\"pt-6\">\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-white\">Security Settings</h3>\n                <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">Manage user's security settings and preferences.</p>\n\n                <div className=\"mt-6 space-y-6\">\n                  <div>\n                    <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                      Reset Password\n                    </label>\n                    <div className=\"mt-1\">\n                      <input\n                        type=\"password\"\n                        name=\"password\"\n                        id=\"password\"\n                        className=\"block w-full rounded-md border-gray-300 py-2 px-3 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white sm:text-sm\"\n                        placeholder=\"Enter new password\"\n                      />\n                    </div>\n                    <p className=\"mt-2 text-sm text-gray-500 dark:text-gray-400\">\n                      Leave blank to keep the current password. Last changed: {user.lastPasswordChange ? new Date(user.lastPasswordChange).toLocaleDateString() : 'Never'}\n                    </p>\n                  </div>\n\n                  <div className=\"relative flex items-start\">\n                    <div className=\"flex h-5 items-center\">\n                      <input\n                        id=\"twoFactorEnabled\"\n                        name=\"twoFactorEnabled\"\n                        type=\"checkbox\"\n                        defaultChecked={user.settings.twoFactorEnabled}\n                        className=\"h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800\"\n                      />\n                    </div>\n                    <div className=\"ml-3 text-sm\">\n                      <label htmlFor=\"twoFactorEnabled\" className=\"font-medium text-gray-700 dark:text-gray-300\">\n                        Two-Factor Authentication\n                      </label>\n                      <p className=\"text-gray-500 dark:text-gray-400\">Require two-factor authentication for this user.</p>\n                    </div>\n                  </div>\n\n                  <div>\n                    <label htmlFor=\"sessionTimeout\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                      Session Timeout (minutes)\n                    </label>\n                    <div className=\"mt-1\">\n                      <input\n                        type=\"number\"\n                        name=\"sessionTimeout\"\n                        id=\"sessionTimeout\"\n                        defaultValue={30}\n                        min={5}\n                        max={240}\n                        className=\"block w-full rounded-md border-gray-300 py-2 px-3 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white sm:text-sm\"\n                      />\n                    </div>\n                    <p className=\"mt-2 text-sm text-gray-500 dark:text-gray-400\">\n                      Time in minutes before the user is automatically logged out due to inactivity.\n                    </p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'activity' && (\n            <div className=\"space-y-6\">\n              {/* Activity Overview */}\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-white\">Activity Overview</h3>\n                <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">View user's activity and usage statistics.</p>\n\n                <div className=\"mt-6 grid gap-6 sm:grid-cols-2 lg:grid-cols-4\">\n                  <div className=\"overflow-hidden rounded-lg bg-white shadow dark:bg-gray-800\">\n                    <div className=\"px-4 py-5 sm:p-6\">\n                      <dt className=\"truncate text-sm font-medium text-gray-500 dark:text-gray-400\">Total Logins</dt>\n                      <dd className=\"mt-1 text-3xl font-semibold text-gray-900 dark:text-white\">{user.activity.totalLogins}</dd>\n                    </div>\n                  </div>\n\n                  <div className=\"overflow-hidden rounded-lg bg-white shadow dark:bg-gray-800\">\n                    <div className=\"px-4 py-5 sm:p-6\">\n                      <dt className=\"truncate text-sm font-medium text-gray-500 dark:text-gray-400\">Total Sessions</dt>\n                      <dd className=\"mt-1 text-3xl font-semibold text-gray-900 dark:text-white\">{user.activity.totalSessions}</dd>\n                    </div>\n                  </div>\n\n                  <div className=\"overflow-hidden rounded-lg bg-white shadow dark:bg-gray-800\">\n                    <div className=\"px-4 py-5 sm:p-6\">\n                      <dt className=\"truncate text-sm font-medium text-gray-500 dark:text-gray-400\">Avg. Session Duration</dt>\n                      <dd className=\"mt-1 text-3xl font-semibold text-gray-900 dark:text-white\">\n                        {Math.floor(user.activity.averageSessionDuration / 60)}m\n                      </dd>\n                    </div>\n                  </div>\n\n                  <div className=\"overflow-hidden rounded-lg bg-white shadow dark:bg-gray-800\">\n                    <div className=\"px-4 py-5 sm:p-6\">\n                      <dt className=\"truncate text-sm font-medium text-gray-500 dark:text-gray-400\">Last Active</dt>\n                      <dd className=\"mt-1 text-xl font-semibold text-gray-900 dark:text-white\">\n                        {new Date(user.activity.lastActive).toLocaleDateString()}\n                      </dd>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Favorite Sections & Agents */}\n              <div className=\"pt-6\">\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-white\">Preferences & Favorites</h3>\n                <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">User's preferred sections and favorite agents.</p>\n\n                <div className=\"mt-6 grid gap-6 md:grid-cols-2\">\n                  <div>\n                    <h4 className=\"text-base font-medium text-gray-900 dark:text-white\">Favorite Section</h4>\n                    <div className=\"mt-2 rounded-md bg-gray-50 p-4 dark:bg-gray-800\">\n                      <p className=\"text-gray-700 dark:text-gray-300\">\n                        {user.activity.favoriteSection.split('_').map(word =>\n                          word.charAt(0).toUpperCase() + word.slice(1)\n                        ).join(' ')}\n                      </p>\n                    </div>\n                    <p className=\"mt-2 text-sm text-gray-500 dark:text-gray-400\">\n                      The section of the application this user spends the most time in.\n                    </p>\n                  </div>\n\n                  <div>\n                    <h4 className=\"text-base font-medium text-gray-900 dark:text-white\">Favorite Agents</h4>\n                    <div className=\"mt-2 rounded-md bg-gray-50 p-4 dark:bg-gray-800\">\n                      {user.activity.favoriteAgents.length > 0 ? (\n                        <ul className=\"list-inside list-disc space-y-1 text-gray-700 dark:text-gray-300\">\n                          {user.activity.favoriteAgents.map((agentId) => (\n                            <li key={agentId}>Agent {agentId}</li>\n                          ))}\n                        </ul>\n                      ) : (\n                        <p className=\"text-gray-500 dark:text-gray-400\">No favorite agents yet.</p>\n                      )}\n                    </div>\n                    <p className=\"mt-2 text-sm text-gray-500 dark:text-gray-400\">\n                      The AI agents this user interacts with most frequently.\n                    </p>\n                  </div>\n                </div>\n              </div>\n\n              {/* Recent Activity */}\n              <div className=\"pt-6\">\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-white\">Recent Activity</h3>\n                <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">User's recent actions and interactions.</p>\n\n                <div className=\"mt-6 overflow-hidden rounded-md border border-gray-200 dark:border-gray-700\">\n                  <ul className=\"divide-y divide-gray-200 dark:divide-gray-700\">\n                    <li className=\"px-6 py-4\">\n                      <div className=\"flex items-center justify-between\">\n                        <div>\n                          <p className=\"text-sm font-medium text-gray-900 dark:text-white\">Logged in</p>\n                          <p className=\"text-sm text-gray-500 dark:text-gray-400\">User logged in from Chrome on Windows</p>\n                        </div>\n                        <p className=\"text-sm text-gray-500 dark:text-gray-400\">{new Date(user.activity.lastLogin).toLocaleString()}</p>\n                      </div>\n                    </li>\n                    <li className=\"px-6 py-4\">\n                      <div className=\"flex items-center justify-between\">\n                        <div>\n                          <p className=\"text-sm font-medium text-gray-900 dark:text-white\">Viewed agent details</p>\n                          <p className=\"text-sm text-gray-500 dark:text-gray-400\">Viewed details for Agent 1</p>\n                        </div>\n                        <p className=\"text-sm text-gray-500 dark:text-gray-400\">{new Date(user.activity.lastActive).toLocaleString()}</p>\n                      </div>\n                    </li>\n                    <li className=\"px-6 py-4\">\n                      <div className=\"flex items-center justify-between\">\n                        <div>\n                          <p className=\"text-sm font-medium text-gray-900 dark:text-white\">Updated profile</p>\n                          <p className=\"text-sm text-gray-500 dark:text-gray-400\">Updated job title and department</p>\n                        </div>\n                        <p className=\"text-sm text-gray-500 dark:text-gray-400\">{new Date(user.updatedAt).toLocaleString()}</p>\n                      </div>\n                    </li>\n                  </ul>\n                </div>\n                <p className=\"mt-2 text-sm text-gray-500 dark:text-gray-400\">\n                  Note: In a real application, this would show actual user activity from a database.\n                </p>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAcA;AApBA;;;;;;;;AAuBe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,OAAO,EAAE;IAExB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuC;IAEhF,iBAAiB;IACjB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV,MAAM,WAAW,CAAA,GAAA,4HAAA,CAAA,cAAW,AAAD,EAAE;YAC7B,IAAI,UAAU;gBACZ,QAAQ;YACV;YACA,aAAa;QACf;IACF,GAAG;QAAC;KAAO;IAEX,yBAAyB;IACzB,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,+DAA+D;QAC/D,MAAM;IACR;IAEA,uBAAuB;IACvB,MAAM,eAAe;QACnB,8DAA8D;QAC9D,MAAM,YAAY,OAAO,OAAO,CAAC;QACjC,IAAI,WAAW;YACb,MAAM;YACN,OAAO,IAAI,CAAC;QACd;IACF;IAEA,4BAA4B;IAC5B,MAAM,qBAAqB,CAAC;QAC1B,IAAI,CAAC,MAAM;QAEX,mEAAmE;QACnE,QAAQ;YACN,GAAG,IAAI;YACP,QAAQ;QACV;QAEA,MAAM,CAAC,uBAAuB,EAAE,WAAW;IAC7C;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAmC;;;;;;;;;;;;;;;;;IAIxD;IAEA,IAAI,CAAC,MAAM;QACT,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAA0B;;;;;;8BACxC,8OAAC;oBAAE,WAAU;8BAAwC;;;;;;8BACrD,8OAAC,4JAAA,CAAA,UAAI;oBACH,MAAK;oBACL,WAAU;;sCAEV,8OAAC,oKAAA,CAAA,kBAAe;4BAAC,MAAM,wKAAA,CAAA,cAAW;4BAAE,WAAU;;;;;;wBAAiB;;;;;;;;;;;;;IAKvE;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC,oKAAA,CAAA,kBAAe;wCAAC,MAAM,wKAAA,CAAA,cAAW;wCAAE,WAAU;;;;;;oCAAiB;;;;;;;0CAGjE,8OAAC;gCAAG,WAAU;;oCACX,KAAK,OAAO,CAAC,SAAS;oCAAC;oCAAE,KAAK,OAAO,CAAC,QAAQ;;;;;;;0CAEjD,8OAAC;gCAAE,WAAU;;oCAA2C;oCAC5C,KAAK,EAAE;;;;;;;;;;;;;kCAIrB,8OAAC;wBAAI,WAAU;;4BACZ,KAAK,MAAM,KAAK,yBACf,8OAAC;gCACC,SAAS,IAAM,mBAAmB;gCAClC,WAAU;;kDAEV,8OAAC,oKAAA,CAAA,kBAAe;wCAAC,MAAM,wKAAA,CAAA,SAAM;wCAAE,WAAU;;;;;;oCAAiB;;;;;;qDAI5D,8OAAC;gCACC,SAAS,IAAM,mBAAmB;gCAClC,WAAU;;kDAEV,8OAAC,oKAAA,CAAA,kBAAe;wCAAC,MAAM,wKAAA,CAAA,WAAQ;wCAAE,WAAU;;;;;;oCAAiB;;;;;;;0CAKhE,8OAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,8OAAC,oKAAA,CAAA,kBAAe;wCAAC,MAAM,wKAAA,CAAA,UAAO;wCAAE,WAAU;;;;;;oCAAiB;;;;;;;0CAI7D,8OAAC;gCACC,MAAK;gCACL,MAAK;gCACL,WAAU;;kDAEV,8OAAC,oKAAA,CAAA,kBAAe;wCAAC,MAAM,wKAAA,CAAA,SAAM;wCAAE,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAOhE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,WAAU;oCACV,KAAK,KAAK,OAAO,CAAC,SAAS,IAAI,CAAC,iCAAiC,EAAE,KAAK,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,QAAQ,CAAC,kBAAkB,CAAC;oCACtI,KAAK,GAAG,KAAK,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,QAAQ,EAAE;;;;;;;;;;;0CAG7D,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;;4CACX,KAAK,OAAO,CAAC,SAAS;4CAAC;4CAAE,KAAK,OAAO,CAAC,QAAQ;;;;;;;kDAEjD,8OAAC;wCAAE,WAAU;;4CACV,KAAK,OAAO,CAAC,QAAQ;4CAAE,KAAK,OAAO,CAAC,UAAU,GAAG,CAAC,EAAE,EAAE,KAAK,OAAO,CAAC,UAAU,EAAE,GAAG;;;;;;;kDAErF,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAW,CAAC,wEAAwE,EACxF,KAAK,MAAM,KAAK,WACZ,yEACA,KAAK,MAAM,KAAK,YACd,6EACA,KAAK,MAAM,KAAK,cACd,iEACA,oEACR;0DACC,KAAK,MAAM,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,MAAM,CAAC,KAAK,CAAC;;;;;;0DAE3D,8OAAC;gDAAK,WAAW,CAAC,wEAAwE,EACxF,KAAK,IAAI,KAAK,UACV,6EACA,KAAK,IAAI,KAAK,YACZ,qEACA,KAAK,IAAI,KAAK,SACZ,6EACA,oEACR;0DACC,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,IAAI,CAAC,KAAK,CAAC;;;;;;;;;;;;;;;;;;;;;;;;kCAO7D,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,WAAW,CAAC,2DAA2D,EACrE,cAAc,YACV,kFACA,qJACJ;oCACF,SAAS,IAAM,aAAa;8CAC7B;;;;;;8CAGD,8OAAC;oCACC,WAAW,CAAC,2DAA2D,EACrE,cAAc,aACV,kFACA,qJACJ;oCACF,SAAS,IAAM,aAAa;8CAC7B;;;;;;8CAGD,8OAAC;oCACC,WAAW,CAAC,2DAA2D,EACrE,cAAc,aACV,kFACA,qJACJ;oCACF,SAAS,IAAM,aAAa;8CAC7B;;;;;;;;;;;;;;;;;kCAOL,8OAAC;wBAAI,WAAU;;4BAEZ,cAAc,2BACb,8OAAC;gCAAK,IAAG;gCAAY,UAAU;gCAAc,WAAU;0CACrD,cAAA,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAoD;;;;;;sEAClE,8OAAC;4DAAE,WAAU;sEAAgD;;;;;;;;;;;;8DAG/D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;;sFACC,8OAAC;4EAAM,SAAQ;4EAAY,WAAU;sFAA6D;;;;;;sFAGlG,8OAAC;4EACC,MAAK;4EACL,MAAK;4EACL,IAAG;4EACH,cAAc,KAAK,OAAO,CAAC,SAAS;4EACpC,WAAU;;;;;;;;;;;;8EAGd,8OAAC;;sFACC,8OAAC;4EAAM,SAAQ;4EAAW,WAAU;sFAA6D;;;;;;sFAGjG,8OAAC;4EACC,MAAK;4EACL,MAAK;4EACL,IAAG;4EACH,cAAc,KAAK,OAAO,CAAC,QAAQ;4EACnC,WAAU;;;;;;;;;;;;;;;;;;sEAKhB,8OAAC;;8EACC,8OAAC;oEAAM,SAAQ;oEAAQ,WAAU;8EAA6D;;;;;;8EAG9F,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFACd,cAAA,8OAAC,oKAAA,CAAA,kBAAe;gFAAC,MAAM,wKAAA,CAAA,aAAU;gFAAE,WAAU;;;;;;;;;;;sFAE/C,8OAAC;4EACC,MAAK;4EACL,MAAK;4EACL,IAAG;4EACH,cAAc,KAAK,KAAK;4EACxB,WAAU;;;;;;;;;;;;;;;;;;sEAKhB,8OAAC;;8EACC,8OAAC;oEAAM,SAAQ;oEAAW,WAAU;8EAA6D;;;;;;8EAGjG,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFACd,cAAA,8OAAC,oKAAA,CAAA,kBAAe;gFAAC,MAAM,wKAAA,CAAA,SAAM;gFAAE,WAAU;;;;;;;;;;;sFAE3C,8OAAC;4EACC,MAAK;4EACL,MAAK;4EACL,IAAG;4EACH,cAAc,KAAK,QAAQ;4EAC3B,WAAU;;;;;;;;;;;;;;;;;;sEAKhB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;;sFACC,8OAAC;4EAAM,SAAQ;4EAAO,WAAU;sFAA6D;;;;;;sFAG7F,8OAAC;4EACC,IAAG;4EACH,MAAK;4EACL,cAAc,KAAK,IAAI;4EACvB,WAAU;;8FAEV,8OAAC;oFAAO,OAAM;8FAAQ;;;;;;8FACtB,8OAAC;oFAAO,OAAM;8FAAU;;;;;;8FACxB,8OAAC;oFAAO,OAAM;8FAAO;;;;;;8FACrB,8OAAC;oFAAO,OAAM;8FAAQ;;;;;;;;;;;;;;;;;;8EAG1B,8OAAC;;sFACC,8OAAC;4EAAM,SAAQ;4EAAS,WAAU;sFAA6D;;;;;;sFAG/F,8OAAC;4EACC,IAAG;4EACH,MAAK;4EACL,cAAc,KAAK,MAAM;4EACzB,WAAU;;8FAEV,8OAAC;oFAAO,OAAM;8FAAS;;;;;;8FACvB,8OAAC;oFAAO,OAAM;8FAAW;;;;;;8FACzB,8OAAC;oFAAO,OAAM;8FAAU;;;;;;8FACxB,8OAAC;oFAAO,OAAM;8FAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAQpC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAoD;;;;;;sEAClE,8OAAC;4DAAE,WAAU;sEAAgD;;;;;;;;;;;;8DAG/D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAM,SAAQ;oEAAW,WAAU;8EAA6D;;;;;;8EAGjG,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFACd,cAAA,8OAAC,oKAAA,CAAA,kBAAe;gFAAC,MAAM,wKAAA,CAAA,WAAQ;gFAAE,WAAU;;;;;;;;;;;sFAE7C,8OAAC;4EACC,MAAK;4EACL,MAAK;4EACL,IAAG;4EACH,cAAc,KAAK,OAAO,CAAC,QAAQ,IAAI;4EACvC,WAAU;;;;;;;;;;;;;;;;;;sEAKhB,8OAAC;;8EACC,8OAAC;oEAAM,SAAQ;oEAAa,WAAU;8EAA6D;;;;;;8EAGnG,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFACd,cAAA,8OAAC,oKAAA,CAAA,kBAAe;gFAAC,MAAM,wKAAA,CAAA,aAAU;gFAAE,WAAU;;;;;;;;;;;sFAE/C,8OAAC;4EACC,MAAK;4EACL,MAAK;4EACL,IAAG;4EACH,cAAc,KAAK,OAAO,CAAC,UAAU,IAAI;4EACzC,WAAU;;;;;;;;;;;;;;;;;;sEAKhB,8OAAC;;8EACC,8OAAC;oEAAM,SAAQ;oEAAW,WAAU;8EAA6D;;;;;;8EAGjG,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFACd,cAAA,8OAAC,oKAAA,CAAA,kBAAe;gFAAC,MAAM,wKAAA,CAAA,iBAAc;gFAAE,WAAU;;;;;;;;;;;sFAEnD,8OAAC;4EACC,MAAK;4EACL,MAAK;4EACL,IAAG;4EACH,cAAc,KAAK,OAAO,CAAC,QAAQ,IAAI;4EACvC,WAAU;;;;;;;;;;;;;;;;;;sEAKhB,8OAAC;;8EACC,8OAAC;oEAAM,SAAQ;oEAAc,WAAU;8EAA6D;;;;;;8EAGpG,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFACd,cAAA,8OAAC,oKAAA,CAAA,kBAAe;gFAAC,MAAM,wKAAA,CAAA,UAAO;gFAAE,WAAU;;;;;;;;;;;sFAE5C,8OAAC;4EACC,MAAK;4EACL,MAAK;4EACL,IAAG;4EACH,cAAc,KAAK,OAAO,CAAC,WAAW,IAAI;4EAC1C,WAAU;;;;;;;;;;;;;;;;;;sEAKhB,8OAAC;;8EACC,8OAAC;oEAAM,SAAQ;oEAAM,WAAU;8EAA6D;;;;;;8EAG5F,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEACC,IAAG;wEACH,MAAK;wEACL,MAAM;wEACN,cAAc,KAAK,OAAO,CAAC,GAAG,IAAI;wEAClC,WAAU;;;;;;;;;;;8EAGd,8OAAC;oEAAE,WAAU;8EAAgD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAQxE,cAAc,4BACb,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAoD;;;;;;0DAClE,8OAAC;gDAAE,WAAU;0DAAgD;;;;;;0DAE7D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEACC,IAAG;oEACH,MAAK;oEACL,MAAK;oEACL,gBAAgB,KAAK,WAAW,CAAC,eAAe;oEAChD,WAAU;;;;;;;;;;;0EAGd,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAM,SAAQ;wEAAkB,WAAU;kFAA+C;;;;;;kFAG1F,8OAAC;wEAAE,WAAU;kFAAmC;;;;;;;;;;;;;;;;;;kEAIpD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEACC,IAAG;oEACH,MAAK;oEACL,MAAK;oEACL,gBAAgB,KAAK,WAAW,CAAC,aAAa;oEAC9C,WAAU;;;;;;;;;;;0EAGd,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAM,SAAQ;wEAAgB,WAAU;kFAA+C;;;;;;kFAGxF,8OAAC;wEAAE,WAAU;kFAAmC;;;;;;;;;;;;;;;;;;kEAIpD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEACC,IAAG;oEACH,MAAK;oEACL,MAAK;oEACL,gBAAgB,KAAK,WAAW,CAAC,eAAe;oEAChD,WAAU;;;;;;;;;;;0EAGd,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAM,SAAQ;wEAAkB,WAAU;kFAA+C;;;;;;kFAG1F,8OAAC;wEAAE,WAAU;kFAAmC;;;;;;;;;;;;;;;;;;kEAIpD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEACC,IAAG;oEACH,MAAK;oEACL,MAAK;oEACL,gBAAgB,KAAK,WAAW,CAAC,cAAc;oEAC/C,WAAU;;;;;;;;;;;0EAGd,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAM,SAAQ;wEAAiB,WAAU;kFAA+C;;;;;;kFAGzF,8OAAC;wEAAE,WAAU;kFAAmC;;;;;;;;;;;;;;;;;;kEAIpD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEACC,IAAG;oEACH,MAAK;oEACL,MAAK;oEACL,gBAAgB,KAAK,WAAW,CAAC,cAAc;oEAC/C,WAAU;;;;;;;;;;;0EAGd,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAM,SAAQ;wEAAiB,WAAU;kFAA+C;;;;;;kFAGzF,8OAAC;wEAAE,WAAU;kFAAmC;;;;;;;;;;;;;;;;;;kEAIpD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEACC,IAAG;oEACH,MAAK;oEACL,MAAK;oEACL,gBAAgB,KAAK,WAAW,CAAC,kBAAkB;oEACnD,WAAU;;;;;;;;;;;0EAGd,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAM,SAAQ;wEAAqB,WAAU;kFAA+C;;;;;;kFAG7F,8OAAC;wEAAE,WAAU;kFAAmC;;;;;;;;;;;;;;;;;;kEAIpD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEACC,IAAG;oEACH,MAAK;oEACL,MAAK;oEACL,gBAAgB,KAAK,WAAW,CAAC,gBAAgB;oEACjD,WAAU;;;;;;;;;;;0EAGd,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAM,SAAQ;wEAAmB,WAAU;kFAA+C;;;;;;kFAG3F,8OAAC;wEAAE,WAAU;kFAAmC;;;;;;;;;;;;;;;;;;kEAIpD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEACC,IAAG;oEACH,MAAK;oEACL,MAAK;oEACL,gBAAgB,KAAK,WAAW,CAAC,iBAAiB;oEAClD,WAAU;;;;;;;;;;;0EAGd,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAM,SAAQ;wEAAoB,WAAU;kFAA+C;;;;;;kFAG5F,8OAAC;wEAAE,WAAU;kFAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOxD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAoD;;;;;;0DAClE,8OAAC;gDAAE,WAAU;0DAAgD;;;;;;0DAE7D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAM,SAAQ;gEAAW,WAAU;0EAA6D;;;;;;0EAGjG,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEACC,MAAK;oEACL,MAAK;oEACL,IAAG;oEACH,WAAU;oEACV,aAAY;;;;;;;;;;;0EAGhB,8OAAC;gEAAE,WAAU;;oEAAgD;oEACF,KAAK,kBAAkB,GAAG,IAAI,KAAK,KAAK,kBAAkB,EAAE,kBAAkB,KAAK;;;;;;;;;;;;;kEAIhJ,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEACC,IAAG;oEACH,MAAK;oEACL,MAAK;oEACL,gBAAgB,KAAK,QAAQ,CAAC,gBAAgB;oEAC9C,WAAU;;;;;;;;;;;0EAGd,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAM,SAAQ;wEAAmB,WAAU;kFAA+C;;;;;;kFAG3F,8OAAC;wEAAE,WAAU;kFAAmC;;;;;;;;;;;;;;;;;;kEAIpD,8OAAC;;0EACC,8OAAC;gEAAM,SAAQ;gEAAiB,WAAU;0EAA6D;;;;;;0EAGvG,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEACC,MAAK;oEACL,MAAK;oEACL,IAAG;oEACH,cAAc;oEACd,KAAK;oEACL,KAAK;oEACL,WAAU;;;;;;;;;;;0EAGd,8OAAC;gEAAE,WAAU;0EAAgD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAStE,cAAc,4BACb,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAoD;;;;;;0DAClE,8OAAC;gDAAE,WAAU;0DAAgD;;;;;;0DAE7D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAAgE;;;;;;8EAC9E,8OAAC;oEAAG,WAAU;8EAA6D,KAAK,QAAQ,CAAC,WAAW;;;;;;;;;;;;;;;;;kEAIxG,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAAgE;;;;;;8EAC9E,8OAAC;oEAAG,WAAU;8EAA6D,KAAK,QAAQ,CAAC,aAAa;;;;;;;;;;;;;;;;;kEAI1G,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAAgE;;;;;;8EAC9E,8OAAC;oEAAG,WAAU;;wEACX,KAAK,KAAK,CAAC,KAAK,QAAQ,CAAC,sBAAsB,GAAG;wEAAI;;;;;;;;;;;;;;;;;;kEAK7D,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAAgE;;;;;;8EAC9E,8OAAC;oEAAG,WAAU;8EACX,IAAI,KAAK,KAAK,QAAQ,CAAC,UAAU,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQhE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAoD;;;;;;0DAClE,8OAAC;gDAAE,WAAU;0DAAgD;;;;;;0DAE7D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAsD;;;;;;0EACpE,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAE,WAAU;8EACV,KAAK,QAAQ,CAAC,eAAe,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,OAC5C,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,IAC1C,IAAI,CAAC;;;;;;;;;;;0EAGX,8OAAC;gEAAE,WAAU;0EAAgD;;;;;;;;;;;;kEAK/D,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAsD;;;;;;0EACpE,8OAAC;gEAAI,WAAU;0EACZ,KAAK,QAAQ,CAAC,cAAc,CAAC,MAAM,GAAG,kBACrC,8OAAC;oEAAG,WAAU;8EACX,KAAK,QAAQ,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,wBACjC,8OAAC;;gFAAiB;gFAAO;;2EAAhB;;;;;;;;;yFAIb,8OAAC;oEAAE,WAAU;8EAAmC;;;;;;;;;;;0EAGpD,8OAAC;gEAAE,WAAU;0EAAgD;;;;;;;;;;;;;;;;;;;;;;;;kDAQnE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAoD;;;;;;0DAClE,8OAAC;gDAAE,WAAU;0DAAgD;;;;;;0DAE7D,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC;gFAAE,WAAU;0FAAoD;;;;;;0FACjE,8OAAC;gFAAE,WAAU;0FAA2C;;;;;;;;;;;;kFAE1D,8OAAC;wEAAE,WAAU;kFAA4C,IAAI,KAAK,KAAK,QAAQ,CAAC,SAAS,EAAE,cAAc;;;;;;;;;;;;;;;;;sEAG7G,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC;gFAAE,WAAU;0FAAoD;;;;;;0FACjE,8OAAC;gFAAE,WAAU;0FAA2C;;;;;;;;;;;;kFAE1D,8OAAC;wEAAE,WAAU;kFAA4C,IAAI,KAAK,KAAK,QAAQ,CAAC,UAAU,EAAE,cAAc;;;;;;;;;;;;;;;;;sEAG9G,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC;gFAAE,WAAU;0FAAoD;;;;;;0FACjE,8OAAC;gFAAE,WAAU;0FAA2C;;;;;;;;;;;;kFAE1D,8OAAC;wEAAE,WAAU;kFAA4C,IAAI,KAAK,KAAK,SAAS,EAAE,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAKxG,8OAAC;gDAAE,WAAU;0DAAgD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU7E", "debugId": null}}]}