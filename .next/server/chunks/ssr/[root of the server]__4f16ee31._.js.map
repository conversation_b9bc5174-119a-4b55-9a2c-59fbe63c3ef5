{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { User, AuthState, LoginCredentials, RegisterCredentials } from '@/types/auth';\n\ninterface AuthContextType extends AuthState {\n  login: (credentials: LoginCredentials) => Promise<void>;\n  register: (credentials: RegisterCredentials) => Promise<void>;\n  logout: () => Promise<void>;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const router = useRouter();\n\n  const [authState, setAuthState] = useState<AuthState>({\n    user: null,\n    isLoading: true,\n    error: null,\n  });\n\n  // Load user from localStorage on mount\n  useEffect(() => {\n    const loadUser = () => {\n      try {\n        console.log('Loading user from localStorage...');\n        const userJson = localStorage.getItem('user');\n        console.log('User JSON from localStorage:', userJson);\n\n        if (userJson) {\n          const user = JSON.parse(userJson) as User;\n          console.log('Parsed user:', user);\n          setAuthState({\n            user,\n            isLoading: false,\n            error: null,\n          });\n        } else {\n          console.log('No user found in localStorage');\n          setAuthState({\n            user: null,\n            isLoading: false,\n            error: null,\n          });\n        }\n      } catch (error) {\n        console.error('Error loading user from localStorage:', error);\n        setAuthState({\n          user: null,\n          isLoading: false,\n          error: 'Failed to load user data',\n        });\n      }\n    };\n\n    // Only run in browser environment\n    if (typeof window !== 'undefined') {\n      loadUser();\n    }\n  }, []);\n\n  const login = async (credentials: LoginCredentials) => {\n    console.log('Login function called with:', credentials.email);\n    try {\n      console.log('Setting loading state...');\n      setAuthState((prev) => ({ ...prev, isLoading: true, error: null }));\n\n      console.log('Sending login request to API...');\n      const response = await fetch('/api/login', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(credentials),\n      });\n\n      console.log('API response status:', response.status);\n      const data = await response.json();\n      console.log('API response data:', data);\n\n      if (!response.ok || !data.success) {\n        setAuthState((prev) => ({\n          ...prev,\n          isLoading: false,\n          error: data.message || 'Login failed',\n        }));\n        return;\n      }\n\n      console.log('Login successful, storing user data...');\n      // Store user in localStorage\n      localStorage.setItem('user', JSON.stringify(data.user));\n\n      // Also set a cookie for the middleware\n      document.cookie = `user=${JSON.stringify(data.user)}; path=/; max-age=86400`;\n\n      console.log('Updating auth state...');\n      // Update auth state\n      setAuthState({\n        user: data.user,\n        isLoading: false,\n        error: null,\n      });\n\n      console.log('Redirecting to homepage...');\n      router.push('/');\n    } catch (error) {\n      setAuthState((prev) => ({\n        ...prev,\n        isLoading: false,\n        error: 'An unexpected error occurred',\n      }));\n    }\n  };\n\n  const register = async (credentials: RegisterCredentials) => {\n    try {\n      setAuthState((prev) => ({ ...prev, isLoading: true, error: null }));\n\n      // In a real app, this would be an API call to create a new user\n      const response = await fetch('/api/auth/register', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(credentials),\n      });\n\n      const data = await response.json();\n\n      if (!response.ok) {\n        setAuthState((prev) => ({\n          ...prev,\n          isLoading: false,\n          error: data.message || 'Registration failed',\n        }));\n        return;\n      }\n\n      // Automatically log in after successful registration\n      await login({\n        email: credentials.email,\n        password: credentials.password,\n      });\n    } catch (error) {\n      setAuthState((prev) => ({\n        ...prev,\n        isLoading: false,\n        error: 'An unexpected error occurred',\n      }));\n    }\n  };\n\n  const logout = async () => {\n    try {\n      setAuthState((prev) => ({ ...prev, isLoading: true }));\n\n      // Remove user from localStorage\n      localStorage.removeItem('user');\n\n      // Also clear the cookie\n      document.cookie = 'user=; path=/; max-age=0';\n\n      // Update auth state\n      setAuthState({\n        user: null,\n        isLoading: false,\n        error: null,\n      });\n\n      router.push('/auth/login');\n    } catch (error) {\n      setAuthState((prev) => ({\n        ...prev,\n        isLoading: false,\n        error: 'Logout failed',\n      }));\n    }\n  };\n\n  return (\n    <AuthContext.Provider\n      value={{\n        ...authState,\n        login,\n        register,\n        logout,\n      }}\n    >\n      {children}\n    </AuthContext.Provider>\n  );\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAYA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;IACtE,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;QACpD,MAAM;QACN,WAAW;QACX,OAAO;IACT;IAEA,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW;YACf,IAAI;gBACF,QAAQ,GAAG,CAAC;gBACZ,MAAM,WAAW,aAAa,OAAO,CAAC;gBACtC,QAAQ,GAAG,CAAC,gCAAgC;gBAE5C,IAAI,UAAU;oBACZ,MAAM,OAAO,KAAK,KAAK,CAAC;oBACxB,QAAQ,GAAG,CAAC,gBAAgB;oBAC5B,aAAa;wBACX;wBACA,WAAW;wBACX,OAAO;oBACT;gBACF,OAAO;oBACL,QAAQ,GAAG,CAAC;oBACZ,aAAa;wBACX,MAAM;wBACN,WAAW;wBACX,OAAO;oBACT;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yCAAyC;gBACvD,aAAa;oBACX,MAAM;oBACN,WAAW;oBACX,OAAO;gBACT;YACF;QACF;QAEA,kCAAkC;QAClC,uCAAmC;;QAEnC;IACF,GAAG,EAAE;IAEL,MAAM,QAAQ,OAAO;QACnB,QAAQ,GAAG,CAAC,+BAA+B,YAAY,KAAK;QAC5D,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,aAAa,CAAC,OAAS,CAAC;oBAAE,GAAG,IAAI;oBAAE,WAAW;oBAAM,OAAO;gBAAK,CAAC;YAEjE,QAAQ,GAAG,CAAC;YACZ,MAAM,WAAW,MAAM,MAAM,cAAc;gBACzC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,QAAQ,GAAG,CAAC,wBAAwB,SAAS,MAAM;YACnD,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,QAAQ,GAAG,CAAC,sBAAsB;YAElC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK,OAAO,EAAE;gBACjC,aAAa,CAAC,OAAS,CAAC;wBACtB,GAAG,IAAI;wBACP,WAAW;wBACX,OAAO,KAAK,OAAO,IAAI;oBACzB,CAAC;gBACD;YACF;YAEA,QAAQ,GAAG,CAAC;YACZ,6BAA6B;YAC7B,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC,KAAK,IAAI;YAErD,uCAAuC;YACvC,SAAS,MAAM,GAAG,CAAC,KAAK,EAAE,KAAK,SAAS,CAAC,KAAK,IAAI,EAAE,uBAAuB,CAAC;YAE5E,QAAQ,GAAG,CAAC;YACZ,oBAAoB;YACpB,aAAa;gBACX,MAAM,KAAK,IAAI;gBACf,WAAW;gBACX,OAAO;YACT;YAEA,QAAQ,GAAG,CAAC;YACZ,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,aAAa,CAAC,OAAS,CAAC;oBACtB,GAAG,IAAI;oBACP,WAAW;oBACX,OAAO;gBACT,CAAC;QACH;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,IAAI;YACF,aAAa,CAAC,OAAS,CAAC;oBAAE,GAAG,IAAI;oBAAE,WAAW;oBAAM,OAAO;gBAAK,CAAC;YAEjE,gEAAgE;YAChE,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,aAAa,CAAC,OAAS,CAAC;wBACtB,GAAG,IAAI;wBACP,WAAW;wBACX,OAAO,KAAK,OAAO,IAAI;oBACzB,CAAC;gBACD;YACF;YAEA,qDAAqD;YACrD,MAAM,MAAM;gBACV,OAAO,YAAY,KAAK;gBACxB,UAAU,YAAY,QAAQ;YAChC;QACF,EAAE,OAAO,OAAO;YACd,aAAa,CAAC,OAAS,CAAC;oBACtB,GAAG,IAAI;oBACP,WAAW;oBACX,OAAO;gBACT,CAAC;QACH;IACF;IAEA,MAAM,SAAS;QACb,IAAI;YACF,aAAa,CAAC,OAAS,CAAC;oBAAE,GAAG,IAAI;oBAAE,WAAW;gBAAK,CAAC;YAEpD,gCAAgC;YAChC,aAAa,UAAU,CAAC;YAExB,wBAAwB;YACxB,SAAS,MAAM,GAAG;YAElB,oBAAoB;YACpB,aAAa;gBACX,MAAM;gBACN,WAAW;gBACX,OAAO;YACT;YAEA,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,aAAa,CAAC,OAAS,CAAC;oBACtB,GAAG,IAAI;oBACP,WAAW;oBACX,OAAO;gBACT,CAAC;QACH;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QACnB,OAAO;YACL,GAAG,SAAS;YACZ;YACA;YACA;QACF;kBAEC;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 234, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Image from 'next/image';\nimport Link from 'next/link';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport {\n  faSearch,\n  faBell,\n  faUser,\n  faGear,\n  faRightFromBracket,\n  faMoon,\n  faSun,\n  faBars\n} from '@fortawesome/free-solid-svg-icons';\n\nexport default function Header() {\n  const { user, logout } = useAuth();\n  const [isProfileOpen, setIsProfileOpen] = useState(false);\n\n  return (\n    <header className=\"fixed top-0 left-0 right-0 z-50 w-full\">\n      <div className=\"flex h-16 items-center justify-between px-4 md:px-6\">\n        <div className=\"flex items-center\">\n          {/* Hamburger menu button (visible only on tablet/mobile) */}\n          <button\n            type=\"button\"\n            className=\"p-2 text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300\"\n            onClick={() => {\n              if (typeof window !== 'undefined') {\n                // Toggle sidebar-open class for mobile view\n                document.documentElement.classList.toggle('sidebar-open');\n\n                // Dispatch a custom event to notify the sidebar component\n                window.dispatchEvent(new Event('sidebar-toggle'));\n              }\n            }}\n            aria-label=\"Toggle menu\"\n          >\n            <FontAwesomeIcon icon={faBars} className=\"h-6 w-6\" />\n            <span className=\"sr-only\">Toggle sidebar</span>\n          </button>\n\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center ml-4 md:ml-0\">\n            <div className=\"relative h-8 w-8 mr-2\">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                viewBox=\"0 0 24 24\"\n                fill=\"none\"\n                stroke=\"currentColor\"\n                strokeWidth=\"2\"\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                className=\"h-8 w-8 text-blue-600\"\n              >\n                <path d=\"M12 2a4 4 0 0 1 4 4v4a4 4 0 0 1-4 4 4 4 0 0 1-4-4V6a4 4 0 0 1 4-4z\" />\n                <path d=\"M18 8a4 4 0 0 1 4 4v2a4 4 0 0 1-4 4 4 4 0 0 1-4-4v-2a4 4 0 0 1 4-4z\" />\n                <path d=\"M6 8a4 4 0 0 1 4 4v2a4 4 0 0 1-4 4 4 4 0 0 1-4-4v-2a4 4 0 0 1 4-4z\" />\n              </svg>\n            </div>\n            <span className=\"text-xl font-semibold text-gray-900 dark:text-white\">AI Hub</span>\n          </Link>\n        </div>\n\n        <div className=\"flex items-center space-x-4\">\n          {/* Search */}\n          <div className=\"hidden md:flex relative\">\n            <div className=\"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none\">\n              <FontAwesomeIcon icon={faSearch} className=\"h-4 w-4 text-gray-400\" />\n            </div>\n            <input\n              type=\"search\"\n              className=\"block w-full rounded-md border border-gray-200 bg-gray-50 py-2 pl-10 pr-3 text-sm text-gray-900 placeholder:text-gray-500 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white dark:placeholder:text-gray-400\"\n              placeholder=\"Search agents...\"\n            />\n          </div>\n\n          {/* Notifications */}\n          <button\n            type=\"button\"\n            className=\"p-2 text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300\"\n          >\n            <FontAwesomeIcon icon={faBell} className=\"h-6 w-6\" />\n            <span className=\"sr-only\">Notifications</span>\n          </button>\n\n          {/* Profile dropdown */}\n          <div className=\"relative\">\n            {user ? (\n              <>\n                <button\n                  type=\"button\"\n                  className=\"flex items-center rounded-full\"\n                  onClick={() => setIsProfileOpen(!isProfileOpen)}\n                >\n                  <div className=\"relative h-8 w-8 rounded-full bg-gray-200 overflow-hidden\">\n                    {user.image ? (\n                      <Image\n                        src={user.image}\n                        alt={user.name || 'User'}\n                        width={32}\n                        height={32}\n                        className=\"h-full w-full object-cover\"\n                      />\n                    ) : (\n                      <FontAwesomeIcon icon={faUser} className=\"absolute h-8 w-8 text-gray-400\" />\n                    )}\n                  </div>\n                </button>\n\n                {isProfileOpen && (\n                  <div className=\"absolute right-0 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-gray-800 dark:ring-gray-700\">\n                    <div className=\"border-b border-gray-200 px-4 py-2 dark:border-gray-700\">\n                      <p className=\"text-sm font-medium text-gray-900 dark:text-white\">{user.name}</p>\n                      <p className=\"truncate text-xs text-gray-500 dark:text-gray-400\">{user.email}</p>\n                    </div>\n                    <Link\n                      href=\"/profile\"\n                      className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700\"\n                      onClick={() => setIsProfileOpen(false)}\n                    >\n                      Your Profile\n                    </Link>\n                    <Link\n                      href=\"/settings\"\n                      className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700\"\n                      onClick={() => setIsProfileOpen(false)}\n                    >\n                      Settings\n                    </Link>\n                    <button\n                      className=\"block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700\"\n                      onClick={() => {\n                        setIsProfileOpen(false);\n                        logout();\n                      }}\n                    >\n                      Sign out\n                    </button>\n                  </div>\n                )}\n              </>\n            ) : (\n              <Link\n                href=\"/auth/login\"\n                className=\"rounded-md bg-blue-600 px-3 py-1.5 text-sm font-medium text-white hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800\"\n              >\n                Sign in\n              </Link>\n            )}\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AAkBe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BACC,MAAK;4BACL,WAAU;4BACV,SAAS;gCACP,uCAAmC;;gCAMnC;4BACF;4BACA,cAAW;;8CAEX,8OAAC,oKAAA,CAAA,kBAAe;oCAAC,MAAM,wKAAA,CAAA,SAAM;oCAAE,WAAU;;;;;;8CACzC,8OAAC;oCAAK,WAAU;8CAAU;;;;;;;;;;;;sCAI5B,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,OAAM;wCACN,SAAQ;wCACR,MAAK;wCACL,QAAO;wCACP,aAAY;wCACZ,eAAc;wCACd,gBAAe;wCACf,WAAU;;0DAEV,8OAAC;gDAAK,GAAE;;;;;;0DACR,8OAAC;gDAAK,GAAE;;;;;;0DACR,8OAAC;gDAAK,GAAE;;;;;;;;;;;;;;;;;8CAGZ,8OAAC;oCAAK,WAAU;8CAAsD;;;;;;;;;;;;;;;;;;8BAI1E,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,oKAAA,CAAA,kBAAe;wCAAC,MAAM,wKAAA,CAAA,WAAQ;wCAAE,WAAU;;;;;;;;;;;8CAE7C,8OAAC;oCACC,MAAK;oCACL,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAKhB,8OAAC;4BACC,MAAK;4BACL,WAAU;;8CAEV,8OAAC,oKAAA,CAAA,kBAAe;oCAAC,MAAM,wKAAA,CAAA,SAAM;oCAAE,WAAU;;;;;;8CACzC,8OAAC;oCAAK,WAAU;8CAAU;;;;;;;;;;;;sCAI5B,8OAAC;4BAAI,WAAU;sCACZ,qBACC;;kDACE,8OAAC;wCACC,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,iBAAiB,CAAC;kDAEjC,cAAA,8OAAC;4CAAI,WAAU;sDACZ,KAAK,KAAK,iBACT,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAK,KAAK,KAAK;gDACf,KAAK,KAAK,IAAI,IAAI;gDAClB,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;qEAGZ,8OAAC,oKAAA,CAAA,kBAAe;gDAAC,MAAM,wKAAA,CAAA,SAAM;gDAAE,WAAU;;;;;;;;;;;;;;;;oCAK9C,+BACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAqD,KAAK,IAAI;;;;;;kEAC3E,8OAAC;wDAAE,WAAU;kEAAqD,KAAK,KAAK;;;;;;;;;;;;0DAE9E,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,iBAAiB;0DACjC;;;;;;0DAGD,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,iBAAiB;0DACjC;;;;;;0DAGD,8OAAC;gDACC,WAAU;gDACV,SAAS;oDACP,iBAAiB;oDACjB;gDACF;0DACD;;;;;;;;;;;;;6DAOP,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}, {"offset": {"line": 568, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport {\n  faHome,\n  faTableCells,\n  faChartLine,\n  faHeart,\n  faGear,\n  faCircleQuestion,\n  faHistory,\n  faBookmark,\n  faUsers,\n  faBars,\n  faGraduationCap,\n  faVideo,\n  faNewspaper,\n  faBlog,\n  faToolbox,\n  faShieldAlt,\n  faUserCog,\n  faSitemap\n} from '@fortawesome/free-solid-svg-icons';\n\ntype NavItem = {\n  name: string;\n  href: string;\n  icon: React.ReactNode;\n  group?: string; // Optional group for visual separation\n};\n\nexport default function Sidebar() {\n  const pathname = usePathname();\n  const [isCollapsed, setIsCollapsed] = useState(false);\n  const [isMounted, setIsMounted] = useState(false);\n\n  // Define all navigation items in a flat list\n  const navigationItems: NavItem[] = [\n    // Discover group\n    {\n      name: 'Home',\n      href: '/',\n      icon: <FontAwesomeIcon icon={faHome} className=\"h-5 w-5\" />,\n      group: 'Discover'\n    },\n    {\n      name: 'Browse Agents',\n      href: '/browse',\n      icon: <FontAwesomeIcon icon={faTableCells} className=\"h-5 w-5\" />,\n      group: 'Discover'\n    },\n    {\n      name: 'Popular Agents',\n      href: '/popular',\n      icon: <FontAwesomeIcon icon={faChartLine} className=\"h-5 w-5\" />,\n      group: 'Discover'\n    },\n    {\n      name: 'New Releases',\n      href: '/new-releases',\n      icon: <FontAwesomeIcon icon={faCircleQuestion} className=\"h-5 w-5\" />,\n      group: 'Discover'\n    },\n\n    // My Agents group\n    {\n      name: 'Favorites',\n      href: '/favorites',\n      icon: <FontAwesomeIcon icon={faHeart} className=\"h-5 w-5\" />,\n      group: 'My Agents'\n    },\n    {\n      name: 'Recent Sessions',\n      href: '/recent',\n      icon: <FontAwesomeIcon icon={faHistory} className=\"h-5 w-5\" />,\n      group: 'My Agents'\n    },\n    {\n      name: 'Saved Sessions',\n      href: '/saved',\n      icon: <FontAwesomeIcon icon={faBookmark} className=\"h-5 w-5\" />,\n      group: 'My Agents'\n    },\n\n    // Learning group\n    {\n      name: 'Learning Hub',\n      href: '/learning',\n      icon: <FontAwesomeIcon icon={faGraduationCap} className=\"h-5 w-5\" />,\n      group: 'Learning'\n    },\n    {\n      name: 'Videos',\n      href: '/learning/videos',\n      icon: <FontAwesomeIcon icon={faVideo} className=\"h-5 w-5\" />,\n      group: 'Learning'\n    },\n    {\n      name: 'Articles',\n      href: '/learning/articles',\n      icon: <FontAwesomeIcon icon={faNewspaper} className=\"h-5 w-5\" />,\n      group: 'Learning'\n    },\n    {\n      name: 'Blog',\n      href: '/learning/blog',\n      icon: <FontAwesomeIcon icon={faBlog} className=\"h-5 w-5\" />,\n      group: 'Learning'\n    },\n    {\n      name: 'Resources',\n      href: '/learning/resources',\n      icon: <FontAwesomeIcon icon={faToolbox} className=\"h-5 w-5\" />,\n      group: 'Learning'\n    },\n\n    // Admin group\n    {\n      name: 'Admin Dashboard',\n      href: '/admin',\n      icon: <FontAwesomeIcon icon={faUserCog} className=\"h-5 w-5\" />,\n      group: 'Admin'\n    },\n    {\n      name: 'AI Organization',\n      href: '/admin/ai-org',\n      icon: <FontAwesomeIcon icon={faSitemap} className=\"h-5 w-5\" />,\n      group: 'Admin'\n    },\n\n  ];\n\n  // Use useEffect to handle client-side only code\n  React.useEffect(() => {\n    setIsMounted(true);\n  }, []);\n\n  // Load collapsed state from localStorage on mount and listen for changes\n  React.useEffect(() => {\n    if (typeof window !== 'undefined') {\n      // Initial load of collapsed state\n      const loadCollapsedState = () => {\n        const savedState = localStorage.getItem('sidebarCollapsed');\n        if (savedState !== null) {\n          setIsCollapsed(savedState === 'true');\n        }\n      };\n\n      // Load initial state\n      loadCollapsedState();\n\n      // Handle sidebar toggle event from header\n      const handleSidebarToggle = () => {\n        // Update collapsed state from localStorage\n        loadCollapsedState();\n\n        // Force a re-render when the sidebar-open class changes for mobile\n        if (window.innerWidth < 768) {\n          setIsMounted(prev => !prev);\n          setIsMounted(prev => !prev);\n        }\n      };\n\n      // Listen for the custom sidebar-toggle event\n      window.addEventListener('sidebar-toggle', handleSidebarToggle);\n\n      // Also listen for storage events (in case localStorage changes in another tab)\n      window.addEventListener('storage', (e) => {\n        if (e.key === 'sidebarCollapsed') {\n          loadCollapsedState();\n        }\n      });\n\n      return () => {\n        window.removeEventListener('sidebar-toggle', handleSidebarToggle);\n        window.removeEventListener('storage', loadCollapsedState);\n      };\n    }\n  }, []);\n\n  return (\n    <aside\n      className={`fixed left-0 z-50 flex flex-col h-[calc(100vh-64px)] transition-all duration-300 ${\n        isCollapsed ? 'w-16' : 'w-64'\n      } md:translate-x-0 ${\n        isMounted && typeof window !== 'undefined' && document.documentElement.classList.contains('sidebar-open')\n          ? 'translate-x-0'\n          : '-translate-x-full'\n      }`}\n    >\n      <div className=\"flex h-16 items-center justify-between border-b border-gray-200/30 px-4 dark:border-gray-800/30 mt-2\">\n        <span className={`text-container text-lg font-semibold text-gray-900 dark:text-white whitespace-nowrap overflow-hidden ${isCollapsed ? 'hidden' : ''}`}>\n          AI Hub\n        </span>\n        {isCollapsed && <div className=\"w-full\"></div>}\n\n        {/* Hamburger toggle button (visible only on desktop) */}\n        <button\n          type=\"button\"\n          className={`p-2 text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300 ${isCollapsed ? 'mx-auto' : ''}`}\n          onClick={() => {\n            const newState = !isCollapsed;\n            setIsCollapsed(newState);\n\n            // Store preference in localStorage\n            if (typeof window !== 'undefined') {\n              localStorage.setItem('sidebarCollapsed', newState.toString());\n\n              // Dispatch a custom event to notify other components\n              window.dispatchEvent(new Event('sidebar-toggle'));\n            }\n          }}\n          aria-label=\"Toggle sidebar\"\n        >\n          <FontAwesomeIcon icon={faBars} className=\"h-5 w-5\" />\n          <span className=\"sr-only\">Toggle sidebar</span>\n        </button>\n      </div>\n\n      {/* Horizontal line to separate toggle from navigation */}\n      <div className=\"border-b border-gray-200/30 dark:border-gray-800/30 my-2\"></div>\n\n      <div className=\"flex-1 overflow-y-auto p-4 h-full\">\n        <nav className=\"space-y-6\">\n          {/* Group items by their group property */}\n          {Object.entries(\n            navigationItems.reduce((acc, item) => {\n              const group = item.group || 'Other';\n              if (!acc[group]) acc[group] = [];\n              acc[group].push(item);\n              return acc;\n            }, {} as Record<string, NavItem[]>)\n          ).map(([group, items]) => (\n            <div key={group} className=\"space-y-2\">\n              {/* Show group name only when sidebar is expanded */}\n              {!isCollapsed && (\n                <div className=\"text-container mb-2 px-3 text-xs font-semibold uppercase tracking-wider text-gray-500 dark:text-gray-400 whitespace-nowrap overflow-hidden\">\n                  {group}\n                </div>\n              )}\n\n              <ul className=\"space-y-1\">\n                {items.map((item) => (\n                  <li key={item.name}>\n                    <Link\n                      href={item.href}\n                      className={`flex items-center rounded-md px-3 py-2 text-sm font-medium ${\n                        pathname === item.href\n                          ? 'bg-blue-50 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400'\n                          : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800'\n                      } ${isCollapsed ? 'justify-center' : ''}`}\n                      title={isCollapsed ? item.name : undefined}\n                    >\n                      <span className={`icon-container ${isCollapsed ? '' : 'mr-3'}`}>{item.icon}</span>\n                      {!isCollapsed && <span className=\"text-container whitespace-nowrap overflow-hidden\">{item.name}</span>}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n          ))}\n        </nav>\n      </div>\n    </aside>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAkCe,SAAS;IACtB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,6CAA6C;IAC7C,MAAM,kBAA6B;QACjC,iBAAiB;QACjB;YACE,MAAM;YACN,MAAM;YACN,oBAAM,8OAAC,oKAAA,CAAA,kBAAe;gBAAC,MAAM,wKAAA,CAAA,SAAM;gBAAE,WAAU;;;;;;YAC/C,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,oBAAM,8OAAC,oKAAA,CAAA,kBAAe;gBAAC,MAAM,wKAAA,CAAA,eAAY;gBAAE,WAAU;;;;;;YACrD,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,oBAAM,8OAAC,oKAAA,CAAA,kBAAe;gBAAC,MAAM,wKAAA,CAAA,cAAW;gBAAE,WAAU;;;;;;YACpD,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,oBAAM,8OAAC,oKAAA,CAAA,kBAAe;gBAAC,MAAM,wKAAA,CAAA,mBAAgB;gBAAE,WAAU;;;;;;YACzD,OAAO;QACT;QAEA,kBAAkB;QAClB;YACE,MAAM;YACN,MAAM;YACN,oBAAM,8OAAC,oKAAA,CAAA,kBAAe;gBAAC,MAAM,wKAAA,CAAA,UAAO;gBAAE,WAAU;;;;;;YAChD,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,oBAAM,8OAAC,oKAAA,CAAA,kBAAe;gBAAC,MAAM,wKAAA,CAAA,YAAS;gBAAE,WAAU;;;;;;YAClD,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,oBAAM,8OAAC,oKAAA,CAAA,kBAAe;gBAAC,MAAM,wKAAA,CAAA,aAAU;gBAAE,WAAU;;;;;;YACnD,OAAO;QACT;QAEA,iBAAiB;QACjB;YACE,MAAM;YACN,MAAM;YACN,oBAAM,8OAAC,oKAAA,CAAA,kBAAe;gBAAC,MAAM,wKAAA,CAAA,kBAAe;gBAAE,WAAU;;;;;;YACxD,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,oBAAM,8OAAC,oKAAA,CAAA,kBAAe;gBAAC,MAAM,wKAAA,CAAA,UAAO;gBAAE,WAAU;;;;;;YAChD,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,oBAAM,8OAAC,oKAAA,CAAA,kBAAe;gBAAC,MAAM,wKAAA,CAAA,cAAW;gBAAE,WAAU;;;;;;YACpD,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,oBAAM,8OAAC,oKAAA,CAAA,kBAAe;gBAAC,MAAM,wKAAA,CAAA,SAAM;gBAAE,WAAU;;;;;;YAC/C,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,oBAAM,8OAAC,oKAAA,CAAA,kBAAe;gBAAC,MAAM,wKAAA,CAAA,YAAS;gBAAE,WAAU;;;;;;YAClD,OAAO;QACT;QAEA,cAAc;QACd;YACE,MAAM;YACN,MAAM;YACN,oBAAM,8OAAC,oKAAA,CAAA,kBAAe;gBAAC,MAAM,wKAAA,CAAA,YAAS;gBAAE,WAAU;;;;;;YAClD,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,oBAAM,8OAAC,oKAAA,CAAA,kBAAe;gBAAC,MAAM,wKAAA,CAAA,YAAS;gBAAE,WAAU;;;;;;YAClD,OAAO;QACT;KAED;IAED,gDAAgD;IAChD,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,aAAa;IACf,GAAG,EAAE;IAEL,yEAAyE;IACzE,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,uCAAmC;;QAsCnC;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QACC,WAAW,CAAC,iFAAiF,EAC3F,cAAc,SAAS,OACxB,kBAAkB,EACjB,aAAa,gBAAkB,eAAe,SAAS,eAAe,CAAC,SAAS,CAAC,QAAQ,CAAC,yDAEtF,qBACJ;;0BAEF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAW,CAAC,qGAAqG,EAAE,cAAc,WAAW,IAAI;kCAAE;;;;;;oBAGvJ,6BAAe,8OAAC;wBAAI,WAAU;;;;;;kCAG/B,8OAAC;wBACC,MAAK;wBACL,WAAW,CAAC,kFAAkF,EAAE,cAAc,YAAY,IAAI;wBAC9H,SAAS;4BACP,MAAM,WAAW,CAAC;4BAClB,eAAe;4BAEf,mCAAmC;4BACnC,uCAAmC;;4BAKnC;wBACF;wBACA,cAAW;;0CAEX,8OAAC,oKAAA,CAAA,kBAAe;gCAAC,MAAM,wKAAA,CAAA,SAAM;gCAAE,WAAU;;;;;;0CACzC,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;0BAK9B,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BAEZ,OAAO,OAAO,CACb,gBAAgB,MAAM,CAAC,CAAC,KAAK;wBAC3B,MAAM,QAAQ,KAAK,KAAK,IAAI;wBAC5B,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM,GAAG,EAAE;wBAChC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC;wBAChB,OAAO;oBACT,GAAG,CAAC,IACJ,GAAG,CAAC,CAAC,CAAC,OAAO,MAAM,iBACnB,8OAAC;4BAAgB,WAAU;;gCAExB,CAAC,6BACA,8OAAC;oCAAI,WAAU;8CACZ;;;;;;8CAIL,8OAAC;oCAAG,WAAU;8CACX,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAW,CAAC,2DAA2D,EACrE,aAAa,KAAK,IAAI,GAClB,oEACA,4EACL,CAAC,EAAE,cAAc,mBAAmB,IAAI;gDACzC,OAAO,cAAc,KAAK,IAAI,GAAG;;kEAEjC,8OAAC;wDAAK,WAAW,CAAC,eAAe,EAAE,cAAc,KAAK,QAAQ;kEAAG,KAAK,IAAI;;;;;;oDACzE,CAAC,6BAAe,8OAAC;wDAAK,WAAU;kEAAoD,KAAK,IAAI;;;;;;;;;;;;2CAXzF,KAAK,IAAI;;;;;;;;;;;2BAVd;;;;;;;;;;;;;;;;;;;;;AAgCtB", "debugId": null}}, {"offset": {"line": 945, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/components/layout/Layout.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect } from 'react';\nimport Header from './Header';\nimport Sidebar from './Sidebar';\n\nexport default function Layout({ children }: { children: React.ReactNode }) {\n  // We don't need to track sidebar state anymore since it doesn't affect main content width\n  // But we keep the event listeners for other components that might need it\n\n  // Check if sidebar is collapsed on mount and listen for changes\n  useEffect(() => {\n    if (typeof window === 'undefined') return;\n\n    const updateSidebarState = () => {\n      // We no longer need to update state, just dispatch events for other components\n      // This function is kept for consistency with the event listeners\n    };\n\n    // Initial load\n    updateSidebarState();\n\n    // Listen for changes to the sidebar state\n    const handleStorageChange = (e: StorageEvent) => {\n      if (e.key === 'sidebarCollapsed') {\n        updateSidebarState();\n      }\n    };\n\n    // Listen for custom sidebar toggle event\n    const handleSidebarToggle = () => {\n      updateSidebarState();\n    };\n\n    window.addEventListener('storage', handleStorageChange);\n    window.addEventListener('sidebar-toggle', handleSidebarToggle);\n\n    return () => {\n      window.removeEventListener('storage', handleStorageChange);\n      window.removeEventListener('sidebar-toggle', handleSidebarToggle);\n    };\n  }, []);\n\n  // Close sidebar when clicking outside on mobile\n  useEffect(() => {\n    if (typeof window === 'undefined') return;\n\n    const handleClickOutside = (event: MouseEvent) => {\n      const sidebar = document.querySelector('aside');\n      const sidebarButton = document.querySelector('button[aria-label=\"Toggle sidebar\"]');\n      const menuButton = document.querySelector('button[aria-label=\"Toggle menu\"]');\n\n      // Check if we clicked outside the sidebar and both toggle buttons\n      if (\n        sidebar &&\n        !sidebar.contains(event.target as Node) &&\n        sidebarButton &&\n        !sidebarButton.contains(event.target as Node) &&\n        menuButton &&\n        !menuButton.contains(event.target as Node) &&\n        document.documentElement.classList.contains('sidebar-open')\n      ) {\n        document.documentElement.classList.remove('sidebar-open');\n      }\n    };\n\n    // Also close sidebar when clicking on the overlay (the ::before element)\n    const handleOverlayClick = (event: MouseEvent) => {\n      const html = document.documentElement;\n\n      // Check if we clicked on the overlay (not on the sidebar or buttons)\n      if (\n        html.classList.contains('sidebar-open') &&\n        event.target === html\n      ) {\n        html.classList.remove('sidebar-open');\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    document.addEventListener('click', handleOverlayClick, true);\n\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n      document.removeEventListener('click', handleOverlayClick, true);\n    };\n  }, []);\n\n  return (\n    <div className=\"flex min-h-screen flex-col overflow-hidden\">\n      <Header />\n      <div className=\"flex flex-1 pt-16\">\n        <Sidebar />\n        <main\n          className=\"flex-1 transition-all duration-300 w-full overflow-y-auto h-[calc(100vh-64px)]\"\n        >\n          <div className=\"container mx-auto px-4 py-8 pb-16 md:px-6 lg:px-8 min-h-[calc(100vh-64px)]\">\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS,OAAO,EAAE,QAAQ,EAAiC;IACxE,0FAA0F;IAC1F,0EAA0E;IAE1E,gEAAgE;IAChE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wCAAmC;;QAEnC,MAAM;QAQN,0CAA0C;QAC1C,MAAM;QAMN,yCAAyC;QACzC,MAAM;IAWR,GAAG,EAAE;IAEL,gDAAgD;IAChD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wCAAmC;;QAEnC,MAAM;QAmBN,yEAAyE;QACzE,MAAM;IAmBR,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,UAAM;;;;;0BACP,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,uIAAA,CAAA,UAAO;;;;;kCACR,8OAAC;wBACC,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 1028, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/components/layout/ConditionalLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { usePathname } from 'next/navigation';\nimport Layout from './Layout';\n\nexport default function ConditionalLayout({ children }: { children: React.ReactNode }) {\n  const pathname = usePathname();\n  \n  // Check if the current path is an auth route\n  const isAuthRoute = pathname?.startsWith('/auth');\n  \n  // If it's an auth route, render children directly without the main layout\n  if (isAuthRoute) {\n    return <>{children}</>;\n  }\n  \n  // For all other routes, use the main layout with header and sidebar\n  return <Layout>{children}</Layout>;\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAMe,SAAS,kBAAkB,EAAE,QAAQ,EAAiC;IACnF,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,6CAA6C;IAC7C,MAAM,cAAc,UAAU,WAAW;IAEzC,0EAA0E;IAC1E,IAAI,aAAa;QACf,qBAAO;sBAAG;;IACZ;IAEA,oEAAoE;IACpE,qBAAO,8OAAC,sIAAA,CAAA,UAAM;kBAAE;;;;;;AAClB", "debugId": null}}, {"offset": {"line": 1063, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/app/providers.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { AuthProvider } from '@/contexts/AuthContext';\n\nexport default function Providers({ children }: { children: React.ReactNode }) {\n  return (\n    <AuthProvider>{children}</AuthProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKe,SAAS,UAAU,EAAE,QAAQ,EAAiC;IAC3E,qBACE,8OAAC,+HAAA,CAAA,eAAY;kBAAE;;;;;;AAEnB", "debugId": null}}]}