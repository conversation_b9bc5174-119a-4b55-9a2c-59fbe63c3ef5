{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/auth/login/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { useRouter, useSearchParams } from 'next/navigation';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { faEnvelope, faLock, faSpinner, faExclamationTriangle } from '@fortawesome/free-solid-svg-icons';\n\nexport default function LoginPage() {\n  const router = useRouter();\n  const searchParams = useSearchParams();\n  const callbackUrl = searchParams.get('callbackUrl') || '/';\n\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  // Check if user is already logged in\n  useEffect(() => {\n    const user = localStorage.getItem('user');\n    if (user) {\n      router.push(callbackUrl);\n    }\n  }, [router, callbackUrl]);\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    console.log('Login form submitted');\n\n    if (!email || !password) {\n      setError('Email and password are required');\n      return;\n    }\n\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      // Call the login API directly\n      const response = await fetch('/api/login', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ email, password }),\n      });\n\n      const data = await response.json();\n\n      if (!response.ok || !data.success) {\n        setError(data.message || 'Login failed');\n        setIsLoading(false);\n        return;\n      }\n\n      // Store user data\n      console.log('Storing user data:', data.user);\n      localStorage.setItem('user', JSON.stringify(data.user));\n\n      // Set cookie with proper encoding\n      const encodedUser = encodeURIComponent(JSON.stringify(data.user));\n      document.cookie = `user=${encodedUser}; path=/; max-age=86400`;\n\n      // Redirect to the callback URL\n      console.log('Login successful, redirecting to:', callbackUrl);\n      window.location.href = callbackUrl;\n    } catch (error) {\n      console.error('Login error:', error);\n      setError('An unexpected error occurred');\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"text-center\">\n        <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">Sign in to your account</h2>\n        <p className=\"mt-2 text-sm text-gray-600 dark:text-gray-400\">\n          Or{' '}\n          <Link href=\"/auth/register\" className=\"font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300\">\n            create a new account\n          </Link>\n        </p>\n      </div>\n\n      {error && (\n        <div className=\"rounded-md bg-red-50 p-4 dark:bg-red-900/30\">\n          <div className=\"flex\">\n            <div className=\"flex-shrink-0\">\n              <FontAwesomeIcon icon={faExclamationTriangle} className=\"h-5 w-5 text-red-400\" />\n            </div>\n            <div className=\"ml-3\">\n              <h3 className=\"text-sm font-medium text-red-800 dark:text-red-200\">Login failed</h3>\n              <div className=\"mt-2 text-sm text-red-700 dark:text-red-300\">\n                <p>{error}</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      <form className=\"space-y-6\" onSubmit={handleSubmit}>\n        <div>\n          <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n            Email address\n          </label>\n          <div className=\"relative mt-1 rounded-md shadow-sm\">\n            <div className=\"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3\">\n              <FontAwesomeIcon icon={faEnvelope} className=\"h-5 w-5 text-gray-400\" />\n            </div>\n            <input\n              id=\"email\"\n              name=\"email\"\n              type=\"email\"\n              autoComplete=\"email\"\n              required\n              value={email}\n              onChange={(e) => setEmail(e.target.value)}\n              className=\"block w-full rounded-md border-gray-300 pl-10 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400\"\n              placeholder=\"<EMAIL>\"\n            />\n          </div>\n        </div>\n\n        <div>\n          <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n            Password\n          </label>\n          <div className=\"relative mt-1 rounded-md shadow-sm\">\n            <div className=\"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3\">\n              <FontAwesomeIcon icon={faLock} className=\"h-5 w-5 text-gray-400\" />\n            </div>\n            <input\n              id=\"password\"\n              name=\"password\"\n              type=\"password\"\n              autoComplete=\"current-password\"\n              required\n              value={password}\n              onChange={(e) => setPassword(e.target.value)}\n              className=\"block w-full rounded-md border-gray-300 pl-10 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400\"\n              placeholder=\"••••••••\"\n            />\n          </div>\n        </div>\n\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center\">\n            <input\n              id=\"remember-me\"\n              name=\"remember-me\"\n              type=\"checkbox\"\n              className=\"h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:focus:ring-blue-600\"\n            />\n            <label htmlFor=\"remember-me\" className=\"ml-2 block text-sm text-gray-700 dark:text-gray-300\">\n              Remember me\n            </label>\n          </div>\n\n          <div className=\"text-sm\">\n            <Link href=\"/auth/forgot-password\" className=\"font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300\">\n              Forgot your password?\n            </Link>\n          </div>\n        </div>\n\n        <div>\n          <button\n            type=\"submit\"\n            disabled={isLoading}\n            className=\"flex w-full justify-center rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-75 dark:bg-blue-700 dark:hover:bg-blue-800\"\n          >\n            {isLoading ? (\n              <>\n                <FontAwesomeIcon icon={faSpinner} className=\"mr-2 h-4 w-4 animate-spin\" />\n                Signing in...\n              </>\n            ) : (\n              'Sign in'\n            )}\n          </button>\n        </div>\n      </form>\n\n      <div className=\"relative\">\n        <div className=\"absolute inset-0 flex items-center\">\n          <div className=\"w-full border-t border-gray-300 dark:border-gray-600\"></div>\n        </div>\n        <div className=\"relative flex justify-center text-sm\">\n          <span className=\"bg-white px-2 text-gray-500 dark:bg-gray-800 dark:text-gray-400\">Demo Accounts</span>\n        </div>\n      </div>\n\n      <div className=\"grid grid-cols-2 gap-3 text-sm\">\n        <div className=\"rounded-md border border-gray-200 p-3 dark:border-gray-700\">\n          <p className=\"font-medium text-gray-900 dark:text-white\">Admin User</p>\n          <p className=\"text-gray-500 dark:text-gray-400\"><EMAIL></p>\n          <p className=\"text-gray-500 dark:text-gray-400\">admin123</p>\n        </div>\n        <div className=\"rounded-md border border-gray-200 p-3 dark:border-gray-700\">\n          <p className=\"font-medium text-gray-900 dark:text-white\">Regular User</p>\n          <p className=\"text-gray-500 dark:text-gray-400\"><EMAIL></p>\n          <p className=\"text-gray-500 dark:text-gray-400\">password123</p>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,cAAc,aAAa,GAAG,CAAC,kBAAkB;IAEvD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,qCAAqC;IACrC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,OAAO,aAAa,OAAO,CAAC;QAClC,IAAI,MAAM;YACR,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAQ;KAAY;IAExB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,QAAQ,GAAG,CAAC;QAEZ,IAAI,CAAC,SAAS,CAAC,UAAU;YACvB,SAAS;YACT;QACF;QAEA,aAAa;QACb,SAAS;QAET,IAAI;YACF,8BAA8B;YAC9B,MAAM,WAAW,MAAM,MAAM,cAAc;gBACzC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAO;gBAAS;YACzC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK,OAAO,EAAE;gBACjC,SAAS,KAAK,OAAO,IAAI;gBACzB,aAAa;gBACb;YACF;YAEA,kBAAkB;YAClB,QAAQ,GAAG,CAAC,sBAAsB,KAAK,IAAI;YAC3C,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC,KAAK,IAAI;YAErD,kCAAkC;YAClC,MAAM,cAAc,mBAAmB,KAAK,SAAS,CAAC,KAAK,IAAI;YAC/D,SAAS,MAAM,GAAG,CAAC,KAAK,EAAE,YAAY,uBAAuB,CAAC;YAE9D,+BAA+B;YAC/B,QAAQ,GAAG,CAAC,qCAAqC;YACjD,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,SAAS;YACT,aAAa;QACf;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAmD;;;;;;kCACjE,8OAAC;wBAAE,WAAU;;4BAAgD;4BACxD;0CACH,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAiB,WAAU;0CAA4F;;;;;;;;;;;;;;;;;;YAMrI,uBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,oKAAA,CAAA,kBAAe;gCAAC,MAAM,wKAAA,CAAA,wBAAqB;gCAAE,WAAU;;;;;;;;;;;sCAE1D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAqD;;;;;;8CACnE,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;kDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOd,8OAAC;gBAAK,WAAU;gBAAY,UAAU;;kCACpC,8OAAC;;0CACC,8OAAC;gCAAM,SAAQ;gCAAQ,WAAU;0CAA6D;;;;;;0CAG9F,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oKAAA,CAAA,kBAAe;4CAAC,MAAM,wKAAA,CAAA,aAAU;4CAAE,WAAU;;;;;;;;;;;kDAE/C,8OAAC;wCACC,IAAG;wCACH,MAAK;wCACL,MAAK;wCACL,cAAa;wCACb,QAAQ;wCACR,OAAO;wCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wCACxC,WAAU;wCACV,aAAY;;;;;;;;;;;;;;;;;;kCAKlB,8OAAC;;0CACC,8OAAC;gCAAM,SAAQ;gCAAW,WAAU;0CAA6D;;;;;;0CAGjG,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oKAAA,CAAA,kBAAe;4CAAC,MAAM,wKAAA,CAAA,SAAM;4CAAE,WAAU;;;;;;;;;;;kDAE3C,8OAAC;wCACC,IAAG;wCACH,MAAK;wCACL,MAAK;wCACL,cAAa;wCACb,QAAQ;wCACR,OAAO;wCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;wCAC3C,WAAU;wCACV,aAAY;;;;;;;;;;;;;;;;;;kCAKlB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,IAAG;wCACH,MAAK;wCACL,MAAK;wCACL,WAAU;;;;;;kDAEZ,8OAAC;wCAAM,SAAQ;wCAAc,WAAU;kDAAsD;;;;;;;;;;;;0CAK/F,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAwB,WAAU;8CAA4F;;;;;;;;;;;;;;;;;kCAM7I,8OAAC;kCACC,cAAA,8OAAC;4BACC,MAAK;4BACL,UAAU;4BACV,WAAU;sCAET,0BACC;;kDACE,8OAAC,oKAAA,CAAA,kBAAe;wCAAC,MAAM,wKAAA,CAAA,YAAS;wCAAE,WAAU;;;;;;oCAA8B;;+CAI5E;;;;;;;;;;;;;;;;;0BAMR,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;;;;;;;;;;kCAEjB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAK,WAAU;sCAAkE;;;;;;;;;;;;;;;;;0BAItF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAA4C;;;;;;0CACzD,8OAAC;gCAAE,WAAU;0CAAmC;;;;;;0CAChD,8OAAC;gCAAE,WAAU;0CAAmC;;;;;;;;;;;;kCAElD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAA4C;;;;;;0CACzD,8OAAC;gCAAE,WAAU;0CAAmC;;;;;;0CAChD,8OAAC;gCAAE,WAAU;0CAAmC;;;;;;;;;;;;;;;;;;;;;;;;AAK1D", "debugId": null}}]}