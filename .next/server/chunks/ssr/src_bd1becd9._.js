module.exports = {

"[project]/src/data/learning-resources.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getArticles": (()=>getArticles),
    "getBlogPosts": (()=>getBlogPosts),
    "getCombinedLearningResources": (()=>getCombinedLearningResources),
    "getFeaturedResources": (()=>getFeaturedResources),
    "getPopularResources": (()=>getPopularResources),
    "getRecentResources": (()=>getRecentResources),
    "getResourceById": (()=>getResourceById),
    "getVideos": (()=>getVideos),
    "learningResources": (()=>learningResources)
});
const learningResources = [
    // Videos
    {
        id: 'video-1',
        title: 'Introduction to AI Agents',
        description: 'Learn the basics of AI agents and how they can help you in your daily tasks.',
        type: 'video',
        thumbnail: 'https://placehold.co/640x360.png?text=Introduction+to+AI+Agents',
        content: 'This video provides a comprehensive introduction to AI agents, covering their capabilities, limitations, and practical applications.',
        url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        tags: [
            'beginner',
            'introduction',
            'ai-basics'
        ],
        author: 'Dr. Sarah Johnson',
        publishedAt: new Date('2023-10-15')
    },
    {
        id: 'video-2',
        title: 'Advanced AI Agent Techniques',
        description: 'Dive deeper into AI agent capabilities with advanced techniques and strategies.',
        type: 'video',
        thumbnail: 'https://placehold.co/640x360.png?text=Advanced+AI+Agent+Techniques',
        content: 'This advanced tutorial explores sophisticated AI agent techniques, including prompt engineering, context management, and multi-agent systems.',
        url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        tags: [
            'advanced',
            'techniques',
            'prompt-engineering'
        ],
        author: 'Prof. Michael Chen',
        publishedAt: new Date('2023-11-05')
    },
    {
        id: 'video-3',
        title: 'AI Agents for Data Analysis',
        description: 'Learn how to use AI agents to analyze and visualize complex datasets.',
        type: 'video',
        thumbnail: 'https://placehold.co/640x360.png?text=AI+Agents+for+Data+Analysis',
        content: 'This tutorial demonstrates how to leverage AI agents for data analysis tasks, from data cleaning to visualization and interpretation.',
        url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        tags: [
            'data-analysis',
            'visualization',
            'practical'
        ],
        author: 'Emma Rodriguez',
        publishedAt: new Date('2023-12-10')
    },
    // Articles
    {
        id: 'article-1',
        title: 'The Future of AI Agents in Enterprise',
        description: 'An in-depth look at how AI agents are transforming enterprise operations and decision-making.',
        type: 'article',
        thumbnail: 'https://placehold.co/800x400.png?text=Future+of+AI+Agents',
        content: `
# The Future of AI Agents in Enterprise

Artificial Intelligence (AI) agents are rapidly transforming how enterprises operate, making processes more efficient and enabling new capabilities that were previously impossible. This article explores the current state and future potential of AI agents in enterprise settings.

## Current Applications

Today, enterprises are using AI agents for:

- **Customer Service**: Handling routine inquiries and providing 24/7 support
- **Data Analysis**: Processing large datasets to extract actionable insights
- **Process Automation**: Streamlining workflows and reducing manual tasks
- **Decision Support**: Providing recommendations based on complex data analysis

## Future Directions

Looking ahead, we can expect AI agents to evolve in several key ways:

### 1. Increased Autonomy

Future AI agents will operate with greater independence, making decisions and taking actions with minimal human oversight. This will free up human workers to focus on more creative and strategic tasks.

### 2. Multi-Agent Systems

Rather than single agents working in isolation, we'll see sophisticated networks of specialized agents collaborating to solve complex problems. These multi-agent systems will be able to tackle challenges that would be impossible for a single agent.

### 3. Enhanced Personalization

AI agents will become increasingly adept at understanding individual user preferences and adapting their behavior accordingly. This will enable highly personalized experiences for employees and customers alike.

### 4. Seamless Integration

The distinction between AI agents and traditional software will blur, with AI capabilities being embedded throughout enterprise systems rather than existing as standalone tools.

## Challenges and Considerations

Despite their promise, enterprises must navigate several challenges when implementing AI agents:

- **Data Privacy**: Ensuring that AI agents handle sensitive information appropriately
- **Transparency**: Making AI decision-making processes understandable to humans
- **Skills Gap**: Training employees to work effectively alongside AI agents
- **Ethical Considerations**: Addressing questions about automation and job displacement

## Conclusion

AI agents represent a transformative technology for enterprises, offering unprecedented opportunities for efficiency, innovation, and growth. Organizations that thoughtfully integrate these tools into their operations will gain significant competitive advantages in the years ahead.
    `,
        tags: [
            'enterprise',
            'future-trends',
            'business'
        ],
        author: 'Dr. James Wilson',
        publishedAt: new Date('2023-09-20')
    },
    {
        id: 'article-2',
        title: 'Ethical Considerations for AI Agent Deployment',
        description: 'Exploring the ethical dimensions of deploying AI agents in various contexts.',
        type: 'article',
        thumbnail: 'https://placehold.co/800x400.png?text=AI+Ethics',
        content: `
# Ethical Considerations for AI Agent Deployment

As AI agents become increasingly integrated into our daily lives and business operations, it's crucial to consider the ethical implications of their deployment. This article examines key ethical considerations that should guide the development and implementation of AI agent systems.

## Transparency and Explainability

One of the fundamental ethical requirements for AI agents is transparency. Users should understand:

- When they are interacting with an AI rather than a human
- The general capabilities and limitations of the AI system
- How the AI makes decisions or recommendations

Explainability is equally important—the ability to provide understandable explanations for why an AI agent took a particular action or made a specific recommendation.

## Privacy and Data Protection

AI agents often require access to significant amounts of data to function effectively. Organizations must:

- Be transparent about what data is collected and how it's used
- Implement robust security measures to protect sensitive information
- Comply with relevant regulations like GDPR or CCPA
- Minimize data collection to what's necessary for the agent to function

## Fairness and Bias Mitigation

AI agents can inadvertently perpetuate or amplify existing biases if not carefully designed and monitored:

- Training data should be diverse and representative
- Systems should be regularly audited for biased outcomes
- Developers should implement bias detection and mitigation techniques
- Organizations should establish clear standards for fairness

## Accountability and Oversight

Clear lines of accountability are essential when deploying AI agents:

- Organizations should designate specific individuals responsible for AI systems
- Regular auditing and monitoring should be implemented
- Feedback mechanisms should allow users to report concerns
- Processes should exist to address harmful or unintended consequences

## Human Autonomy and Agency

AI agents should enhance human capabilities rather than diminish human agency:

- Users should maintain meaningful control over important decisions
- Systems should be designed to complement human judgment, not replace it
- People should be able to override AI recommendations when appropriate
- The division of labor between humans and AI should be thoughtfully considered

## Conclusion

Ethical deployment of AI agents requires ongoing attention and commitment. By prioritizing transparency, privacy, fairness, accountability, and human agency, organizations can harness the benefits of AI while minimizing potential harms. As these technologies continue to evolve, our ethical frameworks must evolve alongside them, ensuring that AI agents serve human values and well-being.
    `,
        tags: [
            'ethics',
            'responsible-ai',
            'governance'
        ],
        author: 'Prof. Elena Martinez',
        publishedAt: new Date('2023-10-05')
    },
    // Blog Posts
    {
        id: 'blog-1',
        title: 'How We Built Our First AI Agent',
        description: 'A behind-the-scenes look at our journey developing our first AI agent.',
        type: 'blog',
        thumbnail: 'https://placehold.co/800x400.png?text=Building+AI+Agents',
        content: `
# How We Built Our First AI Agent: Lessons Learned

When we set out to build our first AI agent six months ago, we had no idea how challenging—and rewarding—the journey would be. In this post, I'll share our experience, including the obstacles we faced and the insights we gained along the way.

## The Initial Vision

Our goal was straightforward: create an AI agent that could help our customer support team handle routine inquiries, freeing them to focus on more complex issues. We envisioned a system that could:

- Answer frequently asked questions
- Help users troubleshoot common problems
- Collect necessary information before escalating to a human agent
- Learn and improve over time

Simple enough, right? Not quite.

## Challenges We Encountered

### 1. Defining the Scope

Our first challenge was scope creep. As we brainstormed capabilities, our simple support agent began transforming into an all-purpose assistant that would do everything from technical support to sales to product recommendations.

**Lesson learned**: Start narrow and expand later. We eventually refocused on technical support for a specific product line, which gave us a manageable scope.

### 2. Data Quality Issues

We initially trained our agent on our support documentation and past ticket logs. However, we quickly discovered inconsistencies in how our team had resolved similar issues in the past, leading to confused responses from our agent.

**Lesson learned**: Clean and standardize your training data before implementation. We ended up creating a curated dataset of best-practice responses.

### 3. Integration Complexities

Connecting our agent to existing systems—our knowledge base, CRM, and ticketing system—proved more difficult than anticipated.

**Lesson learned**: Plan your integration strategy early and thoroughly. Consider building a middleware layer if your systems don't have robust APIs.

### 4. User Acceptance

Some team members were hesitant to adopt the agent, fearing it might eventually replace them.

**Lesson learned**: Involve end-users from the beginning and emphasize how the agent will enhance their work rather than replace it.

## What Worked Well

Despite the challenges, several approaches proved successful:

### 1. Iterative Development

Rather than aiming for perfection from the start, we released early versions internally and gathered feedback. This allowed us to identify and address issues quickly.

### 2. Human-in-the-Loop Design

We designed our agent to collaborate with human agents rather than operate independently. This improved performance and helped with team acceptance.

### 3. Clear Success Metrics

We established specific metrics to evaluate our agent's performance, including resolution rate, customer satisfaction, and time savings for human agents.

### 4. Continuous Learning

We implemented a feedback loop where human agents could flag problematic responses, helping our system improve over time.

## Results and Next Steps

Six months in, our AI agent now successfully handles about 40% of initial customer inquiries, reducing wait times and allowing our support team to focus on more complex issues.

Our next steps include:

- Expanding to additional product lines
- Implementing more sophisticated natural language understanding
- Adding proactive support capabilities
- Exploring voice interface options

## Conclusion

Building an effective AI agent is more challenging than it might initially appear, but the benefits can be substantial. By starting with a focused scope, prioritizing data quality, planning integrations carefully, and involving end-users throughout the process, you can create an agent that genuinely enhances your team's capabilities.

Have you built an AI agent for your organization? I'd love to hear about your experience in the comments!
    `,
        tags: [
            'case-study',
            'development',
            'lessons-learned'
        ],
        author: 'Alex Thompson',
        publishedAt: new Date('2023-11-15')
    },
    {
        id: 'blog-2',
        title: 'Monthly AI Agent Updates - January 2024',
        description: 'The latest updates and improvements to our AI agent platform.',
        type: 'blog',
        thumbnail: 'https://placehold.co/800x400.png?text=January+Updates',
        content: `
# Monthly AI Agent Updates - January 2024

Welcome to our first monthly update of 2024! We've been hard at work improving our AI agent platform, and we're excited to share the latest enhancements, bug fixes, and upcoming features.

## New Features

### 1. Enhanced Context Management

Our agents can now maintain context more effectively across longer conversations. This means they can refer back to information mentioned earlier in the discussion without requiring users to repeat themselves.

### 2. Multi-Modal Capabilities

Agents can now process and respond to both text and images. This is particularly useful for troubleshooting scenarios where users can share screenshots of errors or problems they're experiencing.

### 3. Custom Agent Creation UI

We've launched a new user interface for creating and customizing agents without coding. This drag-and-drop interface allows you to:

- Define conversation flows
- Create custom knowledge bases
- Set up integration with your existing tools
- Customize the agent's tone and personality

## Improvements

### 1. Performance Optimization

We've significantly improved response times across the platform:

- 40% faster initial response time
- 25% reduction in token usage
- Improved handling of concurrent requests

### 2. Better Error Handling

Agents now recover more gracefully from misunderstandings and provide clearer guidance when they need additional information.

### 3. Enhanced Analytics

The analytics dashboard now provides deeper insights into:

- Common user queries and pain points
- Success rates for different types of requests
- Patterns in escalations to human agents
- User satisfaction metrics

## Bug Fixes

- Fixed an issue where agents occasionally lost context after API errors
- Resolved a problem with webhook integrations timing out
- Fixed formatting issues in exported conversation logs
- Addressed authentication issues with certain SSO providers

## Coming Soon

We're excited about several features currently in development:

### 1. Agent Collaboration

Soon, multiple specialized agents will be able to collaborate on complex requests, each handling the aspects they're best suited for.

### 2. Scheduled Actions

Agents will be able to perform actions at scheduled times, such as sending follow-ups or checking on the status of issues.

### 3. Voice Interface

We're working on a voice interface that will allow users to interact with agents through spoken conversation.

## Community Spotlight

We want to highlight some impressive implementations from our community:

- **HealthTech Solutions** created an agent that helps patients understand their medication schedules and potential side effects
- **GlobalLogistics** built an agent that provides real-time shipping updates and resolves delivery issues
- **EduLearn** developed a tutoring agent that adapts to different learning styles

## Feedback Request

We're considering several directions for our Q2 roadmap and would love your input. Please take our quick survey to help prioritize upcoming features: [Survey Link]

## Conclusion

Thank you for being part of our community! As always, we welcome your feedback and suggestions. You can reach our <NAME_EMAIL> or through the feedback form in your dashboard.

Happy agent building!

The AI Agent Platform Team
    `,
        tags: [
            'updates',
            'new-features',
            'roadmap'
        ],
        author: 'The AI Hub Team',
        publishedAt: new Date('2024-01-10')
    }
];
const getVideos = ()=>learningResources.filter((resource)=>resource.type === 'video');
const getArticles = ()=>learningResources.filter((resource)=>resource.type === 'article');
const getBlogPosts = ()=>learningResources.filter((resource)=>resource.type === 'blog');
const getResourceById = (id)=>learningResources.find((resource)=>resource.id === id);
const getFeaturedResources = (count = 3)=>{
    // In a real app, you might have a 'featured' flag or use other criteria
    // Here we'll just return the most recent resources
    return [
        ...learningResources
    ].sort((a, b)=>b.publishedAt.getTime() - a.publishedAt.getTime()).slice(0, count);
};
const getRecentResources = (count = 4)=>{
    return [
        ...learningResources
    ].sort((a, b)=>b.publishedAt.getTime() - a.publishedAt.getTime()).slice(0, count);
};
const getPopularResources = (count = 4)=>{
    // In a real app, this would be based on view counts or user engagement
    // For now, we'll shuffle the array and take the first few items
    const shuffled = [
        ...learningResources
    ].sort(()=>0.5 - Math.random());
    return shuffled.slice(0, count);
};
const getCombinedLearningResources = (resources)=>{
    return resources.map((resource)=>({
            ...resource,
            // Add a URL based on the resource type
            viewUrl: resource.type === 'video' ? `/learning/videos/${resource.id}` : resource.type === 'article' ? `/learning/articles/${resource.id}` : `/learning/blog/${resource.id}`
        }));
};
}}),
"[project]/src/app/learning/videos/page.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>VideosPage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$react$2d$fontawesome$2f$index$2e$es$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@fortawesome/react-fontawesome/index.es.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@fortawesome/free-solid-svg-icons/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$learning$2d$resources$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/data/learning-resources.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
function VideosPage() {
    const videos = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$learning$2d$resources$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getVideos"])();
    const [searchTerm, setSearchTerm] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('');
    const filteredVideos = videos.filter((video)=>video.title.toLowerCase().includes(searchTerm.toLowerCase()) || video.description.toLowerCase().includes(searchTerm.toLowerCase()) || video.tags.some((tag)=>tag.toLowerCase().includes(searchTerm.toLowerCase())));
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "space-y-8",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "rounded-lg bg-gradient-to-r from-blue-600 to-indigo-700 p-8 text-white shadow-lg",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                        className: "mb-4 text-3xl font-bold",
                        children: "Video Tutorials"
                    }, void 0, false, {
                        fileName: "[project]/src/app/learning/videos/page.tsx",
                        lineNumber: 23,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "mb-6 text-lg",
                        children: "Watch step-by-step tutorials and demonstrations to help you master AI agents and get the most out of our platform."
                    }, void 0, false, {
                        fileName: "[project]/src/app/learning/videos/page.tsx",
                        lineNumber: 24,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "relative max-w-md",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$react$2d$fontawesome$2f$index$2e$es$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FontAwesomeIcon"], {
                                    icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["faSearch"],
                                    className: "h-5 w-5 text-gray-400"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/learning/videos/page.tsx",
                                    lineNumber: 29,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/learning/videos/page.tsx",
                                lineNumber: 28,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                type: "text",
                                className: "block w-full rounded-md border-0 bg-white bg-opacity-20 py-2 pl-10 pr-4 text-white placeholder-gray-300 backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50",
                                placeholder: "Search videos...",
                                value: searchTerm,
                                onChange: (e)=>setSearchTerm(e.target.value)
                            }, void 0, false, {
                                fileName: "[project]/src/app/learning/videos/page.tsx",
                                lineNumber: 31,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/learning/videos/page.tsx",
                        lineNumber: 27,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/learning/videos/page.tsx",
                lineNumber: 22,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "grid gap-6 md:grid-cols-2 lg:grid-cols-3",
                children: filteredVideos.length > 0 ? filteredVideos.map((video)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "group overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm transition hover:shadow-md dark:border-gray-800 dark:bg-gray-900",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "relative h-48 overflow-hidden",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                        src: video.thumbnail || 'https://via.placeholder.com/640x360.png?text=Video+Tutorial',
                                        alt: video.title,
                                        className: "h-full w-full object-cover transition duration-300 group-hover:scale-105"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/learning/videos/page.tsx",
                                        lineNumber: 49,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 opacity-0 transition group-hover:opacity-100",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex h-16 w-16 items-center justify-center rounded-full bg-blue-600 text-white",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$react$2d$fontawesome$2f$index$2e$es$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FontAwesomeIcon"], {
                                                icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["faPlay"],
                                                className: "h-6 w-6"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/learning/videos/page.tsx",
                                                lineNumber: 56,
                                                columnNumber: 21
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/learning/videos/page.tsx",
                                            lineNumber: 55,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/learning/videos/page.tsx",
                                        lineNumber: 54,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/learning/videos/page.tsx",
                                lineNumber: 48,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "p-4",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                        className: "mb-2 text-lg font-semibold text-gray-900 dark:text-white",
                                        children: video.title
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/learning/videos/page.tsx",
                                        lineNumber: 61,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "mb-3 text-sm text-gray-600 dark:text-gray-400",
                                        children: video.description
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/learning/videos/page.tsx",
                                        lineNumber: 62,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "mb-3 flex flex-wrap gap-2",
                                        children: video.tags.map((tag)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800 dark:bg-blue-900/30 dark:text-blue-300",
                                                children: tag
                                            }, tag, false, {
                                                fileName: "[project]/src/app/learning/videos/page.tsx",
                                                lineNumber: 65,
                                                columnNumber: 21
                                            }, this))
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/learning/videos/page.tsx",
                                        lineNumber: 63,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center justify-between",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "text-xs text-gray-500 dark:text-gray-500",
                                                children: video.publishedAt.toLocaleDateString()
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/learning/videos/page.tsx",
                                                lineNumber: 74,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                                href: video.url,
                                                target: "_blank",
                                                rel: "noopener noreferrer",
                                                className: "rounded-md bg-blue-600 px-3 py-1 text-sm font-medium text-white hover:bg-blue-700",
                                                children: "Watch Video"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/learning/videos/page.tsx",
                                                lineNumber: 77,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/learning/videos/page.tsx",
                                        lineNumber: 73,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/learning/videos/page.tsx",
                                lineNumber: 60,
                                columnNumber: 15
                            }, this)
                        ]
                    }, video.id, true, {
                        fileName: "[project]/src/app/learning/videos/page.tsx",
                        lineNumber: 44,
                        columnNumber: 13
                    }, this)) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "col-span-full flex flex-col items-center justify-center py-12 text-center",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$react$2d$fontawesome$2f$index$2e$es$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FontAwesomeIcon"], {
                            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["faTags"],
                            className: "mb-4 h-12 w-12 text-gray-400"
                        }, void 0, false, {
                            fileName: "[project]/src/app/learning/videos/page.tsx",
                            lineNumber: 91,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                            className: "mb-2 text-lg font-medium text-gray-900 dark:text-white",
                            children: "No videos found"
                        }, void 0, false, {
                            fileName: "[project]/src/app/learning/videos/page.tsx",
                            lineNumber: 92,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-gray-500 dark:text-gray-400",
                            children: "We couldn't find any videos matching your search. Try different keywords or browse all videos."
                        }, void 0, false, {
                            fileName: "[project]/src/app/learning/videos/page.tsx",
                            lineNumber: 93,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            onClick: ()=>setSearchTerm(''),
                            className: "mt-4 rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700",
                            children: "Clear Search"
                        }, void 0, false, {
                            fileName: "[project]/src/app/learning/videos/page.tsx",
                            lineNumber: 96,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/learning/videos/page.tsx",
                    lineNumber: 90,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/learning/videos/page.tsx",
                lineNumber: 41,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/learning/videos/page.tsx",
        lineNumber: 21,
        columnNumber: 5
    }, this);
}
}}),

};

//# sourceMappingURL=src_bd1becd9._.js.map