{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/components/animations/FuturisticBackground.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect, useRef } from 'react';\n\ninterface Particle {\n  x: number;\n  y: number;\n  size: number;\n  speedX: number;\n  speedY: number;\n  opacity: number;\n  color: string;\n}\n\nexport default function FuturisticBackground() {\n  const canvasRef = useRef<HTMLCanvasElement>(null);\n  const particles = useRef<Particle[]>([]);\n  const animationFrameId = useRef<number>(0);\n  const mousePosition = useRef({ x: 0, y: 0 });\n\n  useEffect(() => {\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n\n    const ctx = canvas.getContext('2d');\n    if (!ctx) return;\n\n    // Set canvas to full window size\n    const handleResize = () => {\n      canvas.width = window.innerWidth;\n      canvas.height = window.innerHeight;\n      initParticles();\n    };\n\n    // Track mouse movement\n    const handleMouseMove = (e: MouseEvent) => {\n      mousePosition.current = {\n        x: e.clientX,\n        y: e.clientY\n      };\n    };\n\n    // Initialize particles\n    const initParticles = () => {\n      particles.current = [];\n      const particleCount = Math.min(Math.floor(window.innerWidth * window.innerHeight / 15000), 100);\n      \n      for (let i = 0; i < particleCount; i++) {\n        particles.current.push({\n          x: Math.random() * canvas.width,\n          y: Math.random() * canvas.height,\n          size: Math.random() * 2 + 0.5,\n          speedX: (Math.random() - 0.5) * 0.3,\n          speedY: (Math.random() - 0.5) * 0.3,\n          opacity: Math.random() * 0.5 + 0.1,\n          color: getRandomColor()\n        });\n      }\n    };\n\n    // Get a random color from our futuristic palette\n    const getRandomColor = () => {\n      const colors = [\n        'rgba(64, 196, 255, 1)',  // Bright blue\n        'rgba(120, 81, 255, 1)',  // Purple\n        'rgba(255, 64, 129, 1)',  // Pink\n        'rgba(0, 176, 255, 1)',   // Light blue\n        'rgba(124, 77, 255, 1)',  // Indigo\n        'rgba(29, 233, 182, 1)'   // Teal\n      ];\n      return colors[Math.floor(Math.random() * colors.length)];\n    };\n\n    // Draw a single particle\n    const drawParticle = (particle: Particle) => {\n      if (!ctx) return;\n      \n      ctx.beginPath();\n      ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);\n      ctx.fillStyle = particle.color.replace('1)', `${particle.opacity})`);\n      ctx.fill();\n    };\n\n    // Draw connections between particles that are close to each other\n    const drawConnections = () => {\n      if (!ctx) return;\n      \n      const maxDistance = 150;\n      \n      for (let i = 0; i < particles.current.length; i++) {\n        for (let j = i + 1; j < particles.current.length; j++) {\n          const dx = particles.current[i].x - particles.current[j].x;\n          const dy = particles.current[i].y - particles.current[j].y;\n          const distance = Math.sqrt(dx * dx + dy * dy);\n          \n          if (distance < maxDistance) {\n            const opacity = 0.2 * (1 - distance / maxDistance);\n            ctx.beginPath();\n            ctx.moveTo(particles.current[i].x, particles.current[i].y);\n            ctx.lineTo(particles.current[j].x, particles.current[j].y);\n            ctx.strokeStyle = `rgba(255, 255, 255, ${opacity})`;\n            ctx.lineWidth = 0.5;\n            ctx.stroke();\n          }\n        }\n      }\n    };\n\n    // Update particle positions and draw them\n    const animate = () => {\n      if (!ctx || !canvas) return;\n      \n      ctx.clearRect(0, 0, canvas.width, canvas.height);\n      \n      // Update and draw particles\n      particles.current.forEach(particle => {\n        // Update position\n        particle.x += particle.speedX;\n        particle.y += particle.speedY;\n        \n        // Bounce off edges\n        if (particle.x < 0 || particle.x > canvas.width) {\n          particle.speedX *= -1;\n        }\n        \n        if (particle.y < 0 || particle.y > canvas.height) {\n          particle.speedY *= -1;\n        }\n        \n        // Subtle mouse interaction\n        const dx = mousePosition.current.x - particle.x;\n        const dy = mousePosition.current.y - particle.y;\n        const distance = Math.sqrt(dx * dx + dy * dy);\n        \n        if (distance < 150) {\n          const angle = Math.atan2(dy, dx);\n          const force = 0.1 * (1 - distance / 150);\n          \n          particle.speedX -= Math.cos(angle) * force;\n          particle.speedY -= Math.sin(angle) * force;\n          \n          // Limit speed\n          const speed = Math.sqrt(particle.speedX * particle.speedX + particle.speedY * particle.speedY);\n          if (speed > 1) {\n            particle.speedX = (particle.speedX / speed) * 1;\n            particle.speedY = (particle.speedY / speed) * 1;\n          }\n        }\n        \n        // Draw the particle\n        drawParticle(particle);\n      });\n      \n      // Draw connections\n      drawConnections();\n      \n      // Continue animation\n      animationFrameId.current = requestAnimationFrame(animate);\n    };\n\n    // Set up event listeners\n    window.addEventListener('resize', handleResize);\n    window.addEventListener('mousemove', handleMouseMove);\n    \n    // Initialize and start animation\n    handleResize();\n    animate();\n    \n    // Clean up\n    return () => {\n      window.removeEventListener('resize', handleResize);\n      window.removeEventListener('mousemove', handleMouseMove);\n      cancelAnimationFrame(animationFrameId.current);\n    };\n  }, []);\n\n  return (\n    <canvas\n      ref={canvasRef}\n      className=\"fixed top-0 left-0 -z-10 h-full w-full opacity-40 pointer-events-none\"\n    />\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAce,SAAS;IACtB,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAqB;IAC5C,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAc,EAAE;IACvC,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAU;IACxC,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAE1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,SAAS,UAAU,OAAO;QAChC,IAAI,CAAC,QAAQ;QAEb,MAAM,MAAM,OAAO,UAAU,CAAC;QAC9B,IAAI,CAAC,KAAK;QAEV,iCAAiC;QACjC,MAAM,eAAe;YACnB,OAAO,KAAK,GAAG,OAAO,UAAU;YAChC,OAAO,MAAM,GAAG,OAAO,WAAW;YAClC;QACF;QAEA,uBAAuB;QACvB,MAAM,kBAAkB,CAAC;YACvB,cAAc,OAAO,GAAG;gBACtB,GAAG,EAAE,OAAO;gBACZ,GAAG,EAAE,OAAO;YACd;QACF;QAEA,uBAAuB;QACvB,MAAM,gBAAgB;YACpB,UAAU,OAAO,GAAG,EAAE;YACtB,MAAM,gBAAgB,KAAK,GAAG,CAAC,KAAK,KAAK,CAAC,OAAO,UAAU,GAAG,OAAO,WAAW,GAAG,QAAQ;YAE3F,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,IAAK;gBACtC,UAAU,OAAO,CAAC,IAAI,CAAC;oBACrB,GAAG,KAAK,MAAM,KAAK,OAAO,KAAK;oBAC/B,GAAG,KAAK,MAAM,KAAK,OAAO,MAAM;oBAChC,MAAM,KAAK,MAAM,KAAK,IAAI;oBAC1B,QAAQ,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;oBAChC,QAAQ,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;oBAChC,SAAS,KAAK,MAAM,KAAK,MAAM;oBAC/B,OAAO;gBACT;YACF;QACF;QAEA,iDAAiD;QACjD,MAAM,iBAAiB;YACrB,MAAM,SAAS;gBACb;gBACA;gBACA;gBACA;gBACA;gBACA,wBAA0B,OAAO;aAClC;YACD,OAAO,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO,MAAM,EAAE;QAC1D;QAEA,yBAAyB;QACzB,MAAM,eAAe,CAAC;YACpB,IAAI,CAAC,KAAK;YAEV,IAAI,SAAS;YACb,IAAI,GAAG,CAAC,SAAS,CAAC,EAAE,SAAS,CAAC,EAAE,SAAS,IAAI,EAAE,GAAG,KAAK,EAAE,GAAG;YAC5D,IAAI,SAAS,GAAG,SAAS,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,SAAS,OAAO,CAAC,CAAC,CAAC;YACnE,IAAI,IAAI;QACV;QAEA,kEAAkE;QAClE,MAAM,kBAAkB;YACtB,IAAI,CAAC,KAAK;YAEV,MAAM,cAAc;YAEpB,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,OAAO,CAAC,MAAM,EAAE,IAAK;gBACjD,IAAK,IAAI,IAAI,IAAI,GAAG,IAAI,UAAU,OAAO,CAAC,MAAM,EAAE,IAAK;oBACrD,MAAM,KAAK,UAAU,OAAO,CAAC,EAAE,CAAC,CAAC,GAAG,UAAU,OAAO,CAAC,EAAE,CAAC,CAAC;oBAC1D,MAAM,KAAK,UAAU,OAAO,CAAC,EAAE,CAAC,CAAC,GAAG,UAAU,OAAO,CAAC,EAAE,CAAC,CAAC;oBAC1D,MAAM,WAAW,KAAK,IAAI,CAAC,KAAK,KAAK,KAAK;oBAE1C,IAAI,WAAW,aAAa;wBAC1B,MAAM,UAAU,MAAM,CAAC,IAAI,WAAW,WAAW;wBACjD,IAAI,SAAS;wBACb,IAAI,MAAM,CAAC,UAAU,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE,UAAU,OAAO,CAAC,EAAE,CAAC,CAAC;wBACzD,IAAI,MAAM,CAAC,UAAU,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE,UAAU,OAAO,CAAC,EAAE,CAAC,CAAC;wBACzD,IAAI,WAAW,GAAG,CAAC,oBAAoB,EAAE,QAAQ,CAAC,CAAC;wBACnD,IAAI,SAAS,GAAG;wBAChB,IAAI,MAAM;oBACZ;gBACF;YACF;QACF;QAEA,0CAA0C;QAC1C,MAAM,UAAU;YACd,IAAI,CAAC,OAAO,CAAC,QAAQ;YAErB,IAAI,SAAS,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;YAE/C,4BAA4B;YAC5B,UAAU,OAAO,CAAC,OAAO,CAAC,CAAA;gBACxB,kBAAkB;gBAClB,SAAS,CAAC,IAAI,SAAS,MAAM;gBAC7B,SAAS,CAAC,IAAI,SAAS,MAAM;gBAE7B,mBAAmB;gBACnB,IAAI,SAAS,CAAC,GAAG,KAAK,SAAS,CAAC,GAAG,OAAO,KAAK,EAAE;oBAC/C,SAAS,MAAM,IAAI,CAAC;gBACtB;gBAEA,IAAI,SAAS,CAAC,GAAG,KAAK,SAAS,CAAC,GAAG,OAAO,MAAM,EAAE;oBAChD,SAAS,MAAM,IAAI,CAAC;gBACtB;gBAEA,2BAA2B;gBAC3B,MAAM,KAAK,cAAc,OAAO,CAAC,CAAC,GAAG,SAAS,CAAC;gBAC/C,MAAM,KAAK,cAAc,OAAO,CAAC,CAAC,GAAG,SAAS,CAAC;gBAC/C,MAAM,WAAW,KAAK,IAAI,CAAC,KAAK,KAAK,KAAK;gBAE1C,IAAI,WAAW,KAAK;oBAClB,MAAM,QAAQ,KAAK,KAAK,CAAC,IAAI;oBAC7B,MAAM,QAAQ,MAAM,CAAC,IAAI,WAAW,GAAG;oBAEvC,SAAS,MAAM,IAAI,KAAK,GAAG,CAAC,SAAS;oBACrC,SAAS,MAAM,IAAI,KAAK,GAAG,CAAC,SAAS;oBAErC,cAAc;oBACd,MAAM,QAAQ,KAAK,IAAI,CAAC,SAAS,MAAM,GAAG,SAAS,MAAM,GAAG,SAAS,MAAM,GAAG,SAAS,MAAM;oBAC7F,IAAI,QAAQ,GAAG;wBACb,SAAS,MAAM,GAAG,AAAC,SAAS,MAAM,GAAG,QAAS;wBAC9C,SAAS,MAAM,GAAG,AAAC,SAAS,MAAM,GAAG,QAAS;oBAChD;gBACF;gBAEA,oBAAoB;gBACpB,aAAa;YACf;YAEA,mBAAmB;YACnB;YAEA,qBAAqB;YACrB,iBAAiB,OAAO,GAAG,sBAAsB;QACnD;QAEA,yBAAyB;QACzB,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,gBAAgB,CAAC,aAAa;QAErC,iCAAiC;QACjC;QACA;QAEA,WAAW;QACX,OAAO;YACL,OAAO,mBAAmB,CAAC,UAAU;YACrC,OAAO,mBAAmB,CAAC,aAAa;YACxC,qBAAqB,iBAAiB,OAAO;QAC/C;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QACC,KAAK;QACL,WAAU;;;;;;AAGhB", "debugId": null}}]}