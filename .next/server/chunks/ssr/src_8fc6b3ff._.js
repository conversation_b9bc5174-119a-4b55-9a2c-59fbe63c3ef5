module.exports = {

"[project]/src/data/resources.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getCategoryById": (()=>getCategoryById),
    "getResourcesByCategory": (()=>getResourcesByCategory),
    "resourceCategories": (()=>resourceCategories),
    "resources": (()=>resources)
});
const resourceCategories = [
    {
        id: 'productivity',
        name: 'Productivity Tools',
        description: 'Tools to enhance personal and team productivity, including task management, note-taking, and time tracking.',
        icon: 'bolt'
    },
    {
        id: 'project-management',
        name: 'Project Management',
        description: 'Software and platforms for planning, executing, and monitoring projects of all sizes.',
        icon: 'tasks'
    },
    {
        id: 'design',
        name: 'Design Tools',
        description: 'Resources for UI/UX design, graphic design, prototyping, and visual collaboration.',
        icon: 'palette'
    },
    {
        id: 'development',
        name: 'Development Tools',
        description: 'Tools and services for software development, coding, testing, and deployment.',
        icon: 'code'
    },
    {
        id: 'research',
        name: 'Research Tools',
        description: 'Resources for market research, user research, academic research, and data collection.',
        icon: 'magnifying-glass-chart'
    },
    {
        id: 'analytics',
        name: 'Analytics & Data',
        description: 'Tools for data analysis, visualization, business intelligence, and reporting.',
        icon: 'chart-line'
    },
    {
        id: 'communication',
        name: 'Communication',
        description: 'Platforms for team communication, client meetings, presentations, and email management.',
        icon: 'comments'
    },
    {
        id: 'collaboration',
        name: 'Collaboration',
        description: 'Tools that facilitate teamwork, knowledge sharing, and cross-functional collaboration.',
        icon: 'users-gear'
    }
];
const resources = [
    // Productivity Tools
    {
        id: 'notion-ai',
        name: 'Notion AI',
        description: 'AI-powered writing assistant integrated into Notion that helps draft, edit, summarize, and brainstorm content directly in your workspace.',
        url: 'https://www.notion.so/product/ai',
        category: 'productivity',
        tags: [
            'writing-assistant',
            'content-generation',
            'summarization'
        ],
        pricing: 'paid',
        logoUrl: 'https://upload.wikimedia.org/wikipedia/commons/4/45/Notion_app_logo.png'
    },
    {
        id: 'mem',
        name: 'Mem',
        description: 'AI-powered workspace that automatically organizes your notes and knowledge with powerful search and retrieval capabilities.',
        url: 'https://mem.ai',
        category: 'productivity',
        tags: [
            'note-taking',
            'knowledge-management',
            'ai-organization'
        ],
        pricing: 'freemium',
        logoUrl: 'https://mem.ai/assets/favicons/favicon.svg'
    },
    {
        id: 'otter',
        name: 'Otter.ai',
        description: 'AI meeting assistant that records, transcribes, and summarizes meetings in real-time with speaker identification.',
        url: 'https://otter.ai',
        category: 'productivity',
        tags: [
            'transcription',
            'meeting-assistant',
            'note-taking'
        ],
        pricing: 'freemium',
        logoUrl: 'https://assets-global.website-files.com/618e9316785b3582a5178502/6230b90e3dceec1c2208f309_favicon-256x256.png'
    },
    {
        id: 'reclaim',
        name: 'Reclaim.ai',
        description: 'AI calendar assistant that automatically schedules your tasks, habits, and meetings to optimize your time and protect your calendar.',
        url: 'https://reclaim.ai',
        category: 'productivity',
        tags: [
            'calendar-management',
            'time-blocking',
            'scheduling'
        ],
        pricing: 'freemium',
        logoUrl: 'https://reclaim.ai/favicon/favicon-32x32.png'
    },
    {
        id: 'timetask',
        name: 'TimeTask AI',
        description: 'AI-powered time tracking tool that automatically categorizes your work and provides insights to improve productivity.',
        url: 'https://timetask.ai',
        category: 'productivity',
        tags: [
            'time-tracking',
            'productivity-analytics',
            'work-insights'
        ],
        pricing: 'freemium',
        logoUrl: 'https://timetask.ai/favicon.ico'
    },
    // Project Management
    {
        id: 'asana-ai',
        name: 'Asana AI',
        description: 'AI assistant integrated into Asana that helps teams plan projects, write task descriptions, summarize discussions, and automate workflows.',
        url: 'https://asana.com/ai',
        category: 'project-management',
        tags: [
            'task-management',
            'ai-assistant',
            'workflow-automation'
        ],
        pricing: 'paid',
        logoUrl: 'https://upload.wikimedia.org/wikipedia/commons/3/3b/Asana_logo.svg'
    },
    {
        id: 'clickup-ai',
        name: 'ClickUp AI',
        description: 'AI-powered project management assistant that writes, edits, summarizes, and generates content directly within your project management workflow.',
        url: 'https://clickup.com/ai',
        category: 'project-management',
        tags: [
            'content-generation',
            'task-automation',
            'summarization'
        ],
        pricing: 'paid',
        logoUrl: 'https://clickup.com/landing/images/clickup-logo.svg'
    },
    {
        id: 'motion',
        name: 'Motion',
        description: 'AI-powered project management tool that automatically schedules and prioritizes tasks based on deadlines, priorities, and team capacity.',
        url: 'https://www.usemotion.com',
        category: 'project-management',
        tags: [
            'ai-scheduling',
            'task-prioritization',
            'time-management'
        ],
        pricing: 'paid',
        logoUrl: 'https://www.usemotion.com/favicon.png'
    },
    {
        id: 'forecast',
        name: 'Forecast',
        description: 'AI-native platform for project and resource management that predicts project delivery dates, resource needs, and budget requirements.',
        url: 'https://www.forecast.app',
        category: 'project-management',
        tags: [
            'resource-planning',
            'project-forecasting',
            'budget-management'
        ],
        pricing: 'paid',
        logoUrl: 'https://www.forecast.app/hubfs/favicon-1.png'
    },
    {
        id: 'teamwork-ai',
        name: 'Teamwork AI',
        description: 'AI-powered project management platform that automates routine tasks, provides insights, and helps teams deliver projects more efficiently.',
        url: 'https://www.teamwork.com/ai-project-management-software',
        category: 'project-management',
        tags: [
            'task-automation',
            'project-insights',
            'team-collaboration'
        ],
        pricing: 'paid',
        logoUrl: 'https://www.teamwork.com/app/themes/teamwork-theme/dist/images/favicon/apple-touch-icon.png'
    },
    // Design Tools
    {
        id: 'midjourney',
        name: 'Midjourney',
        description: 'AI image generation tool that creates stunning visuals from text descriptions, ideal for concept art, illustrations, and design inspiration.',
        url: 'https://www.midjourney.com',
        category: 'design',
        tags: [
            'ai-image-generation',
            'concept-art',
            'visual-design'
        ],
        pricing: 'paid',
        logoUrl: 'https://upload.wikimedia.org/wikipedia/commons/e/e6/Midjourney_Emblem.png'
    },
    {
        id: 'figma-ai',
        name: 'Figma AI',
        description: 'AI-powered design features in Figma that help generate and edit designs, create variations, and improve design workflows.',
        url: 'https://www.figma.com/ai',
        category: 'design',
        tags: [
            'ui-design',
            'design-generation',
            'workflow-automation'
        ],
        pricing: 'paid',
        logoUrl: 'https://upload.wikimedia.org/wikipedia/commons/3/33/Figma-logo.svg'
    },
    {
        id: 'gamma',
        name: 'Gamma',
        description: 'AI-powered presentation platform that transforms simple text prompts into beautiful, presentation-ready slides and documents.',
        url: 'https://gamma.app',
        category: 'design',
        tags: [
            'presentations',
            'content-generation',
            'slide-design'
        ],
        pricing: 'freemium',
        logoUrl: 'https://assets-global.website-files.com/6127a84dfe068e153ef20572/62b1399ae4703b5c4f195f80_Favicon.png'
    },
    {
        id: 'canva-ai',
        name: 'Canva AI',
        description: 'Suite of AI tools in Canva that help generate designs, text, images, and edit photos to create professional-looking content quickly.',
        url: 'https://www.canva.com/ai-image-generator/',
        category: 'design',
        tags: [
            'graphic-design',
            'image-generation',
            'text-to-image'
        ],
        pricing: 'freemium',
        logoUrl: 'https://upload.wikimedia.org/wikipedia/commons/0/08/Canva_icon_2021.svg'
    },
    {
        id: 'runway',
        name: 'Runway',
        description: 'AI-powered creative suite for generating and editing videos, images, and 3D content with text prompts and intuitive controls.',
        url: 'https://runwayml.com',
        category: 'design',
        tags: [
            'video-generation',
            'image-editing',
            'creative-tools'
        ],
        pricing: 'freemium',
        logoUrl: 'https://runwayml.com/favicon.svg'
    },
    // Development Tools
    {
        id: 'github-copilot',
        name: 'GitHub Copilot',
        description: 'AI pair programmer that suggests code completions and entire functions in real-time, directly in your editor.',
        url: 'https://github.com/features/copilot',
        category: 'development',
        tags: [
            'code-completion',
            'pair-programming',
            'ai-coding'
        ],
        pricing: 'paid',
        logoUrl: 'https://github.githubassets.com/assets/GitHub-Mark-ea2971cee799.png'
    },
    {
        id: 'augment-agent',
        name: 'Augment Agent',
        description: 'AI coding assistant that helps developers understand, modify, and debug code with natural language, providing context-aware suggestions.',
        url: 'https://www.augmentcode.com/',
        category: 'development',
        tags: [
            'code-understanding',
            'debugging',
            'ai-assistant'
        ],
        pricing: 'paid',
        logoUrl: 'https://www.augmentcode.com/favicon.ico'
    },
    {
        id: 'tabnine',
        name: 'Tabnine',
        description: 'AI code completion tool that predicts and suggests code based on context and patterns, supporting multiple programming languages and IDEs.',
        url: 'https://www.tabnine.com',
        category: 'development',
        tags: [
            'code-completion',
            'ai-assistant',
            'multi-language'
        ],
        pricing: 'freemium',
        logoUrl: 'https://www.tabnine.com/favicon.ico'
    },
    {
        id: 'codeium',
        name: 'Codeium',
        description: 'Free AI-powered code completion and chat tool that helps developers write code faster with context-aware suggestions.',
        url: 'https://codeium.com',
        category: 'development',
        tags: [
            'code-completion',
            'code-chat',
            'free'
        ],
        pricing: 'freemium',
        logoUrl: 'https://codeium.com/favicon.ico'
    },
    {
        id: 'warp',
        name: 'Warp',
        description: 'AI-powered terminal that makes the command line more productive with features like AI command search, suggestions, and explanations.',
        url: 'https://www.warp.dev',
        category: 'development',
        tags: [
            'terminal',
            'command-line',
            'productivity'
        ],
        pricing: 'freemium',
        logoUrl: 'https://assets.warp.dev/warp_logo.png'
    },
    // Research Tools
    {
        id: 'elicit',
        name: 'Elicit',
        description: 'AI research assistant that helps you find, summarize, and analyze academic papers, extracting key insights and generating literature reviews.',
        url: 'https://elicit.org',
        category: 'research',
        tags: [
            'academic-research',
            'paper-summaries',
            'literature-review'
        ],
        pricing: 'freemium',
        logoUrl: 'https://elicit.org/favicon.ico'
    },
    {
        id: 'consensus',
        name: 'Consensus',
        description: 'AI-powered search engine for scientific research that provides summaries of papers and highlights key findings with citations.',
        url: 'https://consensus.app',
        category: 'research',
        tags: [
            'scientific-search',
            'paper-summaries',
            'evidence-based'
        ],
        pricing: 'freemium',
        logoUrl: 'https://consensus.app/favicon.ico'
    },
    {
        id: 'perplexity',
        name: 'Perplexity AI',
        description: 'AI-powered answer engine that provides comprehensive, cited responses to complex research questions with sources and references.',
        url: 'https://www.perplexity.ai',
        category: 'research',
        tags: [
            'answer-engine',
            'information-search',
            'cited-responses'
        ],
        pricing: 'freemium',
        logoUrl: 'https://www.perplexity.ai/favicon.ico'
    },
    {
        id: 'scholarai',
        name: 'ScholarAI',
        description: 'AI research assistant that helps academics search, summarize, and analyze scientific papers, generating insights and connections.',
        url: 'https://scholarai.io',
        category: 'research',
        tags: [
            'academic-research',
            'paper-analysis',
            'literature-review'
        ],
        pricing: 'freemium',
        logoUrl: 'https://scholarai.io/favicon.ico'
    },
    {
        id: 'researchrabbit',
        name: 'ResearchRabbit',
        description: 'AI-powered literature discovery tool that helps researchers find relevant papers and visualize connections between publications.',
        url: 'https://www.researchrabbit.ai',
        category: 'research',
        tags: [
            'literature-discovery',
            'citation-network',
            'research-mapping'
        ],
        pricing: 'freemium',
        logoUrl: 'https://www.researchrabbit.ai/favicon.png'
    },
    // Analytics & Data
    {
        id: 'obviously-ai',
        name: 'Obviously AI',
        description: 'No-code AI platform that allows anyone to build and deploy machine learning models for prediction and analysis without coding.',
        url: 'https://www.obviously.ai',
        category: 'analytics',
        tags: [
            'no-code-ai',
            'predictive-analytics',
            'machine-learning'
        ],
        pricing: 'paid',
        logoUrl: 'https://www.obviously.ai/favicon.ico'
    },
    {
        id: 'akkio',
        name: 'Akkio',
        description: 'AI platform that makes it easy for businesses to build, deploy, and use predictive models without data science expertise.',
        url: 'https://www.akkio.com',
        category: 'analytics',
        tags: [
            'predictive-analytics',
            'no-code',
            'business-intelligence'
        ],
        pricing: 'paid',
        logoUrl: 'https://www.akkio.com/favicon.ico'
    },
    {
        id: 'deepnote',
        name: 'Deepnote',
        description: 'AI-enhanced collaborative data notebook that combines the best of notebooks, spreadsheets, and visual interfaces for data analysis.',
        url: 'https://deepnote.com',
        category: 'analytics',
        tags: [
            'data-science',
            'notebooks',
            'collaboration'
        ],
        pricing: 'freemium',
        logoUrl: 'https://deepnote.com/favicon.ico'
    },
    {
        id: 'hex',
        name: 'Hex',
        description: 'Collaborative data platform with AI capabilities that helps teams explore, analyze, and share data insights through notebooks and apps.',
        url: 'https://hex.tech',
        category: 'analytics',
        tags: [
            'data-science',
            'collaboration',
            'data-apps'
        ],
        pricing: 'freemium',
        logoUrl: 'https://hex.tech/favicon.ico'
    },
    {
        id: 'einblick',
        name: 'Einblick',
        description: 'AI-powered data science platform that accelerates analysis with collaborative canvas, automated insights, and predictive capabilities.',
        url: 'https://www.einblick.ai',
        category: 'analytics',
        tags: [
            'data-science',
            'visual-analytics',
            'collaboration'
        ],
        pricing: 'freemium',
        logoUrl: 'https://www.einblick.ai/favicon.ico'
    },
    // Communication
    {
        id: 'chatgpt',
        name: 'ChatGPT',
        description: 'AI assistant that can draft emails, summarize conversations, generate content, and help with various communication tasks.',
        url: 'https://chat.openai.com',
        category: 'communication',
        tags: [
            'ai-assistant',
            'content-generation',
            'writing-help'
        ],
        pricing: 'freemium',
        logoUrl: 'https://chat.openai.com/apple-touch-icon.png'
    },
    {
        id: 'fireflies',
        name: 'Fireflies.ai',
        description: 'AI meeting assistant that records, transcribes, and analyzes voice conversations, creating searchable notes and action items.',
        url: 'https://fireflies.ai',
        category: 'communication',
        tags: [
            'meeting-assistant',
            'transcription',
            'meeting-insights'
        ],
        pricing: 'freemium',
        logoUrl: 'https://fireflies.ai/favicon.ico'
    },
    {
        id: 'grammarly',
        name: 'Grammarly',
        description: 'AI writing assistant that helps improve grammar, clarity, engagement, and delivery in all types of written communication.',
        url: 'https://www.grammarly.com',
        category: 'communication',
        tags: [
            'writing-assistant',
            'grammar-checker',
            'communication-improvement'
        ],
        pricing: 'freemium',
        logoUrl: 'https://static.grammarly.com/assets/files/efe57d016d9efff36da7884c193b646b/favicon-32x32.png'
    },
    {
        id: 'summary-ai',
        name: 'Summary AI',
        description: 'AI tool that automatically summarizes meetings, articles, and documents to extract key points and action items.',
        url: 'https://www.summary.ai',
        category: 'communication',
        tags: [
            'summarization',
            'meeting-notes',
            'information-extraction'
        ],
        pricing: 'freemium',
        logoUrl: 'https://www.summary.ai/favicon.ico'
    },
    {
        id: 'descript',
        name: 'Descript',
        description: 'All-in-one audio and video editing platform with AI features like transcription, content editing, and voice generation.',
        url: 'https://www.descript.com',
        category: 'communication',
        tags: [
            'video-editing',
            'transcription',
            'content-creation'
        ],
        pricing: 'freemium',
        logoUrl: 'https://www.descript.com/favicon.ico'
    },
    // Collaboration
    {
        id: 'coda-ai',
        name: 'Coda AI',
        description: 'AI-powered collaborative document platform that combines docs, spreadsheets, and apps with AI assistance for content generation and analysis.',
        url: 'https://coda.io/product/ai-alpha',
        category: 'collaboration',
        tags: [
            'documents',
            'ai-writing',
            'team-workspace'
        ],
        pricing: 'freemium',
        logoUrl: 'https://cdn.coda.io/icons/png/color/coda-192.png'
    },
    {
        id: 'tome',
        name: 'Tome',
        description: 'AI-powered storytelling format that helps teams create beautiful, interactive presentations and documents with generative AI.',
        url: 'https://tome.app',
        category: 'collaboration',
        tags: [
            'presentations',
            'storytelling',
            'content-generation'
        ],
        pricing: 'freemium',
        logoUrl: 'https://tome.app/favicon.ico'
    },
    {
        id: 'mural-ai',
        name: 'MURAL AI',
        description: 'AI-enhanced digital workspace for visual collaboration that helps teams brainstorm, plan, and solve problems together.',
        url: 'https://www.mural.co/ai-features',
        category: 'collaboration',
        tags: [
            'visual-collaboration',
            'brainstorming',
            'workshops'
        ],
        pricing: 'paid',
        logoUrl: 'https://assets-global.website-files.com/62e11362da2667ac3d0e6ed5/62e3a2c2d605d15a6bffc4a2_favicon-32.png'
    },
    {
        id: 'notion-ai-collab',
        name: 'Notion AI for Teams',
        description: 'AI-powered collaborative workspace that helps teams create, organize, and share knowledge with AI assistance for content and insights.',
        url: 'https://www.notion.so/product/ai',
        category: 'collaboration',
        tags: [
            'team-workspace',
            'knowledge-management',
            'ai-writing'
        ],
        pricing: 'paid',
        logoUrl: 'https://upload.wikimedia.org/wikipedia/commons/4/45/Notion_app_logo.png'
    },
    {
        id: 'craft-ai',
        name: 'Craft AI',
        description: 'AI-powered document editor and knowledge base that helps teams create beautiful documents and organize information.',
        url: 'https://www.craft.do/features/ai',
        category: 'collaboration',
        tags: [
            'documents',
            'knowledge-base',
            'content-creation'
        ],
        pricing: 'freemium',
        logoUrl: 'https://www.craft.do/images/favicon.ico'
    }
];
const getResourcesByCategory = (category)=>{
    return resources.filter((resource)=>resource.category === category);
};
const getCategoryById = (id)=>{
    return resourceCategories.find((category)=>category.id === id);
};
}}),
"[project]/src/app/learning/resources/[category]/page.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>CategoryPage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$react$2d$fontawesome$2f$index$2e$es$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@fortawesome/react-fontawesome/index.es.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@fortawesome/free-solid-svg-icons/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$resources$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/data/resources.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
;
;
// Map category IDs to FontAwesome icons
const categoryIcons = {
    'productivity': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["faBolt"],
    'project-management': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["faTasks"],
    'design': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["faPalette"],
    'development': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["faCode"],
    'research': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["faMagnifyingGlassChart"],
    'analytics': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["faChartLine"],
    'communication': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["faComments"],
    'collaboration': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["faUsersGear"]
};
function CategoryPage() {
    const params = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useParams"])();
    const categoryId = params.category;
    const category = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$resources$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getCategoryById"])(categoryId);
    const resources = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$resources$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getResourcesByCategory"])(categoryId);
    const [searchTerm, setSearchTerm] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('');
    const [selectedPricing, setSelectedPricing] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    // Get all unique tags from resources in this category
    const allTags = Array.from(new Set(resources.flatMap((resource)=>resource.tags || []))).sort();
    // Get all unique pricing options from resources in this category
    const allPricingOptions = Array.from(new Set(resources.map((resource)=>resource.pricing))).filter(Boolean);
    // Filter resources based on search term and pricing filter
    const filteredResources = resources.filter((resource)=>{
        const matchesSearch = resource.name.toLowerCase().includes(searchTerm.toLowerCase()) || resource.description.toLowerCase().includes(searchTerm.toLowerCase()) || resource.tags && resource.tags.some((tag)=>tag.toLowerCase().includes(searchTerm.toLowerCase()));
        const matchesPricing = selectedPricing.length === 0 || resource.pricing && selectedPricing.includes(resource.pricing);
        return matchesSearch && matchesPricing;
    });
    // Toggle pricing filter
    const togglePricingFilter = (pricing)=>{
        if (selectedPricing.includes(pricing)) {
            setSelectedPricing(selectedPricing.filter((p)=>p !== pricing));
        } else {
            setSelectedPricing([
                ...selectedPricing,
                pricing
            ]);
        }
    };
    if (!category) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex flex-col items-center justify-center py-12 text-center",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                    className: "mb-4 text-2xl font-bold",
                    children: "Category Not Found"
                }, void 0, false, {
                    fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                    lineNumber: 81,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "mb-6 text-gray-600 dark:text-gray-400",
                    children: "The resource category you're looking for doesn't exist."
                }, void 0, false, {
                    fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                    lineNumber: 82,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                    href: "/learning/resources",
                    className: "flex items-center rounded-md bg-teal-600 px-4 py-2 text-white hover:bg-teal-700",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$react$2d$fontawesome$2f$index$2e$es$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FontAwesomeIcon"], {
                            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["faArrowLeft"],
                            className: "mr-2 h-4 w-4"
                        }, void 0, false, {
                            fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                            lineNumber: 87,
                            columnNumber: 11
                        }, this),
                        "Back to Resources"
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                    lineNumber: 83,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
            lineNumber: 80,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "space-y-8",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "rounded-lg bg-gradient-to-r from-teal-600 to-emerald-700 p-8 text-white shadow-lg",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        href: "/learning/resources",
                        className: "mb-4 inline-flex items-center rounded-md bg-white bg-opacity-20 px-3 py-1 text-sm font-medium text-white backdrop-blur-sm transition hover:bg-opacity-30",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$react$2d$fontawesome$2f$index$2e$es$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FontAwesomeIcon"], {
                                icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["faArrowLeft"],
                                className: "mr-2 h-3 w-3"
                            }, void 0, false, {
                                fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                lineNumber: 101,
                                columnNumber: 11
                            }, this),
                            "Back to Resources"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                        lineNumber: 97,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "mr-4 flex h-16 w-16 items-center justify-center rounded-full bg-white bg-opacity-20 backdrop-blur-sm",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$react$2d$fontawesome$2f$index$2e$es$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FontAwesomeIcon"], {
                                    icon: categoryIcons[category.id],
                                    className: "h-8 w-8"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                    lineNumber: 107,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                lineNumber: 106,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                        className: "mb-2 text-3xl font-bold",
                                        children: category.name
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                        lineNumber: 110,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-lg",
                                        children: category.description
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                        lineNumber: 111,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                lineNumber: 109,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                        lineNumber: 105,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                lineNumber: 96,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex flex-col space-y-4 md:flex-row md:space-y-0 md:space-x-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "w-full md:w-1/4",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "rounded-lg border border-gray-200 bg-white p-4 shadow-sm dark:border-gray-800 dark:bg-gray-900",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                    className: "mb-4 font-semibold",
                                    children: "Search & Filter"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                    lineNumber: 120,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "mb-4",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                            htmlFor: "search",
                                            className: "mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300",
                                            children: "Search"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                            lineNumber: 124,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "relative",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$react$2d$fontawesome$2f$index$2e$es$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FontAwesomeIcon"], {
                                                        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["faSearch"],
                                                        className: "h-4 w-4 text-gray-400"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                        lineNumber: 129,
                                                        columnNumber: 19
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                    lineNumber: 128,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                    type: "text",
                                                    id: "search",
                                                    className: "block w-full rounded-md border-gray-300 pl-10 focus:border-teal-500 focus:ring-teal-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white sm:text-sm",
                                                    placeholder: "Search resources...",
                                                    value: searchTerm,
                                                    onChange: (e)=>setSearchTerm(e.target.value)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                    lineNumber: 131,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                            lineNumber: 127,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                    lineNumber: 123,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "mb-4",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                            className: "mb-2 flex items-center text-sm font-medium text-gray-700 dark:text-gray-300",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$react$2d$fontawesome$2f$index$2e$es$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FontAwesomeIcon"], {
                                                    icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["faFilter"],
                                                    className: "mr-2 h-3 w-3"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                    lineNumber: 145,
                                                    columnNumber: 17
                                                }, this),
                                                "Pricing"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                            lineNumber: 144,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "space-y-2",
                                            children: allPricingOptions.map((pricing)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex items-center",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                            id: `pricing-${pricing}`,
                                                            type: "checkbox",
                                                            className: "h-4 w-4 rounded border-gray-300 text-teal-600 focus:ring-teal-500 dark:border-gray-700 dark:bg-gray-800",
                                                            checked: selectedPricing.includes(pricing),
                                                            onChange: ()=>togglePricingFilter(pricing)
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                            lineNumber: 151,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                            htmlFor: `pricing-${pricing}`,
                                                            className: "ml-2 text-sm text-gray-700 dark:text-gray-300",
                                                            children: pricing.charAt(0).toUpperCase() + pricing.slice(1)
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                            lineNumber: 158,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, pricing, true, {
                                                    fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                    lineNumber: 150,
                                                    columnNumber: 19
                                                }, this))
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                            lineNumber: 148,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                    lineNumber: 143,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                            className: "mb-2 flex items-center text-sm font-medium text-gray-700 dark:text-gray-300",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$react$2d$fontawesome$2f$index$2e$es$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FontAwesomeIcon"], {
                                                    icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["faTag"],
                                                    className: "mr-2 h-3 w-3"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                    lineNumber: 172,
                                                    columnNumber: 17
                                                }, this),
                                                "Popular Tags"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                            lineNumber: 171,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex flex-wrap gap-2",
                                            children: allTags.slice(0, 10).map((tag)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    onClick: ()=>setSearchTerm(tag),
                                                    className: "inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700",
                                                    children: tag
                                                }, tag, false, {
                                                    fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                    lineNumber: 177,
                                                    columnNumber: 19
                                                }, this))
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                            lineNumber: 175,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                    lineNumber: 170,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                            lineNumber: 119,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                        lineNumber: 118,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "w-full md:w-3/4",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "rounded-lg border border-gray-200 bg-white shadow-sm dark:border-gray-800 dark:bg-gray-900",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "overflow-x-auto",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("table", {
                                    className: "min-w-full divide-y divide-gray-200 dark:divide-gray-800",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("thead", {
                                            className: "bg-gray-50 dark:bg-gray-800",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                        scope: "col",
                                                        className: "px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400",
                                                        children: "Name"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                        lineNumber: 197,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                        scope: "col",
                                                        className: "px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400",
                                                        children: "Description"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                        lineNumber: 200,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                        scope: "col",
                                                        className: "px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400",
                                                        children: "Pricing"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                        lineNumber: 203,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                        scope: "col",
                                                        className: "px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400",
                                                        children: "Link"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                        lineNumber: 206,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                lineNumber: 196,
                                                columnNumber: 19
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                            lineNumber: 195,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("tbody", {
                                            className: "divide-y divide-gray-200 bg-white dark:divide-gray-800 dark:bg-gray-900",
                                            children: filteredResources.length > 0 ? filteredResources.map((resource)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                                    className: "hover:bg-gray-50 dark:hover:bg-gray-800",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                            className: "whitespace-nowrap px-6 py-4",
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "flex items-center",
                                                                children: [
                                                                    resource.logoUrl && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                                                        src: resource.logoUrl,
                                                                        alt: `${resource.name} logo`,
                                                                        className: "mr-3 h-8 w-8 rounded-full object-contain"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                                        lineNumber: 218,
                                                                        columnNumber: 31
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: "font-medium text-gray-900 dark:text-white",
                                                                        children: resource.name
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                                        lineNumber: 224,
                                                                        columnNumber: 29
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                                lineNumber: 216,
                                                                columnNumber: 27
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                            lineNumber: 215,
                                                            columnNumber: 25
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                            className: "px-6 py-4",
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "text-sm text-gray-600 dark:text-gray-400",
                                                                children: [
                                                                    resource.description,
                                                                    resource.tags && resource.tags.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: "mt-2 flex flex-wrap gap-1",
                                                                        children: resource.tags.map((tag)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                className: "inline-flex items-center rounded-full bg-teal-100 px-2 py-0.5 text-xs font-medium text-teal-800 dark:bg-teal-900/30 dark:text-teal-300",
                                                                                children: tag
                                                                            }, tag, false, {
                                                                                fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                                                lineNumber: 233,
                                                                                columnNumber: 35
                                                                            }, this))
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                                        lineNumber: 231,
                                                                        columnNumber: 31
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                                lineNumber: 228,
                                                                columnNumber: 27
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                            lineNumber: 227,
                                                            columnNumber: 25
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                            className: "whitespace-nowrap px-6 py-4",
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "inline-flex rounded-full px-2 text-xs font-semibold leading-5",
                                                                children: [
                                                                    resource.pricing === 'free' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: "rounded-full bg-green-100 px-2 py-0.5 text-xs font-medium text-green-800 dark:bg-green-900/30 dark:text-green-300",
                                                                        children: "Free"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                                        lineNumber: 247,
                                                                        columnNumber: 31
                                                                    }, this),
                                                                    resource.pricing === 'freemium' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: "rounded-full bg-blue-100 px-2 py-0.5 text-xs font-medium text-blue-800 dark:bg-blue-900/30 dark:text-blue-300",
                                                                        children: "Freemium"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                                        lineNumber: 252,
                                                                        columnNumber: 31
                                                                    }, this),
                                                                    resource.pricing === 'paid' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: "rounded-full bg-purple-100 px-2 py-0.5 text-xs font-medium text-purple-800 dark:bg-purple-900/30 dark:text-purple-300",
                                                                        children: "Paid"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                                        lineNumber: 257,
                                                                        columnNumber: 31
                                                                    }, this),
                                                                    resource.pricing === 'enterprise' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: "rounded-full bg-gray-100 px-2 py-0.5 text-xs font-medium text-gray-800 dark:bg-gray-700 dark:text-gray-300",
                                                                        children: "Enterprise"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                                        lineNumber: 262,
                                                                        columnNumber: 31
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                                lineNumber: 245,
                                                                columnNumber: 27
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                            lineNumber: 244,
                                                            columnNumber: 25
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                            className: "whitespace-nowrap px-6 py-4 text-sm font-medium",
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                                                href: resource.url,
                                                                target: "_blank",
                                                                rel: "noopener noreferrer",
                                                                className: "text-teal-600 hover:text-teal-900 dark:text-teal-400 dark:hover:text-teal-300",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$react$2d$fontawesome$2f$index$2e$es$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FontAwesomeIcon"], {
                                                                        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["faExternalLinkAlt"],
                                                                        className: "mr-1 h-3 w-3"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                                        lineNumber: 275,
                                                                        columnNumber: 29
                                                                    }, this),
                                                                    "Visit"
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                                lineNumber: 269,
                                                                columnNumber: 27
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                            lineNumber: 268,
                                                            columnNumber: 25
                                                        }, this)
                                                    ]
                                                }, resource.id, true, {
                                                    fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                    lineNumber: 214,
                                                    columnNumber: 23
                                                }, this)) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                    colSpan: 4,
                                                    className: "px-6 py-10 text-center",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "text-gray-500 dark:text-gray-400",
                                                            children: "No resources found matching your criteria."
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                            lineNumber: 284,
                                                            columnNumber: 25
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                            onClick: ()=>{
                                                                setSearchTerm('');
                                                                setSelectedPricing([]);
                                                            },
                                                            className: "mt-2 text-sm font-medium text-teal-600 hover:text-teal-500 dark:text-teal-400",
                                                            children: "Clear filters"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                            lineNumber: 285,
                                                            columnNumber: 25
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                    lineNumber: 283,
                                                    columnNumber: 23
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                                lineNumber: 282,
                                                columnNumber: 21
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                            lineNumber: 211,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                    lineNumber: 194,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                                lineNumber: 193,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                            lineNumber: 192,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                        lineNumber: 191,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
                lineNumber: 116,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/learning/resources/[category]/page.tsx",
        lineNumber: 95,
        columnNumber: 5
    }, this);
}
}}),

};

//# sourceMappingURL=src_8fc6b3ff._.js.map