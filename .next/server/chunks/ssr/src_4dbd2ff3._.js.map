{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/data/mock-analytics.ts"], "sourcesContent": ["import {\n  SectionAnalytics,\n  AgentAnalytics,\n  ContentAnalytics,\n  UserAnalytics,\n  DashboardAnalytics,\n  AgentInteraction\n} from '@/types/analytics';\n\n// Mock section analytics\nexport const mockSectionAnalytics: SectionAnalytics[] = [\n  {\n    section: 'home',\n    viewCount: 12453,\n    uniqueUsers: 3827,\n    averageDuration: 124, // seconds\n    bounceRate: 0.32,\n    trend: 'up',\n    percentChange: 12.5\n  },\n  {\n    section: 'browse',\n    viewCount: 8932,\n    uniqueUsers: 2945,\n    averageDuration: 205,\n    bounceRate: 0.18,\n    trend: 'up',\n    percentChange: 8.3\n  },\n  {\n    section: 'agent_detail',\n    viewCount: 6721,\n    uniqueUsers: 2103,\n    averageDuration: 342,\n    bounceRate: 0.22,\n    trend: 'up',\n    percentChange: 15.7\n  },\n  {\n    section: 'learning',\n    viewCount: 5438,\n    uniqueUsers: 1876,\n    averageDuration: 278,\n    bounceRate: 0.25,\n    trend: 'up',\n    percentChange: 32.1\n  },\n  {\n    section: 'learning_videos',\n    viewCount: 2876,\n    uniqueUsers: 1243,\n    averageDuration: 412,\n    bounceRate: 0.15,\n    trend: 'up',\n    percentChange: 28.4\n  },\n  {\n    section: 'learning_articles',\n    viewCount: 2143,\n    uniqueUsers: 987,\n    averageDuration: 325,\n    bounceRate: 0.21,\n    trend: 'up',\n    percentChange: 18.9\n  },\n  {\n    section: 'learning_blog',\n    viewCount: 1876,\n    uniqueUsers: 754,\n    averageDuration: 298,\n    bounceRate: 0.28,\n    trend: 'stable',\n    percentChange: 2.3\n  },\n  {\n    section: 'learning_resources',\n    viewCount: 3254,\n    uniqueUsers: 1432,\n    averageDuration: 245,\n    bounceRate: 0.19,\n    trend: 'up',\n    percentChange: 42.7\n  },\n  {\n    section: 'profile',\n    viewCount: 3876,\n    uniqueUsers: 2143,\n    averageDuration: 187,\n    bounceRate: 0.12,\n    trend: 'stable',\n    percentChange: 1.8\n  },\n  {\n    section: 'admin',\n    viewCount: 876,\n    uniqueUsers: 32,\n    averageDuration: 543,\n    bounceRate: 0.05,\n    trend: 'up',\n    percentChange: 7.2\n  }\n];\n\n// Mock agent analytics\nexport const mockAgentAnalytics: AgentAnalytics[] = [\n  {\n    agentId: '1',\n    agentName: 'Research Assistant',\n    interactionCount: 8765,\n    uniqueUsers: 2143,\n    averageDuration: 432,\n    sensitivityScore: 42,\n    popularQueries: [\n      { query: 'research on market trends', count: 342 },\n      { query: 'analyze competitor data', count: 287 },\n      { query: 'summarize this report', count: 254 }\n    ],\n    externalApiUsage: {\n      'OpenAI GPT-4': 5432,\n      'Google Search': 2143,\n      'Bing Search': 876\n    }\n  },\n  {\n    agentId: '2',\n    agentName: 'Data Analyst',\n    interactionCount: 7654,\n    uniqueUsers: 1876,\n    averageDuration: 387,\n    sensitivityScore: 68,\n    popularQueries: [\n      { query: 'analyze this dataset', count: 432 },\n      { query: 'create visualization for', count: 321 },\n      { query: 'find patterns in data', count: 287 }\n    ],\n    externalApiUsage: {\n      'OpenAI GPT-4': 4321,\n      'Azure OpenAI': 2876,\n      'Tableau API': 1243\n    }\n  },\n  {\n    agentId: '3',\n    agentName: 'Code Assistant',\n    interactionCount: 6543,\n    uniqueUsers: 1543,\n    averageDuration: 456,\n    sensitivityScore: 35,\n    popularQueries: [\n      { query: 'debug this code', count: 543 },\n      { query: 'optimize this function', count: 432 },\n      { query: 'explain this error', count: 321 }\n    ],\n    externalApiUsage: {\n      'OpenAI Codex': 3876,\n      'GitHub Copilot': 2143,\n      'Anthropic Claude': 987\n    }\n  },\n  {\n    agentId: '4',\n    agentName: 'Writing Assistant',\n    interactionCount: 5432,\n    uniqueUsers: 1432,\n    averageDuration: 321,\n    sensitivityScore: 28,\n    popularQueries: [\n      { query: 'write an email about', count: 654 },\n      { query: 'improve this paragraph', count: 543 },\n      { query: 'summarize this document', count: 432 }\n    ],\n    externalApiUsage: {\n      'OpenAI GPT-4': 3254,\n      'Anthropic Claude': 1876,\n      'Grammarly API': 987\n    }\n  },\n  {\n    agentId: '5',\n    agentName: 'Customer Support',\n    interactionCount: 4321,\n    uniqueUsers: 1243,\n    averageDuration: 276,\n    sensitivityScore: 72,\n    popularQueries: [\n      { query: 'how to reset password', count: 765 },\n      { query: 'billing issue with', count: 654 },\n      { query: 'cancel my subscription', count: 543 }\n    ],\n    externalApiUsage: {\n      'OpenAI GPT-4': 2143,\n      'Zendesk API': 1876,\n      'Salesforce API': 987\n    }\n  }\n];\n\n// Mock content analytics\nexport const mockContentAnalytics: ContentAnalytics[] = [\n  {\n    contentId: 'video-1',\n    contentTitle: 'Introduction to AI Agents',\n    contentType: 'video',\n    viewCount: 3254,\n    uniqueUsers: 2143,\n    averageDuration: 432,\n    completionRate: 0.78,\n    engagementScore: 87\n  },\n  {\n    contentId: 'article-1',\n    contentTitle: 'The Future of AI Agents in Enterprise',\n    contentType: 'article',\n    viewCount: 2876,\n    uniqueUsers: 1876,\n    averageDuration: 387,\n    completionRate: 0.65,\n    engagementScore: 72\n  },\n  {\n    contentId: 'blog-1',\n    contentTitle: 'How We Built Our First AI Agent',\n    contentType: 'blog',\n    viewCount: 2143,\n    uniqueUsers: 1543,\n    averageDuration: 321,\n    completionRate: 0.82,\n    engagementScore: 84\n  },\n  {\n    contentId: 'resource-github-copilot',\n    contentTitle: 'GitHub Copilot',\n    contentType: 'resource',\n    viewCount: 1876,\n    uniqueUsers: 1432,\n    averageDuration: 187,\n    completionRate: 0.91,\n    engagementScore: 76\n  },\n  {\n    contentId: 'video-2',\n    contentTitle: 'Advanced AI Agent Techniques',\n    contentType: 'video',\n    viewCount: 1654,\n    uniqueUsers: 1243,\n    averageDuration: 456,\n    completionRate: 0.72,\n    engagementScore: 81\n  }\n];\n\n// Mock user analytics\nexport const mockUserAnalytics: UserAnalytics[] = [\n  {\n    userId: 'user-1',\n    userName: 'John Smith',\n    lastActive: new Date('2023-05-15T14:32:21'),\n    totalSessions: 87,\n    totalTimeSpent: 24321, // seconds\n    favoriteSection: 'learning',\n    favoriteAgents: ['1', '3'],\n    sensitivityScore: 28,\n    riskLevel: 'low'\n  },\n  {\n    userId: 'user-2',\n    userName: 'Emily Johnson',\n    lastActive: new Date('2023-05-15T16:45:32'),\n    totalSessions: 132,\n    totalTimeSpent: 43265,\n    favoriteSection: 'agent_detail',\n    favoriteAgents: ['2', '4'],\n    sensitivityScore: 65,\n    riskLevel: 'medium'\n  },\n  {\n    userId: 'user-3',\n    userName: 'Michael Brown',\n    lastActive: new Date('2023-05-15T12:21:43'),\n    totalSessions: 54,\n    totalTimeSpent: 18765,\n    favoriteSection: 'browse',\n    favoriteAgents: ['1', '5'],\n    sensitivityScore: 42,\n    riskLevel: 'low'\n  },\n  {\n    userId: 'user-4',\n    userName: 'Sarah Davis',\n    lastActive: new Date('2023-05-15T09:54:12'),\n    totalSessions: 76,\n    totalTimeSpent: 28943,\n    favoriteSection: 'learning_resources',\n    favoriteAgents: ['3', '4'],\n    sensitivityScore: 18,\n    riskLevel: 'none'\n  },\n  {\n    userId: 'user-5',\n    userName: 'David Wilson',\n    lastActive: new Date('2023-05-15T17:32:45'),\n    totalSessions: 112,\n    totalTimeSpent: 38765,\n    favoriteSection: 'learning_videos',\n    favoriteAgents: ['2', '3'],\n    sensitivityScore: 87,\n    riskLevel: 'high'\n  }\n];\n\n// Mock sensitive interactions\nexport const mockSensitiveInteractions: AgentInteraction[] = [\n  {\n    id: 'interaction-1',\n    userId: 'user-5',\n    agentId: '2',\n    timestamp: new Date('2023-05-15T16:32:21'),\n    queryText: 'Analyze this customer data with credit card information',\n    responseText: 'I\\'ve detected sensitive information in your request. I\\'ve redacted the credit card numbers for security purposes. Please avoid sharing sensitive financial information.',\n    duration: 32,\n    sensitivityScore: 92,\n    sensitivityFlags: ['credit_card', 'financial_data', 'pii'],\n    externalApiUsed: true,\n    externalApiName: 'OpenAI GPT-4'\n  },\n  {\n    id: 'interaction-2',\n    userId: 'user-2',\n    agentId: '5',\n    timestamp: new Date('2023-05-15T14:21:43'),\n    queryText: 'Help me draft an email to our clients about their account information including their addresses',\n    responseText: 'I can help draft an email template, but I notice you\\'re mentioning client addresses which could be sensitive. Consider using placeholder text instead of actual addresses in your draft.',\n    duration: 45,\n    sensitivityScore: 78,\n    sensitivityFlags: ['pii', 'address_data', 'client_information'],\n    externalApiUsed: true,\n    externalApiName: 'Anthropic Claude'\n  },\n  {\n    id: 'interaction-3',\n    userId: 'user-5',\n    agentId: '1',\n    timestamp: new Date('2023-05-15T11:43:32'),\n    queryText: 'Research competitive analysis for our confidential Project Falcon',\n    responseText: 'I can help with competitive analysis research. I notice you mentioned \"confidential Project Falcon\" - please be careful about sharing internal project names or confidential information.',\n    duration: 67,\n    sensitivityScore: 82,\n    sensitivityFlags: ['confidential', 'internal_project', 'competitive_data'],\n    externalApiUsed: true,\n    externalApiName: 'OpenAI GPT-4'\n  }\n];\n\n// Mock dashboard analytics\nexport const mockDashboardAnalytics: DashboardAnalytics = {\n  totalUsers: 5432,\n  activeUsers: {\n    daily: 1243,\n    weekly: 2876,\n    monthly: 4321\n  },\n  newUsers: {\n    daily: 87,\n    weekly: 432,\n    monthly: 1876\n  },\n  totalAgentInteractions: 32543,\n  totalContentViews: 28765,\n  averageSessionDuration: 324, // seconds\n  sensitivityAlerts: 187,\n  topSections: mockSectionAnalytics.slice(0, 5),\n  topAgents: mockAgentAnalytics.slice(0, 3),\n  topContent: mockContentAnalytics.slice(0, 3),\n  recentSensitiveInteractions: mockSensitiveInteractions\n};\n"], "names": [], "mappings": ";;;;;;;;AAUO,MAAM,uBAA2C;IACtD;QACE,SAAS;QACT,WAAW;QACX,aAAa;QACb,iBAAiB;QACjB,YAAY;QACZ,OAAO;QACP,eAAe;IACjB;IACA;QACE,SAAS;QACT,WAAW;QACX,aAAa;QACb,iBAAiB;QACjB,YAAY;QACZ,OAAO;QACP,eAAe;IACjB;IACA;QACE,SAAS;QACT,WAAW;QACX,aAAa;QACb,iBAAiB;QACjB,YAAY;QACZ,OAAO;QACP,eAAe;IACjB;IACA;QACE,SAAS;QACT,WAAW;QACX,aAAa;QACb,iBAAiB;QACjB,YAAY;QACZ,OAAO;QACP,eAAe;IACjB;IACA;QACE,SAAS;QACT,WAAW;QACX,aAAa;QACb,iBAAiB;QACjB,YAAY;QACZ,OAAO;QACP,eAAe;IACjB;IACA;QACE,SAAS;QACT,WAAW;QACX,aAAa;QACb,iBAAiB;QACjB,YAAY;QACZ,OAAO;QACP,eAAe;IACjB;IACA;QACE,SAAS;QACT,WAAW;QACX,aAAa;QACb,iBAAiB;QACjB,YAAY;QACZ,OAAO;QACP,eAAe;IACjB;IACA;QACE,SAAS;QACT,WAAW;QACX,aAAa;QACb,iBAAiB;QACjB,YAAY;QACZ,OAAO;QACP,eAAe;IACjB;IACA;QACE,SAAS;QACT,WAAW;QACX,aAAa;QACb,iBAAiB;QACjB,YAAY;QACZ,OAAO;QACP,eAAe;IACjB;IACA;QACE,SAAS;QACT,WAAW;QACX,aAAa;QACb,iBAAiB;QACjB,YAAY;QACZ,OAAO;QACP,eAAe;IACjB;CACD;AAGM,MAAM,qBAAuC;IAClD;QACE,SAAS;QACT,WAAW;QACX,kBAAkB;QAClB,aAAa;QACb,iBAAiB;QACjB,kBAAkB;QAClB,gBAAgB;YACd;gBAAE,OAAO;gBAA6B,OAAO;YAAI;YACjD;gBAAE,OAAO;gBAA2B,OAAO;YAAI;YAC/C;gBAAE,OAAO;gBAAyB,OAAO;YAAI;SAC9C;QACD,kBAAkB;YAChB,gBAAgB;YAChB,iBAAiB;YACjB,eAAe;QACjB;IACF;IACA;QACE,SAAS;QACT,WAAW;QACX,kBAAkB;QAClB,aAAa;QACb,iBAAiB;QACjB,kBAAkB;QAClB,gBAAgB;YACd;gBAAE,OAAO;gBAAwB,OAAO;YAAI;YAC5C;gBAAE,OAAO;gBAA4B,OAAO;YAAI;YAChD;gBAAE,OAAO;gBAAyB,OAAO;YAAI;SAC9C;QACD,kBAAkB;YAChB,gBAAgB;YAChB,gBAAgB;YAChB,eAAe;QACjB;IACF;IACA;QACE,SAAS;QACT,WAAW;QACX,kBAAkB;QAClB,aAAa;QACb,iBAAiB;QACjB,kBAAkB;QAClB,gBAAgB;YACd;gBAAE,OAAO;gBAAmB,OAAO;YAAI;YACvC;gBAAE,OAAO;gBAA0B,OAAO;YAAI;YAC9C;gBAAE,OAAO;gBAAsB,OAAO;YAAI;SAC3C;QACD,kBAAkB;YAChB,gBAAgB;YAChB,kBAAkB;YAClB,oBAAoB;QACtB;IACF;IACA;QACE,SAAS;QACT,WAAW;QACX,kBAAkB;QAClB,aAAa;QACb,iBAAiB;QACjB,kBAAkB;QAClB,gBAAgB;YACd;gBAAE,OAAO;gBAAwB,OAAO;YAAI;YAC5C;gBAAE,OAAO;gBAA0B,OAAO;YAAI;YAC9C;gBAAE,OAAO;gBAA2B,OAAO;YAAI;SAChD;QACD,kBAAkB;YAChB,gBAAgB;YAChB,oBAAoB;YACpB,iBAAiB;QACnB;IACF;IACA;QACE,SAAS;QACT,WAAW;QACX,kBAAkB;QAClB,aAAa;QACb,iBAAiB;QACjB,kBAAkB;QAClB,gBAAgB;YACd;gBAAE,OAAO;gBAAyB,OAAO;YAAI;YAC7C;gBAAE,OAAO;gBAAsB,OAAO;YAAI;YAC1C;gBAAE,OAAO;gBAA0B,OAAO;YAAI;SAC/C;QACD,kBAAkB;YAChB,gBAAgB;YAChB,eAAe;YACf,kBAAkB;QACpB;IACF;CACD;AAGM,MAAM,uBAA2C;IACtD;QACE,WAAW;QACX,cAAc;QACd,aAAa;QACb,WAAW;QACX,aAAa;QACb,iBAAiB;QACjB,gBAAgB;QAChB,iBAAiB;IACnB;IACA;QACE,WAAW;QACX,cAAc;QACd,aAAa;QACb,WAAW;QACX,aAAa;QACb,iBAAiB;QACjB,gBAAgB;QAChB,iBAAiB;IACnB;IACA;QACE,WAAW;QACX,cAAc;QACd,aAAa;QACb,WAAW;QACX,aAAa;QACb,iBAAiB;QACjB,gBAAgB;QAChB,iBAAiB;IACnB;IACA;QACE,WAAW;QACX,cAAc;QACd,aAAa;QACb,WAAW;QACX,aAAa;QACb,iBAAiB;QACjB,gBAAgB;QAChB,iBAAiB;IACnB;IACA;QACE,WAAW;QACX,cAAc;QACd,aAAa;QACb,WAAW;QACX,aAAa;QACb,iBAAiB;QACjB,gBAAgB;QAChB,iBAAiB;IACnB;CACD;AAGM,MAAM,oBAAqC;IAChD;QACE,QAAQ;QACR,UAAU;QACV,YAAY,IAAI,KAAK;QACrB,eAAe;QACf,gBAAgB;QAChB,iBAAiB;QACjB,gBAAgB;YAAC;YAAK;SAAI;QAC1B,kBAAkB;QAClB,WAAW;IACb;IACA;QACE,QAAQ;QACR,UAAU;QACV,YAAY,IAAI,KAAK;QACrB,eAAe;QACf,gBAAgB;QAChB,iBAAiB;QACjB,gBAAgB;YAAC;YAAK;SAAI;QAC1B,kBAAkB;QAClB,WAAW;IACb;IACA;QACE,QAAQ;QACR,UAAU;QACV,YAAY,IAAI,KAAK;QACrB,eAAe;QACf,gBAAgB;QAChB,iBAAiB;QACjB,gBAAgB;YAAC;YAAK;SAAI;QAC1B,kBAAkB;QAClB,WAAW;IACb;IACA;QACE,QAAQ;QACR,UAAU;QACV,YAAY,IAAI,KAAK;QACrB,eAAe;QACf,gBAAgB;QAChB,iBAAiB;QACjB,gBAAgB;YAAC;YAAK;SAAI;QAC1B,kBAAkB;QAClB,WAAW;IACb;IACA;QACE,QAAQ;QACR,UAAU;QACV,YAAY,IAAI,KAAK;QACrB,eAAe;QACf,gBAAgB;QAChB,iBAAiB;QACjB,gBAAgB;YAAC;YAAK;SAAI;QAC1B,kBAAkB;QAClB,WAAW;IACb;CACD;AAGM,MAAM,4BAAgD;IAC3D;QACE,IAAI;QACJ,QAAQ;QACR,SAAS;QACT,WAAW,IAAI,KAAK;QACpB,WAAW;QACX,cAAc;QACd,UAAU;QACV,kBAAkB;QAClB,kBAAkB;YAAC;YAAe;YAAkB;SAAM;QAC1D,iBAAiB;QACjB,iBAAiB;IACnB;IACA;QACE,IAAI;QACJ,QAAQ;QACR,SAAS;QACT,WAAW,IAAI,KAAK;QACpB,WAAW;QACX,cAAc;QACd,UAAU;QACV,kBAAkB;QAClB,kBAAkB;YAAC;YAAO;YAAgB;SAAqB;QAC/D,iBAAiB;QACjB,iBAAiB;IACnB;IACA;QACE,IAAI;QACJ,QAAQ;QACR,SAAS;QACT,WAAW,IAAI,KAAK;QACpB,WAAW;QACX,cAAc;QACd,UAAU;QACV,kBAAkB;QAClB,kBAAkB;YAAC;YAAgB;YAAoB;SAAmB;QAC1E,iBAAiB;QACjB,iBAAiB;IACnB;CACD;AAGM,MAAM,yBAA6C;IACxD,YAAY;IACZ,aAAa;QACX,OAAO;QACP,QAAQ;QACR,SAAS;IACX;IACA,UAAU;QACR,OAAO;QACP,QAAQ;QACR,SAAS;IACX;IACA,wBAAwB;IACxB,mBAAmB;IACnB,wBAAwB;IACxB,mBAAmB;IACnB,aAAa,qBAAqB,KAAK,CAAC,GAAG;IAC3C,WAAW,mBAAmB,KAAK,CAAC,GAAG;IACvC,YAAY,qBAAqB,KAAK,CAAC,GAAG;IAC1C,6BAA6B;AAC/B", "debugId": null}}, {"offset": {"line": 448, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/admin/analytics/components/PlatformUsageSection.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { faArrowUp, faArrowDown, faMinus } from '@fortawesome/free-solid-svg-icons';\nimport { SectionAnalytics } from '@/types/analytics';\n\ninterface PlatformUsageSectionProps {\n  sectionAnalytics: SectionAnalytics[];\n}\n\nexport default function PlatformUsageSection({ sectionAnalytics }: PlatformUsageSectionProps) {\n  // Helper function to format section names for display\n  const formatSectionName = (section: string) => {\n    return section\n      .split('_')\n      .map(word => word.charAt(0).toUpperCase() + word.slice(1))\n      .join(' ');\n  };\n\n  // Helper function to get trend icon\n  const getTrendIcon = (trend: 'up' | 'down' | 'stable') => {\n    if (trend === 'up') return <FontAwesomeIcon icon={faArrowUp} className=\"h-3 w-3 text-green-500\" />;\n    if (trend === 'down') return <FontAwesomeIcon icon={faArrowDown} className=\"h-3 w-3 text-red-500\" />;\n    return <FontAwesomeIcon icon={faMinus} className=\"h-3 w-3 text-gray-500\" />;\n  };\n\n  // Helper function to format duration\n  const formatDuration = (seconds: number) => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}m ${remainingSeconds}s`;\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex items-center justify-between\">\n        <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white\">Platform Usage Analytics</h2>\n        <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n          Showing data for all platform sections\n        </div>\n      </div>\n\n      {/* Section usage metrics table */}\n      <div className=\"overflow-hidden rounded-lg border border-gray-200 dark:border-gray-800\">\n        <div className=\"overflow-x-auto\">\n          <table className=\"min-w-full divide-y divide-gray-200 dark:divide-gray-800\">\n            <thead className=\"bg-gray-50 dark:bg-gray-800\">\n              <tr>\n                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400\">\n                  Section\n                </th>\n                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400\">\n                  Views\n                </th>\n                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400\">\n                  Unique Users\n                </th>\n                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400\">\n                  Avg. Duration\n                </th>\n                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400\">\n                  Bounce Rate\n                </th>\n                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400\">\n                  Trend\n                </th>\n              </tr>\n            </thead>\n            <tbody className=\"divide-y divide-gray-200 bg-white dark:divide-gray-800 dark:bg-gray-900\">\n              {sectionAnalytics.map((section) => (\n                <tr key={section.section} className=\"hover:bg-gray-50 dark:hover:bg-gray-800\">\n                  <td className=\"whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900 dark:text-white\">\n                    {formatSectionName(section.section)}\n                  </td>\n                  <td className=\"whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400\">\n                    {section.viewCount.toLocaleString()}\n                  </td>\n                  <td className=\"whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400\">\n                    {section.uniqueUsers.toLocaleString()}\n                  </td>\n                  <td className=\"whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400\">\n                    {formatDuration(section.averageDuration)}\n                  </td>\n                  <td className=\"whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400\">\n                    {(section.bounceRate * 100).toFixed(1)}%\n                  </td>\n                  <td className=\"whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400\">\n                    <div className=\"flex items-center\">\n                      {getTrendIcon(section.trend)}\n                      <span className={`ml-1 ${\n                        section.trend === 'up' \n                          ? 'text-green-600 dark:text-green-400' \n                          : section.trend === 'down' \n                            ? 'text-red-600 dark:text-red-400' \n                            : 'text-gray-600 dark:text-gray-400'\n                      }`}>\n                        {section.percentChange}%\n                      </span>\n                    </div>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n\n      {/* Usage insights */}\n      <div className=\"grid gap-6 md:grid-cols-2\">\n        <div className=\"rounded-lg border border-gray-200 bg-white p-6 dark:border-gray-800 dark:bg-gray-900\">\n          <h3 className=\"mb-4 text-base font-semibold text-gray-900 dark:text-white\">Key Insights</h3>\n          <ul className=\"space-y-3 text-sm text-gray-600 dark:text-gray-400\">\n            <li className=\"flex items-start\">\n              <span className=\"mr-2 text-green-500\">•</span>\n              <span>The <strong>Learning Resources</strong> section has seen the highest growth at <strong>42.7%</strong>, indicating strong interest in AI tools and resources.</span>\n            </li>\n            <li className=\"flex items-start\">\n              <span className=\"mr-2 text-green-500\">•</span>\n              <span>Users spend the most time in the <strong>Admin</strong> section (9m 3s on average), followed by <strong>Video</strong> content (6m 52s).</span>\n            </li>\n            <li className=\"flex items-start\">\n              <span className=\"mr-2 text-amber-500\">•</span>\n              <span>The <strong>Blog</strong> section has the highest bounce rate at <strong>28%</strong>, suggesting content may need improvement or better integration with other sections.</span>\n            </li>\n            <li className=\"flex items-start\">\n              <span className=\"mr-2 text-blue-500\">•</span>\n              <span>The <strong>Home</strong> page receives the most traffic but has a relatively high bounce rate of <strong>32%</strong>, indicating potential for optimization.</span>\n            </li>\n          </ul>\n        </div>\n\n        <div className=\"rounded-lg border border-gray-200 bg-white p-6 dark:border-gray-800 dark:bg-gray-900\">\n          <h3 className=\"mb-4 text-base font-semibold text-gray-900 dark:text-white\">Recommendations</h3>\n          <ul className=\"space-y-3 text-sm text-gray-600 dark:text-gray-400\">\n            <li className=\"flex items-start\">\n              <span className=\"mr-2 text-blue-500\">•</span>\n              <span>Consider featuring more learning resources on the home page to capitalize on growing interest.</span>\n            </li>\n            <li className=\"flex items-start\">\n              <span className=\"mr-2 text-blue-500\">•</span>\n              <span>Improve blog content engagement by adding related resources and agent recommendations.</span>\n            </li>\n            <li className=\"flex items-start\">\n              <span className=\"mr-2 text-blue-500\">•</span>\n              <span>Optimize the home page with clearer calls-to-action to reduce bounce rate and guide users to high-value sections.</span>\n            </li>\n            <li className=\"flex items-start\">\n              <span className=\"mr-2 text-blue-500\">•</span>\n              <span>Consider adding more interactive elements to the learning videos section to increase engagement time.</span>\n            </li>\n          </ul>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAWe,SAAS,qBAAqB,EAAE,gBAAgB,EAA6B;IAC1F,sDAAsD;IACtD,MAAM,oBAAoB,CAAC;QACzB,OAAO,QACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,IACtD,IAAI,CAAC;IACV;IAEA,oCAAoC;IACpC,MAAM,eAAe,CAAC;QACpB,IAAI,UAAU,MAAM,qBAAO,8OAAC,oKAAA,CAAA,kBAAe;YAAC,MAAM,wKAAA,CAAA,YAAS;YAAE,WAAU;;;;;;QACvE,IAAI,UAAU,QAAQ,qBAAO,8OAAC,oKAAA,CAAA,kBAAe;YAAC,MAAM,wKAAA,CAAA,cAAW;YAAE,WAAU;;;;;;QAC3E,qBAAO,8OAAC,oKAAA,CAAA,kBAAe;YAAC,MAAM,wKAAA,CAAA,UAAO;YAAE,WAAU;;;;;;IACnD;IAEA,qCAAqC;IACrC,MAAM,iBAAiB,CAAC;QACtB,MAAM,UAAU,KAAK,KAAK,CAAC,UAAU;QACrC,MAAM,mBAAmB,UAAU;QACnC,OAAO,GAAG,QAAQ,EAAE,EAAE,iBAAiB,CAAC,CAAC;IAC3C;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsD;;;;;;kCACpE,8OAAC;wBAAI,WAAU;kCAA2C;;;;;;;;;;;;0BAM5D,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAM,WAAU;;0CACf,8OAAC;gCAAM,WAAU;0CACf,cAAA,8OAAC;;sDACC,8OAAC;4CAAG,OAAM;4CAAM,WAAU;sDAAoG;;;;;;sDAG9H,8OAAC;4CAAG,OAAM;4CAAM,WAAU;sDAAoG;;;;;;sDAG9H,8OAAC;4CAAG,OAAM;4CAAM,WAAU;sDAAoG;;;;;;sDAG9H,8OAAC;4CAAG,OAAM;4CAAM,WAAU;sDAAoG;;;;;;sDAG9H,8OAAC;4CAAG,OAAM;4CAAM,WAAU;sDAAoG;;;;;;sDAG9H,8OAAC;4CAAG,OAAM;4CAAM,WAAU;sDAAoG;;;;;;;;;;;;;;;;;0CAKlI,8OAAC;gCAAM,WAAU;0CACd,iBAAiB,GAAG,CAAC,CAAC,wBACrB,8OAAC;wCAAyB,WAAU;;0DAClC,8OAAC;gDAAG,WAAU;0DACX,kBAAkB,QAAQ,OAAO;;;;;;0DAEpC,8OAAC;gDAAG,WAAU;0DACX,QAAQ,SAAS,CAAC,cAAc;;;;;;0DAEnC,8OAAC;gDAAG,WAAU;0DACX,QAAQ,WAAW,CAAC,cAAc;;;;;;0DAErC,8OAAC;gDAAG,WAAU;0DACX,eAAe,QAAQ,eAAe;;;;;;0DAEzC,8OAAC;gDAAG,WAAU;;oDACX,CAAC,QAAQ,UAAU,GAAG,GAAG,EAAE,OAAO,CAAC;oDAAG;;;;;;;0DAEzC,8OAAC;gDAAG,WAAU;0DACZ,cAAA,8OAAC;oDAAI,WAAU;;wDACZ,aAAa,QAAQ,KAAK;sEAC3B,8OAAC;4DAAK,WAAW,CAAC,KAAK,EACrB,QAAQ,KAAK,KAAK,OACd,uCACA,QAAQ,KAAK,KAAK,SAChB,mCACA,oCACN;;gEACC,QAAQ,aAAa;gEAAC;;;;;;;;;;;;;;;;;;;uCA1BtB,QAAQ,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;0BAsClC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA6D;;;;;;0CAC3E,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAK,WAAU;0DAAsB;;;;;;0DACtC,8OAAC;;oDAAK;kEAAI,8OAAC;kEAAO;;;;;;oDAA2B;kEAAwC,8OAAC;kEAAO;;;;;;oDAAc;;;;;;;;;;;;;kDAE7G,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAK,WAAU;0DAAsB;;;;;;0DACtC,8OAAC;;oDAAK;kEAAiC,8OAAC;kEAAO;;;;;;oDAAc;kEAAyC,8OAAC;kEAAO;;;;;;oDAAc;;;;;;;;;;;;;kDAE9H,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAK,WAAU;0DAAsB;;;;;;0DACtC,8OAAC;;oDAAK;kEAAI,8OAAC;kEAAO;;;;;;oDAAa;kEAAwC,8OAAC;kEAAO;;;;;;oDAAY;;;;;;;;;;;;;kDAE7F,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAK,WAAU;0DAAqB;;;;;;0DACrC,8OAAC;;oDAAK;kEAAI,8OAAC;kEAAO;;;;;;oDAAa;kEAAyE,8OAAC;kEAAO;;;;;;oDAAY;;;;;;;;;;;;;;;;;;;;;;;;;kCAKlI,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA6D;;;;;;0CAC3E,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAK,WAAU;0DAAqB;;;;;;0DACrC,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAK,WAAU;0DAAqB;;;;;;0DACrC,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAK,WAAU;0DAAqB;;;;;;0DACrC,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAK,WAAU;0DAAqB;;;;;;0DACrC,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpB", "debugId": null}}, {"offset": {"line": 1041, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/admin/analytics/components/AgentUsageSection.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { faExclamationTriangle, faCheckCircle, faInfoCircle } from '@fortawesome/free-solid-svg-icons';\nimport { AgentAnalytics } from '@/types/analytics';\n\ninterface AgentUsageSectionProps {\n  agentAnalytics: AgentAnalytics[];\n}\n\nexport default function AgentUsageSection({ agentAnalytics }: AgentUsageSectionProps) {\n  // Helper function to format duration\n  const formatDuration = (seconds: number) => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}m ${remainingSeconds}s`;\n  };\n\n  // Helper function to get sensitivity level\n  const getSensitivityLevel = (score: number) => {\n    if (score >= 75) return { level: 'High', color: 'text-red-500 dark:text-red-400' };\n    if (score >= 50) return { level: 'Medium', color: 'text-amber-500 dark:text-amber-400' };\n    if (score >= 25) return { level: 'Low', color: 'text-yellow-500 dark:text-yellow-400' };\n    return { level: 'Minimal', color: 'text-green-500 dark:text-green-400' };\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex items-center justify-between\">\n        <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white\">Agent Usage Analytics</h2>\n        <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n          Showing data for all agents\n        </div>\n      </div>\n\n      {/* Agent usage metrics table */}\n      <div className=\"overflow-hidden rounded-lg border border-gray-200 dark:border-gray-800\">\n        <div className=\"overflow-x-auto\">\n          <table className=\"min-w-full divide-y divide-gray-200 dark:divide-gray-800\">\n            <thead className=\"bg-gray-50 dark:bg-gray-800\">\n              <tr>\n                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400\">\n                  Agent\n                </th>\n                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400\">\n                  Interactions\n                </th>\n                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400\">\n                  Unique Users\n                </th>\n                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400\">\n                  Avg. Duration\n                </th>\n                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400\">\n                  Sensitivity\n                </th>\n                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400\">\n                  External APIs\n                </th>\n              </tr>\n            </thead>\n            <tbody className=\"divide-y divide-gray-200 bg-white dark:divide-gray-800 dark:bg-gray-900\">\n              {agentAnalytics.map((agent) => {\n                const sensitivityInfo = getSensitivityLevel(agent.sensitivityScore);\n                return (\n                  <tr key={agent.agentId} className=\"hover:bg-gray-50 dark:hover:bg-gray-800\">\n                    <td className=\"whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900 dark:text-white\">\n                      {agent.agentName}\n                    </td>\n                    <td className=\"whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400\">\n                      {agent.interactionCount.toLocaleString()}\n                    </td>\n                    <td className=\"whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400\">\n                      {agent.uniqueUsers.toLocaleString()}\n                    </td>\n                    <td className=\"whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400\">\n                      {formatDuration(agent.averageDuration)}\n                    </td>\n                    <td className=\"whitespace-nowrap px-6 py-4 text-sm\">\n                      <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${sensitivityInfo.color}`}>\n                        {agent.sensitivityScore >= 75 && (\n                          <FontAwesomeIcon icon={faExclamationTriangle} className=\"mr-1 h-3 w-3\" />\n                        )}\n                        {sensitivityInfo.level} ({agent.sensitivityScore})\n                      </span>\n                    </td>\n                    <td className=\"px-6 py-4 text-sm text-gray-500 dark:text-gray-400\">\n                      <div className=\"flex flex-col space-y-1\">\n                        {Object.entries(agent.externalApiUsage).map(([api, count]) => (\n                          <div key={api} className=\"flex items-center text-xs\">\n                            <span className=\"font-medium\">{api}:</span>\n                            <span className=\"ml-1\">{count.toLocaleString()}</span>\n                          </div>\n                        ))}\n                      </div>\n                    </td>\n                  </tr>\n                );\n              })}\n            </tbody>\n          </table>\n        </div>\n      </div>\n\n      {/* Popular queries section */}\n      <div className=\"grid gap-6 md:grid-cols-2\">\n        <div className=\"rounded-lg border border-gray-200 bg-white p-6 dark:border-gray-800 dark:bg-gray-900\">\n          <h3 className=\"mb-4 text-base font-semibold text-gray-900 dark:text-white\">Popular Queries</h3>\n          \n          <div className=\"space-y-4\">\n            {agentAnalytics.slice(0, 3).map((agent) => (\n              <div key={agent.agentId} className=\"space-y-2\">\n                <h4 className=\"text-sm font-medium text-gray-900 dark:text-white\">{agent.agentName}</h4>\n                <ul className=\"space-y-1 text-sm text-gray-600 dark:text-gray-400\">\n                  {agent.popularQueries.map((query, index) => (\n                    <li key={index} className=\"flex items-center justify-between\">\n                      <span className=\"truncate\">{query.query}</span>\n                      <span className=\"ml-2 text-xs text-gray-500 dark:text-gray-500\">{query.count} times</span>\n                    </li>\n                  ))}\n                </ul>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        <div className=\"rounded-lg border border-gray-200 bg-white p-6 dark:border-gray-800 dark:bg-gray-900\">\n          <h3 className=\"mb-4 text-base font-semibold text-gray-900 dark:text-white\">Sensitivity Insights</h3>\n          \n          <div className=\"space-y-3\">\n            <div className=\"flex items-start\">\n              <FontAwesomeIcon icon={faExclamationTriangle} className=\"mr-2 mt-0.5 h-4 w-4 text-red-500\" />\n              <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n                <p className=\"font-medium text-gray-900 dark:text-white\">High Sensitivity Detected</p>\n                <p>The <strong>Customer Support</strong> and <strong>Data Analyst</strong> agents show high sensitivity scores, indicating potential exposure to sensitive information.</p>\n              </div>\n            </div>\n            \n            <div className=\"flex items-start\">\n              <FontAwesomeIcon icon={faInfoCircle} className=\"mr-2 mt-0.5 h-4 w-4 text-blue-500\" />\n              <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n                <p className=\"font-medium text-gray-900 dark:text-white\">External API Usage</p>\n                <p>OpenAI GPT-4 is the most commonly used external API across all agents, accounting for 62% of all external API calls.</p>\n              </div>\n            </div>\n            \n            <div className=\"flex items-start\">\n              <FontAwesomeIcon icon={faCheckCircle} className=\"mr-2 mt-0.5 h-4 w-4 text-green-500\" />\n              <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n                <p className=\"font-medium text-gray-900 dark:text-white\">Low Risk Agents</p>\n                <p>The <strong>Writing Assistant</strong> and <strong>Code Assistant</strong> agents show the lowest sensitivity scores, making them safer for general use.</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAWe,SAAS,kBAAkB,EAAE,cAAc,EAA0B;IAClF,qCAAqC;IACrC,MAAM,iBAAiB,CAAC;QACtB,MAAM,UAAU,KAAK,KAAK,CAAC,UAAU;QACrC,MAAM,mBAAmB,UAAU;QACnC,OAAO,GAAG,QAAQ,EAAE,EAAE,iBAAiB,CAAC,CAAC;IAC3C;IAEA,2CAA2C;IAC3C,MAAM,sBAAsB,CAAC;QAC3B,IAAI,SAAS,IAAI,OAAO;YAAE,OAAO;YAAQ,OAAO;QAAiC;QACjF,IAAI,SAAS,IAAI,OAAO;YAAE,OAAO;YAAU,OAAO;QAAqC;QACvF,IAAI,SAAS,IAAI,OAAO;YAAE,OAAO;YAAO,OAAO;QAAuC;QACtF,OAAO;YAAE,OAAO;YAAW,OAAO;QAAqC;IACzE;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsD;;;;;;kCACpE,8OAAC;wBAAI,WAAU;kCAA2C;;;;;;;;;;;;0BAM5D,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAM,WAAU;;0CACf,8OAAC;gCAAM,WAAU;0CACf,cAAA,8OAAC;;sDACC,8OAAC;4CAAG,OAAM;4CAAM,WAAU;sDAAoG;;;;;;sDAG9H,8OAAC;4CAAG,OAAM;4CAAM,WAAU;sDAAoG;;;;;;sDAG9H,8OAAC;4CAAG,OAAM;4CAAM,WAAU;sDAAoG;;;;;;sDAG9H,8OAAC;4CAAG,OAAM;4CAAM,WAAU;sDAAoG;;;;;;sDAG9H,8OAAC;4CAAG,OAAM;4CAAM,WAAU;sDAAoG;;;;;;sDAG9H,8OAAC;4CAAG,OAAM;4CAAM,WAAU;sDAAoG;;;;;;;;;;;;;;;;;0CAKlI,8OAAC;gCAAM,WAAU;0CACd,eAAe,GAAG,CAAC,CAAC;oCACnB,MAAM,kBAAkB,oBAAoB,MAAM,gBAAgB;oCAClE,qBACE,8OAAC;wCAAuB,WAAU;;0DAChC,8OAAC;gDAAG,WAAU;0DACX,MAAM,SAAS;;;;;;0DAElB,8OAAC;gDAAG,WAAU;0DACX,MAAM,gBAAgB,CAAC,cAAc;;;;;;0DAExC,8OAAC;gDAAG,WAAU;0DACX,MAAM,WAAW,CAAC,cAAc;;;;;;0DAEnC,8OAAC;gDAAG,WAAU;0DACX,eAAe,MAAM,eAAe;;;;;;0DAEvC,8OAAC;gDAAG,WAAU;0DACZ,cAAA,8OAAC;oDAAK,WAAW,CAAC,wEAAwE,EAAE,gBAAgB,KAAK,EAAE;;wDAChH,MAAM,gBAAgB,IAAI,oBACzB,8OAAC,oKAAA,CAAA,kBAAe;4DAAC,MAAM,wKAAA,CAAA,wBAAqB;4DAAE,WAAU;;;;;;wDAEzD,gBAAgB,KAAK;wDAAC;wDAAG,MAAM,gBAAgB;wDAAC;;;;;;;;;;;;0DAGrD,8OAAC;gDAAG,WAAU;0DACZ,cAAA,8OAAC;oDAAI,WAAU;8DACZ,OAAO,OAAO,CAAC,MAAM,gBAAgB,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBACvD,8OAAC;4DAAc,WAAU;;8EACvB,8OAAC;oEAAK,WAAU;;wEAAe;wEAAI;;;;;;;8EACnC,8OAAC;oEAAK,WAAU;8EAAQ,MAAM,cAAc;;;;;;;2DAFpC;;;;;;;;;;;;;;;;uCAxBT,MAAM,OAAO;;;;;gCAiC1B;;;;;;;;;;;;;;;;;;;;;;0BAOR,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA6D;;;;;;0CAE3E,8OAAC;gCAAI,WAAU;0CACZ,eAAe,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,sBAC/B,8OAAC;wCAAwB,WAAU;;0DACjC,8OAAC;gDAAG,WAAU;0DAAqD,MAAM,SAAS;;;;;;0DAClF,8OAAC;gDAAG,WAAU;0DACX,MAAM,cAAc,CAAC,GAAG,CAAC,CAAC,OAAO,sBAChC,8OAAC;wDAAe,WAAU;;0EACxB,8OAAC;gEAAK,WAAU;0EAAY,MAAM,KAAK;;;;;;0EACvC,8OAAC;gEAAK,WAAU;;oEAAiD,MAAM,KAAK;oEAAC;;;;;;;;uDAFtE;;;;;;;;;;;uCAJL,MAAM,OAAO;;;;;;;;;;;;;;;;kCAe7B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA6D;;;;;;0CAE3E,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oKAAA,CAAA,kBAAe;gDAAC,MAAM,wKAAA,CAAA,wBAAqB;gDAAE,WAAU;;;;;;0DACxD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAA4C;;;;;;kEACzD,8OAAC;;4DAAE;0EAAI,8OAAC;0EAAO;;;;;;4DAAyB;0EAAK,8OAAC;0EAAO;;;;;;4DAAqB;;;;;;;;;;;;;;;;;;;kDAI9E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oKAAA,CAAA,kBAAe;gDAAC,MAAM,wKAAA,CAAA,eAAY;gDAAE,WAAU;;;;;;0DAC/C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAA4C;;;;;;kEACzD,8OAAC;kEAAE;;;;;;;;;;;;;;;;;;kDAIP,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oKAAA,CAAA,kBAAe;gDAAC,MAAM,wKAAA,CAAA,gBAAa;gDAAE,WAAU;;;;;;0DAChD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAA4C;;;;;;kEACzD,8OAAC;;4DAAE;0EAAI,8OAAC;0EAAO;;;;;;4DAA0B;0EAAK,8OAAC;0EAAO;;;;;;4DAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ7F", "debugId": null}}, {"offset": {"line": 1601, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/admin/analytics/components/SensitiveDataSection.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { \n  faExclamationTriangle, \n  faShieldAlt, \n  faEye, \n  faLock,\n  faUserSecret,\n  faCreditCard,\n  faFileContract,\n  faIdCard\n} from '@fortawesome/free-solid-svg-icons';\nimport { AgentInteraction, UserAnalytics } from '@/types/analytics';\n\ninterface SensitiveDataSectionProps {\n  sensitiveInteractions: AgentInteraction[];\n  highRiskUsers: UserAnalytics[];\n}\n\nexport default function SensitiveDataSection({ \n  sensitiveInteractions, \n  highRiskUsers \n}: SensitiveDataSectionProps) {\n  // Helper function to format date\n  const formatDate = (date: Date) => {\n    return new Date(date).toLocaleString();\n  };\n\n  // Helper function to get sensitivity flag icon\n  const getSensitivityFlagIcon = (flag: string) => {\n    switch (flag) {\n      case 'pii':\n      case 'personal_data':\n        return <FontAwesomeIcon icon={faIdCard} className=\"h-3 w-3\" />;\n      case 'credit_card':\n      case 'financial_data':\n        return <FontAwesomeIcon icon={faCreditCard} className=\"h-3 w-3\" />;\n      case 'confidential':\n      case 'internal_project':\n        return <FontAwesomeIcon icon={faUserSecret} className=\"h-3 w-3\" />;\n      case 'address_data':\n      case 'client_information':\n        return <FontAwesomeIcon icon={faFileContract} className=\"h-3 w-3\" />;\n      default:\n        return <FontAwesomeIcon icon={faLock} className=\"h-3 w-3\" />;\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex items-center justify-between\">\n        <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white\">Sensitive Data Monitoring</h2>\n        <div className=\"rounded-full bg-red-100 px-3 py-1 text-xs font-medium text-red-800 dark:bg-red-900/30 dark:text-red-300\">\n          <FontAwesomeIcon icon={faExclamationTriangle} className=\"mr-1 h-3 w-3\" />\n          {sensitiveInteractions.length} alerts detected\n        </div>\n      </div>\n\n      {/* Alert summary */}\n      <div className=\"rounded-lg border border-red-200 bg-red-50 p-4 dark:border-red-900/50 dark:bg-red-900/10\">\n        <div className=\"flex\">\n          <div className=\"flex-shrink-0\">\n            <FontAwesomeIcon icon={faShieldAlt} className=\"h-5 w-5 text-red-600 dark:text-red-400\" />\n          </div>\n          <div className=\"ml-3\">\n            <h3 className=\"text-sm font-medium text-red-800 dark:text-red-300\">Sensitive Data Alert</h3>\n            <div className=\"mt-2 text-sm text-red-700 dark:text-red-300\">\n              <p>\n                Our system has detected potential sensitive data being shared with AI agents. \n                This may include personal information, financial data, or confidential business information.\n                Review the interactions below and take appropriate action.\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Recent sensitive interactions */}\n      <div>\n        <h3 className=\"mb-4 text-base font-semibold text-gray-900 dark:text-white\">Recent Sensitive Interactions</h3>\n        <div className=\"space-y-4\">\n          {sensitiveInteractions.map((interaction) => (\n            <div \n              key={interaction.id} \n              className=\"rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900\"\n            >\n              <div className=\"mb-2 flex items-center justify-between\">\n                <div className=\"flex items-center\">\n                  <FontAwesomeIcon icon={faEye} className=\"mr-2 h-4 w-4 text-amber-500\" />\n                  <span className=\"font-medium text-gray-900 dark:text-white\">\n                    Interaction with {interaction.agentId}\n                  </span>\n                </div>\n                <span className=\"text-xs text-gray-500 dark:text-gray-400\">\n                  {formatDate(interaction.timestamp)}\n                </span>\n              </div>\n              \n              <div className=\"mb-2 rounded bg-gray-50 p-3 dark:bg-gray-800\">\n                <p className=\"text-sm text-gray-700 dark:text-gray-300\">\n                  <span className=\"font-medium text-gray-900 dark:text-white\">User Query: </span>\n                  {interaction.queryText}\n                </p>\n              </div>\n              \n              <div className=\"mb-3 rounded bg-gray-50 p-3 dark:bg-gray-800\">\n                <p className=\"text-sm text-gray-700 dark:text-gray-300\">\n                  <span className=\"font-medium text-gray-900 dark:text-white\">Agent Response: </span>\n                  {interaction.responseText}\n                </p>\n              </div>\n              \n              <div className=\"flex flex-wrap items-center justify-between\">\n                <div className=\"flex items-center\">\n                  <span className=\"mr-2 text-xs font-medium text-gray-500 dark:text-gray-400\">\n                    User ID: {interaction.userId}\n                  </span>\n                  <span className=\"text-xs font-medium text-gray-500 dark:text-gray-400\">\n                    External API: {interaction.externalApiName}\n                  </span>\n                </div>\n                \n                <div className=\"flex flex-wrap gap-1\">\n                  {interaction.sensitivityFlags.map((flag) => (\n                    <span \n                      key={flag}\n                      className=\"inline-flex items-center rounded-full bg-red-100 px-2 py-0.5 text-xs font-medium text-red-800 dark:bg-red-900/30 dark:text-red-300\"\n                    >\n                      {getSensitivityFlagIcon(flag)}\n                      <span className=\"ml-1\">{flag.replace('_', ' ')}</span>\n                    </span>\n                  ))}\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* High risk users */}\n      <div>\n        <h3 className=\"mb-4 text-base font-semibold text-gray-900 dark:text-white\">Users with High Sensitivity Risk</h3>\n        <div className=\"overflow-hidden rounded-lg border border-gray-200 dark:border-gray-800\">\n          <div className=\"overflow-x-auto\">\n            <table className=\"min-w-full divide-y divide-gray-200 dark:divide-gray-800\">\n              <thead className=\"bg-gray-50 dark:bg-gray-800\">\n                <tr>\n                  <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400\">\n                    User\n                  </th>\n                  <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400\">\n                    Last Active\n                  </th>\n                  <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400\">\n                    Sessions\n                  </th>\n                  <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400\">\n                    Favorite Section\n                  </th>\n                  <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400\">\n                    Risk Level\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"divide-y divide-gray-200 bg-white dark:divide-gray-800 dark:bg-gray-900\">\n                {highRiskUsers.map((user) => (\n                  <tr key={user.userId} className=\"hover:bg-gray-50 dark:hover:bg-gray-800\">\n                    <td className=\"whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900 dark:text-white\">\n                      {user.userName}\n                    </td>\n                    <td className=\"whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400\">\n                      {formatDate(user.lastActive)}\n                    </td>\n                    <td className=\"whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400\">\n                      {user.totalSessions}\n                    </td>\n                    <td className=\"whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400\">\n                      {user.favoriteSection.replace('_', ' ')}\n                    </td>\n                    <td className=\"whitespace-nowrap px-6 py-4 text-sm\">\n                      <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${\n                        user.riskLevel === 'high' \n                          ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300' \n                          : user.riskLevel === 'medium'\n                            ? 'bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300'\n                            : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300'\n                      }`}>\n                        {user.riskLevel}\n                      </span>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        </div>\n      </div>\n\n      {/* Recommendations */}\n      <div className=\"rounded-lg border border-gray-200 bg-white p-6 dark:border-gray-800 dark:bg-gray-900\">\n        <h3 className=\"mb-4 text-base font-semibold text-gray-900 dark:text-white\">Recommendations</h3>\n        <ul className=\"space-y-3 text-sm text-gray-600 dark:text-gray-400\">\n          <li className=\"flex items-start\">\n            <span className=\"mr-2 text-blue-500\">•</span>\n            <span>Consider implementing additional data filters for the <strong>Customer Support</strong> and <strong>Data Analyst</strong> agents to prevent sensitive data processing.</span>\n          </li>\n          <li className=\"flex items-start\">\n            <span className=\"mr-2 text-blue-500\">•</span>\n            <span>Schedule a security review with high-risk users to ensure they understand data handling policies.</span>\n          </li>\n          <li className=\"flex items-start\">\n            <span className=\"mr-2 text-blue-500\">•</span>\n            <span>Update agent prompts to include stronger warnings about sharing sensitive information.</span>\n          </li>\n          <li className=\"flex items-start\">\n            <span className=\"mr-2 text-blue-500\">•</span>\n            <span>Consider implementing a pre-submission review for interactions with external APIs to prevent data leakage.</span>\n          </li>\n        </ul>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAqBe,SAAS,qBAAqB,EAC3C,qBAAqB,EACrB,aAAa,EACa;IAC1B,iCAAiC;IACjC,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,MAAM,cAAc;IACtC;IAEA,+CAA+C;IAC/C,MAAM,yBAAyB,CAAC;QAC9B,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,qBAAO,8OAAC,oKAAA,CAAA,kBAAe;oBAAC,MAAM,wKAAA,CAAA,WAAQ;oBAAE,WAAU;;;;;;YACpD,KAAK;YACL,KAAK;gBACH,qBAAO,8OAAC,oKAAA,CAAA,kBAAe;oBAAC,MAAM,wKAAA,CAAA,eAAY;oBAAE,WAAU;;;;;;YACxD,KAAK;YACL,KAAK;gBACH,qBAAO,8OAAC,oKAAA,CAAA,kBAAe;oBAAC,MAAM,wKAAA,CAAA,eAAY;oBAAE,WAAU;;;;;;YACxD,KAAK;YACL,KAAK;gBACH,qBAAO,8OAAC,oKAAA,CAAA,kBAAe;oBAAC,MAAM,wKAAA,CAAA,iBAAc;oBAAE,WAAU;;;;;;YAC1D;gBACE,qBAAO,8OAAC,oKAAA,CAAA,kBAAe;oBAAC,MAAM,wKAAA,CAAA,SAAM;oBAAE,WAAU;;;;;;QACpD;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsD;;;;;;kCACpE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oKAAA,CAAA,kBAAe;gCAAC,MAAM,wKAAA,CAAA,wBAAqB;gCAAE,WAAU;;;;;;4BACvD,sBAAsB,MAAM;4BAAC;;;;;;;;;;;;;0BAKlC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,oKAAA,CAAA,kBAAe;gCAAC,MAAM,wKAAA,CAAA,cAAW;gCAAE,WAAU;;;;;;;;;;;sCAEhD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAqD;;;;;;8CACnE,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;kDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWX,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAA6D;;;;;;kCAC3E,8OAAC;wBAAI,WAAU;kCACZ,sBAAsB,GAAG,CAAC,CAAC,4BAC1B,8OAAC;gCAEC,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oKAAA,CAAA,kBAAe;wDAAC,MAAM,wKAAA,CAAA,QAAK;wDAAE,WAAU;;;;;;kEACxC,8OAAC;wDAAK,WAAU;;4DAA4C;4DACxC,YAAY,OAAO;;;;;;;;;;;;;0DAGzC,8OAAC;gDAAK,WAAU;0DACb,WAAW,YAAY,SAAS;;;;;;;;;;;;kDAIrC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;;8DACX,8OAAC;oDAAK,WAAU;8DAA4C;;;;;;gDAC3D,YAAY,SAAS;;;;;;;;;;;;kDAI1B,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;;8DACX,8OAAC;oDAAK,WAAU;8DAA4C;;;;;;gDAC3D,YAAY,YAAY;;;;;;;;;;;;kDAI7B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;;4DAA4D;4DAChE,YAAY,MAAM;;;;;;;kEAE9B,8OAAC;wDAAK,WAAU;;4DAAuD;4DACtD,YAAY,eAAe;;;;;;;;;;;;;0DAI9C,8OAAC;gDAAI,WAAU;0DACZ,YAAY,gBAAgB,CAAC,GAAG,CAAC,CAAC,qBACjC,8OAAC;wDAEC,WAAU;;4DAET,uBAAuB;0EACxB,8OAAC;gEAAK,WAAU;0EAAQ,KAAK,OAAO,CAAC,KAAK;;;;;;;uDAJrC;;;;;;;;;;;;;;;;;+BA1CR,YAAY,EAAE;;;;;;;;;;;;;;;;0BAyD3B,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAA6D;;;;;;kCAC3E,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAM,WAAU;;kDACf,8OAAC;wCAAM,WAAU;kDACf,cAAA,8OAAC;;8DACC,8OAAC;oDAAG,OAAM;oDAAM,WAAU;8DAAoG;;;;;;8DAG9H,8OAAC;oDAAG,OAAM;oDAAM,WAAU;8DAAoG;;;;;;8DAG9H,8OAAC;oDAAG,OAAM;oDAAM,WAAU;8DAAoG;;;;;;8DAG9H,8OAAC;oDAAG,OAAM;oDAAM,WAAU;8DAAoG;;;;;;8DAG9H,8OAAC;oDAAG,OAAM;oDAAM,WAAU;8DAAoG;;;;;;;;;;;;;;;;;kDAKlI,8OAAC;wCAAM,WAAU;kDACd,cAAc,GAAG,CAAC,CAAC,qBAClB,8OAAC;gDAAqB,WAAU;;kEAC9B,8OAAC;wDAAG,WAAU;kEACX,KAAK,QAAQ;;;;;;kEAEhB,8OAAC;wDAAG,WAAU;kEACX,WAAW,KAAK,UAAU;;;;;;kEAE7B,8OAAC;wDAAG,WAAU;kEACX,KAAK,aAAa;;;;;;kEAErB,8OAAC;wDAAG,WAAU;kEACX,KAAK,eAAe,CAAC,OAAO,CAAC,KAAK;;;;;;kEAErC,8OAAC;wDAAG,WAAU;kEACZ,cAAA,8OAAC;4DAAK,WAAW,CAAC,wEAAwE,EACxF,KAAK,SAAS,KAAK,SACf,iEACA,KAAK,SAAS,KAAK,WACjB,yEACA,4EACN;sEACC,KAAK,SAAS;;;;;;;;;;;;+CArBZ,KAAK,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAiChC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA6D;;;;;;kCAC3E,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAK,WAAU;kDAAqB;;;;;;kDACrC,8OAAC;;4CAAK;0DAAsD,8OAAC;0DAAO;;;;;;4CAAyB;0DAAK,8OAAC;0DAAO;;;;;;4CAAqB;;;;;;;;;;;;;0CAEjI,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAK,WAAU;kDAAqB;;;;;;kDACrC,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAK,WAAU;kDAAqB;;;;;;kDACrC,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAK,WAAU;kDAAqB;;;;;;kDACrC,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlB", "debugId": null}}, {"offset": {"line": 2278, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/admin/analytics/components/UserBehaviorSection.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { \n  faUser, \n  faClock, \n  faCalendarAlt,\n  faHeart,\n  faRobot\n} from '@fortawesome/free-solid-svg-icons';\nimport { UserAnalytics, ContentAnalytics } from '@/types/analytics';\n\ninterface UserBehaviorSectionProps {\n  userAnalytics: UserAnalytics[];\n  contentAnalytics: ContentAnalytics[];\n}\n\nexport default function UserBehaviorSection({ \n  userAnalytics, \n  contentAnalytics \n}: UserBehaviorSectionProps) {\n  // Helper function to format date\n  const formatDate = (date: Date) => {\n    return new Date(date).toLocaleDateString();\n  };\n\n  // Helper function to format time\n  const formatTime = (seconds: number) => {\n    const hours = Math.floor(seconds / 3600);\n    const minutes = Math.floor((seconds % 3600) / 60);\n    \n    if (hours > 0) {\n      return `${hours}h ${minutes}m`;\n    }\n    return `${minutes}m`;\n  };\n\n  // Helper function to format section name\n  const formatSectionName = (section: string) => {\n    return section\n      .split('_')\n      .map(word => word.charAt(0).toUpperCase() + word.slice(1))\n      .join(' ');\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex items-center justify-between\">\n        <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white\">User Behavior Analytics</h2>\n        <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n          Showing data for active users\n        </div>\n      </div>\n\n      {/* User activity metrics */}\n      <div className=\"overflow-hidden rounded-lg border border-gray-200 dark:border-gray-800\">\n        <div className=\"overflow-x-auto\">\n          <table className=\"min-w-full divide-y divide-gray-200 dark:divide-gray-800\">\n            <thead className=\"bg-gray-50 dark:bg-gray-800\">\n              <tr>\n                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400\">\n                  User\n                </th>\n                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400\">\n                  Last Active\n                </th>\n                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400\">\n                  Sessions\n                </th>\n                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400\">\n                  Total Time\n                </th>\n                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400\">\n                  Favorite Section\n                </th>\n                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400\">\n                  Favorite Agents\n                </th>\n              </tr>\n            </thead>\n            <tbody className=\"divide-y divide-gray-200 bg-white dark:divide-gray-800 dark:bg-gray-900\">\n              {userAnalytics.map((user) => (\n                <tr key={user.userId} className=\"hover:bg-gray-50 dark:hover:bg-gray-800\">\n                  <td className=\"whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900 dark:text-white\">\n                    <div className=\"flex items-center\">\n                      <FontAwesomeIcon icon={faUser} className=\"mr-2 h-4 w-4 text-gray-400\" />\n                      {user.userName}\n                    </div>\n                  </td>\n                  <td className=\"whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400\">\n                    <div className=\"flex items-center\">\n                      <FontAwesomeIcon icon={faCalendarAlt} className=\"mr-2 h-4 w-4 text-gray-400\" />\n                      {formatDate(user.lastActive)}\n                    </div>\n                  </td>\n                  <td className=\"whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400\">\n                    {user.totalSessions}\n                  </td>\n                  <td className=\"whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400\">\n                    <div className=\"flex items-center\">\n                      <FontAwesomeIcon icon={faClock} className=\"mr-2 h-4 w-4 text-gray-400\" />\n                      {formatTime(user.totalTimeSpent)}\n                    </div>\n                  </td>\n                  <td className=\"whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400\">\n                    {formatSectionName(user.favoriteSection)}\n                  </td>\n                  <td className=\"px-6 py-4 text-sm text-gray-500 dark:text-gray-400\">\n                    <div className=\"flex items-center space-x-1\">\n                      <FontAwesomeIcon icon={faHeart} className=\"h-4 w-4 text-red-400\" />\n                      <span>{user.favoriteAgents.join(', ')}</span>\n                    </div>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n\n      {/* Content engagement */}\n      <div>\n        <h3 className=\"mb-4 text-base font-semibold text-gray-900 dark:text-white\">Content Engagement</h3>\n        <div className=\"grid gap-4 sm:grid-cols-2 lg:grid-cols-3\">\n          {contentAnalytics.map((content) => (\n            <div \n              key={content.contentId}\n              className=\"rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900\"\n            >\n              <div className=\"mb-2 flex items-center justify-between\">\n                <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${\n                  content.contentType === 'video' \n                    ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300' \n                    : content.contentType === 'article'\n                      ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'\n                      : content.contentType === 'blog'\n                        ? 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300'\n                        : 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300'\n                }`}>\n                  {content.contentType}\n                </span>\n                <div className=\"flex items-center text-xs text-gray-500 dark:text-gray-400\">\n                  <FontAwesomeIcon icon={faUser} className=\"mr-1 h-3 w-3\" />\n                  {content.uniqueUsers.toLocaleString()}\n                </div>\n              </div>\n              \n              <h4 className=\"mb-2 text-sm font-medium text-gray-900 dark:text-white line-clamp-1\">\n                {content.contentTitle}\n              </h4>\n              \n              <div className=\"space-y-2\">\n                <div className=\"flex items-center justify-between text-xs\">\n                  <span className=\"text-gray-500 dark:text-gray-400\">Views</span>\n                  <span className=\"font-medium text-gray-900 dark:text-white\">{content.viewCount.toLocaleString()}</span>\n                </div>\n                \n                <div className=\"flex items-center justify-between text-xs\">\n                  <span className=\"text-gray-500 dark:text-gray-400\">Avg. Duration</span>\n                  <span className=\"font-medium text-gray-900 dark:text-white\">{formatTime(content.averageDuration)}</span>\n                </div>\n                \n                <div className=\"flex items-center justify-between text-xs\">\n                  <span className=\"text-gray-500 dark:text-gray-400\">Completion Rate</span>\n                  <span className=\"font-medium text-gray-900 dark:text-white\">{(content.completionRate * 100).toFixed(0)}%</span>\n                </div>\n                \n                <div className=\"flex items-center justify-between text-xs\">\n                  <span className=\"text-gray-500 dark:text-gray-400\">Engagement Score</span>\n                  <span className={`font-medium ${\n                    content.engagementScore >= 80 \n                      ? 'text-green-600 dark:text-green-400' \n                      : content.engagementScore >= 60\n                        ? 'text-blue-600 dark:text-blue-400'\n                        : 'text-amber-600 dark:text-amber-400'\n                  }`}>\n                    {content.engagementScore}/100\n                  </span>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* User journey insights */}\n      <div className=\"grid gap-6 md:grid-cols-2\">\n        <div className=\"rounded-lg border border-gray-200 bg-white p-6 dark:border-gray-800 dark:bg-gray-900\">\n          <h3 className=\"mb-4 text-base font-semibold text-gray-900 dark:text-white\">User Journey Insights</h3>\n          <ul className=\"space-y-3 text-sm text-gray-600 dark:text-gray-400\">\n            <li className=\"flex items-start\">\n              <span className=\"mr-2 text-blue-500\">•</span>\n              <span>Most users start on the <strong>Home</strong> page, then navigate to either <strong>Browse</strong> or <strong>Learning Resources</strong>.</span>\n            </li>\n            <li className=\"flex items-start\">\n              <span className=\"mr-2 text-blue-500\">•</span>\n              <span>Users who engage with <strong>Learning Videos</strong> spend 42% more time on the platform overall.</span>\n            </li>\n            <li className=\"flex items-start\">\n              <span className=\"mr-2 text-blue-500\">•</span>\n              <span>The most common user path is: Home → Browse → Agent Detail → Chat.</span>\n            </li>\n            <li className=\"flex items-start\">\n              <span className=\"mr-2 text-blue-500\">•</span>\n              <span>Users who favorite at least one agent are 3.5x more likely to return within 7 days.</span>\n            </li>\n          </ul>\n        </div>\n\n        <div className=\"rounded-lg border border-gray-200 bg-white p-6 dark:border-gray-800 dark:bg-gray-900\">\n          <h3 className=\"mb-4 text-base font-semibold text-gray-900 dark:text-white\">Recommendations</h3>\n          <ul className=\"space-y-3 text-sm text-gray-600 dark:text-gray-400\">\n            <li className=\"flex items-start\">\n              <span className=\"mr-2 text-blue-500\">•</span>\n              <span>Encourage users to favorite agents by highlighting this feature during onboarding.</span>\n            </li>\n            <li className=\"flex items-start\">\n              <span className=\"mr-2 text-blue-500\">•</span>\n              <span>Promote learning videos more prominently to increase overall engagement time.</span>\n            </li>\n            <li className=\"flex items-start\">\n              <span className=\"mr-2 text-blue-500\">•</span>\n              <span>Consider implementing personalized content recommendations based on user behavior.</span>\n            </li>\n            <li className=\"flex items-start\">\n              <span className=\"mr-2 text-blue-500\">•</span>\n              <span>Add more interactive elements to blog posts to increase their relatively low engagement scores.</span>\n            </li>\n          </ul>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAkBe,SAAS,oBAAoB,EAC1C,aAAa,EACb,gBAAgB,EACS;IACzB,iCAAiC;IACjC,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,MAAM,kBAAkB;IAC1C;IAEA,iCAAiC;IACjC,MAAM,aAAa,CAAC;QAClB,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;QACnC,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,UAAU,OAAQ;QAE9C,IAAI,QAAQ,GAAG;YACb,OAAO,GAAG,MAAM,EAAE,EAAE,QAAQ,CAAC,CAAC;QAChC;QACA,OAAO,GAAG,QAAQ,CAAC,CAAC;IACtB;IAEA,yCAAyC;IACzC,MAAM,oBAAoB,CAAC;QACzB,OAAO,QACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,IACtD,IAAI,CAAC;IACV;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsD;;;;;;kCACpE,8OAAC;wBAAI,WAAU;kCAA2C;;;;;;;;;;;;0BAM5D,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAM,WAAU;;0CACf,8OAAC;gCAAM,WAAU;0CACf,cAAA,8OAAC;;sDACC,8OAAC;4CAAG,OAAM;4CAAM,WAAU;sDAAoG;;;;;;sDAG9H,8OAAC;4CAAG,OAAM;4CAAM,WAAU;sDAAoG;;;;;;sDAG9H,8OAAC;4CAAG,OAAM;4CAAM,WAAU;sDAAoG;;;;;;sDAG9H,8OAAC;4CAAG,OAAM;4CAAM,WAAU;sDAAoG;;;;;;sDAG9H,8OAAC;4CAAG,OAAM;4CAAM,WAAU;sDAAoG;;;;;;sDAG9H,8OAAC;4CAAG,OAAM;4CAAM,WAAU;sDAAoG;;;;;;;;;;;;;;;;;0CAKlI,8OAAC;gCAAM,WAAU;0CACd,cAAc,GAAG,CAAC,CAAC,qBAClB,8OAAC;wCAAqB,WAAU;;0DAC9B,8OAAC;gDAAG,WAAU;0DACZ,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oKAAA,CAAA,kBAAe;4DAAC,MAAM,wKAAA,CAAA,SAAM;4DAAE,WAAU;;;;;;wDACxC,KAAK,QAAQ;;;;;;;;;;;;0DAGlB,8OAAC;gDAAG,WAAU;0DACZ,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oKAAA,CAAA,kBAAe;4DAAC,MAAM,wKAAA,CAAA,gBAAa;4DAAE,WAAU;;;;;;wDAC/C,WAAW,KAAK,UAAU;;;;;;;;;;;;0DAG/B,8OAAC;gDAAG,WAAU;0DACX,KAAK,aAAa;;;;;;0DAErB,8OAAC;gDAAG,WAAU;0DACZ,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oKAAA,CAAA,kBAAe;4DAAC,MAAM,wKAAA,CAAA,UAAO;4DAAE,WAAU;;;;;;wDACzC,WAAW,KAAK,cAAc;;;;;;;;;;;;0DAGnC,8OAAC;gDAAG,WAAU;0DACX,kBAAkB,KAAK,eAAe;;;;;;0DAEzC,8OAAC;gDAAG,WAAU;0DACZ,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oKAAA,CAAA,kBAAe;4DAAC,MAAM,wKAAA,CAAA,UAAO;4DAAE,WAAU;;;;;;sEAC1C,8OAAC;sEAAM,KAAK,cAAc,CAAC,IAAI,CAAC;;;;;;;;;;;;;;;;;;uCA5B7B,KAAK,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;0BAuC9B,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAA6D;;;;;;kCAC3E,8OAAC;wBAAI,WAAU;kCACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,8OAAC;gCAEC,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAW,CAAC,wEAAwE,EACxF,QAAQ,WAAW,KAAK,UACpB,qEACA,QAAQ,WAAW,KAAK,YACtB,yEACA,QAAQ,WAAW,KAAK,SACtB,6EACA,4EACR;0DACC,QAAQ,WAAW;;;;;;0DAEtB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oKAAA,CAAA,kBAAe;wDAAC,MAAM,wKAAA,CAAA,SAAM;wDAAE,WAAU;;;;;;oDACxC,QAAQ,WAAW,CAAC,cAAc;;;;;;;;;;;;;kDAIvC,8OAAC;wCAAG,WAAU;kDACX,QAAQ,YAAY;;;;;;kDAGvB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAmC;;;;;;kEACnD,8OAAC;wDAAK,WAAU;kEAA6C,QAAQ,SAAS,CAAC,cAAc;;;;;;;;;;;;0DAG/F,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAmC;;;;;;kEACnD,8OAAC;wDAAK,WAAU;kEAA6C,WAAW,QAAQ,eAAe;;;;;;;;;;;;0DAGjG,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAmC;;;;;;kEACnD,8OAAC;wDAAK,WAAU;;4DAA6C,CAAC,QAAQ,cAAc,GAAG,GAAG,EAAE,OAAO,CAAC;4DAAG;;;;;;;;;;;;;0DAGzG,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAmC;;;;;;kEACnD,8OAAC;wDAAK,WAAW,CAAC,YAAY,EAC5B,QAAQ,eAAe,IAAI,KACvB,uCACA,QAAQ,eAAe,IAAI,KACzB,qCACA,sCACN;;4DACC,QAAQ,eAAe;4DAAC;;;;;;;;;;;;;;;;;;;;+BAlD1B,QAAQ,SAAS;;;;;;;;;;;;;;;;0BA4D9B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA6D;;;;;;0CAC3E,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAK,WAAU;0DAAqB;;;;;;0DACrC,8OAAC;;oDAAK;kEAAwB,8OAAC;kEAAO;;;;;;oDAAa;kEAA+B,8OAAC;kEAAO;;;;;;oDAAe;kEAAI,8OAAC;kEAAO;;;;;;oDAA2B;;;;;;;;;;;;;kDAElJ,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAK,WAAU;0DAAqB;;;;;;0DACrC,8OAAC;;oDAAK;kEAAsB,8OAAC;kEAAO;;;;;;oDAAwB;;;;;;;;;;;;;kDAE9D,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAK,WAAU;0DAAqB;;;;;;0DACrC,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAK,WAAU;0DAAqB;;;;;;0DACrC,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;kCAKZ,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA6D;;;;;;0CAC3E,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAK,WAAU;0DAAqB;;;;;;0DACrC,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAK,WAAU;0DAAqB;;;;;;0DACrC,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAK,WAAU;0DAAqB;;;;;;0DACrC,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAK,WAAU;0DAAqB;;;;;;0DACrC,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpB", "debugId": null}}, {"offset": {"line": 3056, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/admin/analytics/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport {\n  faChartLine,\n  faUsers,\n  faRobot,\n  faShieldAlt,\n  faArrowLeft,\n  faCalendarAlt,\n  faExclamationTriangle,\n  faChartBar\n} from '@fortawesome/free-solid-svg-icons';\nimport {\n  mockDashboardAnalytics,\n  mockSectionAnalytics,\n  mockAgentAnalytics,\n  mockSensitiveInteractions,\n  mockUserAnalytics,\n  mockContentAnalytics\n} from '@/data/mock-analytics';\n\n// Import component sections\nimport PlatformUsageSection from './components/PlatformUsageSection';\nimport AgentUsageSection from './components/AgentUsageSection';\nimport SensitiveDataSection from './components/SensitiveDataSection';\nimport UserBehaviorSection from './components/UserBehaviorSection';\n\nexport default function AnalyticsPage() {\n  const [timeRange, setTimeRange] = useState<'day' | 'week' | 'month'>('week');\n  const [activeTab, setActiveTab] = useState<'overview' | 'platform' | 'agent' | 'sensitive' | 'user'>('overview');\n  const analytics = mockDashboardAnalytics;\n\n  // Filter high risk users\n  const highRiskUsers = mockUserAnalytics.filter(user => user.riskLevel === 'high' || user.riskLevel === 'medium');\n\n  // Map timeRange to the keys in the analytics object\n  const timeRangeMap = {\n    'day': 'daily',\n    'week': 'weekly',\n    'month': 'monthly'\n  } as const;\n\n  return (\n    <div className=\"space-y-8\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <Link\n            href=\"/admin\"\n            className=\"mb-4 inline-flex items-center text-sm font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300\"\n          >\n            <FontAwesomeIcon icon={faArrowLeft} className=\"mr-2 h-4 w-4\" />\n            Back to Admin Dashboard\n          </Link>\n          <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">Analytics Dashboard</h1>\n        </div>\n\n        {/* Time range selector */}\n        <div className=\"flex items-center rounded-lg border border-gray-200 bg-white p-1 dark:border-gray-700 dark:bg-gray-800\">\n          <button\n            onClick={() => setTimeRange('day')}\n            className={`rounded px-3 py-1.5 text-sm font-medium ${\n              timeRange === 'day'\n                ? 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900/50 dark:text-indigo-300'\n                : 'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200'\n            }`}\n          >\n            Day\n          </button>\n          <button\n            onClick={() => setTimeRange('week')}\n            className={`rounded px-3 py-1.5 text-sm font-medium ${\n              timeRange === 'week'\n                ? 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900/50 dark:text-indigo-300'\n                : 'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200'\n            }`}\n          >\n            Week\n          </button>\n          <button\n            onClick={() => setTimeRange('month')}\n            className={`rounded px-3 py-1.5 text-sm font-medium ${\n              timeRange === 'month'\n                ? 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900/50 dark:text-indigo-300'\n                : 'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200'\n            }`}\n          >\n            Month\n          </button>\n        </div>\n      </div>\n\n      {/* Overview stats */}\n      <div className=\"grid gap-6 sm:grid-cols-2 lg:grid-cols-4\">\n        {/* Total users */}\n        <div className=\"rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-800 dark:bg-gray-900\">\n          <div className=\"flex items-center\">\n            <div className=\"mr-4 flex h-12 w-12 items-center justify-center rounded-full bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400\">\n              <FontAwesomeIcon icon={faUsers} className=\"h-6 w-6\" />\n            </div>\n            <div>\n              <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Total Users</p>\n              <h3 className=\"text-2xl font-bold text-gray-900 dark:text-white\">{analytics.totalUsers.toLocaleString()}</h3>\n            </div>\n          </div>\n          <div className=\"mt-4 flex items-center text-sm\">\n            <span className=\"font-medium text-green-600 dark:text-green-400\">\n              +{analytics.newUsers[timeRangeMap[timeRange]]} new\n            </span>\n            <span className=\"ml-1 text-gray-500 dark:text-gray-400\">\n              in the last {timeRange === 'day' ? '24 hours' : timeRange === 'week' ? '7 days' : '30 days'}\n            </span>\n          </div>\n        </div>\n\n        {/* Active users */}\n        <div className=\"rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-800 dark:bg-gray-900\">\n          <div className=\"flex items-center\">\n            <div className=\"mr-4 flex h-12 w-12 items-center justify-center rounded-full bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400\">\n              <FontAwesomeIcon icon={faUsers} className=\"h-6 w-6\" />\n            </div>\n            <div>\n              <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Active Users</p>\n              <h3 className=\"text-2xl font-bold text-gray-900 dark:text-white\">{analytics.activeUsers[timeRangeMap[timeRange]].toLocaleString()}</h3>\n            </div>\n          </div>\n          <div className=\"mt-4 flex items-center text-sm\">\n            <span className=\"font-medium text-gray-900 dark:text-white\">\n              {Math.round((analytics.activeUsers[timeRangeMap[timeRange]] / analytics.totalUsers) * 100)}%\n            </span>\n            <span className=\"ml-1 text-gray-500 dark:text-gray-400\">\n              of total users\n            </span>\n          </div>\n        </div>\n\n        {/* Agent interactions */}\n        <div className=\"rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-800 dark:bg-gray-900\">\n          <div className=\"flex items-center\">\n            <div className=\"mr-4 flex h-12 w-12 items-center justify-center rounded-full bg-purple-100 text-purple-600 dark:bg-purple-900/30 dark:text-purple-400\">\n              <FontAwesomeIcon icon={faRobot} className=\"h-6 w-6\" />\n            </div>\n            <div>\n              <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Agent Interactions</p>\n              <h3 className=\"text-2xl font-bold text-gray-900 dark:text-white\">{analytics.totalAgentInteractions.toLocaleString()}</h3>\n            </div>\n          </div>\n          <div className=\"mt-4 flex items-center text-sm\">\n            <span className=\"font-medium text-gray-900 dark:text-white\">\n              {Math.round(analytics.totalAgentInteractions / analytics.activeUsers[timeRangeMap[timeRange]])}\n            </span>\n            <span className=\"ml-1 text-gray-500 dark:text-gray-400\">\n              per active user\n            </span>\n          </div>\n        </div>\n\n        {/* Sensitivity alerts */}\n        <div className=\"rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-800 dark:bg-gray-900\">\n          <div className=\"flex items-center\">\n            <div className=\"mr-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100 text-red-600 dark:bg-red-900/30 dark:text-red-400\">\n              <FontAwesomeIcon icon={faExclamationTriangle} className=\"h-6 w-6\" />\n            </div>\n            <div>\n              <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Sensitivity Alerts</p>\n              <h3 className=\"text-2xl font-bold text-gray-900 dark:text-white\">{analytics.sensitivityAlerts.toLocaleString()}</h3>\n            </div>\n          </div>\n          <div className=\"mt-4 flex items-center text-sm\">\n            <span className={`font-medium ${\n              analytics.sensitivityAlerts > 100 ? 'text-red-600 dark:text-red-400' : 'text-yellow-600 dark:text-yellow-400'\n            }`}>\n              {analytics.sensitivityAlerts > 100 ? 'High' : 'Moderate'}\n            </span>\n            <span className=\"ml-1 text-gray-500 dark:text-gray-400\">\n              alert level\n            </span>\n          </div>\n        </div>\n      </div>\n\n      {/* Navigation tabs for different analytics sections */}\n      <div className=\"border-b border-gray-200 dark:border-gray-800\">\n        <nav className=\"-mb-px flex space-x-8\">\n          <button\n            onClick={() => setActiveTab('overview')}\n            className={`py-4 px-1 text-sm font-medium border-b-2 ${\n              activeTab === 'overview'\n                ? 'border-indigo-500 text-indigo-600 dark:border-indigo-400 dark:text-indigo-400'\n                : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 dark:text-gray-400 dark:hover:border-gray-700 dark:hover:text-gray-300'\n            }`}\n          >\n            Overview\n          </button>\n          <button\n            onClick={() => setActiveTab('platform')}\n            className={`py-4 px-1 text-sm font-medium border-b-2 ${\n              activeTab === 'platform'\n                ? 'border-indigo-500 text-indigo-600 dark:border-indigo-400 dark:text-indigo-400'\n                : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 dark:text-gray-400 dark:hover:border-gray-700 dark:hover:text-gray-300'\n            }`}\n          >\n            Platform Usage\n          </button>\n          <button\n            onClick={() => setActiveTab('agent')}\n            className={`py-4 px-1 text-sm font-medium border-b-2 ${\n              activeTab === 'agent'\n                ? 'border-indigo-500 text-indigo-600 dark:border-indigo-400 dark:text-indigo-400'\n                : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 dark:text-gray-400 dark:hover:border-gray-700 dark:hover:text-gray-300'\n            }`}\n          >\n            Agent Usage\n          </button>\n          <button\n            onClick={() => setActiveTab('sensitive')}\n            className={`py-4 px-1 text-sm font-medium border-b-2 ${\n              activeTab === 'sensitive'\n                ? 'border-indigo-500 text-indigo-600 dark:border-indigo-400 dark:text-indigo-400'\n                : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 dark:text-gray-400 dark:hover:border-gray-700 dark:hover:text-gray-300'\n            }`}\n          >\n            Sensitive Data\n          </button>\n          <button\n            onClick={() => setActiveTab('user')}\n            className={`py-4 px-1 text-sm font-medium border-b-2 ${\n              activeTab === 'user'\n                ? 'border-indigo-500 text-indigo-600 dark:border-indigo-400 dark:text-indigo-400'\n                : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 dark:text-gray-400 dark:hover:border-gray-700 dark:hover:text-gray-300'\n            }`}\n          >\n            User Behavior\n          </button>\n        </nav>\n      </div>\n\n      {/* Tab content */}\n      {activeTab === 'overview' && (\n        <div className=\"rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-800 dark:bg-gray-900\">\n          <h2 className=\"mb-4 text-lg font-semibold text-gray-900 dark:text-white\">Analytics Overview</h2>\n          <p className=\"mb-4 text-gray-600 dark:text-gray-400\">\n            This dashboard provides an overview of platform usage, agent interactions, and potential sensitive data concerns.\n            Use the tabs above to explore detailed analytics for specific areas.\n          </p>\n\n          <div className=\"mt-6 grid gap-6 md:grid-cols-2\">\n            <div>\n              <h3 className=\"mb-3 text-base font-medium text-gray-900 dark:text-white\">Key Metrics</h3>\n              <ul className=\"space-y-2 text-sm text-gray-600 dark:text-gray-400\">\n                <li className=\"flex items-center justify-between\">\n                  <span>Total Users:</span>\n                  <span className=\"font-medium text-gray-900 dark:text-white\">{analytics.totalUsers.toLocaleString()}</span>\n                </li>\n                <li className=\"flex items-center justify-between\">\n                  <span>Active Users (Weekly):</span>\n                  <span className=\"font-medium text-gray-900 dark:text-white\">{analytics.activeUsers.weekly.toLocaleString()}</span>\n                </li>\n                <li className=\"flex items-center justify-between\">\n                  <span>Total Agent Interactions:</span>\n                  <span className=\"font-medium text-gray-900 dark:text-white\">{analytics.totalAgentInteractions.toLocaleString()}</span>\n                </li>\n                <li className=\"flex items-center justify-between\">\n                  <span>Total Content Views:</span>\n                  <span className=\"font-medium text-gray-900 dark:text-white\">{analytics.totalContentViews.toLocaleString()}</span>\n                </li>\n                <li className=\"flex items-center justify-between\">\n                  <span>Average Session Duration:</span>\n                  <span className=\"font-medium text-gray-900 dark:text-white\">\n                    {Math.floor(analytics.averageSessionDuration / 60)}m {analytics.averageSessionDuration % 60}s\n                  </span>\n                </li>\n                <li className=\"flex items-center justify-between\">\n                  <span>Sensitivity Alerts:</span>\n                  <span className=\"font-medium text-red-600 dark:text-red-400\">{analytics.sensitivityAlerts.toLocaleString()}</span>\n                </li>\n              </ul>\n            </div>\n\n            <div>\n              <h3 className=\"mb-3 text-base font-medium text-gray-900 dark:text-white\">Quick Summary</h3>\n              <ul className=\"space-y-2 text-sm text-gray-600 dark:text-gray-400\">\n                <li className=\"flex items-start\">\n                  <span className=\"mr-2 text-green-500\">•</span>\n                  <span>The platform has seen a <strong>12.5%</strong> increase in user activity over the past week.</span>\n                </li>\n                <li className=\"flex items-start\">\n                  <span className=\"mr-2 text-green-500\">•</span>\n                  <span>The <strong>Learning Resources</strong> section has the highest growth rate at <strong>42.7%</strong>.</span>\n                </li>\n                <li className=\"flex items-start\">\n                  <span className=\"mr-2 text-amber-500\">•</span>\n                  <span>The <strong>Customer Support</strong> agent has the highest sensitivity score and requires monitoring.</span>\n                </li>\n                <li className=\"flex items-start\">\n                  <span className=\"mr-2 text-red-500\">•</span>\n                  <span>There are <strong>{analytics.sensitivityAlerts}</strong> sensitivity alerts that need review.</span>\n                </li>\n              </ul>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {activeTab === 'platform' && (\n        <PlatformUsageSection sectionAnalytics={mockSectionAnalytics} />\n      )}\n\n      {activeTab === 'agent' && (\n        <AgentUsageSection agentAnalytics={mockAgentAnalytics} />\n      )}\n\n      {activeTab === 'sensitive' && (\n        <SensitiveDataSection\n          sensitiveInteractions={mockSensitiveInteractions}\n          highRiskUsers={highRiskUsers}\n        />\n      )}\n\n      {activeTab === 'user' && (\n        <UserBehaviorSection\n          userAnalytics={mockUserAnalytics}\n          contentAnalytics={mockContentAnalytics}\n        />\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAUA;AASA,4BAA4B;AAC5B;AACA;AACA;AACA;AA5BA;;;;;;;;;;;AA8Be,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4B;IACrE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4D;IACrG,MAAM,YAAY,gIAAA,CAAA,yBAAsB;IAExC,yBAAyB;IACzB,MAAM,gBAAgB,gIAAA,CAAA,oBAAiB,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,SAAS,KAAK,UAAU,KAAK,SAAS,KAAK;IAEvG,oDAAoD;IACpD,MAAM,eAAe;QACnB,OAAO;QACP,QAAQ;QACR,SAAS;IACX;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC,oKAAA,CAAA,kBAAe;wCAAC,MAAM,wKAAA,CAAA,cAAW;wCAAE,WAAU;;;;;;oCAAiB;;;;;;;0CAGjE,8OAAC;gCAAG,WAAU;0CAAmD;;;;;;;;;;;;kCAInE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAC,wCAAwC,EAClD,cAAc,QACV,6EACA,iFACJ;0CACH;;;;;;0CAGD,8OAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAC,wCAAwC,EAClD,cAAc,SACV,6EACA,iFACJ;0CACH;;;;;;0CAGD,8OAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAC,wCAAwC,EAClD,cAAc,UACV,6EACA,iFACJ;0CACH;;;;;;;;;;;;;;;;;;0BAOL,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oKAAA,CAAA,kBAAe;4CAAC,MAAM,wKAAA,CAAA,UAAO;4CAAE,WAAU;;;;;;;;;;;kDAE5C,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAuD;;;;;;0DACpE,8OAAC;gDAAG,WAAU;0DAAoD,UAAU,UAAU,CAAC,cAAc;;;;;;;;;;;;;;;;;;0CAGzG,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;;4CAAiD;4CAC7D,UAAU,QAAQ,CAAC,YAAY,CAAC,UAAU,CAAC;4CAAC;;;;;;;kDAEhD,8OAAC;wCAAK,WAAU;;4CAAwC;4CACzC,cAAc,QAAQ,aAAa,cAAc,SAAS,WAAW;;;;;;;;;;;;;;;;;;;kCAMxF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oKAAA,CAAA,kBAAe;4CAAC,MAAM,wKAAA,CAAA,UAAO;4CAAE,WAAU;;;;;;;;;;;kDAE5C,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAuD;;;;;;0DACpE,8OAAC;gDAAG,WAAU;0DAAoD,UAAU,WAAW,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,cAAc;;;;;;;;;;;;;;;;;;0CAGnI,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;;4CACb,KAAK,KAAK,CAAC,AAAC,UAAU,WAAW,CAAC,YAAY,CAAC,UAAU,CAAC,GAAG,UAAU,UAAU,GAAI;4CAAK;;;;;;;kDAE7F,8OAAC;wCAAK,WAAU;kDAAwC;;;;;;;;;;;;;;;;;;kCAO5D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oKAAA,CAAA,kBAAe;4CAAC,MAAM,wKAAA,CAAA,UAAO;4CAAE,WAAU;;;;;;;;;;;kDAE5C,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAuD;;;;;;0DACpE,8OAAC;gDAAG,WAAU;0DAAoD,UAAU,sBAAsB,CAAC,cAAc;;;;;;;;;;;;;;;;;;0CAGrH,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDACb,KAAK,KAAK,CAAC,UAAU,sBAAsB,GAAG,UAAU,WAAW,CAAC,YAAY,CAAC,UAAU,CAAC;;;;;;kDAE/F,8OAAC;wCAAK,WAAU;kDAAwC;;;;;;;;;;;;;;;;;;kCAO5D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oKAAA,CAAA,kBAAe;4CAAC,MAAM,wKAAA,CAAA,wBAAqB;4CAAE,WAAU;;;;;;;;;;;kDAE1D,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAuD;;;;;;0DACpE,8OAAC;gDAAG,WAAU;0DAAoD,UAAU,iBAAiB,CAAC,cAAc;;;;;;;;;;;;;;;;;;0CAGhH,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAW,CAAC,YAAY,EAC5B,UAAU,iBAAiB,GAAG,MAAM,mCAAmC,wCACvE;kDACC,UAAU,iBAAiB,GAAG,MAAM,SAAS;;;;;;kDAEhD,8OAAC;wCAAK,WAAU;kDAAwC;;;;;;;;;;;;;;;;;;;;;;;;0BAQ9D,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS,IAAM,aAAa;4BAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,aACV,kFACA,qJACJ;sCACH;;;;;;sCAGD,8OAAC;4BACC,SAAS,IAAM,aAAa;4BAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,aACV,kFACA,qJACJ;sCACH;;;;;;sCAGD,8OAAC;4BACC,SAAS,IAAM,aAAa;4BAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,UACV,kFACA,qJACJ;sCACH;;;;;;sCAGD,8OAAC;4BACC,SAAS,IAAM,aAAa;4BAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,cACV,kFACA,qJACJ;sCACH;;;;;;sCAGD,8OAAC;4BACC,SAAS,IAAM,aAAa;4BAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,SACV,kFACA,qJACJ;sCACH;;;;;;;;;;;;;;;;;YAOJ,cAAc,4BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA2D;;;;;;kCACzE,8OAAC;wBAAE,WAAU;kCAAwC;;;;;;kCAKrD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA2D;;;;;;kDACzE,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;kEAAK;;;;;;kEACN,8OAAC;wDAAK,WAAU;kEAA6C,UAAU,UAAU,CAAC,cAAc;;;;;;;;;;;;0DAElG,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;kEAAK;;;;;;kEACN,8OAAC;wDAAK,WAAU;kEAA6C,UAAU,WAAW,CAAC,MAAM,CAAC,cAAc;;;;;;;;;;;;0DAE1G,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;kEAAK;;;;;;kEACN,8OAAC;wDAAK,WAAU;kEAA6C,UAAU,sBAAsB,CAAC,cAAc;;;;;;;;;;;;0DAE9G,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;kEAAK;;;;;;kEACN,8OAAC;wDAAK,WAAU;kEAA6C,UAAU,iBAAiB,CAAC,cAAc;;;;;;;;;;;;0DAEzG,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;kEAAK;;;;;;kEACN,8OAAC;wDAAK,WAAU;;4DACb,KAAK,KAAK,CAAC,UAAU,sBAAsB,GAAG;4DAAI;4DAAG,UAAU,sBAAsB,GAAG;4DAAG;;;;;;;;;;;;;0DAGhG,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;kEAAK;;;;;;kEACN,8OAAC;wDAAK,WAAU;kEAA8C,UAAU,iBAAiB,CAAC,cAAc;;;;;;;;;;;;;;;;;;;;;;;;0CAK9G,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA2D;;;;;;kDACzE,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAK,WAAU;kEAAsB;;;;;;kEACtC,8OAAC;;4DAAK;0EAAwB,8OAAC;0EAAO;;;;;;4DAAc;;;;;;;;;;;;;0DAEtD,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAK,WAAU;kEAAsB;;;;;;kEACtC,8OAAC;;4DAAK;0EAAI,8OAAC;0EAAO;;;;;;4DAA2B;0EAAwC,8OAAC;0EAAO;;;;;;4DAAc;;;;;;;;;;;;;0DAE7G,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAK,WAAU;kEAAsB;;;;;;kEACtC,8OAAC;;4DAAK;0EAAI,8OAAC;0EAAO;;;;;;4DAAyB;;;;;;;;;;;;;0DAE7C,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAK,WAAU;kEAAoB;;;;;;kEACpC,8OAAC;;4DAAK;0EAAU,8OAAC;0EAAQ,UAAU,iBAAiB;;;;;;4DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQzE,cAAc,4BACb,8OAAC,uKAAA,CAAA,UAAoB;gBAAC,kBAAkB,gIAAA,CAAA,uBAAoB;;;;;;YAG7D,cAAc,yBACb,8OAAC,oKAAA,CAAA,UAAiB;gBAAC,gBAAgB,gIAAA,CAAA,qBAAkB;;;;;;YAGtD,cAAc,6BACb,8OAAC,uKAAA,CAAA,UAAoB;gBACnB,uBAAuB,gIAAA,CAAA,4BAAyB;gBAChD,eAAe;;;;;;YAIlB,cAAc,wBACb,8OAAC,sKAAA,CAAA,UAAmB;gBAClB,eAAe,gIAAA,CAAA,oBAAiB;gBAChC,kBAAkB,gIAAA,CAAA,uBAAoB;;;;;;;;;;;;AAKhD", "debugId": null}}]}