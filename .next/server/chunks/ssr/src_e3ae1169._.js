module.exports = {

"[project]/src/data/resources-original.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "resourceCategories": (()=>resourceCategories),
    "resources": (()=>resources)
});
const resourceCategories = [
    {
        id: 'productivity',
        name: 'Productivity Tools',
        description: 'Tools to enhance personal and team productivity, including task management, note-taking, and time tracking.',
        icon: 'bolt'
    },
    {
        id: 'project-management',
        name: 'Project Management',
        description: 'Software and platforms for planning, executing, and monitoring projects of all sizes.',
        icon: 'tasks'
    },
    {
        id: 'design',
        name: 'Design Tools',
        description: 'Resources for UI/UX design, graphic design, prototyping, and visual collaboration.',
        icon: 'palette'
    },
    {
        id: 'development',
        name: 'Development Tools',
        description: 'Tools and services for software development, coding, testing, and deployment.',
        icon: 'code'
    },
    {
        id: 'research',
        name: 'Research Tools',
        description: 'Resources for market research, user research, academic research, and data collection.',
        icon: 'magnifying-glass-chart'
    },
    {
        id: 'analytics',
        name: 'Analytics & Data',
        description: 'Tools for data analysis, visualization, business intelligence, and reporting.',
        icon: 'chart-line'
    },
    {
        id: 'communication',
        name: 'Communication',
        description: 'Platforms for team communication, client meetings, presentations, and email management.',
        icon: 'comments'
    },
    {
        id: 'collaboration',
        name: 'Collaboration',
        description: 'Tools that facilitate teamwork, knowledge sharing, and cross-functional collaboration.',
        icon: 'users-gear'
    }
];
const resources = [
    {
        id: 'notion',
        name: 'Notion',
        description: 'All-in-one workspace for notes, tasks, wikis, and databases.',
        url: 'https://www.notion.so',
        category: 'productivity',
        tags: [
            'note-taking',
            'project-management',
            'wiki'
        ],
        pricing: 'freemium',
        logoUrl: 'https://www.notion.so/images/favicon.ico'
    },
    {
        id: 'figma',
        name: 'Figma',
        description: 'Collaborative interface design tool for teams.',
        url: 'https://www.figma.com',
        category: 'design',
        tags: [
            'ui-design',
            'prototyping',
            'collaboration'
        ],
        pricing: 'freemium',
        logoUrl: 'https://static.figma.com/app/icon/1/favicon.png'
    },
    {
        id: 'vscode',
        name: 'Visual Studio Code',
        description: 'Free, open-source code editor with powerful development features.',
        url: 'https://code.visualstudio.com',
        category: 'development',
        tags: [
            'code-editor',
            'debugging',
            'extensions'
        ],
        pricing: 'free',
        logoUrl: 'https://code.visualstudio.com/favicon.ico'
    },
    {
        id: 'slack',
        name: 'Slack',
        description: 'Channel-based messaging platform for teams and workplaces.',
        url: 'https://slack.com',
        category: 'communication',
        tags: [
            'messaging',
            'team-communication',
            'integrations'
        ],
        pricing: 'freemium',
        logoUrl: 'https://a.slack-edge.com/80588/marketing/img/meta/favicon-32.png'
    },
    {
        id: 'trello',
        name: 'Trello',
        description: 'Visual tool for organizing work with boards, lists, and cards.',
        url: 'https://trello.com',
        category: 'project-management',
        tags: [
            'kanban',
            'task-management',
            'collaboration'
        ],
        pricing: 'freemium',
        logoUrl: 'https://a.trellocdn.com/prgb/dist/images/ios/apple-touch-icon-152x152-precomposed.0307bc39ec6c9ff499c8.png'
    },
    {
        id: 'google-analytics',
        name: 'Google Analytics',
        description: 'Web analytics service that tracks and reports website traffic.',
        url: 'https://analytics.google.com',
        category: 'analytics',
        tags: [
            'web-analytics',
            'reporting',
            'user-behavior'
        ],
        pricing: 'freemium',
        logoUrl: 'https://www.google.com/analytics/images/ga_icon_256.png'
    },
    {
        id: 'miro',
        name: 'Miro',
        description: 'Online collaborative whiteboard platform for teams.',
        url: 'https://miro.com',
        category: 'collaboration',
        tags: [
            'whiteboard',
            'brainstorming',
            'visual-collaboration'
        ],
        pricing: 'freemium',
        logoUrl: 'https://miro.com/static/images/favicon/apple-touch-icon.png'
    },
    {
        id: 'google-scholar',
        name: 'Google Scholar',
        description: 'Search engine for academic literature and research papers.',
        url: 'https://scholar.google.com',
        category: 'research',
        tags: [
            'academic-research',
            'citations',
            'literature-search'
        ],
        pricing: 'free',
        logoUrl: 'https://scholar.google.com/favicon.ico'
    }
];
}}),
"[project]/src/data/resources.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getCategoryById": (()=>getCategoryById),
    "getResourcesByCategory": (()=>getResourcesByCategory),
    "resources": (()=>combinedResources)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$resources$2d$original$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/data/resources-original.ts [app-ssr] (ecmascript)");
;
// New resources from the AI Presentation Series Summary PDF
const newResources = [
    // Productivity Tools
    {
        id: 'perplexity-ai',
        name: 'Perplexity AI',
        description: 'An AI-powered search engine with a chatbot interface that understands and responds to user queries using GPT-3.5.',
        url: 'https://www.perplexity.ai/',
        category: 'productivity',
        tags: [
            'search-engine',
            'ai-assistant',
            'research'
        ],
        pricing: 'freemium',
        logoUrl: 'https://www.perplexity.ai/favicon.ico'
    },
    {
        id: 'claude-ai',
        name: 'Claude AI',
        description: 'Analyzes and suggests improvements for very long content, similar to ChatGPT but with enhanced capabilities for handling lengthy documents.',
        url: 'https://claude.ai/',
        category: 'productivity',
        tags: [
            'ai-assistant',
            'content-generation',
            'document-analysis'
        ],
        pricing: 'freemium',
        logoUrl: 'https://claude.ai/favicon.ico'
    },
    {
        id: 'fathom-ai',
        name: 'Fathom AI',
        description: 'Zoom app that records, transcribes, and highlights key moments from calls, making meeting follow-up more efficient.',
        url: 'https://fathom.video/',
        category: 'productivity',
        tags: [
            'meeting-assistant',
            'transcription',
            'video-conferencing'
        ],
        pricing: 'freemium',
        logoUrl: 'https://fathom.video/favicon.ico'
    },
    {
        id: 'plaud-ai',
        name: 'Plaud.ai',
        description: 'AI tool for note-taking and transcription that helps capture and organize information from meetings and conversations.',
        url: 'https://www.plaud.ai/',
        category: 'productivity',
        tags: [
            'note-taking',
            'transcription',
            'meeting-assistant'
        ],
        pricing: 'freemium',
        logoUrl: 'https://www.plaud.ai/favicon.ico'
    },
    {
        id: 'whisper',
        name: 'Whisper',
        description: 'Converts audio to text and vice versa using AI, providing accurate transcription for various languages and accents.',
        url: 'https://openai.com/index/whisper/',
        category: 'productivity',
        tags: [
            'transcription',
            'audio-processing',
            'speech-to-text'
        ],
        pricing: 'freemium',
        logoUrl: 'https://openai.com/favicon.ico'
    },
    {
        id: 'notebooklm',
        name: 'NotebookLM',
        description: 'Tool for data management and note-taking that uses AI to help organize and retrieve information efficiently.',
        url: 'https://notebooklm.google.com',
        category: 'productivity',
        tags: [
            'note-taking',
            'knowledge-management',
            'ai-organization'
        ],
        pricing: 'free',
        logoUrl: 'https://notebooklm.google.com/favicon.ico'
    },
    {
        id: 'deepl',
        name: 'DeepL',
        description: 'Translation tool for efficient and accurate translations between multiple languages, powered by advanced AI.',
        url: 'https://www.deepl.com',
        category: 'productivity',
        tags: [
            'translation',
            'language-processing',
            'communication'
        ],
        pricing: 'freemium',
        logoUrl: 'https://www.deepl.com/favicon.ico'
    },
    // Design Tools
    {
        id: 'midjourney',
        name: 'Midjourney',
        description: 'Generates images from descriptive language, similar to DALL-E and Stable Diffusion, with a focus on artistic quality.',
        url: 'https://www.midjourney.com/',
        category: 'design',
        tags: [
            'image-generation',
            'ai-art',
            'creative-tools'
        ],
        pricing: 'paid',
        logoUrl: 'https://www.midjourney.com/favicon.ico'
    },
    {
        id: 'bing-images',
        name: 'Bing Image Creator',
        description: 'Provides tools for creating images from text descriptions, potentially offering more features than Midjourney.',
        url: 'https://www.bing.com/images/create',
        category: 'design',
        tags: [
            'image-generation',
            'ai-art',
            'creative-tools'
        ],
        pricing: 'free',
        logoUrl: 'https://www.bing.com/favicon.ico'
    },
    {
        id: 'meta-imagine',
        name: 'Meta Imagine',
        description: 'Uses models to generate images from textual prompts provided by the user, created by Meta (Facebook).',
        url: 'https://imagine.meta.com',
        category: 'design',
        tags: [
            'image-generation',
            'ai-art',
            'creative-tools'
        ],
        pricing: 'free',
        logoUrl: 'https://imagine.meta.com/favicon.ico'
    },
    {
        id: 'designer-microsoft',
        name: 'Microsoft Designer',
        description: 'Microsoft\'s answer to Canva, focusing on design solutions with AI-powered features for creating professional graphics.',
        url: 'https://designer.microsoft.com/',
        category: 'design',
        tags: [
            'graphic-design',
            'presentation',
            'marketing-materials'
        ],
        pricing: 'freemium',
        logoUrl: 'https://designer.microsoft.com/favicon.ico'
    },
    {
        id: 'runway',
        name: 'Runway',
        description: 'A creative platform for video production and editing with AI-powered tools for visual effects and content creation.',
        url: 'https://app.runwayml.com/',
        category: 'design',
        tags: [
            'video-editing',
            'visual-effects',
            'content-creation'
        ],
        pricing: 'freemium',
        logoUrl: 'https://app.runwayml.com/favicon.ico'
    },
    {
        id: 'clipdrop',
        name: 'Clipdrop',
        description: 'Developed by Stability AI, for various image and video editing tasks with AI-powered features.',
        url: 'https://clipdrop.co/',
        category: 'design',
        tags: [
            'image-editing',
            'video-editing',
            'content-creation'
        ],
        pricing: 'freemium',
        logoUrl: 'https://clipdrop.co/favicon.ico'
    },
    {
        id: 'interior-ai',
        name: 'Interior AI',
        description: 'Interior design tool for generating and visualizing room layouts using AI to create realistic interior designs.',
        url: 'https://interiorai.com/',
        category: 'design',
        tags: [
            'interior-design',
            'visualization',
            'architecture'
        ],
        pricing: 'freemium',
        logoUrl: 'https://interiorai.com/favicon.ico'
    },
    {
        id: 'meshy-ai',
        name: 'Meshy.ai',
        description: 'For 3D modeling, used in architecture and design to create and manipulate 3D objects with AI assistance.',
        url: 'https://www.meshy.ai',
        category: 'design',
        tags: [
            '3d-modeling',
            'architecture',
            'design'
        ],
        pricing: 'freemium',
        logoUrl: 'https://www.meshy.ai/favicon.ico'
    },
    {
        id: 'mnml-ai',
        name: 'MNML AI',
        description: 'Architecture design assistant that helps create minimalist architectural designs with AI guidance.',
        url: 'https://mnml.ai',
        category: 'design',
        tags: [
            'architecture',
            'design',
            'minimalism'
        ],
        pricing: 'freemium',
        logoUrl: 'https://mnml.ai/favicon.ico'
    },
    {
        id: 'ulama-tech',
        name: 'Ulama.tech',
        description: 'For architectural design, specifically for structure and planning with AI-powered tools.',
        url: 'http://ulama.tech',
        category: 'design',
        tags: [
            'architecture',
            'structural-design',
            'planning'
        ],
        pricing: 'freemium',
        logoUrl: 'http://ulama.tech/favicon.ico'
    },
    {
        id: 'weshop-ai',
        name: 'WeShop',
        description: 'Produce high-quality product images inexpensively and quickly using AI-generated visuals.',
        url: 'https://www.weshop.ai/',
        category: 'design',
        tags: [
            'product-photography',
            'e-commerce',
            'marketing'
        ],
        pricing: 'paid',
        logoUrl: 'https://www.weshop.ai/favicon.ico'
    },
    {
        id: 'botika',
        name: 'Botika',
        description: 'Helps fashion retailers save on photo costs and boost sales using AI-generated models for product visualization.',
        url: 'https://botika.io/',
        category: 'design',
        tags: [
            'fashion',
            'e-commerce',
            'product-visualization'
        ],
        pricing: 'paid',
        logoUrl: 'https://botika.io/favicon.ico'
    },
    {
        id: 'flux-ai',
        name: 'Flux AI',
        description: 'Enables creative image generation and animation with AI-powered tools for designers and artists.',
        url: 'https://flux-ai.io/',
        category: 'design',
        tags: [
            'image-generation',
            'animation',
            'creative-tools'
        ],
        pricing: 'freemium',
        logoUrl: 'https://flux-ai.io/favicon.ico'
    },
    // Development Tools
    {
        id: '10web',
        name: '10Web',
        description: 'An AI-Powered WordPress Platform for website development and management with automated features.',
        url: 'https://10web.io/',
        category: 'development',
        tags: [
            'wordpress',
            'website-builder',
            'automation'
        ],
        pricing: 'paid',
        logoUrl: 'https://10web.io/favicon.ico'
    },
    {
        id: 'framer',
        name: 'Framer',
        description: 'A tool for building interactive websites and web applications with a focus on design and user experience.',
        url: 'https://framer.com/',
        category: 'development',
        tags: [
            'website-builder',
            'prototyping',
            'design'
        ],
        pricing: 'freemium',
        logoUrl: 'https://framer.com/favicon.ico'
    },
    {
        id: 'github-copilot',
        name: 'GitHub Copilot',
        description: 'AI pair programmer that assists in code completion and suggestions within code editors, powered by OpenAI Codex.',
        url: 'https://github.com/features/copilot',
        category: 'development',
        tags: [
            'coding-assistant',
            'pair-programming',
            'code-completion'
        ],
        pricing: 'paid',
        logoUrl: 'https://github.com/favicon.ico'
    },
    {
        id: 'github-spark',
        name: 'GitHub Spark',
        description: 'AI tool for building web applications using natural language, aiming to lower the barrier to software development.',
        url: 'https://github.com/features',
        category: 'development',
        tags: [
            'web-development',
            'no-code',
            'ai-coding'
        ],
        pricing: 'paid',
        logoUrl: 'https://github.com/favicon.ico'
    },
    {
        id: 'langchain',
        name: 'LangChain',
        description: 'Builds AI-powered applications by connecting large language models with external data sources and tools.',
        url: 'https://www.langchain.com/',
        category: 'development',
        tags: [
            'llm-framework',
            'ai-development',
            'integration'
        ],
        pricing: 'free',
        logoUrl: 'https://www.langchain.com/favicon.ico'
    },
    // Communication
    {
        id: 'heygen',
        name: 'HeyGen',
        description: 'Produces studio quality videos with AI-generated avatars and voices for professional communication.',
        url: 'https://www.heygen.com',
        category: 'communication',
        tags: [
            'video-creation',
            'avatars',
            'presentation'
        ],
        pricing: 'paid',
        logoUrl: 'https://www.heygen.com/favicon.ico'
    },
    {
        id: 'beautiful-ai',
        name: 'Beautiful.ai',
        description: 'Presentation tool that simplifies the creation of professional presentations using AI to handle design elements.',
        url: 'http://beautiful.ai',
        category: 'communication',
        tags: [
            'presentation',
            'slides',
            'design'
        ],
        pricing: 'freemium',
        logoUrl: 'http://beautiful.ai/favicon.ico'
    },
    {
        id: 'gamma-app',
        name: 'Gamma.app',
        description: 'Creates engaging presentations by transforming ideas into visually appealing slides with AI assistance.',
        url: 'https://gamma.app/',
        category: 'communication',
        tags: [
            'presentation',
            'slides',
            'design'
        ],
        pricing: 'freemium',
        logoUrl: 'https://gamma.app/favicon.ico'
    },
    {
        id: 'decktopus',
        name: 'Decktopus',
        description: 'An AI-powered tool that assists in creating presentation starting points with professional templates and designs.',
        url: 'https://decktopus.com',
        category: 'communication',
        tags: [
            'presentation',
            'slides',
            'design'
        ],
        pricing: 'freemium',
        logoUrl: 'https://decktopus.com/favicon.ico'
    },
    {
        id: 'opus',
        name: 'Opus',
        description: 'Transforms long videos into short clips with a single click using generative AI for more effective communication.',
        url: 'https://www.opus.pro/',
        category: 'communication',
        tags: [
            'video-editing',
            'content-creation',
            'summarization'
        ],
        pricing: 'paid',
        logoUrl: 'https://www.opus.pro/favicon.ico'
    },
    {
        id: 'vapi',
        name: 'VAPI',
        description: 'Builds and optimizes voice agents for customer service and communication applications.',
        url: 'https://vapi.ai/',
        category: 'communication',
        tags: [
            'voice-agents',
            'customer-service',
            'automation'
        ],
        pricing: 'paid',
        logoUrl: 'https://vapi.ai/favicon.ico'
    },
    {
        id: 'sora',
        name: 'Sora',
        description: 'Turn text instructions into detailed video scenes for communication and presentation purposes.',
        url: 'https://openai.com/sora',
        category: 'communication',
        tags: [
            'video-generation',
            'content-creation',
            'presentation'
        ],
        pricing: 'paid',
        logoUrl: 'https://openai.com/favicon.ico'
    },
    // Collaboration
    {
        id: 'rancelab',
        name: 'RanceLab',
        description: 'Integrates WhatsApp with other platforms for better team collaboration and customer communication.',
        url: 'https://www.rancelab.com/',
        category: 'collaboration',
        tags: [
            'whatsapp-integration',
            'communication',
            'customer-service'
        ],
        pricing: 'paid',
        logoUrl: 'https://www.rancelab.com/favicon.ico'
    },
    {
        id: 'lawgeex',
        name: 'LawGeex',
        description: 'Legal automation platform that uses AI to review contracts and facilitate legal collaboration.',
        url: 'https://www.lawgeex.com/',
        category: 'collaboration',
        tags: [
            'legal',
            'contract-review',
            'automation'
        ],
        pricing: 'paid',
        logoUrl: 'https://www.lawgeex.com/favicon.ico'
    },
    // Analytics & Data
    {
        id: 'browse-ai',
        name: 'BrowseAI',
        description: 'Facilitates data extraction and monitoring from websites for easy data acquisition and analysis.',
        url: 'https://www.browse.ai/',
        category: 'analytics',
        tags: [
            'data-extraction',
            'web-scraping',
            'monitoring'
        ],
        pricing: 'freemium',
        logoUrl: 'https://www.browse.ai/favicon.ico'
    },
    {
        id: 'relevance-ai',
        name: 'Relevance AI',
        description: 'Platform providing AI-driven insights and analytics to enhance business decision-making.',
        url: 'https://relevanceai.com/',
        category: 'analytics',
        tags: [
            'data-analysis',
            'insights',
            'business-intelligence'
        ],
        pricing: 'freemium',
        logoUrl: 'https://relevanceai.com/favicon.ico'
    },
    // Project Management
    {
        id: 'make-com',
        name: 'Make.com',
        description: 'Automation platform for streamlining workflows and processes using AI to connect apps and automate tasks.',
        url: 'https://make.com',
        category: 'project-management',
        tags: [
            'automation',
            'workflow',
            'integration'
        ],
        pricing: 'freemium',
        logoUrl: 'https://make.com/favicon.ico'
    },
    {
        id: 'zapier-central',
        name: 'Zapier Central',
        description: 'Automating tasks and workflows using AI-powered integrations between different applications and services.',
        url: 'https://zapier.com/central',
        category: 'project-management',
        tags: [
            'automation',
            'workflow',
            'integration'
        ],
        pricing: 'freemium',
        logoUrl: 'https://zapier.com/favicon.ico'
    },
    {
        id: 'agents-ai',
        name: 'Agents.ai',
        description: 'Professional network of AI agents for business automation and task management.',
        url: 'https://agents.ai/',
        category: 'project-management',
        tags: [
            'automation',
            'ai-agents',
            'task-management'
        ],
        pricing: 'paid',
        logoUrl: 'https://agents.ai/favicon.ico'
    },
    {
        id: 'napkin-ai',
        name: 'Napkin.ai',
        description: 'Useful for generating content, proofreading, and ideation feedback for project planning and documentation.',
        url: 'http://napkin.ai',
        category: 'project-management',
        tags: [
            'content-generation',
            'ideation',
            'documentation'
        ],
        pricing: 'freemium',
        logoUrl: 'http://napkin.ai/favicon.ico'
    }
];
// Combine existing resources with new resources, avoiding duplicates
const combinedResources = [
    ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$resources$2d$original$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["resources"],
    ...newResources.filter((newResource)=>!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$resources$2d$original$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["resources"].some((existingResource)=>existingResource.id === newResource.id || existingResource.name.toLowerCase() === newResource.name.toLowerCase()))
];
;
function getResourcesByCategory(categoryId) {
    return combinedResources.filter((resource)=>resource.category === categoryId);
}
function getCategoryById(categoryId) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$resources$2d$original$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["resourceCategories"].find((category)=>category.id === categoryId);
}
}}),
"[project]/src/data/resources.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$resources$2d$original$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/data/resources-original.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$resources$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/data/resources.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/app/learning/resources/page.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>ResourcesPage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$react$2d$fontawesome$2f$index$2e$es$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@fortawesome/react-fontawesome/index.es.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@fortawesome/free-solid-svg-icons/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$resources$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/data/resources.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$resources$2d$original$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/data/resources-original.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
// Map category IDs to FontAwesome icons
const categoryIcons = {
    'productivity': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["faBolt"],
    'project-management': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["faTasks"],
    'design': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["faPalette"],
    'development': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["faCode"],
    'research': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["faMagnifyingGlassChart"],
    'analytics': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["faChartLine"],
    'communication': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["faComments"],
    'collaboration': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["faUsersGear"]
};
function ResourcesPage() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "space-y-8",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "rounded-lg bg-gradient-to-r from-teal-600 to-emerald-700 p-8 text-white shadow-lg",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                        className: "mb-4 text-3xl font-bold",
                        children: "Resources Directory"
                    }, void 0, false, {
                        fileName: "[project]/src/app/learning/resources/page.tsx",
                        lineNumber: 35,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "mb-6 text-lg",
                        children: "Explore our curated collection of tools and services for consulting professionals. Find resources for productivity, project management, design, development, research, and more."
                    }, void 0, false, {
                        fileName: "[project]/src/app/learning/resources/page.tsx",
                        lineNumber: 36,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/learning/resources/page.tsx",
                lineNumber: 34,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",
                children: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$resources$2d$original$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["resourceCategories"].map((category)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        href: `/learning/resources/${category.id}`,
                        className: "group flex flex-col rounded-lg border border-gray-200 bg-white p-6 shadow-sm transition hover:shadow-md dark:border-gray-800 dark:bg-gray-900",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-teal-100 text-teal-600 dark:bg-teal-900/30 dark:text-teal-400",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$react$2d$fontawesome$2f$index$2e$es$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FontAwesomeIcon"], {
                                    icon: categoryIcons[category.id],
                                    className: "h-6 w-6"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/learning/resources/page.tsx",
                                    lineNumber: 49,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/learning/resources/page.tsx",
                                lineNumber: 48,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "mb-2 text-xl font-bold text-gray-900 group-hover:text-teal-600 dark:text-white dark:group-hover:text-teal-400",
                                children: category.name
                            }, void 0, false, {
                                fileName: "[project]/src/app/learning/resources/page.tsx",
                                lineNumber: 51,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "mb-4 flex-1 text-gray-600 dark:text-gray-400",
                                children: category.description
                            }, void 0, false, {
                                fileName: "[project]/src/app/learning/resources/page.tsx",
                                lineNumber: 54,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "mt-auto flex items-center text-sm font-medium text-teal-600 dark:text-teal-400",
                                children: [
                                    "Browse resources",
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$react$2d$fontawesome$2f$index$2e$es$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FontAwesomeIcon"], {
                                        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["faArrowRight"],
                                        className: "ml-1 h-3 w-3"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/learning/resources/page.tsx",
                                        lineNumber: 57,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/learning/resources/page.tsx",
                                lineNumber: 55,
                                columnNumber: 13
                            }, this)
                        ]
                    }, category.id, true, {
                        fileName: "[project]/src/app/learning/resources/page.tsx",
                        lineNumber: 43,
                        columnNumber: 11
                    }, this))
            }, void 0, false, {
                fileName: "[project]/src/app/learning/resources/page.tsx",
                lineNumber: 41,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-800 dark:bg-gray-900",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        className: "mb-4 text-2xl font-bold",
                        children: "About Our Resources Directory"
                    }, void 0, false, {
                        fileName: "[project]/src/app/learning/resources/page.tsx",
                        lineNumber: 64,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "mb-4 text-gray-600 dark:text-gray-400",
                        children: "This directory is regularly updated with the latest tools and services that can help consulting professionals work more efficiently and deliver better results for clients. Our team carefully evaluates each resource before adding it to the directory."
                    }, void 0, false, {
                        fileName: "[project]/src/app/learning/resources/page.tsx",
                        lineNumber: 65,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "mb-4 text-gray-600 dark:text-gray-400",
                        children: "Each category includes a variety of tools with different pricing models, from free to enterprise-level solutions, allowing you to find options that fit your specific needs and budget."
                    }, void 0, false, {
                        fileName: "[project]/src/app/learning/resources/page.tsx",
                        lineNumber: 68,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mt-4 rounded-md bg-teal-50 p-4 dark:bg-teal-900/20",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "mb-2 font-semibold text-teal-800 dark:text-teal-300",
                                children: "Have a suggestion?"
                            }, void 0, false, {
                                fileName: "[project]/src/app/learning/resources/page.tsx",
                                lineNumber: 72,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-teal-700 dark:text-teal-400",
                                children: "If you know of a great tool that should be included in our directory, please let us know. We're always looking to expand our collection with valuable resources."
                            }, void 0, false, {
                                fileName: "[project]/src/app/learning/resources/page.tsx",
                                lineNumber: 73,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/learning/resources/page.tsx",
                        lineNumber: 71,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/learning/resources/page.tsx",
                lineNumber: 63,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/learning/resources/page.tsx",
        lineNumber: 33,
        columnNumber: 5
    }, this);
}
}}),

};

//# sourceMappingURL=src_e3ae1169._.js.map