{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/app/auth/register/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { faEnvelope, faLock, faUser, faSpinner } from '@fortawesome/free-solid-svg-icons';\n\nexport default function RegisterPage() {\n  const { register, isLoading, error } = useAuth();\n  \n  const [name, setName] = useState('');\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [confirmPassword, setConfirmPassword] = useState('');\n  const [formError, setFormError] = useState('');\n  \n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setFormError('');\n    \n    if (!name || !email || !password) {\n      setFormError('All fields are required');\n      return;\n    }\n    \n    if (password !== confirmPassword) {\n      setFormError('Passwords do not match');\n      return;\n    }\n    \n    if (password.length < 8) {\n      setFormError('Password must be at least 8 characters long');\n      return;\n    }\n    \n    try {\n      await register({ name, email, password });\n      // The redirect is handled in the AuthContext\n    } catch (error) {\n      setFormError('An error occurred during registration');\n    }\n  };\n  \n  return (\n    <div className=\"space-y-6\">\n      <div className=\"text-center\">\n        <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">Create a new account</h2>\n        <p className=\"mt-2 text-sm text-gray-600 dark:text-gray-400\">\n          Or{' '}\n          <Link href=\"/auth/login\" className=\"font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300\">\n            sign in to your existing account\n          </Link>\n        </p>\n      </div>\n      \n      {(error || formError) && (\n        <div className=\"rounded-md bg-red-50 p-4 dark:bg-red-900/30\">\n          <div className=\"flex\">\n            <div className=\"flex-shrink-0\">\n              <svg className=\"h-5 w-5 text-red-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\" aria-hidden=\"true\">\n                <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z\" clipRule=\"evenodd\" />\n              </svg>\n            </div>\n            <div className=\"ml-3\">\n              <h3 className=\"text-sm font-medium text-red-800 dark:text-red-200\">Registration failed</h3>\n              <div className=\"mt-2 text-sm text-red-700 dark:text-red-300\">\n                <p>{error || formError}</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n      \n      <form className=\"space-y-6\" onSubmit={handleSubmit}>\n        <div>\n          <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n            Full Name\n          </label>\n          <div className=\"relative mt-1 rounded-md shadow-sm\">\n            <div className=\"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3\">\n              <FontAwesomeIcon icon={faUser} className=\"h-5 w-5 text-gray-400\" />\n            </div>\n            <input\n              id=\"name\"\n              name=\"name\"\n              type=\"text\"\n              autoComplete=\"name\"\n              required\n              value={name}\n              onChange={(e) => setName(e.target.value)}\n              className=\"block w-full rounded-md border-gray-300 pl-10 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400\"\n              placeholder=\"John Doe\"\n            />\n          </div>\n        </div>\n        \n        <div>\n          <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n            Email address\n          </label>\n          <div className=\"relative mt-1 rounded-md shadow-sm\">\n            <div className=\"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3\">\n              <FontAwesomeIcon icon={faEnvelope} className=\"h-5 w-5 text-gray-400\" />\n            </div>\n            <input\n              id=\"email\"\n              name=\"email\"\n              type=\"email\"\n              autoComplete=\"email\"\n              required\n              value={email}\n              onChange={(e) => setEmail(e.target.value)}\n              className=\"block w-full rounded-md border-gray-300 pl-10 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400\"\n              placeholder=\"<EMAIL>\"\n            />\n          </div>\n        </div>\n        \n        <div>\n          <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n            Password\n          </label>\n          <div className=\"relative mt-1 rounded-md shadow-sm\">\n            <div className=\"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3\">\n              <FontAwesomeIcon icon={faLock} className=\"h-5 w-5 text-gray-400\" />\n            </div>\n            <input\n              id=\"password\"\n              name=\"password\"\n              type=\"password\"\n              autoComplete=\"new-password\"\n              required\n              value={password}\n              onChange={(e) => setPassword(e.target.value)}\n              className=\"block w-full rounded-md border-gray-300 pl-10 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400\"\n              placeholder=\"••••••••\"\n            />\n          </div>\n          <p className=\"mt-1 text-xs text-gray-500 dark:text-gray-400\">\n            Password must be at least 8 characters long\n          </p>\n        </div>\n        \n        <div>\n          <label htmlFor=\"confirmPassword\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n            Confirm Password\n          </label>\n          <div className=\"relative mt-1 rounded-md shadow-sm\">\n            <div className=\"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3\">\n              <FontAwesomeIcon icon={faLock} className=\"h-5 w-5 text-gray-400\" />\n            </div>\n            <input\n              id=\"confirmPassword\"\n              name=\"confirmPassword\"\n              type=\"password\"\n              autoComplete=\"new-password\"\n              required\n              value={confirmPassword}\n              onChange={(e) => setConfirmPassword(e.target.value)}\n              className=\"block w-full rounded-md border-gray-300 pl-10 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400\"\n              placeholder=\"••••••••\"\n            />\n          </div>\n        </div>\n        \n        <div>\n          <button\n            type=\"submit\"\n            disabled={isLoading}\n            className=\"flex w-full justify-center rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-75 dark:bg-blue-700 dark:hover:bg-blue-800\"\n          >\n            {isLoading ? (\n              <>\n                <FontAwesomeIcon icon={faSpinner} className=\"mr-2 h-4 w-4 animate-spin\" />\n                Creating account...\n              </>\n            ) : (\n              'Create account'\n            )}\n          </button>\n        </div>\n      </form>\n      \n      <p className=\"text-center text-xs text-gray-500 dark:text-gray-400\">\n        By creating an account, you agree to our{' '}\n        <Link href=\"/terms\" className=\"font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300\">\n          Terms of Service\n        </Link>{' '}\n        and{' '}\n        <Link href=\"/privacy\" className=\"font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300\">\n          Privacy Policy\n        </Link>\n      </p>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAE7C,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,aAAa;QAEb,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,UAAU;YAChC,aAAa;YACb;QACF;QAEA,IAAI,aAAa,iBAAiB;YAChC,aAAa;YACb;QACF;QAEA,IAAI,SAAS,MAAM,GAAG,GAAG;YACvB,aAAa;YACb;QACF;QAEA,IAAI;YACF,MAAM,SAAS;gBAAE;gBAAM;gBAAO;YAAS;QACvC,6CAA6C;QAC/C,EAAE,OAAO,OAAO;YACd,aAAa;QACf;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAmD;;;;;;kCACjE,8OAAC;wBAAE,WAAU;;4BAAgD;4BACxD;0CACH,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAc,WAAU;0CAA4F;;;;;;;;;;;;;;;;;;YAMlI,CAAC,SAAS,SAAS,mBAClB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;gCAAuB,SAAQ;gCAAY,MAAK;gCAAe,eAAY;0CACxF,cAAA,8OAAC;oCAAK,UAAS;oCAAU,GAAE;oCAAgN,UAAS;;;;;;;;;;;;;;;;sCAGxP,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAqD;;;;;;8CACnE,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;kDAAG,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvB,8OAAC;gBAAK,WAAU;gBAAY,UAAU;;kCACpC,8OAAC;;0CACC,8OAAC;gCAAM,SAAQ;gCAAO,WAAU;0CAA6D;;;;;;0CAG7F,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oKAAA,CAAA,kBAAe;4CAAC,MAAM,wKAAA,CAAA,SAAM;4CAAE,WAAU;;;;;;;;;;;kDAE3C,8OAAC;wCACC,IAAG;wCACH,MAAK;wCACL,MAAK;wCACL,cAAa;wCACb,QAAQ;wCACR,OAAO;wCACP,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;wCACvC,WAAU;wCACV,aAAY;;;;;;;;;;;;;;;;;;kCAKlB,8OAAC;;0CACC,8OAAC;gCAAM,SAAQ;gCAAQ,WAAU;0CAA6D;;;;;;0CAG9F,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oKAAA,CAAA,kBAAe;4CAAC,MAAM,wKAAA,CAAA,aAAU;4CAAE,WAAU;;;;;;;;;;;kDAE/C,8OAAC;wCACC,IAAG;wCACH,MAAK;wCACL,MAAK;wCACL,cAAa;wCACb,QAAQ;wCACR,OAAO;wCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wCACxC,WAAU;wCACV,aAAY;;;;;;;;;;;;;;;;;;kCAKlB,8OAAC;;0CACC,8OAAC;gCAAM,SAAQ;gCAAW,WAAU;0CAA6D;;;;;;0CAGjG,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oKAAA,CAAA,kBAAe;4CAAC,MAAM,wKAAA,CAAA,SAAM;4CAAE,WAAU;;;;;;;;;;;kDAE3C,8OAAC;wCACC,IAAG;wCACH,MAAK;wCACL,MAAK;wCACL,cAAa;wCACb,QAAQ;wCACR,OAAO;wCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;wCAC3C,WAAU;wCACV,aAAY;;;;;;;;;;;;0CAGhB,8OAAC;gCAAE,WAAU;0CAAgD;;;;;;;;;;;;kCAK/D,8OAAC;;0CACC,8OAAC;gCAAM,SAAQ;gCAAkB,WAAU;0CAA6D;;;;;;0CAGxG,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oKAAA,CAAA,kBAAe;4CAAC,MAAM,wKAAA,CAAA,SAAM;4CAAE,WAAU;;;;;;;;;;;kDAE3C,8OAAC;wCACC,IAAG;wCACH,MAAK;wCACL,MAAK;wCACL,cAAa;wCACb,QAAQ;wCACR,OAAO;wCACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;wCAClD,WAAU;wCACV,aAAY;;;;;;;;;;;;;;;;;;kCAKlB,8OAAC;kCACC,cAAA,8OAAC;4BACC,MAAK;4BACL,UAAU;4BACV,WAAU;sCAET,0BACC;;kDACE,8OAAC,oKAAA,CAAA,kBAAe;wCAAC,MAAM,wKAAA,CAAA,YAAS;wCAAE,WAAU;;;;;;oCAA8B;;+CAI5E;;;;;;;;;;;;;;;;;0BAMR,8OAAC;gBAAE,WAAU;;oBAAuD;oBACzB;kCACzC,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAS,WAAU;kCAA4F;;;;;;oBAElH;oBAAI;oBACR;kCACJ,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAW,WAAU;kCAA4F;;;;;;;;;;;;;;;;;;AAMpI", "debugId": null}}]}