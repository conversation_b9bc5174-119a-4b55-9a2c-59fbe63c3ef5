{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/lib/agents.ts"], "sourcesContent": ["import { Agent, Message, Session } from '@/types/agent';\n\n// Mock data for agents\nconst agents: Agent[] = [\n  {\n    id: '1',\n    name: 'Data Analyst',\n    description: 'Analyze data sets and generate insights with natural language queries.',\n    longDescription: 'The Data Analyst agent helps you explore and understand your data through natural language. Ask questions about your data, request visualizations, or get statistical summaries without writing complex queries or code. This agent can work with various data formats including CSV, Excel, SQL databases, and more.',\n    category: 'Analytics',\n    capabilities: [\n      'Natural language data queries',\n      'Data visualization generation',\n      'Statistical analysis',\n      'Anomaly detection',\n      'Trend identification',\n      'Report generation'\n    ],\n    usageCount: 1245,\n    isNew: false,\n    isFeatured: true,\n    createdAt: '2023-10-15',\n    updatedAt: '2024-03-01',\n    version: '2.3.0',\n    creator: 'Data Science Team',\n    avatarUrl: '/agents/data-analyst.svg',\n    tags: ['data', 'analytics', 'visualization', 'statistics'],\n    relatedAgentIds: ['5', '7', '8']\n  },\n  {\n    id: '2',\n    name: 'Code Assistant',\n    description: 'Help with coding tasks, debugging, and code reviews across multiple languages.',\n    longDescription: 'The Code Assistant agent is your programming partner. It can help write, debug, and optimize code in multiple languages including Python, JavaScript, Java, C++, and more. Ask for code examples, get help with debugging, or request code reviews. The agent can also explain complex code and suggest improvements.',\n    category: 'Development',\n    capabilities: [\n      'Code generation',\n      'Debugging assistance',\n      'Code optimization',\n      'Code explanation',\n      'Multiple language support',\n      'Best practices recommendations'\n    ],\n    usageCount: 3421,\n    isNew: false,\n    isFeatured: true,\n    createdAt: '2023-08-10',\n    updatedAt: '2024-02-15',\n    version: '3.1.0',\n    creator: 'Engineering Team',\n    avatarUrl: '/agents/code-assistant.svg',\n    tags: ['coding', 'programming', 'development', 'debugging'],\n    relatedAgentIds: ['9', '10']\n  },\n  {\n    id: '3',\n    name: 'Research Companion',\n    description: 'Find, summarize, and organize research papers and articles.',\n    longDescription: 'The Research Companion agent helps you stay on top of the latest research in your field. It can find relevant papers, summarize key findings, and help you organize your research materials. Ask for papers on specific topics, get summaries of complex articles, or request literature reviews.',\n    category: 'Research',\n    capabilities: [\n      'Research paper search',\n      'Article summarization',\n      'Literature review assistance',\n      'Citation generation',\n      'Research organization',\n      'Key findings extraction'\n    ],\n    usageCount: 876,\n    isNew: true,\n    isFeatured: true,\n    createdAt: '2024-01-20',\n    updatedAt: '2024-03-10',\n    version: '1.2.0',\n    creator: 'Research & Development',\n    avatarUrl: '/agents/research-companion.svg',\n    tags: ['research', 'papers', 'academic', 'literature'],\n    relatedAgentIds: ['11', '5']\n  },\n  {\n    id: '4',\n    name: 'Meeting Summarizer',\n    description: 'Generate concise summaries and action items from meeting transcripts.',\n    longDescription: 'The Meeting Summarizer agent helps you capture the important points from your meetings. Upload a transcript or recording, and the agent will identify key discussion points, decisions made, and action items assigned. It can also generate meeting minutes in various formats.',\n    category: 'Productivity',\n    capabilities: [\n      'Meeting transcript analysis',\n      'Key point extraction',\n      'Action item identification',\n      'Decision tracking',\n      'Meeting minutes generation',\n      'Follow-up reminder creation'\n    ],\n    usageCount: 2134,\n    isNew: false,\n    isFeatured: true,\n    createdAt: '2023-11-05',\n    updatedAt: '2024-02-28',\n    version: '2.0.1',\n    creator: 'Productivity Team',\n    avatarUrl: '/agents/meeting-summarizer.svg',\n    tags: ['meetings', 'productivity', 'transcription', 'summaries'],\n    relatedAgentIds: ['5', '6', '12']\n  },\n  {\n    id: '5',\n    name: 'Document Analyzer',\n    description: 'Extract key information from documents and generate summaries.',\n    longDescription: 'The Document Analyzer agent helps you process and understand documents quickly. Upload any document (PDF, Word, etc.), and the agent will extract key information, generate summaries, and answer questions about the content. It can handle contracts, reports, articles, and more.',\n    category: 'Productivity',\n    capabilities: [\n      'Document parsing',\n      'Key information extraction',\n      'Summary generation',\n      'Question answering',\n      'Multiple format support',\n      'Entity recognition'\n    ],\n    usageCount: 567,\n    isNew: false,\n    isFeatured: false,\n    createdAt: '2023-09-12',\n    updatedAt: '2024-01-30',\n    version: '1.5.2',\n    creator: 'Content Team',\n    avatarUrl: '/agents/document-analyzer.svg',\n    tags: ['documents', 'analysis', 'extraction', 'summaries'],\n    relatedAgentIds: ['1', '3', '4']\n  },\n  {\n    id: '6',\n    name: 'Presentation Creator',\n    description: 'Generate professional presentations from outlines or topics.',\n    longDescription: 'The Presentation Creator agent helps you build compelling presentations quickly. Provide a topic or outline, and the agent will generate slide content, suggest visuals, and help you structure your presentation for maximum impact. It can create presentations for various purposes including business, education, and sales.',\n    category: 'Productivity',\n    capabilities: [\n      'Slide content generation',\n      'Presentation structure suggestions',\n      'Visual element recommendations',\n      'Talking points creation',\n      'Multiple template support',\n      'Export to PowerPoint/Google Slides'\n    ],\n    usageCount: 321,\n    isNew: true,\n    isFeatured: false,\n    createdAt: '2024-02-01',\n    updatedAt: '2024-03-15',\n    version: '1.0.0',\n    creator: 'Marketing Team',\n    avatarUrl: '/agents/presentation-creator.svg',\n    tags: ['presentations', 'slides', 'design', 'content'],\n    relatedAgentIds: ['4', '13']\n  },\n  {\n    id: '7',\n    name: 'Data Visualization Expert',\n    description: 'Create beautiful and informative data visualizations from your datasets.',\n    longDescription: 'The Data Visualization Expert agent helps you transform raw data into compelling visual stories. Upload your data and describe what you want to highlight, and the agent will generate appropriate charts, graphs, and interactive visualizations that make your data insights clear and impactful.',\n    category: 'Analytics',\n    capabilities: [\n      'Chart and graph generation',\n      'Interactive visualization creation',\n      'Color palette optimization',\n      'Data storytelling assistance',\n      'Multiple export formats',\n      'Accessibility considerations'\n    ],\n    usageCount: 892,\n    isNew: false,\n    isFeatured: false,\n    createdAt: '2023-11-18',\n    updatedAt: '2024-02-10',\n    version: '1.8.0',\n    creator: 'Data Science Team',\n    avatarUrl: '/agents/data-analyst.svg',\n    tags: ['visualization', 'charts', 'graphs', 'data'],\n    relatedAgentIds: ['1', '8']\n  },\n  {\n    id: '8',\n    name: 'Predictive Analytics Agent',\n    description: 'Forecast trends and make predictions based on historical data.',\n    longDescription: 'The Predictive Analytics Agent uses machine learning algorithms to analyze your historical data and generate forecasts and predictions. It can identify patterns, trends, and anomalies to help you make data-driven decisions about future outcomes.',\n    category: 'Analytics',\n    capabilities: [\n      'Time series forecasting',\n      'Trend analysis',\n      'Anomaly detection',\n      'Predictive modeling',\n      'Scenario planning',\n      'Confidence interval calculation'\n    ],\n    usageCount: 754,\n    isNew: true,\n    isFeatured: false,\n    createdAt: '2024-01-05',\n    updatedAt: '2024-03-20',\n    version: '1.2.1',\n    creator: 'Data Science Team',\n    avatarUrl: '/agents/data-analyst.svg',\n    tags: ['predictions', 'forecasting', 'analytics', 'trends'],\n    relatedAgentIds: ['1', '7']\n  },\n  {\n    id: '9',\n    name: 'API Documentation Generator',\n    description: 'Automatically generate comprehensive API documentation from your code.',\n    longDescription: 'The API Documentation Generator analyzes your API code and generates clear, comprehensive documentation. It can create reference docs, guides, examples, and interactive API explorers to help developers understand and use your APIs effectively.',\n    category: 'Development',\n    capabilities: [\n      'API reference generation',\n      'Code example creation',\n      'Interactive API explorer',\n      'Multiple format support',\n      'Versioning assistance',\n      'Consistency checking'\n    ],\n    usageCount: 623,\n    isNew: false,\n    isFeatured: false,\n    createdAt: '2023-10-08',\n    updatedAt: '2024-02-12',\n    version: '2.1.0',\n    creator: 'Engineering Team',\n    avatarUrl: '/agents/code-assistant.svg',\n    tags: ['documentation', 'API', 'development', 'reference'],\n    relatedAgentIds: ['2', '10']\n  },\n  {\n    id: '10',\n    name: 'Code Reviewer',\n    description: 'Get detailed code reviews with suggestions for improvements and best practices.',\n    longDescription: 'The Code Reviewer agent analyzes your code for bugs, security issues, performance problems, and adherence to best practices. It provides detailed feedback with specific suggestions for improvements and explanations of potential issues.',\n    category: 'Development',\n    capabilities: [\n      'Bug detection',\n      'Security vulnerability scanning',\n      'Performance optimization',\n      'Best practice enforcement',\n      'Code style consistency',\n      'Refactoring suggestions'\n    ],\n    usageCount: 1876,\n    isNew: false,\n    isFeatured: false,\n    createdAt: '2023-09-20',\n    updatedAt: '2024-03-05',\n    version: '2.4.0',\n    creator: 'Engineering Team',\n    avatarUrl: '/agents/code-assistant.svg',\n    tags: ['code review', 'quality', 'security', 'best practices'],\n    relatedAgentIds: ['2', '9']\n  },\n  {\n    id: '11',\n    name: 'Literature Review Assistant',\n    description: 'Compile comprehensive literature reviews on any research topic.',\n    longDescription: 'The Literature Review Assistant helps researchers compile thorough literature reviews by finding relevant papers, organizing them by themes, identifying key findings, and highlighting research gaps. It can save weeks of manual research work.',\n    category: 'Research',\n    capabilities: [\n      'Research paper discovery',\n      'Thematic organization',\n      'Gap analysis',\n      'Citation management',\n      'Summary generation',\n      'Trend identification'\n    ],\n    usageCount: 542,\n    isNew: true,\n    isFeatured: false,\n    createdAt: '2024-02-15',\n    updatedAt: '2024-03-18',\n    version: '1.0.2',\n    creator: 'Research & Development',\n    avatarUrl: '/agents/research-companion.svg',\n    tags: ['literature review', 'research', 'academic', 'papers'],\n    relatedAgentIds: ['3']\n  },\n  {\n    id: '12',\n    name: 'Project Manager Assistant',\n    description: 'Track projects, manage tasks, and coordinate team activities efficiently.',\n    longDescription: 'The Project Manager Assistant helps you plan, track, and manage projects from start to finish. It can create project timelines, assign and track tasks, identify risks, generate status reports, and facilitate team communication to keep your projects on track.',\n    category: 'Productivity',\n    capabilities: [\n      'Project planning',\n      'Task management',\n      'Timeline creation',\n      'Risk assessment',\n      'Status reporting',\n      'Resource allocation'\n    ],\n    usageCount: 1432,\n    isNew: false,\n    isFeatured: false,\n    createdAt: '2023-08-25',\n    updatedAt: '2024-02-20',\n    version: '2.2.0',\n    creator: 'Productivity Team',\n    avatarUrl: '/agents/meeting-summarizer.svg',\n    tags: ['project management', 'tasks', 'planning', 'coordination'],\n    relatedAgentIds: ['4', '13']\n  },\n  {\n    id: '13',\n    name: 'Content Creator',\n    description: 'Generate high-quality content for blogs, social media, and marketing materials.',\n    longDescription: 'The Content Creator agent helps you produce engaging content for various platforms. Provide a topic and target audience, and it will generate blog posts, social media updates, marketing copy, and more, tailored to your brand voice and content strategy.',\n    category: 'Marketing',\n    capabilities: [\n      'Blog post generation',\n      'Social media content creation',\n      'Marketing copy writing',\n      'SEO optimization',\n      'Brand voice consistency',\n      'Content strategy alignment'\n    ],\n    usageCount: 2187,\n    isNew: false,\n    isFeatured: false,\n    createdAt: '2023-07-12',\n    updatedAt: '2024-03-08',\n    version: '3.0.1',\n    creator: 'Marketing Team',\n    avatarUrl: '/agents/presentation-creator.svg',\n    tags: ['content', 'writing', 'marketing', 'social media'],\n    relatedAgentIds: ['6', '12']\n  }\n];\n\n// Mock data for sessions\nconst sessions: Session[] = [\n  {\n    id: 'session1',\n    agentId: '2',\n    title: 'JavaScript Debugging Help',\n    messages: [\n      {\n        id: 'msg1',\n        content: 'Hello! How can I help you with coding today?',\n        role: 'assistant',\n        timestamp: Date.now() - 3600000\n      },\n      {\n        id: 'msg2',\n        content: 'I have a bug in my JavaScript code. The event listener is not working.',\n        role: 'user',\n        timestamp: Date.now() - 3500000\n      },\n      {\n        id: 'msg3',\n        content: 'Let\\'s take a look. Can you share the code that\\'s not working?',\n        role: 'assistant',\n        timestamp: Date.now() - 3400000\n      }\n    ],\n    createdAt: Date.now() - 3600000,\n    updatedAt: Date.now() - 3400000,\n    isSaved: true\n  },\n  {\n    id: 'session2',\n    agentId: '1',\n    title: 'Sales Data Analysis',\n    messages: [\n      {\n        id: 'msg1',\n        content: 'Welcome to Data Analyst. What data would you like to analyze today?',\n        role: 'assistant',\n        timestamp: Date.now() - 86400000\n      },\n      {\n        id: 'msg2',\n        content: 'I need to analyze our Q1 sales data to find trends.',\n        role: 'user',\n        timestamp: Date.now() - 86300000\n      },\n      {\n        id: 'msg3',\n        content: 'I can help with that. Do you have the sales data file you can upload?',\n        role: 'assistant',\n        timestamp: Date.now() - 86200000\n      }\n    ],\n    createdAt: Date.now() - 86400000,\n    updatedAt: Date.now() - 86200000,\n    isSaved: true\n  }\n];\n\n// Function to get all agents\nexport function getAllAgents(): Agent[] {\n  return agents;\n}\n\n// Function to get an agent by ID\nexport function getAgentById(id: string): Agent | undefined {\n  return agents.find(agent => agent.id === id);\n}\n\n// Function to get featured agents\nexport function getFeaturedAgents(): Agent[] {\n  return agents.filter(agent => agent.isFeatured);\n}\n\n// Function to get new agents\nexport function getNewAgents(): Agent[] {\n  return agents.filter(agent => agent.isNew);\n}\n\n// Function to get agents by category\nexport function getAgentsByCategory(category: string): Agent[] {\n  return agents.filter(agent => agent.category === category);\n}\n\n// Function to get agents by tag\nexport function getAgentsByTag(tag: string): Agent[] {\n  return agents.filter(agent => agent.tags?.includes(tag));\n}\n\n// Function to get all unique categories\nexport function getAllCategories(): string[] {\n  const categories = new Set<string>();\n  agents.forEach(agent => categories.add(agent.category));\n  return Array.from(categories).sort();\n}\n\n// Function to get all unique tags\nexport function getAllTags(): string[] {\n  const tags = new Set<string>();\n  agents.forEach(agent => {\n    agent.tags?.forEach(tag => tags.add(tag));\n  });\n  return Array.from(tags).sort();\n}\n\n// Function to get related agents\nexport function getRelatedAgents(agentId: string): Agent[] {\n  const agent = getAgentById(agentId);\n  if (!agent || !agent.relatedAgentIds || agent.relatedAgentIds.length === 0) {\n    return [];\n  }\n\n  return agent.relatedAgentIds\n    .map(id => getAgentById(id))\n    .filter((agent): agent is Agent => agent !== undefined);\n}\n\n// Function to get agents by IDs\nexport function getAgentsByIds(ids: string[]): Agent[] {\n  return ids\n    .map(id => getAgentById(id))\n    .filter((agent): agent is Agent => agent !== undefined);\n}\n\n// Function to search agents\nexport function searchAgents(query: string): Agent[] {\n  if (!query) return agents;\n\n  const lowercaseQuery = query.toLowerCase();\n  return agents.filter(agent =>\n    agent.name.toLowerCase().includes(lowercaseQuery) ||\n    agent.description.toLowerCase().includes(lowercaseQuery) ||\n    agent.category.toLowerCase().includes(lowercaseQuery) ||\n    agent.tags?.some(tag => tag.toLowerCase().includes(lowercaseQuery)) ||\n    agent.capabilities.some(capability => capability.toLowerCase().includes(lowercaseQuery))\n  );\n}\n\n// Function to get sessions for an agent\nexport function getSessionsForAgent(agentId: string): Session[] {\n  return sessions.filter(session => session.agentId === agentId);\n}\n\n// Function to get a session by ID\nexport function getSessionById(sessionId: string): Session | undefined {\n  return sessions.find(session => session.id === sessionId);\n}\n\n// Function to create a new message\nexport function createMessage(content: string, role: 'user' | 'assistant' | 'system'): Message {\n  return {\n    id: `msg_${Date.now()}`,\n    content,\n    role,\n    timestamp: Date.now()\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAEA,uBAAuB;AACvB,MAAM,SAAkB;IACtB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAQ;YAAa;YAAiB;SAAa;QAC1D,iBAAiB;YAAC;YAAK;YAAK;SAAI;IAClC;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAU;YAAe;YAAe;SAAY;QAC3D,iBAAiB;YAAC;YAAK;SAAK;IAC9B;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAY;YAAU;YAAY;SAAa;QACtD,iBAAiB;YAAC;YAAM;SAAI;IAC9B;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAY;YAAgB;YAAiB;SAAY;QAChE,iBAAiB;YAAC;YAAK;YAAK;SAAK;IACnC;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAa;YAAY;YAAc;SAAY;QAC1D,iBAAiB;YAAC;YAAK;YAAK;SAAI;IAClC;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAiB;YAAU;YAAU;SAAU;QACtD,iBAAiB;YAAC;YAAK;SAAK;IAC9B;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAiB;YAAU;YAAU;SAAO;QACnD,iBAAiB;YAAC;YAAK;SAAI;IAC7B;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAe;YAAe;YAAa;SAAS;QAC3D,iBAAiB;YAAC;YAAK;SAAI;IAC7B;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAiB;YAAO;YAAe;SAAY;QAC1D,iBAAiB;YAAC;YAAK;SAAK;IAC9B;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAe;YAAW;YAAY;SAAiB;QAC9D,iBAAiB;YAAC;YAAK;SAAI;IAC7B;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAqB;YAAY;YAAY;SAAS;QAC7D,iBAAiB;YAAC;SAAI;IACxB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAsB;YAAS;YAAY;SAAe;QACjE,iBAAiB;YAAC;YAAK;SAAK;IAC9B;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAW;YAAW;YAAa;SAAe;QACzD,iBAAiB;YAAC;YAAK;SAAK;IAC9B;CACD;AAED,yBAAyB;AACzB,MAAM,WAAsB;IAC1B;QACE,IAAI;QACJ,SAAS;QACT,OAAO;QACP,UAAU;YACR;gBACE,IAAI;gBACJ,SAAS;gBACT,MAAM;gBACN,WAAW,KAAK,GAAG,KAAK;YAC1B;YACA;gBACE,IAAI;gBACJ,SAAS;gBACT,MAAM;gBACN,WAAW,KAAK,GAAG,KAAK;YAC1B;YACA;gBACE,IAAI;gBACJ,SAAS;gBACT,MAAM;gBACN,WAAW,KAAK,GAAG,KAAK;YAC1B;SACD;QACD,WAAW,KAAK,GAAG,KAAK;QACxB,WAAW,KAAK,GAAG,KAAK;QACxB,SAAS;IACX;IACA;QACE,IAAI;QACJ,SAAS;QACT,OAAO;QACP,UAAU;YACR;gBACE,IAAI;gBACJ,SAAS;gBACT,MAAM;gBACN,WAAW,KAAK,GAAG,KAAK;YAC1B;YACA;gBACE,IAAI;gBACJ,SAAS;gBACT,MAAM;gBACN,WAAW,KAAK,GAAG,KAAK;YAC1B;YACA;gBACE,IAAI;gBACJ,SAAS;gBACT,MAAM;gBACN,WAAW,KAAK,GAAG,KAAK;YAC1B;SACD;QACD,WAAW,KAAK,GAAG,KAAK;QACxB,WAAW,KAAK,GAAG,KAAK;QACxB,SAAS;IACX;CACD;AAGM,SAAS;IACd,OAAO;AACT;AAGO,SAAS,aAAa,EAAU;IACrC,OAAO,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;AAC3C;AAGO,SAAS;IACd,OAAO,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,UAAU;AAChD;AAGO,SAAS;IACd,OAAO,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,KAAK;AAC3C;AAGO,SAAS,oBAAoB,QAAgB;IAClD,OAAO,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,QAAQ,KAAK;AACnD;AAGO,SAAS,eAAe,GAAW;IACxC,OAAO,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,IAAI,EAAE,SAAS;AACrD;AAGO,SAAS;IACd,MAAM,aAAa,IAAI;IACvB,OAAO,OAAO,CAAC,CAAA,QAAS,WAAW,GAAG,CAAC,MAAM,QAAQ;IACrD,OAAO,MAAM,IAAI,CAAC,YAAY,IAAI;AACpC;AAGO,SAAS;IACd,MAAM,OAAO,IAAI;IACjB,OAAO,OAAO,CAAC,CAAA;QACb,MAAM,IAAI,EAAE,QAAQ,CAAA,MAAO,KAAK,GAAG,CAAC;IACtC;IACA,OAAO,MAAM,IAAI,CAAC,MAAM,IAAI;AAC9B;AAGO,SAAS,iBAAiB,OAAe;IAC9C,MAAM,QAAQ,aAAa;IAC3B,IAAI,CAAC,SAAS,CAAC,MAAM,eAAe,IAAI,MAAM,eAAe,CAAC,MAAM,KAAK,GAAG;QAC1E,OAAO,EAAE;IACX;IAEA,OAAO,MAAM,eAAe,CACzB,GAAG,CAAC,CAAA,KAAM,aAAa,KACvB,MAAM,CAAC,CAAC,QAA0B,UAAU;AACjD;AAGO,SAAS,eAAe,GAAa;IAC1C,OAAO,IACJ,GAAG,CAAC,CAAA,KAAM,aAAa,KACvB,MAAM,CAAC,CAAC,QAA0B,UAAU;AACjD;AAGO,SAAS,aAAa,KAAa;IACxC,IAAI,CAAC,OAAO,OAAO;IAEnB,MAAM,iBAAiB,MAAM,WAAW;IACxC,OAAO,OAAO,MAAM,CAAC,CAAA,QACnB,MAAM,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,mBAClC,MAAM,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,mBACzC,MAAM,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,mBACtC,MAAM,IAAI,EAAE,KAAK,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC,oBACnD,MAAM,YAAY,CAAC,IAAI,CAAC,CAAA,aAAc,WAAW,WAAW,GAAG,QAAQ,CAAC;AAE5E;AAGO,SAAS,oBAAoB,OAAe;IACjD,OAAO,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,OAAO,KAAK;AACxD;AAGO,SAAS,eAAe,SAAiB;IAC9C,OAAO,SAAS,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;AACjD;AAGO,SAAS,cAAc,OAAe,EAAE,IAAqC;IAClF,OAAO;QACL,IAAI,CAAC,IAAI,EAAE,KAAK,GAAG,IAAI;QACvB;QACA;QACA,WAAW,KAAK,GAAG;IACrB;AACF", "debugId": null}}, {"offset": {"line": 598, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/data/learning-resources.ts"], "sourcesContent": ["import { LearningResource } from '@/types/learning';\n\nexport const learningResources: LearningResource[] = [\n  // Videos\n  {\n    id: 'video-1',\n    title: 'Introduction to AI Agents',\n    description: 'Learn the basics of AI agents and how they can help you in your daily tasks.',\n    type: 'video',\n    thumbnail: 'https://placehold.co/640x360.png?text=Introduction+to+AI+Agents',\n    content: 'This video provides a comprehensive introduction to AI agents, covering their capabilities, limitations, and practical applications.',\n    url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',\n    tags: ['beginner', 'introduction', 'ai-basics'],\n    author: 'Dr. <PERSON>',\n    publishedAt: new Date('2023-10-15'),\n  },\n  {\n    id: 'video-2',\n    title: 'Advanced AI Agent Techniques',\n    description: 'Dive deeper into AI agent capabilities with advanced techniques and strategies.',\n    type: 'video',\n    thumbnail: 'https://placehold.co/640x360.png?text=Advanced+AI+Agent+Techniques',\n    content: 'This advanced tutorial explores sophisticated AI agent techniques, including prompt engineering, context management, and multi-agent systems.',\n    url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',\n    tags: ['advanced', 'techniques', 'prompt-engineering'],\n    author: 'Prof. <PERSON>',\n    publishedAt: new Date('2023-11-05'),\n  },\n  {\n    id: 'video-3',\n    title: 'AI Agents for Data Analysis',\n    description: 'Learn how to use AI agents to analyze and visualize complex datasets.',\n    type: 'video',\n    thumbnail: 'https://placehold.co/640x360.png?text=AI+Agents+for+Data+Analysis',\n    content: 'This tutorial demonstrates how to leverage AI agents for data analysis tasks, from data cleaning to visualization and interpretation.',\n    url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',\n    tags: ['data-analysis', 'visualization', 'practical'],\n    author: 'Emma Rodriguez',\n    publishedAt: new Date('2023-12-10'),\n  },\n\n  // Articles\n  {\n    id: 'article-1',\n    title: 'The Future of AI Agents in Enterprise',\n    description: 'An in-depth look at how AI agents are transforming enterprise operations and decision-making.',\n    type: 'article',\n    thumbnail: 'https://placehold.co/800x400.png?text=Future+of+AI+Agents',\n    content: `\n# The Future of AI Agents in Enterprise\n\nArtificial Intelligence (AI) agents are rapidly transforming how enterprises operate, making processes more efficient and enabling new capabilities that were previously impossible. This article explores the current state and future potential of AI agents in enterprise settings.\n\n## Current Applications\n\nToday, enterprises are using AI agents for:\n\n- **Customer Service**: Handling routine inquiries and providing 24/7 support\n- **Data Analysis**: Processing large datasets to extract actionable insights\n- **Process Automation**: Streamlining workflows and reducing manual tasks\n- **Decision Support**: Providing recommendations based on complex data analysis\n\n## Future Directions\n\nLooking ahead, we can expect AI agents to evolve in several key ways:\n\n### 1. Increased Autonomy\n\nFuture AI agents will operate with greater independence, making decisions and taking actions with minimal human oversight. This will free up human workers to focus on more creative and strategic tasks.\n\n### 2. Multi-Agent Systems\n\nRather than single agents working in isolation, we'll see sophisticated networks of specialized agents collaborating to solve complex problems. These multi-agent systems will be able to tackle challenges that would be impossible for a single agent.\n\n### 3. Enhanced Personalization\n\nAI agents will become increasingly adept at understanding individual user preferences and adapting their behavior accordingly. This will enable highly personalized experiences for employees and customers alike.\n\n### 4. Seamless Integration\n\nThe distinction between AI agents and traditional software will blur, with AI capabilities being embedded throughout enterprise systems rather than existing as standalone tools.\n\n## Challenges and Considerations\n\nDespite their promise, enterprises must navigate several challenges when implementing AI agents:\n\n- **Data Privacy**: Ensuring that AI agents handle sensitive information appropriately\n- **Transparency**: Making AI decision-making processes understandable to humans\n- **Skills Gap**: Training employees to work effectively alongside AI agents\n- **Ethical Considerations**: Addressing questions about automation and job displacement\n\n## Conclusion\n\nAI agents represent a transformative technology for enterprises, offering unprecedented opportunities for efficiency, innovation, and growth. Organizations that thoughtfully integrate these tools into their operations will gain significant competitive advantages in the years ahead.\n    `,\n    tags: ['enterprise', 'future-trends', 'business'],\n    author: 'Dr. James Wilson',\n    publishedAt: new Date('2023-09-20'),\n  },\n  {\n    id: 'article-2',\n    title: 'Ethical Considerations for AI Agent Deployment',\n    description: 'Exploring the ethical dimensions of deploying AI agents in various contexts.',\n    type: 'article',\n    thumbnail: 'https://placehold.co/800x400.png?text=AI+Ethics',\n    content: `\n# Ethical Considerations for AI Agent Deployment\n\nAs AI agents become increasingly integrated into our daily lives and business operations, it's crucial to consider the ethical implications of their deployment. This article examines key ethical considerations that should guide the development and implementation of AI agent systems.\n\n## Transparency and Explainability\n\nOne of the fundamental ethical requirements for AI agents is transparency. Users should understand:\n\n- When they are interacting with an AI rather than a human\n- The general capabilities and limitations of the AI system\n- How the AI makes decisions or recommendations\n\nExplainability is equally important—the ability to provide understandable explanations for why an AI agent took a particular action or made a specific recommendation.\n\n## Privacy and Data Protection\n\nAI agents often require access to significant amounts of data to function effectively. Organizations must:\n\n- Be transparent about what data is collected and how it's used\n- Implement robust security measures to protect sensitive information\n- Comply with relevant regulations like GDPR or CCPA\n- Minimize data collection to what's necessary for the agent to function\n\n## Fairness and Bias Mitigation\n\nAI agents can inadvertently perpetuate or amplify existing biases if not carefully designed and monitored:\n\n- Training data should be diverse and representative\n- Systems should be regularly audited for biased outcomes\n- Developers should implement bias detection and mitigation techniques\n- Organizations should establish clear standards for fairness\n\n## Accountability and Oversight\n\nClear lines of accountability are essential when deploying AI agents:\n\n- Organizations should designate specific individuals responsible for AI systems\n- Regular auditing and monitoring should be implemented\n- Feedback mechanisms should allow users to report concerns\n- Processes should exist to address harmful or unintended consequences\n\n## Human Autonomy and Agency\n\nAI agents should enhance human capabilities rather than diminish human agency:\n\n- Users should maintain meaningful control over important decisions\n- Systems should be designed to complement human judgment, not replace it\n- People should be able to override AI recommendations when appropriate\n- The division of labor between humans and AI should be thoughtfully considered\n\n## Conclusion\n\nEthical deployment of AI agents requires ongoing attention and commitment. By prioritizing transparency, privacy, fairness, accountability, and human agency, organizations can harness the benefits of AI while minimizing potential harms. As these technologies continue to evolve, our ethical frameworks must evolve alongside them, ensuring that AI agents serve human values and well-being.\n    `,\n    tags: ['ethics', 'responsible-ai', 'governance'],\n    author: 'Prof. Elena Martinez',\n    publishedAt: new Date('2023-10-05'),\n  },\n\n  // Blog Posts\n  {\n    id: 'blog-1',\n    title: 'How We Built Our First AI Agent',\n    description: 'A behind-the-scenes look at our journey developing our first AI agent.',\n    type: 'blog',\n    thumbnail: 'https://placehold.co/800x400.png?text=Building+AI+Agents',\n    content: `\n# How We Built Our First AI Agent: Lessons Learned\n\nWhen we set out to build our first AI agent six months ago, we had no idea how challenging—and rewarding—the journey would be. In this post, I'll share our experience, including the obstacles we faced and the insights we gained along the way.\n\n## The Initial Vision\n\nOur goal was straightforward: create an AI agent that could help our customer support team handle routine inquiries, freeing them to focus on more complex issues. We envisioned a system that could:\n\n- Answer frequently asked questions\n- Help users troubleshoot common problems\n- Collect necessary information before escalating to a human agent\n- Learn and improve over time\n\nSimple enough, right? Not quite.\n\n## Challenges We Encountered\n\n### 1. Defining the Scope\n\nOur first challenge was scope creep. As we brainstormed capabilities, our simple support agent began transforming into an all-purpose assistant that would do everything from technical support to sales to product recommendations.\n\n**Lesson learned**: Start narrow and expand later. We eventually refocused on technical support for a specific product line, which gave us a manageable scope.\n\n### 2. Data Quality Issues\n\nWe initially trained our agent on our support documentation and past ticket logs. However, we quickly discovered inconsistencies in how our team had resolved similar issues in the past, leading to confused responses from our agent.\n\n**Lesson learned**: Clean and standardize your training data before implementation. We ended up creating a curated dataset of best-practice responses.\n\n### 3. Integration Complexities\n\nConnecting our agent to existing systems—our knowledge base, CRM, and ticketing system—proved more difficult than anticipated.\n\n**Lesson learned**: Plan your integration strategy early and thoroughly. Consider building a middleware layer if your systems don't have robust APIs.\n\n### 4. User Acceptance\n\nSome team members were hesitant to adopt the agent, fearing it might eventually replace them.\n\n**Lesson learned**: Involve end-users from the beginning and emphasize how the agent will enhance their work rather than replace it.\n\n## What Worked Well\n\nDespite the challenges, several approaches proved successful:\n\n### 1. Iterative Development\n\nRather than aiming for perfection from the start, we released early versions internally and gathered feedback. This allowed us to identify and address issues quickly.\n\n### 2. Human-in-the-Loop Design\n\nWe designed our agent to collaborate with human agents rather than operate independently. This improved performance and helped with team acceptance.\n\n### 3. Clear Success Metrics\n\nWe established specific metrics to evaluate our agent's performance, including resolution rate, customer satisfaction, and time savings for human agents.\n\n### 4. Continuous Learning\n\nWe implemented a feedback loop where human agents could flag problematic responses, helping our system improve over time.\n\n## Results and Next Steps\n\nSix months in, our AI agent now successfully handles about 40% of initial customer inquiries, reducing wait times and allowing our support team to focus on more complex issues.\n\nOur next steps include:\n\n- Expanding to additional product lines\n- Implementing more sophisticated natural language understanding\n- Adding proactive support capabilities\n- Exploring voice interface options\n\n## Conclusion\n\nBuilding an effective AI agent is more challenging than it might initially appear, but the benefits can be substantial. By starting with a focused scope, prioritizing data quality, planning integrations carefully, and involving end-users throughout the process, you can create an agent that genuinely enhances your team's capabilities.\n\nHave you built an AI agent for your organization? I'd love to hear about your experience in the comments!\n    `,\n    tags: ['case-study', 'development', 'lessons-learned'],\n    author: 'Alex Thompson',\n    publishedAt: new Date('2023-11-15'),\n  },\n  {\n    id: 'blog-2',\n    title: 'Monthly AI Agent Updates - January 2024',\n    description: 'The latest updates and improvements to our AI agent platform.',\n    type: 'blog',\n    thumbnail: 'https://placehold.co/800x400.png?text=January+Updates',\n    content: `\n# Monthly AI Agent Updates - January 2024\n\nWelcome to our first monthly update of 2024! We've been hard at work improving our AI agent platform, and we're excited to share the latest enhancements, bug fixes, and upcoming features.\n\n## New Features\n\n### 1. Enhanced Context Management\n\nOur agents can now maintain context more effectively across longer conversations. This means they can refer back to information mentioned earlier in the discussion without requiring users to repeat themselves.\n\n### 2. Multi-Modal Capabilities\n\nAgents can now process and respond to both text and images. This is particularly useful for troubleshooting scenarios where users can share screenshots of errors or problems they're experiencing.\n\n### 3. Custom Agent Creation UI\n\nWe've launched a new user interface for creating and customizing agents without coding. This drag-and-drop interface allows you to:\n\n- Define conversation flows\n- Create custom knowledge bases\n- Set up integration with your existing tools\n- Customize the agent's tone and personality\n\n## Improvements\n\n### 1. Performance Optimization\n\nWe've significantly improved response times across the platform:\n\n- 40% faster initial response time\n- 25% reduction in token usage\n- Improved handling of concurrent requests\n\n### 2. Better Error Handling\n\nAgents now recover more gracefully from misunderstandings and provide clearer guidance when they need additional information.\n\n### 3. Enhanced Analytics\n\nThe analytics dashboard now provides deeper insights into:\n\n- Common user queries and pain points\n- Success rates for different types of requests\n- Patterns in escalations to human agents\n- User satisfaction metrics\n\n## Bug Fixes\n\n- Fixed an issue where agents occasionally lost context after API errors\n- Resolved a problem with webhook integrations timing out\n- Fixed formatting issues in exported conversation logs\n- Addressed authentication issues with certain SSO providers\n\n## Coming Soon\n\nWe're excited about several features currently in development:\n\n### 1. Agent Collaboration\n\nSoon, multiple specialized agents will be able to collaborate on complex requests, each handling the aspects they're best suited for.\n\n### 2. Scheduled Actions\n\nAgents will be able to perform actions at scheduled times, such as sending follow-ups or checking on the status of issues.\n\n### 3. Voice Interface\n\nWe're working on a voice interface that will allow users to interact with agents through spoken conversation.\n\n## Community Spotlight\n\nWe want to highlight some impressive implementations from our community:\n\n- **HealthTech Solutions** created an agent that helps patients understand their medication schedules and potential side effects\n- **GlobalLogistics** built an agent that provides real-time shipping updates and resolves delivery issues\n- **EduLearn** developed a tutoring agent that adapts to different learning styles\n\n## Feedback Request\n\nWe're considering several directions for our Q2 roadmap and would love your input. Please take our quick survey to help prioritize upcoming features: [Survey Link]\n\n## Conclusion\n\nThank you for being part of our community! As always, we welcome your feedback and suggestions. You can reach our <NAME_EMAIL> or through the feedback form in your dashboard.\n\nHappy agent building!\n\nThe AI Agent Platform Team\n    `,\n    tags: ['updates', 'new-features', 'roadmap'],\n    author: 'The AI Hub Team',\n    publishedAt: new Date('2024-01-10'),\n  },\n];\n\n// Helper functions to filter resources by type\nexport const getVideos = () => learningResources.filter(resource => resource.type === 'video');\nexport const getArticles = () => learningResources.filter(resource => resource.type === 'article');\nexport const getBlogPosts = () => learningResources.filter(resource => resource.type === 'blog');\n\n// Helper function to get a resource by ID\nexport const getResourceById = (id: string) => learningResources.find(resource => resource.id === id);\n\n// Helper function to get featured or recent resources\nexport const getFeaturedResources = (count: number = 3) => {\n  // In a real app, you might have a 'featured' flag or use other criteria\n  // Here we'll just return the most recent resources\n  return [...learningResources].sort((a, b) =>\n    b.publishedAt.getTime() - a.publishedAt.getTime()\n  ).slice(0, count);\n};\n\n// Helper function to get the most recent resources across all types\nexport const getRecentResources = (count: number = 4) => {\n  return [...learningResources].sort((a, b) =>\n    b.publishedAt.getTime() - a.publishedAt.getTime()\n  ).slice(0, count);\n};\n\n// Helper function to get popular resources (simulated with a random selection)\nexport const getPopularResources = (count: number = 4) => {\n  // In a real app, this would be based on view counts or user engagement\n  // For now, we'll shuffle the array and take the first few items\n  const shuffled = [...learningResources].sort(() => 0.5 - Math.random());\n  return shuffled.slice(0, count);\n};\n\n// Helper function to combine resources with their type for display\nexport const getCombinedLearningResources = (resources: LearningResource[]) => {\n  return resources.map(resource => ({\n    ...resource,\n    // Add a URL based on the resource type\n    viewUrl: resource.type === 'video'\n      ? `/learning/videos/${resource.id}`\n      : resource.type === 'article'\n        ? `/learning/articles/${resource.id}`\n        : `/learning/blog/${resource.id}`\n  }));\n};\n"], "names": [], "mappings": ";;;;;;;;;;;AAEO,MAAM,oBAAwC;IACnD,SAAS;IACT;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,WAAW;QACX,SAAS;QACT,KAAK;QACL,MAAM;YAAC;YAAY;YAAgB;SAAY;QAC/C,QAAQ;QACR,aAAa,IAAI,KAAK;IACxB;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,WAAW;QACX,SAAS;QACT,KAAK;QACL,MAAM;YAAC;YAAY;YAAc;SAAqB;QACtD,QAAQ;QACR,aAAa,IAAI,KAAK;IACxB;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,WAAW;QACX,SAAS;QACT,KAAK;QACL,MAAM;YAAC;YAAiB;YAAiB;SAAY;QACrD,QAAQ;QACR,aAAa,IAAI,KAAK;IACxB;IAEA,WAAW;IACX;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,WAAW;QACX,SAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA8CV,CAAC;QACD,MAAM;YAAC;YAAc;YAAiB;SAAW;QACjD,QAAQ;QACR,aAAa,IAAI,KAAK;IACxB;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,WAAW;QACX,SAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAsDV,CAAC;QACD,MAAM;YAAC;YAAU;YAAkB;SAAa;QAChD,QAAQ;QACR,aAAa,IAAI,KAAK;IACxB;IAEA,aAAa;IACb;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,WAAW;QACX,SAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA8EV,CAAC;QACD,MAAM;YAAC;YAAc;YAAe;SAAkB;QACtD,QAAQ;QACR,aAAa,IAAI,KAAK;IACxB;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,WAAW;QACX,SAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAyFV,CAAC;QACD,MAAM;YAAC;YAAW;YAAgB;SAAU;QAC5C,QAAQ;QACR,aAAa,IAAI,KAAK;IACxB;CACD;AAGM,MAAM,YAAY,IAAM,kBAAkB,MAAM,CAAC,CAAA,WAAY,SAAS,IAAI,KAAK;AAC/E,MAAM,cAAc,IAAM,kBAAkB,MAAM,CAAC,CAAA,WAAY,SAAS,IAAI,KAAK;AACjF,MAAM,eAAe,IAAM,kBAAkB,MAAM,CAAC,CAAA,WAAY,SAAS,IAAI,KAAK;AAGlF,MAAM,kBAAkB,CAAC,KAAe,kBAAkB,IAAI,CAAC,CAAA,WAAY,SAAS,EAAE,KAAK;AAG3F,MAAM,uBAAuB,CAAC,QAAgB,CAAC;IACpD,wEAAwE;IACxE,mDAAmD;IACnD,OAAO;WAAI;KAAkB,CAAC,IAAI,CAAC,CAAC,GAAG,IACrC,EAAE,WAAW,CAAC,OAAO,KAAK,EAAE,WAAW,CAAC,OAAO,IAC/C,KAAK,CAAC,GAAG;AACb;AAGO,MAAM,qBAAqB,CAAC,QAAgB,CAAC;IAClD,OAAO;WAAI;KAAkB,CAAC,IAAI,CAAC,CAAC,GAAG,IACrC,EAAE,WAAW,CAAC,OAAO,KAAK,EAAE,WAAW,CAAC,OAAO,IAC/C,KAAK,CAAC,GAAG;AACb;AAGO,MAAM,sBAAsB,CAAC,QAAgB,CAAC;IACnD,uEAAuE;IACvE,gEAAgE;IAChE,MAAM,WAAW;WAAI;KAAkB,CAAC,IAAI,CAAC,IAAM,MAAM,KAAK,MAAM;IACpE,OAAO,SAAS,KAAK,CAAC,GAAG;AAC3B;AAGO,MAAM,+BAA+B,CAAC;IAC3C,OAAO,UAAU,GAAG,CAAC,CAAA,WAAY,CAAC;YAChC,GAAG,QAAQ;YACX,uCAAuC;YACvC,SAAS,SAAS,IAAI,KAAK,UACvB,CAAC,iBAAiB,EAAE,SAAS,EAAE,EAAE,GACjC,SAAS,IAAI,KAAK,YAChB,CAAC,mBAAmB,EAAE,SAAS,EAAE,EAAE,GACnC,CAAC,eAAe,EAAE,SAAS,EAAE,EAAE;QACvC,CAAC;AACH", "debugId": null}}, {"offset": {"line": 1026, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/data/resources-original.ts"], "sourcesContent": ["import { Resource, ResourceCategoryInfo } from '@/types/resources';\n\n// Resource categories information\nexport const resourceCategories: ResourceCategoryInfo[] = [\n  {\n    id: 'productivity',\n    name: 'Productivity Tools',\n    description: 'Tools to enhance personal and team productivity, including task management, note-taking, and time tracking.',\n    icon: 'bolt'\n  },\n  {\n    id: 'project-management',\n    name: 'Project Management',\n    description: 'Software and platforms for planning, executing, and monitoring projects of all sizes.',\n    icon: 'tasks'\n  },\n  {\n    id: 'design',\n    name: 'Design Tools',\n    description: 'Resources for UI/UX design, graphic design, prototyping, and visual collaboration.',\n    icon: 'palette'\n  },\n  {\n    id: 'development',\n    name: 'Development Tools',\n    description: 'Tools and services for software development, coding, testing, and deployment.',\n    icon: 'code'\n  },\n  {\n    id: 'research',\n    name: 'Research Tools',\n    description: 'Resources for market research, user research, academic research, and data collection.',\n    icon: 'magnifying-glass-chart'\n  },\n  {\n    id: 'analytics',\n    name: 'Analytics & Data',\n    description: 'Tools for data analysis, visualization, business intelligence, and reporting.',\n    icon: 'chart-line'\n  },\n  {\n    id: 'communication',\n    name: 'Communication',\n    description: 'Platforms for team communication, client meetings, presentations, and email management.',\n    icon: 'comments'\n  },\n  {\n    id: 'collaboration',\n    name: 'Collaboration',\n    description: 'Tools that facilitate teamwork, knowledge sharing, and cross-functional collaboration.',\n    icon: 'users-gear'\n  }\n];\n\n// Sample resources data\nexport const resources: Resource[] = [\n  {\n    id: 'notion',\n    name: 'Notion',\n    description: 'All-in-one workspace for notes, tasks, wikis, and databases.',\n    url: 'https://www.notion.so',\n    category: 'productivity',\n    tags: ['note-taking', 'project-management', 'wiki'],\n    pricing: 'freemium',\n    logoUrl: 'https://www.notion.so/images/favicon.ico'\n  },\n  {\n    id: 'figma',\n    name: 'Figma',\n    description: 'Collaborative interface design tool for teams.',\n    url: 'https://www.figma.com',\n    category: 'design',\n    tags: ['ui-design', 'prototyping', 'collaboration'],\n    pricing: 'freemium',\n    logoUrl: 'https://static.figma.com/app/icon/1/favicon.png'\n  },\n  {\n    id: 'vscode',\n    name: 'Visual Studio Code',\n    description: 'Free, open-source code editor with powerful development features.',\n    url: 'https://code.visualstudio.com',\n    category: 'development',\n    tags: ['code-editor', 'debugging', 'extensions'],\n    pricing: 'free',\n    logoUrl: 'https://code.visualstudio.com/favicon.ico'\n  },\n  {\n    id: 'slack',\n    name: 'Slack',\n    description: 'Channel-based messaging platform for teams and workplaces.',\n    url: 'https://slack.com',\n    category: 'communication',\n    tags: ['messaging', 'team-communication', 'integrations'],\n    pricing: 'freemium',\n    logoUrl: 'https://a.slack-edge.com/80588/marketing/img/meta/favicon-32.png'\n  },\n  {\n    id: 'trello',\n    name: 'Trello',\n    description: 'Visual tool for organizing work with boards, lists, and cards.',\n    url: 'https://trello.com',\n    category: 'project-management',\n    tags: ['kanban', 'task-management', 'collaboration'],\n    pricing: 'freemium',\n    logoUrl: 'https://a.trellocdn.com/prgb/dist/images/ios/apple-touch-icon-152x152-precomposed.0307bc39ec6c9ff499c8.png'\n  },\n  {\n    id: 'google-analytics',\n    name: 'Google Analytics',\n    description: 'Web analytics service that tracks and reports website traffic.',\n    url: 'https://analytics.google.com',\n    category: 'analytics',\n    tags: ['web-analytics', 'reporting', 'user-behavior'],\n    pricing: 'freemium',\n    logoUrl: 'https://www.google.com/analytics/images/ga_icon_256.png'\n  },\n  {\n    id: 'miro',\n    name: 'Miro',\n    description: 'Online collaborative whiteboard platform for teams.',\n    url: 'https://miro.com',\n    category: 'collaboration',\n    tags: ['whiteboard', 'brainstorming', 'visual-collaboration'],\n    pricing: 'freemium',\n    logoUrl: 'https://miro.com/static/images/favicon/apple-touch-icon.png'\n  },\n  {\n    id: 'google-scholar',\n    name: 'Google Scholar',\n    description: 'Search engine for academic literature and research papers.',\n    url: 'https://scholar.google.com',\n    category: 'research',\n    tags: ['academic-research', 'citations', 'literature-search'],\n    pricing: 'free',\n    logoUrl: 'https://scholar.google.com/favicon.ico'\n  }\n];\n"], "names": [], "mappings": ";;;;AAGO,MAAM,qBAA6C;IACxD;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;IACR;CACD;AAGM,MAAM,YAAwB;IACnC;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAe;YAAsB;SAAO;QACnD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAa;YAAe;SAAgB;QACnD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAe;YAAa;SAAa;QAChD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAa;YAAsB;SAAe;QACzD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAU;YAAmB;SAAgB;QACpD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAiB;YAAa;SAAgB;QACrD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAc;YAAiB;SAAuB;QAC7D,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAqB;YAAa;SAAoB;QAC7D,SAAS;QACT,SAAS;IACX;CACD", "debugId": null}}, {"offset": {"line": 1200, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/data/resources.ts"], "sourcesContent": ["import { Resource, ResourceCategoryInfo } from '@/types/resources';\nimport { resources as existingResources, resourceCategories } from './resources-original';\n\n// New resources from the AI Presentation Series Summary PDF\nconst newResources: Resource[] = [\n  // Productivity Tools\n  {\n    id: 'perplexity-ai',\n    name: 'Perplexity AI',\n    description: 'An AI-powered search engine with a chatbot interface that understands and responds to user queries using GPT-3.5.',\n    url: 'https://www.perplexity.ai/',\n    category: 'productivity',\n    tags: ['search-engine', 'ai-assistant', 'research'],\n    pricing: 'freemium',\n    logoUrl: 'https://www.perplexity.ai/favicon.ico'\n  },\n  {\n    id: 'claude-ai',\n    name: 'Claude <PERSON>',\n    description: 'Analyzes and suggests improvements for very long content, similar to ChatGPT but with enhanced capabilities for handling lengthy documents.',\n    url: 'https://claude.ai/',\n    category: 'productivity',\n    tags: ['ai-assistant', 'content-generation', 'document-analysis'],\n    pricing: 'freemium',\n    logoUrl: 'https://claude.ai/favicon.ico'\n  },\n  {\n    id: 'fathom-ai',\n    name: 'Fathom AI',\n    description: 'Zoom app that records, transcribes, and highlights key moments from calls, making meeting follow-up more efficient.',\n    url: 'https://fathom.video/',\n    category: 'productivity',\n    tags: ['meeting-assistant', 'transcription', 'video-conferencing'],\n    pricing: 'freemium',\n    logoUrl: 'https://fathom.video/favicon.ico'\n  },\n  {\n    id: 'plaud-ai',\n    name: 'Plaud.ai',\n    description: 'AI tool for note-taking and transcription that helps capture and organize information from meetings and conversations.',\n    url: 'https://www.plaud.ai/',\n    category: 'productivity',\n    tags: ['note-taking', 'transcription', 'meeting-assistant'],\n    pricing: 'freemium',\n    logoUrl: 'https://www.plaud.ai/favicon.ico'\n  },\n  {\n    id: 'whisper',\n    name: 'Whisper',\n    description: 'Converts audio to text and vice versa using AI, providing accurate transcription for various languages and accents.',\n    url: 'https://openai.com/index/whisper/',\n    category: 'productivity',\n    tags: ['transcription', 'audio-processing', 'speech-to-text'],\n    pricing: 'freemium',\n    logoUrl: 'https://openai.com/favicon.ico'\n  },\n  {\n    id: 'notebooklm',\n    name: 'NotebookLM',\n    description: 'Tool for data management and note-taking that uses AI to help organize and retrieve information efficiently.',\n    url: 'https://notebooklm.google.com',\n    category: 'productivity',\n    tags: ['note-taking', 'knowledge-management', 'ai-organization'],\n    pricing: 'free',\n    logoUrl: 'https://notebooklm.google.com/favicon.ico'\n  },\n  {\n    id: 'deepl',\n    name: 'DeepL',\n    description: 'Translation tool for efficient and accurate translations between multiple languages, powered by advanced AI.',\n    url: 'https://www.deepl.com',\n    category: 'productivity',\n    tags: ['translation', 'language-processing', 'communication'],\n    pricing: 'freemium',\n    logoUrl: 'https://www.deepl.com/favicon.ico'\n  },\n\n  // Design Tools\n  {\n    id: 'midjourney',\n    name: 'Midjourney',\n    description: 'Generates images from descriptive language, similar to DALL-E and Stable Diffusion, with a focus on artistic quality.',\n    url: 'https://www.midjourney.com/',\n    category: 'design',\n    tags: ['image-generation', 'ai-art', 'creative-tools'],\n    pricing: 'paid',\n    logoUrl: 'https://www.midjourney.com/favicon.ico'\n  },\n  {\n    id: 'bing-images',\n    name: 'Bing Image Creator',\n    description: 'Provides tools for creating images from text descriptions, potentially offering more features than Midjourney.',\n    url: 'https://www.bing.com/images/create',\n    category: 'design',\n    tags: ['image-generation', 'ai-art', 'creative-tools'],\n    pricing: 'free',\n    logoUrl: 'https://www.bing.com/favicon.ico'\n  },\n  {\n    id: 'meta-imagine',\n    name: 'Meta Imagine',\n    description: 'Uses models to generate images from textual prompts provided by the user, created by Meta (Facebook).',\n    url: 'https://imagine.meta.com',\n    category: 'design',\n    tags: ['image-generation', 'ai-art', 'creative-tools'],\n    pricing: 'free',\n    logoUrl: 'https://imagine.meta.com/favicon.ico'\n  },\n  {\n    id: 'designer-microsoft',\n    name: 'Microsoft Designer',\n    description: 'Microsoft\\'s answer to Canva, focusing on design solutions with AI-powered features for creating professional graphics.',\n    url: 'https://designer.microsoft.com/',\n    category: 'design',\n    tags: ['graphic-design', 'presentation', 'marketing-materials'],\n    pricing: 'freemium',\n    logoUrl: 'https://designer.microsoft.com/favicon.ico'\n  },\n  {\n    id: 'runway',\n    name: 'Runway',\n    description: 'A creative platform for video production and editing with AI-powered tools for visual effects and content creation.',\n    url: 'https://app.runwayml.com/',\n    category: 'design',\n    tags: ['video-editing', 'visual-effects', 'content-creation'],\n    pricing: 'freemium',\n    logoUrl: 'https://app.runwayml.com/favicon.ico'\n  },\n  {\n    id: 'clipdrop',\n    name: 'Clipdrop',\n    description: 'Developed by Stability AI, for various image and video editing tasks with AI-powered features.',\n    url: 'https://clipdrop.co/',\n    category: 'design',\n    tags: ['image-editing', 'video-editing', 'content-creation'],\n    pricing: 'freemium',\n    logoUrl: 'https://clipdrop.co/favicon.ico'\n  },\n  {\n    id: 'interior-ai',\n    name: 'Interior AI',\n    description: 'Interior design tool for generating and visualizing room layouts using AI to create realistic interior designs.',\n    url: 'https://interiorai.com/',\n    category: 'design',\n    tags: ['interior-design', 'visualization', 'architecture'],\n    pricing: 'freemium',\n    logoUrl: 'https://interiorai.com/favicon.ico'\n  },\n  {\n    id: 'meshy-ai',\n    name: 'Meshy.ai',\n    description: 'For 3D modeling, used in architecture and design to create and manipulate 3D objects with AI assistance.',\n    url: 'https://www.meshy.ai',\n    category: 'design',\n    tags: ['3d-modeling', 'architecture', 'design'],\n    pricing: 'freemium',\n    logoUrl: 'https://www.meshy.ai/favicon.ico'\n  },\n  {\n    id: 'mnml-ai',\n    name: 'MNML AI',\n    description: 'Architecture design assistant that helps create minimalist architectural designs with AI guidance.',\n    url: 'https://mnml.ai',\n    category: 'design',\n    tags: ['architecture', 'design', 'minimalism'],\n    pricing: 'freemium',\n    logoUrl: 'https://mnml.ai/favicon.ico'\n  },\n  {\n    id: 'ulama-tech',\n    name: 'Ulama.tech',\n    description: 'For architectural design, specifically for structure and planning with AI-powered tools.',\n    url: 'http://ulama.tech',\n    category: 'design',\n    tags: ['architecture', 'structural-design', 'planning'],\n    pricing: 'freemium',\n    logoUrl: 'http://ulama.tech/favicon.ico'\n  },\n  {\n    id: 'weshop-ai',\n    name: 'WeShop',\n    description: 'Produce high-quality product images inexpensively and quickly using AI-generated visuals.',\n    url: 'https://www.weshop.ai/',\n    category: 'design',\n    tags: ['product-photography', 'e-commerce', 'marketing'],\n    pricing: 'paid',\n    logoUrl: 'https://www.weshop.ai/favicon.ico'\n  },\n  {\n    id: 'botika',\n    name: 'Botika',\n    description: 'Helps fashion retailers save on photo costs and boost sales using AI-generated models for product visualization.',\n    url: 'https://botika.io/',\n    category: 'design',\n    tags: ['fashion', 'e-commerce', 'product-visualization'],\n    pricing: 'paid',\n    logoUrl: 'https://botika.io/favicon.ico'\n  },\n  {\n    id: 'flux-ai',\n    name: 'Flux AI',\n    description: 'Enables creative image generation and animation with AI-powered tools for designers and artists.',\n    url: 'https://flux-ai.io/',\n    category: 'design',\n    tags: ['image-generation', 'animation', 'creative-tools'],\n    pricing: 'freemium',\n    logoUrl: 'https://flux-ai.io/favicon.ico'\n  },\n\n  // Development Tools\n  {\n    id: '10web',\n    name: '10Web',\n    description: 'An AI-Powered WordPress Platform for website development and management with automated features.',\n    url: 'https://10web.io/',\n    category: 'development',\n    tags: ['wordpress', 'website-builder', 'automation'],\n    pricing: 'paid',\n    logoUrl: 'https://10web.io/favicon.ico'\n  },\n  {\n    id: 'framer',\n    name: 'Framer',\n    description: 'A tool for building interactive websites and web applications with a focus on design and user experience.',\n    url: 'https://framer.com/',\n    category: 'development',\n    tags: ['website-builder', 'prototyping', 'design'],\n    pricing: 'freemium',\n    logoUrl: 'https://framer.com/favicon.ico'\n  },\n  {\n    id: 'github-copilot',\n    name: 'GitHub Copilot',\n    description: 'AI pair programmer that assists in code completion and suggestions within code editors, powered by OpenAI Codex.',\n    url: 'https://github.com/features/copilot',\n    category: 'development',\n    tags: ['coding-assistant', 'pair-programming', 'code-completion'],\n    pricing: 'paid',\n    logoUrl: 'https://github.com/favicon.ico'\n  },\n  {\n    id: 'github-spark',\n    name: 'GitHub Spark',\n    description: 'AI tool for building web applications using natural language, aiming to lower the barrier to software development.',\n    url: 'https://github.com/features',\n    category: 'development',\n    tags: ['web-development', 'no-code', 'ai-coding'],\n    pricing: 'paid',\n    logoUrl: 'https://github.com/favicon.ico'\n  },\n  {\n    id: 'langchain',\n    name: 'LangChain',\n    description: 'Builds AI-powered applications by connecting large language models with external data sources and tools.',\n    url: 'https://www.langchain.com/',\n    category: 'development',\n    tags: ['llm-framework', 'ai-development', 'integration'],\n    pricing: 'free',\n    logoUrl: 'https://www.langchain.com/favicon.ico'\n  },\n\n  // Communication\n  {\n    id: 'heygen',\n    name: 'HeyGen',\n    description: 'Produces studio quality videos with AI-generated avatars and voices for professional communication.',\n    url: 'https://www.heygen.com',\n    category: 'communication',\n    tags: ['video-creation', 'avatars', 'presentation'],\n    pricing: 'paid',\n    logoUrl: 'https://www.heygen.com/favicon.ico'\n  },\n  {\n    id: 'beautiful-ai',\n    name: 'Beautiful.ai',\n    description: 'Presentation tool that simplifies the creation of professional presentations using AI to handle design elements.',\n    url: 'http://beautiful.ai',\n    category: 'communication',\n    tags: ['presentation', 'slides', 'design'],\n    pricing: 'freemium',\n    logoUrl: 'http://beautiful.ai/favicon.ico'\n  },\n  {\n    id: 'gamma-app',\n    name: 'Gamma.app',\n    description: 'Creates engaging presentations by transforming ideas into visually appealing slides with AI assistance.',\n    url: 'https://gamma.app/',\n    category: 'communication',\n    tags: ['presentation', 'slides', 'design'],\n    pricing: 'freemium',\n    logoUrl: 'https://gamma.app/favicon.ico'\n  },\n  {\n    id: 'decktopus',\n    name: 'Decktopus',\n    description: 'An AI-powered tool that assists in creating presentation starting points with professional templates and designs.',\n    url: 'https://decktopus.com',\n    category: 'communication',\n    tags: ['presentation', 'slides', 'design'],\n    pricing: 'freemium',\n    logoUrl: 'https://decktopus.com/favicon.ico'\n  },\n  {\n    id: 'opus',\n    name: 'Opus',\n    description: 'Transforms long videos into short clips with a single click using generative AI for more effective communication.',\n    url: 'https://www.opus.pro/',\n    category: 'communication',\n    tags: ['video-editing', 'content-creation', 'summarization'],\n    pricing: 'paid',\n    logoUrl: 'https://www.opus.pro/favicon.ico'\n  },\n  {\n    id: 'vapi',\n    name: 'VAPI',\n    description: 'Builds and optimizes voice agents for customer service and communication applications.',\n    url: 'https://vapi.ai/',\n    category: 'communication',\n    tags: ['voice-agents', 'customer-service', 'automation'],\n    pricing: 'paid',\n    logoUrl: 'https://vapi.ai/favicon.ico'\n  },\n  {\n    id: 'sora',\n    name: 'Sora',\n    description: 'Turn text instructions into detailed video scenes for communication and presentation purposes.',\n    url: 'https://openai.com/sora',\n    category: 'communication',\n    tags: ['video-generation', 'content-creation', 'presentation'],\n    pricing: 'paid',\n    logoUrl: 'https://openai.com/favicon.ico'\n  },\n\n  // Collaboration\n  {\n    id: 'rancelab',\n    name: 'RanceLab',\n    description: 'Integrates WhatsApp with other platforms for better team collaboration and customer communication.',\n    url: 'https://www.rancelab.com/',\n    category: 'collaboration',\n    tags: ['whatsapp-integration', 'communication', 'customer-service'],\n    pricing: 'paid',\n    logoUrl: 'https://www.rancelab.com/favicon.ico'\n  },\n  {\n    id: 'lawgeex',\n    name: 'LawGeex',\n    description: 'Legal automation platform that uses AI to review contracts and facilitate legal collaboration.',\n    url: 'https://www.lawgeex.com/',\n    category: 'collaboration',\n    tags: ['legal', 'contract-review', 'automation'],\n    pricing: 'paid',\n    logoUrl: 'https://www.lawgeex.com/favicon.ico'\n  },\n\n  // Analytics & Data\n  {\n    id: 'browse-ai',\n    name: 'BrowseAI',\n    description: 'Facilitates data extraction and monitoring from websites for easy data acquisition and analysis.',\n    url: 'https://www.browse.ai/',\n    category: 'analytics',\n    tags: ['data-extraction', 'web-scraping', 'monitoring'],\n    pricing: 'freemium',\n    logoUrl: 'https://www.browse.ai/favicon.ico'\n  },\n  {\n    id: 'relevance-ai',\n    name: 'Relevance AI',\n    description: 'Platform providing AI-driven insights and analytics to enhance business decision-making.',\n    url: 'https://relevanceai.com/',\n    category: 'analytics',\n    tags: ['data-analysis', 'insights', 'business-intelligence'],\n    pricing: 'freemium',\n    logoUrl: 'https://relevanceai.com/favicon.ico'\n  },\n\n  // Project Management\n  {\n    id: 'make-com',\n    name: 'Make.com',\n    description: 'Automation platform for streamlining workflows and processes using AI to connect apps and automate tasks.',\n    url: 'https://make.com',\n    category: 'project-management',\n    tags: ['automation', 'workflow', 'integration'],\n    pricing: 'freemium',\n    logoUrl: 'https://make.com/favicon.ico'\n  },\n  {\n    id: 'zapier-central',\n    name: 'Zapier Central',\n    description: 'Automating tasks and workflows using AI-powered integrations between different applications and services.',\n    url: 'https://zapier.com/central',\n    category: 'project-management',\n    tags: ['automation', 'workflow', 'integration'],\n    pricing: 'freemium',\n    logoUrl: 'https://zapier.com/favicon.ico'\n  },\n  {\n    id: 'agents-ai',\n    name: 'Agents.ai',\n    description: 'Professional network of AI agents for business automation and task management.',\n    url: 'https://agents.ai/',\n    category: 'project-management',\n    tags: ['automation', 'ai-agents', 'task-management'],\n    pricing: 'paid',\n    logoUrl: 'https://agents.ai/favicon.ico'\n  },\n  {\n    id: 'napkin-ai',\n    name: 'Napkin.ai',\n    description: 'Useful for generating content, proofreading, and ideation feedback for project planning and documentation.',\n    url: 'http://napkin.ai',\n    category: 'project-management',\n    tags: ['content-generation', 'ideation', 'documentation'],\n    pricing: 'freemium',\n    logoUrl: 'http://napkin.ai/favicon.ico'\n  }\n];\n\n// Combine existing resources with new resources, avoiding duplicates\nconst combinedResources: Resource[] = [\n  ...existingResources,\n  ...newResources.filter(newResource =>\n    !existingResources.some(existingResource =>\n      existingResource.id === newResource.id ||\n      existingResource.name.toLowerCase() === newResource.name.toLowerCase()\n    )\n  )\n];\n\nexport { resourceCategories, combinedResources as resources };\n\n// Helper functions\nexport function getResourcesByCategory(categoryId: string): Resource[] {\n  return combinedResources.filter(resource => resource.category === categoryId);\n}\n\nexport function getCategoryById(categoryId: string): ResourceCategoryInfo | undefined {\n  return resourceCategories.find(category => category.id === categoryId);\n}\n"], "names": [], "mappings": ";;;;;AACA;;AAEA,4DAA4D;AAC5D,MAAM,eAA2B;IAC/B,qBAAqB;IACrB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAiB;YAAgB;SAAW;QACnD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAgB;YAAsB;SAAoB;QACjE,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAqB;YAAiB;SAAqB;QAClE,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAe;YAAiB;SAAoB;QAC3D,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAiB;YAAoB;SAAiB;QAC7D,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAe;YAAwB;SAAkB;QAChE,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAe;YAAuB;SAAgB;QAC7D,SAAS;QACT,SAAS;IACX;IAEA,eAAe;IACf;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAoB;YAAU;SAAiB;QACtD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAoB;YAAU;SAAiB;QACtD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAoB;YAAU;SAAiB;QACtD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAkB;YAAgB;SAAsB;QAC/D,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAiB;YAAkB;SAAmB;QAC7D,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAiB;YAAiB;SAAmB;QAC5D,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAmB;YAAiB;SAAe;QAC1D,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAe;YAAgB;SAAS;QAC/C,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAgB;YAAU;SAAa;QAC9C,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAgB;YAAqB;SAAW;QACvD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAuB;YAAc;SAAY;QACxD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAW;YAAc;SAAwB;QACxD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAoB;YAAa;SAAiB;QACzD,SAAS;QACT,SAAS;IACX;IAEA,oBAAoB;IACpB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAa;YAAmB;SAAa;QACpD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAmB;YAAe;SAAS;QAClD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAoB;YAAoB;SAAkB;QACjE,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAmB;YAAW;SAAY;QACjD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAiB;YAAkB;SAAc;QACxD,SAAS;QACT,SAAS;IACX;IAEA,gBAAgB;IAChB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAkB;YAAW;SAAe;QACnD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAgB;YAAU;SAAS;QAC1C,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAgB;YAAU;SAAS;QAC1C,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAgB;YAAU;SAAS;QAC1C,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAiB;YAAoB;SAAgB;QAC5D,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAgB;YAAoB;SAAa;QACxD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAoB;YAAoB;SAAe;QAC9D,SAAS;QACT,SAAS;IACX;IAEA,gBAAgB;IAChB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAwB;YAAiB;SAAmB;QACnE,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAS;YAAmB;SAAa;QAChD,SAAS;QACT,SAAS;IACX;IAEA,mBAAmB;IACnB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAmB;YAAgB;SAAa;QACvD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAiB;YAAY;SAAwB;QAC5D,SAAS;QACT,SAAS;IACX;IAEA,qBAAqB;IACrB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAc;YAAY;SAAc;QAC/C,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAc;YAAY;SAAc;QAC/C,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAc;YAAa;SAAkB;QACpD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAsB;YAAY;SAAgB;QACzD,SAAS;QACT,SAAS;IACX;CACD;AAED,qEAAqE;AACrE,MAAM,oBAAgC;OACjC,oIAAA,CAAA,YAAiB;OACjB,aAAa,MAAM,CAAC,CAAA,cACrB,CAAC,oIAAA,CAAA,YAAiB,CAAC,IAAI,CAAC,CAAA,mBACtB,iBAAiB,EAAE,KAAK,YAAY,EAAE,IACtC,iBAAiB,IAAI,CAAC,WAAW,OAAO,YAAY,IAAI,CAAC,WAAW;CAGzE;;AAKM,SAAS,uBAAuB,UAAkB;IACvD,OAAO,kBAAkB,MAAM,CAAC,CAAA,WAAY,SAAS,QAAQ,KAAK;AACpE;AAEO,SAAS,gBAAgB,UAAkB;IAChD,OAAO,oIAAA,CAAA,qBAAkB,CAAC,IAAI,CAAC,CAAA,WAAY,SAAS,EAAE,KAAK;AAC7D", "debugId": null}}, {"offset": {"line": 1804, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/data/combined-resources.ts"], "sourcesContent": ["import { LearningResource } from '@/types/learning';\nimport { Resource } from '@/types/resources';\nimport { getRecentResources, getPopularResources, getCombinedLearningResources } from './learning-resources';\nimport { resources } from './resources';\n\n// Combined resource type for the home page\nexport interface CombinedResource {\n  id: string;\n  title: string;\n  description: string;\n  type: string;\n  thumbnail?: string;\n  url: string;\n  category?: string;\n  tags?: string[];\n  publishedAt?: Date;\n  author?: string;\n}\n\n// Get recent AI tools from the resources section\nexport const getRecentAITools = (count: number = 4): CombinedResource[] => {\n  // In a real app, these would be sorted by date added\n  // For now, we'll just take a random selection\n  const shuffled = [...resources].sort(() => 0.5 - Math.random());\n  const selected = shuffled.slice(0, count);\n  \n  return selected.map(resource => ({\n    id: resource.id,\n    title: resource.name,\n    description: resource.description,\n    type: 'resource',\n    thumbnail: resource.logoUrl,\n    url: `/learning/resources/${resource.category}`,\n    category: resource.category,\n    tags: resource.tags || [],\n    // Simulate a recent date\n    publishedAt: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000)\n  }));\n};\n\n// Get popular AI tools from the resources section\nexport const getPopularAITools = (count: number = 4): CombinedResource[] => {\n  // In a real app, these would be sorted by popularity\n  // For now, we'll just take a different random selection\n  const shuffled = [...resources].sort(() => 0.5 - Math.random());\n  const selected = shuffled.slice(0, count);\n  \n  return selected.map(resource => ({\n    id: resource.id,\n    title: resource.name,\n    description: resource.description,\n    type: 'resource',\n    thumbnail: resource.logoUrl,\n    url: `/learning/resources/${resource.category}`,\n    category: resource.category,\n    tags: resource.tags || []\n  }));\n};\n\n// Get combined recent content from all learning sections\nexport const getCombinedRecentContent = (count: number = 8): CombinedResource[] => {\n  const learningContent = getCombinedLearningResources(getRecentResources(count / 2)).map(resource => ({\n    id: resource.id,\n    title: resource.title,\n    description: resource.description,\n    type: resource.type,\n    thumbnail: resource.thumbnail,\n    url: resource.viewUrl,\n    tags: resource.tags,\n    publishedAt: resource.publishedAt,\n    author: resource.author\n  }));\n  \n  const aiTools = getRecentAITools(count / 2);\n  \n  // Combine and sort by date\n  return [...learningContent, ...aiTools]\n    .sort((a, b) => {\n      if (!a.publishedAt) return 1;\n      if (!b.publishedAt) return -1;\n      return b.publishedAt.getTime() - a.publishedAt.getTime();\n    })\n    .slice(0, count);\n};\n\n// Get combined popular content from all learning sections\nexport const getCombinedPopularContent = (count: number = 8): CombinedResource[] => {\n  const learningContent = getCombinedLearningResources(getPopularResources(count / 2)).map(resource => ({\n    id: resource.id,\n    title: resource.title,\n    description: resource.description,\n    type: resource.type,\n    thumbnail: resource.thumbnail,\n    url: resource.viewUrl,\n    tags: resource.tags,\n    publishedAt: resource.publishedAt,\n    author: resource.author\n  }));\n  \n  const aiTools = getPopularAITools(count / 2);\n  \n  // Combine and shuffle\n  return [...learningContent, ...aiTools]\n    .sort(() => 0.5 - Math.random())\n    .slice(0, count);\n};\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAAA;;;AAiBO,MAAM,mBAAmB,CAAC,QAAgB,CAAC;IAChD,qDAAqD;IACrD,8CAA8C;IAC9C,MAAM,WAAW;WAAI,wIAAA,CAAA,YAAS;KAAC,CAAC,IAAI,CAAC,IAAM,MAAM,KAAK,MAAM;IAC5D,MAAM,WAAW,SAAS,KAAK,CAAC,GAAG;IAEnC,OAAO,SAAS,GAAG,CAAC,CAAA,WAAY,CAAC;YAC/B,IAAI,SAAS,EAAE;YACf,OAAO,SAAS,IAAI;YACpB,aAAa,SAAS,WAAW;YACjC,MAAM;YACN,WAAW,SAAS,OAAO;YAC3B,KAAK,CAAC,oBAAoB,EAAE,SAAS,QAAQ,EAAE;YAC/C,UAAU,SAAS,QAAQ;YAC3B,MAAM,SAAS,IAAI,IAAI,EAAE;YACzB,yBAAyB;YACzB,aAAa,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,KAAK,KAAK,KAAK;QACrF,CAAC;AACH;AAGO,MAAM,oBAAoB,CAAC,QAAgB,CAAC;IACjD,qDAAqD;IACrD,wDAAwD;IACxD,MAAM,WAAW;WAAI,wIAAA,CAAA,YAAS;KAAC,CAAC,IAAI,CAAC,IAAM,MAAM,KAAK,MAAM;IAC5D,MAAM,WAAW,SAAS,KAAK,CAAC,GAAG;IAEnC,OAAO,SAAS,GAAG,CAAC,CAAA,WAAY,CAAC;YAC/B,IAAI,SAAS,EAAE;YACf,OAAO,SAAS,IAAI;YACpB,aAAa,SAAS,WAAW;YACjC,MAAM;YACN,WAAW,SAAS,OAAO;YAC3B,KAAK,CAAC,oBAAoB,EAAE,SAAS,QAAQ,EAAE;YAC/C,UAAU,SAAS,QAAQ;YAC3B,MAAM,SAAS,IAAI,IAAI,EAAE;QAC3B,CAAC;AACH;AAGO,MAAM,2BAA2B,CAAC,QAAgB,CAAC;IACxD,MAAM,kBAAkB,CAAA,GAAA,oIAAA,CAAA,+BAA4B,AAAD,EAAE,CAAA,GAAA,oIAAA,CAAA,qBAAkB,AAAD,EAAE,QAAQ,IAAI,GAAG,CAAC,CAAA,WAAY,CAAC;YACnG,IAAI,SAAS,EAAE;YACf,OAAO,SAAS,KAAK;YACrB,aAAa,SAAS,WAAW;YACjC,MAAM,SAAS,IAAI;YACnB,WAAW,SAAS,SAAS;YAC7B,KAAK,SAAS,OAAO;YACrB,MAAM,SAAS,IAAI;YACnB,aAAa,SAAS,WAAW;YACjC,QAAQ,SAAS,MAAM;QACzB,CAAC;IAED,MAAM,UAAU,iBAAiB,QAAQ;IAEzC,2BAA2B;IAC3B,OAAO;WAAI;WAAoB;KAAQ,CACpC,IAAI,CAAC,CAAC,GAAG;QACR,IAAI,CAAC,EAAE,WAAW,EAAE,OAAO;QAC3B,IAAI,CAAC,EAAE,WAAW,EAAE,OAAO,CAAC;QAC5B,OAAO,EAAE,WAAW,CAAC,OAAO,KAAK,EAAE,WAAW,CAAC,OAAO;IACxD,GACC,KAAK,CAAC,GAAG;AACd;AAGO,MAAM,4BAA4B,CAAC,QAAgB,CAAC;IACzD,MAAM,kBAAkB,CAAA,GAAA,oIAAA,CAAA,+BAA4B,AAAD,EAAE,CAAA,GAAA,oIAAA,CAAA,sBAAmB,AAAD,EAAE,QAAQ,IAAI,GAAG,CAAC,CAAA,WAAY,CAAC;YACpG,IAAI,SAAS,EAAE;YACf,OAAO,SAAS,KAAK;YACrB,aAAa,SAAS,WAAW;YACjC,MAAM,SAAS,IAAI;YACnB,WAAW,SAAS,SAAS;YAC7B,KAAK,SAAS,OAAO;YACrB,MAAM,SAAS,IAAI;YACnB,aAAa,SAAS,WAAW;YACjC,QAAQ,SAAS,MAAM;QACzB,CAAC;IAED,MAAM,UAAU,kBAAkB,QAAQ;IAE1C,sBAAsB;IACtB,OAAO;WAAI;WAAoB;KAAQ,CACpC,IAAI,CAAC,IAAM,MAAM,KAAK,MAAM,IAC5B,KAAK,CAAC,GAAG;AACd", "debugId": null}}, {"offset": {"line": 1901, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/data/news-data.ts"], "sourcesContent": ["export interface NewsItem {\n  id: string;\n  title: string;\n  description: string;\n  date: string;\n  source: string;\n  sourceUrl: string;\n  imageUrl: string;\n  url: string;\n  category: 'research' | 'industry' | 'policy' | 'events';\n  tags: string[];\n  featured?: boolean;\n}\n\nconst newsData: NewsItem[] = [\n  {\n    id: 'news-1',\n    title: 'OpenAI Announces GPT-5 with Enhanced Reasoning Capabilities',\n    description: 'OpenAI has unveiled GPT-5, featuring significant improvements in reasoning, planning, and factual accuracy compared to previous models.',\n    date: '2023-11-15',\n    source: 'TechCrunch',\n    sourceUrl: 'https://techcrunch.com',\n    imageUrl: '/images/news/gpt5-announcement.jpg',\n    url: '/news/openai-announces-gpt5',\n    category: 'research',\n    tags: ['OpenAI', 'GPT-5', 'Language Models'],\n    featured: true\n  },\n  {\n    id: 'news-2',\n    title: 'Google DeepMind Achieves Breakthrough in Protein Structure Prediction',\n    description: 'Google DeepMind researchers have developed a new AI system that can predict protein structures with unprecedented accuracy, potentially revolutionizing drug discovery.',\n    date: '2023-11-10',\n    source: 'Nature',\n    sourceUrl: 'https://nature.com',\n    imageUrl: '/images/news/deepmind-protein.jpg',\n    url: '/news/deepmind-protein-breakthrough',\n    category: 'research',\n    tags: ['Google DeepMind', 'Protein Folding', 'AlphaFold'],\n    featured: true\n  },\n  {\n    id: 'news-3',\n    title: 'EU Passes Comprehensive AI Regulation Framework',\n    description: 'The European Union has approved a landmark AI regulation framework that establishes rules for AI development and deployment across member states.',\n    date: '2023-11-05',\n    source: 'Reuters',\n    sourceUrl: 'https://reuters.com',\n    imageUrl: '/images/news/eu-ai-regulation.jpg',\n    url: '/news/eu-ai-regulation',\n    category: 'policy',\n    tags: ['Regulation', 'EU', 'Policy'],\n    featured: false\n  },\n  {\n    id: 'news-4',\n    title: 'Microsoft Integrates AI Assistants Across Office Suite',\n    description: 'Microsoft has announced the integration of advanced AI assistants across its entire Office suite, enhancing productivity and creativity for users.',\n    date: '2023-10-28',\n    source: 'The Verge',\n    sourceUrl: 'https://theverge.com',\n    imageUrl: '/images/news/microsoft-ai-office.jpg',\n    url: '/news/microsoft-ai-office-integration',\n    category: 'industry',\n    tags: ['Microsoft', 'Office', 'Productivity'],\n    featured: false\n  },\n  {\n    id: 'news-5',\n    title: 'AI-Generated Art Wins Major International Competition',\n    description: 'For the first time, an AI-generated artwork has won a prestigious international art competition, sparking debates about creativity and authorship.',\n    date: '2023-10-20',\n    source: 'Artnet',\n    sourceUrl: 'https://artnet.com',\n    imageUrl: '/images/news/ai-art-competition.jpg',\n    url: '/news/ai-art-competition-winner',\n    category: 'industry',\n    tags: ['AI Art', 'Creativity', 'Competition'],\n    featured: false\n  },\n  {\n    id: 'news-6',\n    title: 'World AI Summit 2023 Announces Speaker Lineup',\n    description: 'The World AI Summit 2023 has announced its speaker lineup, featuring leading researchers, industry executives, and policymakers from around the globe.',\n    date: '2023-10-15',\n    source: 'AI News',\n    sourceUrl: 'https://ainews.com',\n    imageUrl: '/images/news/ai-summit-2023.jpg',\n    url: '/news/world-ai-summit-2023',\n    category: 'events',\n    tags: ['Conference', 'Summit', 'Events'],\n    featured: false\n  },\n  {\n    id: 'news-7',\n    title: 'Anthropic Releases Claude 3 with Multimodal Capabilities',\n    description: 'Anthropic has released Claude 3, its latest AI assistant with enhanced multimodal capabilities, allowing it to process and generate text, images, and audio.',\n    date: '2023-10-10',\n    source: 'VentureBeat',\n    sourceUrl: 'https://venturebeat.com',\n    imageUrl: '/images/news/claude-3-release.jpg',\n    url: '/news/anthropic-claude-3-release',\n    category: 'research',\n    tags: ['Anthropic', 'Claude', 'Multimodal AI'],\n    featured: true\n  },\n  {\n    id: 'news-8',\n    title: 'AI-Powered Drug Discovery Leads to Breakthrough Treatment',\n    description: 'A pharmaceutical company has announced a breakthrough treatment for a rare disease, developed with the help of AI-powered drug discovery platforms.',\n    date: '2023-10-05',\n    source: 'Science Daily',\n    sourceUrl: 'https://sciencedaily.com',\n    imageUrl: '/images/news/ai-drug-discovery.jpg',\n    url: '/news/ai-drug-discovery-breakthrough',\n    category: 'industry',\n    tags: ['Healthcare', 'Drug Discovery', 'Medicine'],\n    featured: false\n  }\n];\n\nexport function getAllNews(): NewsItem[] {\n  return newsData;\n}\n\nexport function getFeaturedNews(count: number = 3): NewsItem[] {\n  return newsData\n    .filter(item => item.featured)\n    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())\n    .slice(0, count);\n}\n\nexport function getLatestNews(count: number = 5): NewsItem[] {\n  return newsData\n    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())\n    .slice(0, count);\n}\n\nexport function getNewsByCategory(category: string, count: number = 5): NewsItem[] {\n  return newsData\n    .filter(item => item.category === category)\n    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())\n    .slice(0, count);\n}\n"], "names": [], "mappings": ";;;;;;AAcA,MAAM,WAAuB;IAC3B;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,QAAQ;QACR,WAAW;QACX,UAAU;QACV,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAU;YAAS;SAAkB;QAC5C,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,QAAQ;QACR,WAAW;QACX,UAAU;QACV,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAmB;YAAmB;SAAY;QACzD,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,QAAQ;QACR,WAAW;QACX,UAAU;QACV,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAc;YAAM;SAAS;QACpC,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,QAAQ;QACR,WAAW;QACX,UAAU;QACV,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAa;YAAU;SAAe;QAC7C,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,QAAQ;QACR,WAAW;QACX,UAAU;QACV,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAU;YAAc;SAAc;QAC7C,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,QAAQ;QACR,WAAW;QACX,UAAU;QACV,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAc;YAAU;SAAS;QACxC,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,QAAQ;QACR,WAAW;QACX,UAAU;QACV,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAa;YAAU;SAAgB;QAC9C,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,QAAQ;QACR,WAAW;QACX,UAAU;QACV,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAc;YAAkB;SAAW;QAClD,UAAU;IACZ;CACD;AAEM,SAAS;IACd,OAAO;AACT;AAEO,SAAS,gBAAgB,QAAgB,CAAC;IAC/C,OAAO,SACJ,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,EAC5B,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO,IACpE,KAAK,CAAC,GAAG;AACd;AAEO,SAAS,cAAc,QAAgB,CAAC;IAC7C,OAAO,SACJ,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO,IACpE,KAAK,CAAC,GAAG;AACd;AAEO,SAAS,kBAAkB,QAAgB,EAAE,QAAgB,CAAC;IACnE,OAAO,SACJ,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK,UACjC,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO,IACpE,KAAK,CAAC,GAAG;AACd", "debugId": null}}, {"offset": {"line": 2063, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/components/animations/AnimatedBackgroundWrapper.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/animations/AnimatedBackgroundWrapper.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/animations/AnimatedBackgroundWrapper.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2T,GACxV,yFACA", "debugId": null}}, {"offset": {"line": 2077, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/components/animations/AnimatedBackgroundWrapper.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/animations/AnimatedBackgroundWrapper.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/animations/AnimatedBackgroundWrapper.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAuS,GACpU,qEACA", "debugId": null}}, {"offset": {"line": 2091, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 2101, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/page.tsx"], "sourcesContent": ["import Link from \"next/link\";\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\nimport {\n  faArrowRight,\n  faGraduationCap,\n  faVideo,\n  faNewspaper,\n  faBlog,\n  faToolbox,\n  faFlask,\n  faIndustry,\n  faLandmark,\n  faCalendarAlt\n} from \"@fortawesome/free-solid-svg-icons\";\nimport { getAllAgents, getFeaturedAgents, getNewAgents } from \"@/lib/agents\";\nimport { getCombinedRecentContent, getCombinedPopularContent } from \"@/data/combined-resources\";\nimport { getFeaturedNews, getLatestNews } from \"@/data/news-data\";\nimport AnimatedBackgroundWrapper from \"@/components/animations/AnimatedBackgroundWrapper\";\n\nexport default function Home() {\n  // Get agents from our data utility\n  const featuredAgents = getFeaturedAgents();\n  const allAgents = getAllAgents();\n\n  // For recently used, we'll simulate by taking the first 2 agents\n  // In a real app, this would come from user history\n  const recentAgents = allAgents.slice(0, 2);\n\n  // Get new agents\n  const newAgents = getNewAgents();\n\n  // Get learning content\n  const recentLearningContent = getCombinedRecentContent(4);\n  const popularLearningContent = getCombinedPopularContent(4);\n\n  // Get news content\n  const featuredNews = getFeaturedNews(2);\n  const latestNews = getLatestNews(4);\n\n  return (\n    <div className=\"space-y-10\">\n      {/* Futuristic animated background */}\n      <AnimatedBackgroundWrapper />\n\n      {/* Colorful elements to enhance the frosted glass effect */}\n      <div className=\"absolute top-0 left-0 right-0 -z-10 overflow-hidden\">\n        <div className=\"h-64 w-64 rounded-full bg-blue-500 opacity-10 blur-3xl absolute -top-20 -left-20\"></div>\n        <div className=\"h-64 w-64 rounded-full bg-purple-500 opacity-10 blur-3xl absolute top-10 left-40\"></div>\n        <div className=\"h-64 w-64 rounded-full bg-pink-500 opacity-10 blur-3xl absolute -top-10 right-20\"></div>\n      </div>\n      {/* Welcome section */}\n      <section className=\"rounded-lg bg-gradient-to-r from-blue-600 to-indigo-700 p-8 text-white\">\n        <div className=\"mx-auto max-w-4xl\">\n          <h1 className=\"mb-4 text-3xl font-bold md:text-4xl\">Welcome to our AI Hub</h1>\n          <p className=\"mb-6 text-lg md:text-xl\">\n            Discover and interact with our collection of AI agents designed to help you work more efficiently & Learn about AI tools and services in our learning hub.\n          </p>\n          <div className=\"flex flex-wrap gap-4\">\n            <Link\n              href=\"/browse\"\n              className=\"rounded-md bg-white px-4 py-2 font-medium text-blue-700 hover:bg-blue-50\"\n            >\n              Browse All Agents\n            </Link>\n            <Link\n              href=\"/favorites\"\n              className=\"rounded-md bg-blue-800 px-4 py-2 font-medium text-white hover:bg-blue-900\"\n            >\n              View Favorites\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* Featured agents section */}\n      <section>\n        <div className=\"mb-6 flex items-center justify-between\">\n          <h2 className=\"text-2xl font-bold\">Featured Agents</h2>\n          <Link href=\"/browse\" className=\"text-blue-600 hover:underline dark:text-blue-400\">\n            View all\n          </Link>\n        </div>\n        <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4\">\n          {featuredAgents.map((agent) => (\n            <div\n              key={agent.id}\n              className=\"rounded-lg border border-gray-200 bg-white p-6 shadow-sm transition-shadow hover:shadow-md dark:border-gray-800 dark:bg-gray-900\"\n            >\n              <div className=\"mb-4 flex items-center justify-between\">\n                <span className=\"rounded-full bg-gray-100 px-3 py-1 text-xs font-medium text-gray-800 dark:bg-gray-800 dark:text-gray-200\">\n                  {agent.category}\n                </span>\n                {agent.isNew && (\n                  <span className=\"rounded-full bg-green-100 px-3 py-1 text-xs font-medium text-green-800 dark:bg-green-900/30 dark:text-green-400\">\n                    New\n                  </span>\n                )}\n              </div>\n              <h3 className=\"mb-2 text-xl font-bold\">{agent.name}</h3>\n              <p className=\"mb-4 text-sm text-gray-600 dark:text-gray-400\">{agent.description}</p>\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-xs text-gray-500 dark:text-gray-500\">\n                  {agent.usageCount.toLocaleString()} uses\n                </span>\n                <Link\n                  href={`/agent/${agent.id}`}\n                  className=\"rounded-md bg-blue-600 px-3 py-1.5 text-sm font-medium text-white hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800\"\n                >\n                  View Agent\n                </Link>\n              </div>\n            </div>\n          ))}\n        </div>\n      </section>\n\n      {/* Recent and New sections in a grid */}\n      <div className=\"grid grid-cols-1 gap-10 lg:grid-cols-2\">\n        {/* Recently used section */}\n        <section>\n          <div className=\"mb-6 flex items-center justify-between\">\n            <h2 className=\"text-2xl font-bold\">Recently Used</h2>\n            <Link href=\"/recent\" className=\"text-blue-600 hover:underline dark:text-blue-400\">\n              View all\n            </Link>\n          </div>\n          <div className=\"space-y-4\">\n            {recentAgents.map((agent) => (\n              <div\n                key={agent.id}\n                className=\"rounded-lg border border-gray-200 bg-white p-4 shadow-sm transition-shadow hover:shadow-md dark:border-gray-800 dark:bg-gray-900\"\n              >\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <h3 className=\"font-bold\">{agent.name}</h3>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-400\">{agent.description}</p>\n                  </div>\n                  <Link\n                    href={`/agent/${agent.id}`}\n                    className=\"rounded-md bg-blue-600 px-3 py-1.5 text-sm font-medium text-white hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800\"\n                  >\n                    View Agent\n                  </Link>\n                </div>\n              </div>\n            ))}\n          </div>\n        </section>\n\n        {/* New agents section */}\n        <section>\n          <div className=\"mb-6 flex items-center justify-between\">\n            <h2 className=\"text-2xl font-bold\">New Agents</h2>\n            <Link href=\"/new-releases\" className=\"text-blue-600 hover:underline dark:text-blue-400\">\n              View all\n            </Link>\n          </div>\n          <div className=\"space-y-4\">\n            {newAgents.map((agent) => (\n              <div\n                key={agent.id}\n                className=\"rounded-lg border border-gray-200 bg-white p-4 shadow-sm transition-shadow hover:shadow-md dark:border-gray-800 dark:bg-gray-900\"\n              >\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <div className=\"mb-1 flex items-center gap-2\">\n                      <h3 className=\"font-bold\">{agent.name}</h3>\n                      <span className=\"rounded-full bg-green-100 px-2 py-0.5 text-xs font-medium text-green-800 dark:bg-green-900/30 dark:text-green-400\">\n                        New\n                      </span>\n                    </div>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-400\">{agent.description}</p>\n                  </div>\n                  <Link\n                    href={`/agent/${agent.id}`}\n                    className=\"rounded-md bg-blue-600 px-3 py-1.5 text-sm font-medium text-white hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800\"\n                  >\n                    View Agent\n                  </Link>\n                </div>\n              </div>\n            ))}\n          </div>\n        </section>\n\n      </div>\n\n      {/* Learning section - Full width */}\n      <section className=\"mt-12 rounded-lg bg-gradient-to-r from-indigo-100 to-purple-100 p-6 dark:from-indigo-950/30 dark:to-purple-950/30 w-full\">\n        <div className=\"mx-auto max-w-7xl\">\n          <div className=\"mb-6 flex items-center\">\n            <FontAwesomeIcon icon={faGraduationCap} className=\"mr-3 h-6 w-6 text-indigo-600 dark:text-indigo-400\" />\n            <h2 className=\"text-2xl font-bold\">Learning Hub</h2>\n          </div>\n\n          {/* Recent learning content */}\n          <div className=\"mb-8\">\n            <div className=\"mb-4 flex items-center justify-between\">\n              <h3 className=\"text-lg font-semibold\">Recent Content</h3>\n              <Link href=\"/learning\" className=\"flex items-center text-sm font-medium text-indigo-600 hover:text-indigo-800 dark:text-indigo-400 dark:hover:text-indigo-300\">\n                Explore all\n                <FontAwesomeIcon icon={faArrowRight} className=\"ml-1 h-3 w-3\" />\n              </Link>\n            </div>\n            <div className=\"grid gap-4 sm:grid-cols-2 lg:grid-cols-4\">\n              {recentLearningContent.map((item) => (\n                <Link\n                  key={item.id}\n                  href={item.url}\n                  className=\"group overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm transition hover:shadow-md dark:border-gray-800 dark:bg-gray-900\"\n                >\n                  <div className=\"relative h-36 overflow-hidden\">\n                    <img\n                      src={item.thumbnail || `https://via.placeholder.com/400x200?text=${encodeURIComponent(item.title)}`}\n                      alt={item.title}\n                      className=\"h-full w-full object-cover transition duration-300 group-hover:scale-105\"\n                    />\n                    <div className=\"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-3\">\n                      <div className=\"flex items-center\">\n                        <span className=\"rounded bg-white/20 px-2 py-1 text-xs font-medium text-white backdrop-blur-sm\">\n                          {item.type === 'video' && (\n                            <>\n                              <FontAwesomeIcon icon={faVideo} className=\"mr-1 h-3 w-3\" />\n                              Video\n                            </>\n                          )}\n                          {item.type === 'article' && (\n                            <>\n                              <FontAwesomeIcon icon={faNewspaper} className=\"mr-1 h-3 w-3\" />\n                              Article\n                            </>\n                          )}\n                          {item.type === 'blog' && (\n                            <>\n                              <FontAwesomeIcon icon={faBlog} className=\"mr-1 h-3 w-3\" />\n                              Blog\n                            </>\n                          )}\n                          {item.type === 'resource' && (\n                            <>\n                              <FontAwesomeIcon icon={faToolbox} className=\"mr-1 h-3 w-3\" />\n                              Resource\n                            </>\n                          )}\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"p-4\">\n                    <h4 className=\"mb-1 font-medium text-gray-900 line-clamp-1 group-hover:text-indigo-600 dark:text-white dark:group-hover:text-indigo-400\">\n                      {item.title}\n                    </h4>\n                    <p className=\"text-sm text-gray-600 line-clamp-2 dark:text-gray-400\">\n                      {item.description}\n                    </p>\n                  </div>\n                </Link>\n              ))}\n            </div>\n          </div>\n\n          {/* Popular learning content */}\n          <div>\n            <div className=\"mb-4 flex items-center justify-between\">\n              <h3 className=\"text-lg font-semibold\">Popular Resources</h3>\n              <Link href=\"/learning/resources\" className=\"flex items-center text-sm font-medium text-indigo-600 hover:text-indigo-800 dark:text-indigo-400 dark:hover:text-indigo-300\">\n                View all resources\n                <FontAwesomeIcon icon={faArrowRight} className=\"ml-1 h-3 w-3\" />\n              </Link>\n            </div>\n            <div className=\"grid gap-4 sm:grid-cols-2 lg:grid-cols-4\">\n              {popularLearningContent.map((item) => (\n                <Link\n                  key={item.id}\n                  href={item.url}\n                  className=\"group overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm transition hover:shadow-md dark:border-gray-800 dark:bg-gray-900\"\n                >\n                  <div className=\"p-4\">\n                    <div className=\"mb-3 flex items-center\">\n                      {item.type === 'video' && (\n                        <FontAwesomeIcon icon={faVideo} className=\"mr-2 h-4 w-4 text-blue-500\" />\n                      )}\n                      {item.type === 'article' && (\n                        <FontAwesomeIcon icon={faNewspaper} className=\"mr-2 h-4 w-4 text-green-500\" />\n                      )}\n                      {item.type === 'blog' && (\n                        <FontAwesomeIcon icon={faBlog} className=\"mr-2 h-4 w-4 text-purple-500\" />\n                      )}\n                      {item.type === 'resource' && (\n                        <FontAwesomeIcon icon={faToolbox} className=\"mr-2 h-4 w-4 text-orange-500\" />\n                      )}\n                      <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                        {item.type.charAt(0).toUpperCase() + item.type.slice(1)}\n                      </span>\n                    </div>\n                    <h4 className=\"mb-1 font-medium text-gray-900 line-clamp-1 group-hover:text-indigo-600 dark:text-white dark:group-hover:text-indigo-400\">\n                      {item.title}\n                    </h4>\n                    <p className=\"text-sm text-gray-600 line-clamp-2 dark:text-gray-400\">\n                      {item.description}\n                    </p>\n                    {item.tags && item.tags.length > 0 && (\n                      <div className=\"mt-3 flex flex-wrap gap-1\">\n                        {item.tags.slice(0, 3).map((tag) => (\n                          <span\n                            key={tag}\n                            className=\"inline-flex items-center rounded-full bg-gray-100 px-2 py-0.5 text-xs font-medium text-gray-800 dark:bg-gray-800 dark:text-gray-200\"\n                          >\n                            {tag}\n                          </span>\n                        ))}\n                      </div>\n                    )}\n                  </div>\n                </Link>\n              ))}\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* AI News Section */}\n      <section className=\"py-10 bg-gray-50 dark:bg-gray-900/50\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"mb-8 text-center\">\n            <h2 className=\"text-3xl font-bold mb-2\">AI News & Updates</h2>\n            <p className=\"text-gray-600 dark:text-gray-400 max-w-2xl mx-auto\">\n              Stay informed about the latest advancements and applications in artificial intelligence\n            </p>\n          </div>\n\n          {/* Featured news */}\n          <div className=\"mb-12\">\n            <div className=\"mb-6 flex items-center justify-between\">\n              <h3 className=\"text-xl font-semibold\">Featured Stories</h3>\n              <Link href=\"/news\" className=\"flex items-center text-sm font-medium text-indigo-600 hover:text-indigo-800 dark:text-indigo-400 dark:hover:text-indigo-300\">\n                View all news\n                <FontAwesomeIcon icon={faArrowRight} className=\"ml-1 h-3 w-3\" />\n              </Link>\n            </div>\n\n            <div className=\"grid gap-6 md:grid-cols-2\">\n              {featuredNews.map((item) => (\n                <Link\n                  key={item.id}\n                  href={item.url}\n                  className=\"group relative overflow-hidden rounded-xl bg-white shadow-lg transition hover:shadow-xl dark:bg-gray-800\"\n                >\n                  <div className=\"aspect-w-16 aspect-h-9 relative overflow-hidden\">\n                    <img\n                      src={item.imageUrl || `https://via.placeholder.com/800x450?text=${encodeURIComponent(item.title)}`}\n                      alt={item.title}\n                      className=\"h-full w-full object-cover transition duration-300 group-hover:scale-105\"\n                    />\n                    <div className=\"absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent\">\n                      <div className=\"absolute bottom-0 left-0 right-0 p-6\">\n                        <div className=\"mb-2 flex items-center\">\n                          {item.category === 'research' && (\n                            <span className=\"rounded-full bg-blue-100 px-3 py-1 text-xs font-medium text-blue-800 dark:bg-blue-900/60 dark:text-blue-200\">\n                              <FontAwesomeIcon icon={faFlask} className=\"mr-1 h-3 w-3\" />\n                              Research\n                            </span>\n                          )}\n                          {item.category === 'industry' && (\n                            <span className=\"rounded-full bg-green-100 px-3 py-1 text-xs font-medium text-green-800 dark:bg-green-900/60 dark:text-green-200\">\n                              <FontAwesomeIcon icon={faIndustry} className=\"mr-1 h-3 w-3\" />\n                              Industry\n                            </span>\n                          )}\n                          {item.category === 'policy' && (\n                            <span className=\"rounded-full bg-purple-100 px-3 py-1 text-xs font-medium text-purple-800 dark:bg-purple-900/60 dark:text-purple-200\">\n                              <FontAwesomeIcon icon={faLandmark} className=\"mr-1 h-3 w-3\" />\n                              Policy\n                            </span>\n                          )}\n                          {item.category === 'events' && (\n                            <span className=\"rounded-full bg-amber-100 px-3 py-1 text-xs font-medium text-amber-800 dark:bg-amber-900/60 dark:text-amber-200\">\n                              <FontAwesomeIcon icon={faCalendarAlt} className=\"mr-1 h-3 w-3\" />\n                              Events\n                            </span>\n                          )}\n                          <span className=\"ml-auto text-xs text-white opacity-80\">{item.date}</span>\n                        </div>\n                        <h4 className=\"mb-2 text-xl font-bold text-white\">{item.title}</h4>\n                        <p className=\"mb-2 text-sm text-white/80\">{item.description}</p>\n                        <div className=\"flex items-center text-xs text-white/70\">\n                          <span>Source: {item.source}</span>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </Link>\n              ))}\n            </div>\n          </div>\n\n          {/* Latest news */}\n          <div>\n            <div className=\"mb-6 flex items-center justify-between\">\n              <h3 className=\"text-xl font-semibold\">Latest Updates</h3>\n              <Link href=\"/news\" className=\"flex items-center text-sm font-medium text-indigo-600 hover:text-indigo-800 dark:text-indigo-400 dark:hover:text-indigo-300\">\n                View all news\n                <FontAwesomeIcon icon={faArrowRight} className=\"ml-1 h-3 w-3\" />\n              </Link>\n            </div>\n\n            <div className=\"grid gap-6 sm:grid-cols-2 lg:grid-cols-4\">\n              {latestNews.map((item) => (\n                <Link\n                  key={item.id}\n                  href={item.url}\n                  className=\"group overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm transition hover:shadow-md dark:border-gray-700 dark:bg-gray-800\"\n                >\n                  <div className=\"relative h-40 overflow-hidden\">\n                    <img\n                      src={item.imageUrl || `https://via.placeholder.com/400x200?text=${encodeURIComponent(item.title)}`}\n                      alt={item.title}\n                      className=\"h-full w-full object-cover transition duration-300 group-hover:scale-105\"\n                    />\n                    <div className=\"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-3\">\n                      <div className=\"flex items-center\">\n                        {item.category === 'research' && (\n                          <span className=\"rounded bg-blue-500/20 px-2 py-1 text-xs font-medium text-white backdrop-blur-sm\">\n                            <FontAwesomeIcon icon={faFlask} className=\"mr-1 h-3 w-3\" />\n                            Research\n                          </span>\n                        )}\n                        {item.category === 'industry' && (\n                          <span className=\"rounded bg-green-500/20 px-2 py-1 text-xs font-medium text-white backdrop-blur-sm\">\n                            <FontAwesomeIcon icon={faIndustry} className=\"mr-1 h-3 w-3\" />\n                            Industry\n                          </span>\n                        )}\n                        {item.category === 'policy' && (\n                          <span className=\"rounded bg-purple-500/20 px-2 py-1 text-xs font-medium text-white backdrop-blur-sm\">\n                            <FontAwesomeIcon icon={faLandmark} className=\"mr-1 h-3 w-3\" />\n                            Policy\n                          </span>\n                        )}\n                        {item.category === 'events' && (\n                          <span className=\"rounded bg-amber-500/20 px-2 py-1 text-xs font-medium text-white backdrop-blur-sm\">\n                            <FontAwesomeIcon icon={faCalendarAlt} className=\"mr-1 h-3 w-3\" />\n                            Events\n                          </span>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"p-4\">\n                    <div className=\"mb-1 flex items-center justify-between\">\n                      <span className=\"text-xs text-gray-500 dark:text-gray-400\">{item.date}</span>\n                      <span className=\"text-xs text-gray-500 dark:text-gray-400\">{item.source}</span>\n                    </div>\n                    <h4 className=\"mb-1 font-medium text-gray-900 line-clamp-2 group-hover:text-indigo-600 dark:text-white dark:group-hover:text-indigo-400\">\n                      {item.title}\n                    </h4>\n                    <p className=\"text-sm text-gray-600 line-clamp-2 dark:text-gray-400\">\n                      {item.description}\n                    </p>\n                  </div>\n                </Link>\n              ))}\n            </div>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAYA;AACA;AACA;AACA;;;;;;;;;AAEe,SAAS;IACtB,mCAAmC;IACnC,MAAM,iBAAiB,CAAA,GAAA,oHAAA,CAAA,oBAAiB,AAAD;IACvC,MAAM,YAAY,CAAA,GAAA,oHAAA,CAAA,eAAY,AAAD;IAE7B,iEAAiE;IACjE,mDAAmD;IACnD,MAAM,eAAe,UAAU,KAAK,CAAC,GAAG;IAExC,iBAAiB;IACjB,MAAM,YAAY,CAAA,GAAA,oHAAA,CAAA,eAAY,AAAD;IAE7B,uBAAuB;IACvB,MAAM,wBAAwB,CAAA,GAAA,oIAAA,CAAA,2BAAwB,AAAD,EAAE;IACvD,MAAM,yBAAyB,CAAA,GAAA,oIAAA,CAAA,4BAAyB,AAAD,EAAE;IAEzD,mBAAmB;IACnB,MAAM,eAAe,CAAA,GAAA,2HAAA,CAAA,kBAAe,AAAD,EAAE;IACrC,MAAM,aAAa,CAAA,GAAA,2HAAA,CAAA,gBAAa,AAAD,EAAE;IAEjC,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,6JAAA,CAAA,UAAyB;;;;;0BAG1B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;sCACpD,8OAAC;4BAAE,WAAU;sCAA0B;;;;;;sCAGvC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;0BAQP,8OAAC;;kCACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAqB;;;;;;0CACnC,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAU,WAAU;0CAAmD;;;;;;;;;;;;kCAIpF,8OAAC;wBAAI,WAAU;kCACZ,eAAe,GAAG,CAAC,CAAC,sBACnB,8OAAC;gCAEC,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DACb,MAAM,QAAQ;;;;;;4CAEhB,MAAM,KAAK,kBACV,8OAAC;gDAAK,WAAU;0DAAkH;;;;;;;;;;;;kDAKtI,8OAAC;wCAAG,WAAU;kDAA0B,MAAM,IAAI;;;;;;kDAClD,8OAAC;wCAAE,WAAU;kDAAiD,MAAM,WAAW;;;;;;kDAC/E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;;oDACb,MAAM,UAAU,CAAC,cAAc;oDAAG;;;;;;;0DAErC,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gDAC1B,WAAU;0DACX;;;;;;;;;;;;;+BAtBE,MAAM,EAAE;;;;;;;;;;;;;;;;0BAgCrB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;;0CACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAqB;;;;;;kDACnC,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAU,WAAU;kDAAmD;;;;;;;;;;;;0CAIpF,8OAAC;gCAAI,WAAU;0CACZ,aAAa,GAAG,CAAC,CAAC,sBACjB,8OAAC;wCAEC,WAAU;kDAEV,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAa,MAAM,IAAI;;;;;;sEACrC,8OAAC;4DAAE,WAAU;sEAA4C,MAAM,WAAW;;;;;;;;;;;;8DAE5E,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;oDAC1B,WAAU;8DACX;;;;;;;;;;;;uCAXE,MAAM,EAAE;;;;;;;;;;;;;;;;kCAqBrB,8OAAC;;0CACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAqB;;;;;;kDACnC,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAgB,WAAU;kDAAmD;;;;;;;;;;;;0CAI1F,8OAAC;gCAAI,WAAU;0CACZ,UAAU,GAAG,CAAC,CAAC,sBACd,8OAAC;wCAEC,WAAU;kDAEV,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAAa,MAAM,IAAI;;;;;;8EACrC,8OAAC;oEAAK,WAAU;8EAAoH;;;;;;;;;;;;sEAItI,8OAAC;4DAAE,WAAU;sEAA4C,MAAM,WAAW;;;;;;;;;;;;8DAE5E,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;oDAC1B,WAAU;8DACX;;;;;;;;;;;;uCAhBE,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;0BA4BvB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oKAAA,CAAA,kBAAe;oCAAC,MAAM,wKAAA,CAAA,kBAAe;oCAAE,WAAU;;;;;;8CAClD,8OAAC;oCAAG,WAAU;8CAAqB;;;;;;;;;;;;sCAIrC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAwB;;;;;;sDACtC,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAY,WAAU;;gDAA8H;8DAE7J,8OAAC,oKAAA,CAAA,kBAAe;oDAAC,MAAM,wKAAA,CAAA,eAAY;oDAAE,WAAU;;;;;;;;;;;;;;;;;;8CAGnD,8OAAC;oCAAI,WAAU;8CACZ,sBAAsB,GAAG,CAAC,CAAC,qBAC1B,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,GAAG;4CACd,WAAU;;8DAEV,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,KAAK,KAAK,SAAS,IAAI,CAAC,yCAAyC,EAAE,mBAAmB,KAAK,KAAK,GAAG;4DACnG,KAAK,KAAK,KAAK;4DACf,WAAU;;;;;;sEAEZ,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAK,WAAU;;wEACb,KAAK,IAAI,KAAK,yBACb;;8FACE,8OAAC,oKAAA,CAAA,kBAAe;oFAAC,MAAM,wKAAA,CAAA,UAAO;oFAAE,WAAU;;;;;;gFAAiB;;;wEAI9D,KAAK,IAAI,KAAK,2BACb;;8FACE,8OAAC,oKAAA,CAAA,kBAAe;oFAAC,MAAM,wKAAA,CAAA,cAAW;oFAAE,WAAU;;;;;;gFAAiB;;;wEAIlE,KAAK,IAAI,KAAK,wBACb;;8FACE,8OAAC,oKAAA,CAAA,kBAAe;oFAAC,MAAM,wKAAA,CAAA,SAAM;oFAAE,WAAU;;;;;;gFAAiB;;;wEAI7D,KAAK,IAAI,KAAK,4BACb;;8FACE,8OAAC,oKAAA,CAAA,kBAAe;oFAAC,MAAM,wKAAA,CAAA,YAAS;oFAAE,WAAU;;;;;;gFAAiB;;;;;;;;;;;;;;;;;;;;;;;;;8DAQzE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEACX,KAAK,KAAK;;;;;;sEAEb,8OAAC;4DAAE,WAAU;sEACV,KAAK,WAAW;;;;;;;;;;;;;2CA9ChB,KAAK,EAAE;;;;;;;;;;;;;;;;sCAuDpB,8OAAC;;8CACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAwB;;;;;;sDACtC,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAsB,WAAU;;gDAA8H;8DAEvK,8OAAC,oKAAA,CAAA,kBAAe;oDAAC,MAAM,wKAAA,CAAA,eAAY;oDAAE,WAAU;;;;;;;;;;;;;;;;;;8CAGnD,8OAAC;oCAAI,WAAU;8CACZ,uBAAuB,GAAG,CAAC,CAAC,qBAC3B,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,GAAG;4CACd,WAAU;sDAEV,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;4DACZ,KAAK,IAAI,KAAK,yBACb,8OAAC,oKAAA,CAAA,kBAAe;gEAAC,MAAM,wKAAA,CAAA,UAAO;gEAAE,WAAU;;;;;;4DAE3C,KAAK,IAAI,KAAK,2BACb,8OAAC,oKAAA,CAAA,kBAAe;gEAAC,MAAM,wKAAA,CAAA,cAAW;gEAAE,WAAU;;;;;;4DAE/C,KAAK,IAAI,KAAK,wBACb,8OAAC,oKAAA,CAAA,kBAAe;gEAAC,MAAM,wKAAA,CAAA,SAAM;gEAAE,WAAU;;;;;;4DAE1C,KAAK,IAAI,KAAK,4BACb,8OAAC,oKAAA,CAAA,kBAAe;gEAAC,MAAM,wKAAA,CAAA,YAAS;gEAAE,WAAU;;;;;;0EAE9C,8OAAC;gEAAK,WAAU;0EACb,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,IAAI,CAAC,KAAK,CAAC;;;;;;;;;;;;kEAGzD,8OAAC;wDAAG,WAAU;kEACX,KAAK,KAAK;;;;;;kEAEb,8OAAC;wDAAE,WAAU;kEACV,KAAK,WAAW;;;;;;oDAElB,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,mBAC/B,8OAAC;wDAAI,WAAU;kEACZ,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,oBAC1B,8OAAC;gEAEC,WAAU;0EAET;+DAHI;;;;;;;;;;;;;;;;2CAhCV,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAiDxB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0B;;;;;;8CACxC,8OAAC;oCAAE,WAAU;8CAAqD;;;;;;;;;;;;sCAMpE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAwB;;;;;;sDACtC,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAQ,WAAU;;gDAA8H;8DAEzJ,8OAAC,oKAAA,CAAA,kBAAe;oDAAC,MAAM,wKAAA,CAAA,eAAY;oDAAE,WAAU;;;;;;;;;;;;;;;;;;8CAInD,8OAAC;oCAAI,WAAU;8CACZ,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,GAAG;4CACd,WAAU;sDAEV,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,KAAK,KAAK,QAAQ,IAAI,CAAC,yCAAyC,EAAE,mBAAmB,KAAK,KAAK,GAAG;wDAClG,KAAK,KAAK,KAAK;wDACf,WAAU;;;;;;kEAEZ,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;wEACZ,KAAK,QAAQ,KAAK,4BACjB,8OAAC;4EAAK,WAAU;;8FACd,8OAAC,oKAAA,CAAA,kBAAe;oFAAC,MAAM,wKAAA,CAAA,UAAO;oFAAE,WAAU;;;;;;gFAAiB;;;;;;;wEAI9D,KAAK,QAAQ,KAAK,4BACjB,8OAAC;4EAAK,WAAU;;8FACd,8OAAC,oKAAA,CAAA,kBAAe;oFAAC,MAAM,wKAAA,CAAA,aAAU;oFAAE,WAAU;;;;;;gFAAiB;;;;;;;wEAIjE,KAAK,QAAQ,KAAK,0BACjB,8OAAC;4EAAK,WAAU;;8FACd,8OAAC,oKAAA,CAAA,kBAAe;oFAAC,MAAM,wKAAA,CAAA,aAAU;oFAAE,WAAU;;;;;;gFAAiB;;;;;;;wEAIjE,KAAK,QAAQ,KAAK,0BACjB,8OAAC;4EAAK,WAAU;;8FACd,8OAAC,oKAAA,CAAA,kBAAe;oFAAC,MAAM,wKAAA,CAAA,gBAAa;oFAAE,WAAU;;;;;;gFAAiB;;;;;;;sFAIrE,8OAAC;4EAAK,WAAU;sFAAyC,KAAK,IAAI;;;;;;;;;;;;8EAEpE,8OAAC;oEAAG,WAAU;8EAAqC,KAAK,KAAK;;;;;;8EAC7D,8OAAC;oEAAE,WAAU;8EAA8B,KAAK,WAAW;;;;;;8EAC3D,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;;4EAAK;4EAAS,KAAK,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2CA1C7B,KAAK,EAAE;;;;;;;;;;;;;;;;sCAqDpB,8OAAC;;8CACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAwB;;;;;;sDACtC,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAQ,WAAU;;gDAA8H;8DAEzJ,8OAAC,oKAAA,CAAA,kBAAe;oDAAC,MAAM,wKAAA,CAAA,eAAY;oDAAE,WAAU;;;;;;;;;;;;;;;;;;8CAInD,8OAAC;oCAAI,WAAU;8CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,GAAG;4CACd,WAAU;;8DAEV,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,KAAK,KAAK,QAAQ,IAAI,CAAC,yCAAyC,EAAE,mBAAmB,KAAK,KAAK,GAAG;4DAClG,KAAK,KAAK,KAAK;4DACf,WAAU;;;;;;sEAEZ,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;;oEACZ,KAAK,QAAQ,KAAK,4BACjB,8OAAC;wEAAK,WAAU;;0FACd,8OAAC,oKAAA,CAAA,kBAAe;gFAAC,MAAM,wKAAA,CAAA,UAAO;gFAAE,WAAU;;;;;;4EAAiB;;;;;;;oEAI9D,KAAK,QAAQ,KAAK,4BACjB,8OAAC;wEAAK,WAAU;;0FACd,8OAAC,oKAAA,CAAA,kBAAe;gFAAC,MAAM,wKAAA,CAAA,aAAU;gFAAE,WAAU;;;;;;4EAAiB;;;;;;;oEAIjE,KAAK,QAAQ,KAAK,0BACjB,8OAAC;wEAAK,WAAU;;0FACd,8OAAC,oKAAA,CAAA,kBAAe;gFAAC,MAAM,wKAAA,CAAA,aAAU;gFAAE,WAAU;;;;;;4EAAiB;;;;;;;oEAIjE,KAAK,QAAQ,KAAK,0BACjB,8OAAC;wEAAK,WAAU;;0FACd,8OAAC,oKAAA,CAAA,kBAAe;gFAAC,MAAM,wKAAA,CAAA,gBAAa;gFAAE,WAAU;;;;;;4EAAiB;;;;;;;;;;;;;;;;;;;;;;;;8DAO3E,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAA4C,KAAK,IAAI;;;;;;8EACrE,8OAAC;oEAAK,WAAU;8EAA4C,KAAK,MAAM;;;;;;;;;;;;sEAEzE,8OAAC;4DAAG,WAAU;sEACX,KAAK,KAAK;;;;;;sEAEb,8OAAC;4DAAE,WAAU;sEACV,KAAK,WAAW;;;;;;;;;;;;;2CAhDhB,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2D9B", "debugId": null}}]}