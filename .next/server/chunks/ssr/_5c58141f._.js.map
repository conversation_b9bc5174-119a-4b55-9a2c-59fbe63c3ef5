{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/lib/agents.ts"], "sourcesContent": ["import { Agent, Message, Session } from '@/types/agent';\n\n// Mock data for agents\nconst agents: Agent[] = [\n  {\n    id: '1',\n    name: 'Data Analyst',\n    description: 'Analyze data sets and generate insights with natural language queries.',\n    longDescription: 'The Data Analyst agent helps you explore and understand your data through natural language. Ask questions about your data, request visualizations, or get statistical summaries without writing complex queries or code. This agent can work with various data formats including CSV, Excel, SQL databases, and more.',\n    category: 'Analytics',\n    capabilities: [\n      'Natural language data queries',\n      'Data visualization generation',\n      'Statistical analysis',\n      'Anomaly detection',\n      'Trend identification',\n      'Report generation'\n    ],\n    usageCount: 1245,\n    isNew: false,\n    isFeatured: true,\n    createdAt: '2023-10-15',\n    updatedAt: '2024-03-01',\n    version: '2.3.0',\n    creator: 'Data Science Team',\n    avatarUrl: '/agents/data-analyst.svg',\n    tags: ['data', 'analytics', 'visualization', 'statistics'],\n    relatedAgentIds: ['5', '7', '8']\n  },\n  {\n    id: '2',\n    name: 'Code Assistant',\n    description: 'Help with coding tasks, debugging, and code reviews across multiple languages.',\n    longDescription: 'The Code Assistant agent is your programming partner. It can help write, debug, and optimize code in multiple languages including Python, JavaScript, Java, C++, and more. Ask for code examples, get help with debugging, or request code reviews. The agent can also explain complex code and suggest improvements.',\n    category: 'Development',\n    capabilities: [\n      'Code generation',\n      'Debugging assistance',\n      'Code optimization',\n      'Code explanation',\n      'Multiple language support',\n      'Best practices recommendations'\n    ],\n    usageCount: 3421,\n    isNew: false,\n    isFeatured: true,\n    createdAt: '2023-08-10',\n    updatedAt: '2024-02-15',\n    version: '3.1.0',\n    creator: 'Engineering Team',\n    avatarUrl: '/agents/code-assistant.svg',\n    tags: ['coding', 'programming', 'development', 'debugging'],\n    relatedAgentIds: ['9', '10']\n  },\n  {\n    id: '3',\n    name: 'Research Companion',\n    description: 'Find, summarize, and organize research papers and articles.',\n    longDescription: 'The Research Companion agent helps you stay on top of the latest research in your field. It can find relevant papers, summarize key findings, and help you organize your research materials. Ask for papers on specific topics, get summaries of complex articles, or request literature reviews.',\n    category: 'Research',\n    capabilities: [\n      'Research paper search',\n      'Article summarization',\n      'Literature review assistance',\n      'Citation generation',\n      'Research organization',\n      'Key findings extraction'\n    ],\n    usageCount: 876,\n    isNew: true,\n    isFeatured: true,\n    createdAt: '2024-01-20',\n    updatedAt: '2024-03-10',\n    version: '1.2.0',\n    creator: 'Research & Development',\n    avatarUrl: '/agents/research-companion.svg',\n    tags: ['research', 'papers', 'academic', 'literature'],\n    relatedAgentIds: ['11', '5']\n  },\n  {\n    id: '4',\n    name: 'Meeting Summarizer',\n    description: 'Generate concise summaries and action items from meeting transcripts.',\n    longDescription: 'The Meeting Summarizer agent helps you capture the important points from your meetings. Upload a transcript or recording, and the agent will identify key discussion points, decisions made, and action items assigned. It can also generate meeting minutes in various formats.',\n    category: 'Productivity',\n    capabilities: [\n      'Meeting transcript analysis',\n      'Key point extraction',\n      'Action item identification',\n      'Decision tracking',\n      'Meeting minutes generation',\n      'Follow-up reminder creation'\n    ],\n    usageCount: 2134,\n    isNew: false,\n    isFeatured: true,\n    createdAt: '2023-11-05',\n    updatedAt: '2024-02-28',\n    version: '2.0.1',\n    creator: 'Productivity Team',\n    avatarUrl: '/agents/meeting-summarizer.svg',\n    tags: ['meetings', 'productivity', 'transcription', 'summaries'],\n    relatedAgentIds: ['5', '6', '12']\n  },\n  {\n    id: '5',\n    name: 'Document Analyzer',\n    description: 'Extract key information from documents and generate summaries.',\n    longDescription: 'The Document Analyzer agent helps you process and understand documents quickly. Upload any document (PDF, Word, etc.), and the agent will extract key information, generate summaries, and answer questions about the content. It can handle contracts, reports, articles, and more.',\n    category: 'Productivity',\n    capabilities: [\n      'Document parsing',\n      'Key information extraction',\n      'Summary generation',\n      'Question answering',\n      'Multiple format support',\n      'Entity recognition'\n    ],\n    usageCount: 567,\n    isNew: false,\n    isFeatured: false,\n    createdAt: '2023-09-12',\n    updatedAt: '2024-01-30',\n    version: '1.5.2',\n    creator: 'Content Team',\n    avatarUrl: '/agents/document-analyzer.svg',\n    tags: ['documents', 'analysis', 'extraction', 'summaries'],\n    relatedAgentIds: ['1', '3', '4']\n  },\n  {\n    id: '6',\n    name: 'Presentation Creator',\n    description: 'Generate professional presentations from outlines or topics.',\n    longDescription: 'The Presentation Creator agent helps you build compelling presentations quickly. Provide a topic or outline, and the agent will generate slide content, suggest visuals, and help you structure your presentation for maximum impact. It can create presentations for various purposes including business, education, and sales.',\n    category: 'Productivity',\n    capabilities: [\n      'Slide content generation',\n      'Presentation structure suggestions',\n      'Visual element recommendations',\n      'Talking points creation',\n      'Multiple template support',\n      'Export to PowerPoint/Google Slides'\n    ],\n    usageCount: 321,\n    isNew: true,\n    isFeatured: false,\n    createdAt: '2024-02-01',\n    updatedAt: '2024-03-15',\n    version: '1.0.0',\n    creator: 'Marketing Team',\n    avatarUrl: '/agents/presentation-creator.svg',\n    tags: ['presentations', 'slides', 'design', 'content'],\n    relatedAgentIds: ['4', '13']\n  },\n  {\n    id: '7',\n    name: 'Data Visualization Expert',\n    description: 'Create beautiful and informative data visualizations from your datasets.',\n    longDescription: 'The Data Visualization Expert agent helps you transform raw data into compelling visual stories. Upload your data and describe what you want to highlight, and the agent will generate appropriate charts, graphs, and interactive visualizations that make your data insights clear and impactful.',\n    category: 'Analytics',\n    capabilities: [\n      'Chart and graph generation',\n      'Interactive visualization creation',\n      'Color palette optimization',\n      'Data storytelling assistance',\n      'Multiple export formats',\n      'Accessibility considerations'\n    ],\n    usageCount: 892,\n    isNew: false,\n    isFeatured: false,\n    createdAt: '2023-11-18',\n    updatedAt: '2024-02-10',\n    version: '1.8.0',\n    creator: 'Data Science Team',\n    avatarUrl: '/agents/data-analyst.svg',\n    tags: ['visualization', 'charts', 'graphs', 'data'],\n    relatedAgentIds: ['1', '8']\n  },\n  {\n    id: '8',\n    name: 'Predictive Analytics Agent',\n    description: 'Forecast trends and make predictions based on historical data.',\n    longDescription: 'The Predictive Analytics Agent uses machine learning algorithms to analyze your historical data and generate forecasts and predictions. It can identify patterns, trends, and anomalies to help you make data-driven decisions about future outcomes.',\n    category: 'Analytics',\n    capabilities: [\n      'Time series forecasting',\n      'Trend analysis',\n      'Anomaly detection',\n      'Predictive modeling',\n      'Scenario planning',\n      'Confidence interval calculation'\n    ],\n    usageCount: 754,\n    isNew: true,\n    isFeatured: false,\n    createdAt: '2024-01-05',\n    updatedAt: '2024-03-20',\n    version: '1.2.1',\n    creator: 'Data Science Team',\n    avatarUrl: '/agents/data-analyst.svg',\n    tags: ['predictions', 'forecasting', 'analytics', 'trends'],\n    relatedAgentIds: ['1', '7']\n  },\n  {\n    id: '9',\n    name: 'API Documentation Generator',\n    description: 'Automatically generate comprehensive API documentation from your code.',\n    longDescription: 'The API Documentation Generator analyzes your API code and generates clear, comprehensive documentation. It can create reference docs, guides, examples, and interactive API explorers to help developers understand and use your APIs effectively.',\n    category: 'Development',\n    capabilities: [\n      'API reference generation',\n      'Code example creation',\n      'Interactive API explorer',\n      'Multiple format support',\n      'Versioning assistance',\n      'Consistency checking'\n    ],\n    usageCount: 623,\n    isNew: false,\n    isFeatured: false,\n    createdAt: '2023-10-08',\n    updatedAt: '2024-02-12',\n    version: '2.1.0',\n    creator: 'Engineering Team',\n    avatarUrl: '/agents/code-assistant.svg',\n    tags: ['documentation', 'API', 'development', 'reference'],\n    relatedAgentIds: ['2', '10']\n  },\n  {\n    id: '10',\n    name: 'Code Reviewer',\n    description: 'Get detailed code reviews with suggestions for improvements and best practices.',\n    longDescription: 'The Code Reviewer agent analyzes your code for bugs, security issues, performance problems, and adherence to best practices. It provides detailed feedback with specific suggestions for improvements and explanations of potential issues.',\n    category: 'Development',\n    capabilities: [\n      'Bug detection',\n      'Security vulnerability scanning',\n      'Performance optimization',\n      'Best practice enforcement',\n      'Code style consistency',\n      'Refactoring suggestions'\n    ],\n    usageCount: 1876,\n    isNew: false,\n    isFeatured: false,\n    createdAt: '2023-09-20',\n    updatedAt: '2024-03-05',\n    version: '2.4.0',\n    creator: 'Engineering Team',\n    avatarUrl: '/agents/code-assistant.svg',\n    tags: ['code review', 'quality', 'security', 'best practices'],\n    relatedAgentIds: ['2', '9']\n  },\n  {\n    id: '11',\n    name: 'Literature Review Assistant',\n    description: 'Compile comprehensive literature reviews on any research topic.',\n    longDescription: 'The Literature Review Assistant helps researchers compile thorough literature reviews by finding relevant papers, organizing them by themes, identifying key findings, and highlighting research gaps. It can save weeks of manual research work.',\n    category: 'Research',\n    capabilities: [\n      'Research paper discovery',\n      'Thematic organization',\n      'Gap analysis',\n      'Citation management',\n      'Summary generation',\n      'Trend identification'\n    ],\n    usageCount: 542,\n    isNew: true,\n    isFeatured: false,\n    createdAt: '2024-02-15',\n    updatedAt: '2024-03-18',\n    version: '1.0.2',\n    creator: 'Research & Development',\n    avatarUrl: '/agents/research-companion.svg',\n    tags: ['literature review', 'research', 'academic', 'papers'],\n    relatedAgentIds: ['3']\n  },\n  {\n    id: '12',\n    name: 'Project Manager Assistant',\n    description: 'Track projects, manage tasks, and coordinate team activities efficiently.',\n    longDescription: 'The Project Manager Assistant helps you plan, track, and manage projects from start to finish. It can create project timelines, assign and track tasks, identify risks, generate status reports, and facilitate team communication to keep your projects on track.',\n    category: 'Productivity',\n    capabilities: [\n      'Project planning',\n      'Task management',\n      'Timeline creation',\n      'Risk assessment',\n      'Status reporting',\n      'Resource allocation'\n    ],\n    usageCount: 1432,\n    isNew: false,\n    isFeatured: false,\n    createdAt: '2023-08-25',\n    updatedAt: '2024-02-20',\n    version: '2.2.0',\n    creator: 'Productivity Team',\n    avatarUrl: '/agents/meeting-summarizer.svg',\n    tags: ['project management', 'tasks', 'planning', 'coordination'],\n    relatedAgentIds: ['4', '13']\n  },\n  {\n    id: '13',\n    name: 'Content Creator',\n    description: 'Generate high-quality content for blogs, social media, and marketing materials.',\n    longDescription: 'The Content Creator agent helps you produce engaging content for various platforms. Provide a topic and target audience, and it will generate blog posts, social media updates, marketing copy, and more, tailored to your brand voice and content strategy.',\n    category: 'Marketing',\n    capabilities: [\n      'Blog post generation',\n      'Social media content creation',\n      'Marketing copy writing',\n      'SEO optimization',\n      'Brand voice consistency',\n      'Content strategy alignment'\n    ],\n    usageCount: 2187,\n    isNew: false,\n    isFeatured: false,\n    createdAt: '2023-07-12',\n    updatedAt: '2024-03-08',\n    version: '3.0.1',\n    creator: 'Marketing Team',\n    avatarUrl: '/agents/presentation-creator.svg',\n    tags: ['content', 'writing', 'marketing', 'social media'],\n    relatedAgentIds: ['6', '12']\n  }\n];\n\n// Mock data for sessions\nconst sessions: Session[] = [\n  {\n    id: 'session1',\n    agentId: '2',\n    title: 'JavaScript Debugging Help',\n    messages: [\n      {\n        id: 'msg1',\n        content: 'Hello! How can I help you with coding today?',\n        role: 'assistant',\n        timestamp: Date.now() - 3600000\n      },\n      {\n        id: 'msg2',\n        content: 'I have a bug in my JavaScript code. The event listener is not working.',\n        role: 'user',\n        timestamp: Date.now() - 3500000\n      },\n      {\n        id: 'msg3',\n        content: 'Let\\'s take a look. Can you share the code that\\'s not working?',\n        role: 'assistant',\n        timestamp: Date.now() - 3400000\n      }\n    ],\n    createdAt: Date.now() - 3600000,\n    updatedAt: Date.now() - 3400000,\n    isSaved: true\n  },\n  {\n    id: 'session2',\n    agentId: '1',\n    title: 'Sales Data Analysis',\n    messages: [\n      {\n        id: 'msg1',\n        content: 'Welcome to Data Analyst. What data would you like to analyze today?',\n        role: 'assistant',\n        timestamp: Date.now() - 86400000\n      },\n      {\n        id: 'msg2',\n        content: 'I need to analyze our Q1 sales data to find trends.',\n        role: 'user',\n        timestamp: Date.now() - 86300000\n      },\n      {\n        id: 'msg3',\n        content: 'I can help with that. Do you have the sales data file you can upload?',\n        role: 'assistant',\n        timestamp: Date.now() - 86200000\n      }\n    ],\n    createdAt: Date.now() - 86400000,\n    updatedAt: Date.now() - 86200000,\n    isSaved: true\n  }\n];\n\n// Function to get all agents\nexport function getAllAgents(): Agent[] {\n  return agents;\n}\n\n// Function to get an agent by ID\nexport function getAgentById(id: string): Agent | undefined {\n  return agents.find(agent => agent.id === id);\n}\n\n// Function to get featured agents\nexport function getFeaturedAgents(): Agent[] {\n  return agents.filter(agent => agent.isFeatured);\n}\n\n// Function to get new agents\nexport function getNewAgents(): Agent[] {\n  return agents.filter(agent => agent.isNew);\n}\n\n// Function to get agents by category\nexport function getAgentsByCategory(category: string): Agent[] {\n  return agents.filter(agent => agent.category === category);\n}\n\n// Function to get agents by tag\nexport function getAgentsByTag(tag: string): Agent[] {\n  return agents.filter(agent => agent.tags?.includes(tag));\n}\n\n// Function to get all unique categories\nexport function getAllCategories(): string[] {\n  const categories = new Set<string>();\n  agents.forEach(agent => categories.add(agent.category));\n  return Array.from(categories).sort();\n}\n\n// Function to get all unique tags\nexport function getAllTags(): string[] {\n  const tags = new Set<string>();\n  agents.forEach(agent => {\n    agent.tags?.forEach(tag => tags.add(tag));\n  });\n  return Array.from(tags).sort();\n}\n\n// Function to get related agents\nexport function getRelatedAgents(agentId: string): Agent[] {\n  const agent = getAgentById(agentId);\n  if (!agent || !agent.relatedAgentIds || agent.relatedAgentIds.length === 0) {\n    return [];\n  }\n\n  return agent.relatedAgentIds\n    .map(id => getAgentById(id))\n    .filter((agent): agent is Agent => agent !== undefined);\n}\n\n// Function to get agents by IDs\nexport function getAgentsByIds(ids: string[]): Agent[] {\n  return ids\n    .map(id => getAgentById(id))\n    .filter((agent): agent is Agent => agent !== undefined);\n}\n\n// Function to search agents\nexport function searchAgents(query: string): Agent[] {\n  if (!query) return agents;\n\n  const lowercaseQuery = query.toLowerCase();\n  return agents.filter(agent =>\n    agent.name.toLowerCase().includes(lowercaseQuery) ||\n    agent.description.toLowerCase().includes(lowercaseQuery) ||\n    agent.category.toLowerCase().includes(lowercaseQuery) ||\n    agent.tags?.some(tag => tag.toLowerCase().includes(lowercaseQuery)) ||\n    agent.capabilities.some(capability => capability.toLowerCase().includes(lowercaseQuery))\n  );\n}\n\n// Function to get sessions for an agent\nexport function getSessionsForAgent(agentId: string): Session[] {\n  return sessions.filter(session => session.agentId === agentId);\n}\n\n// Function to get a session by ID\nexport function getSessionById(sessionId: string): Session | undefined {\n  return sessions.find(session => session.id === sessionId);\n}\n\n// Function to create a new message\nexport function createMessage(content: string, role: 'user' | 'assistant' | 'system'): Message {\n  return {\n    id: `msg_${Date.now()}`,\n    content,\n    role,\n    timestamp: Date.now()\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAEA,uBAAuB;AACvB,MAAM,SAAkB;IACtB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAQ;YAAa;YAAiB;SAAa;QAC1D,iBAAiB;YAAC;YAAK;YAAK;SAAI;IAClC;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAU;YAAe;YAAe;SAAY;QAC3D,iBAAiB;YAAC;YAAK;SAAK;IAC9B;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAY;YAAU;YAAY;SAAa;QACtD,iBAAiB;YAAC;YAAM;SAAI;IAC9B;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAY;YAAgB;YAAiB;SAAY;QAChE,iBAAiB;YAAC;YAAK;YAAK;SAAK;IACnC;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAa;YAAY;YAAc;SAAY;QAC1D,iBAAiB;YAAC;YAAK;YAAK;SAAI;IAClC;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAiB;YAAU;YAAU;SAAU;QACtD,iBAAiB;YAAC;YAAK;SAAK;IAC9B;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAiB;YAAU;YAAU;SAAO;QACnD,iBAAiB;YAAC;YAAK;SAAI;IAC7B;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAe;YAAe;YAAa;SAAS;QAC3D,iBAAiB;YAAC;YAAK;SAAI;IAC7B;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAiB;YAAO;YAAe;SAAY;QAC1D,iBAAiB;YAAC;YAAK;SAAK;IAC9B;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAe;YAAW;YAAY;SAAiB;QAC9D,iBAAiB;YAAC;YAAK;SAAI;IAC7B;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAqB;YAAY;YAAY;SAAS;QAC7D,iBAAiB;YAAC;SAAI;IACxB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAsB;YAAS;YAAY;SAAe;QACjE,iBAAiB;YAAC;YAAK;SAAK;IAC9B;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,OAAO;QACP,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,SAAS;QACT,WAAW;QACX,MAAM;YAAC;YAAW;YAAW;YAAa;SAAe;QACzD,iBAAiB;YAAC;YAAK;SAAK;IAC9B;CACD;AAED,yBAAyB;AACzB,MAAM,WAAsB;IAC1B;QACE,IAAI;QACJ,SAAS;QACT,OAAO;QACP,UAAU;YACR;gBACE,IAAI;gBACJ,SAAS;gBACT,MAAM;gBACN,WAAW,KAAK,GAAG,KAAK;YAC1B;YACA;gBACE,IAAI;gBACJ,SAAS;gBACT,MAAM;gBACN,WAAW,KAAK,GAAG,KAAK;YAC1B;YACA;gBACE,IAAI;gBACJ,SAAS;gBACT,MAAM;gBACN,WAAW,KAAK,GAAG,KAAK;YAC1B;SACD;QACD,WAAW,KAAK,GAAG,KAAK;QACxB,WAAW,KAAK,GAAG,KAAK;QACxB,SAAS;IACX;IACA;QACE,IAAI;QACJ,SAAS;QACT,OAAO;QACP,UAAU;YACR;gBACE,IAAI;gBACJ,SAAS;gBACT,MAAM;gBACN,WAAW,KAAK,GAAG,KAAK;YAC1B;YACA;gBACE,IAAI;gBACJ,SAAS;gBACT,MAAM;gBACN,WAAW,KAAK,GAAG,KAAK;YAC1B;YACA;gBACE,IAAI;gBACJ,SAAS;gBACT,MAAM;gBACN,WAAW,KAAK,GAAG,KAAK;YAC1B;SACD;QACD,WAAW,KAAK,GAAG,KAAK;QACxB,WAAW,KAAK,GAAG,KAAK;QACxB,SAAS;IACX;CACD;AAGM,SAAS;IACd,OAAO;AACT;AAGO,SAAS,aAAa,EAAU;IACrC,OAAO,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;AAC3C;AAGO,SAAS;IACd,OAAO,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,UAAU;AAChD;AAGO,SAAS;IACd,OAAO,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,KAAK;AAC3C;AAGO,SAAS,oBAAoB,QAAgB;IAClD,OAAO,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,QAAQ,KAAK;AACnD;AAGO,SAAS,eAAe,GAAW;IACxC,OAAO,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,IAAI,EAAE,SAAS;AACrD;AAGO,SAAS;IACd,MAAM,aAAa,IAAI;IACvB,OAAO,OAAO,CAAC,CAAA,QAAS,WAAW,GAAG,CAAC,MAAM,QAAQ;IACrD,OAAO,MAAM,IAAI,CAAC,YAAY,IAAI;AACpC;AAGO,SAAS;IACd,MAAM,OAAO,IAAI;IACjB,OAAO,OAAO,CAAC,CAAA;QACb,MAAM,IAAI,EAAE,QAAQ,CAAA,MAAO,KAAK,GAAG,CAAC;IACtC;IACA,OAAO,MAAM,IAAI,CAAC,MAAM,IAAI;AAC9B;AAGO,SAAS,iBAAiB,OAAe;IAC9C,MAAM,QAAQ,aAAa;IAC3B,IAAI,CAAC,SAAS,CAAC,MAAM,eAAe,IAAI,MAAM,eAAe,CAAC,MAAM,KAAK,GAAG;QAC1E,OAAO,EAAE;IACX;IAEA,OAAO,MAAM,eAAe,CACzB,GAAG,CAAC,CAAA,KAAM,aAAa,KACvB,MAAM,CAAC,CAAC,QAA0B,UAAU;AACjD;AAGO,SAAS,eAAe,GAAa;IAC1C,OAAO,IACJ,GAAG,CAAC,CAAA,KAAM,aAAa,KACvB,MAAM,CAAC,CAAC,QAA0B,UAAU;AACjD;AAGO,SAAS,aAAa,KAAa;IACxC,IAAI,CAAC,OAAO,OAAO;IAEnB,MAAM,iBAAiB,MAAM,WAAW;IACxC,OAAO,OAAO,MAAM,CAAC,CAAA,QACnB,MAAM,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,mBAClC,MAAM,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,mBACzC,MAAM,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,mBACtC,MAAM,IAAI,EAAE,KAAK,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC,oBACnD,MAAM,YAAY,CAAC,IAAI,CAAC,CAAA,aAAc,WAAW,WAAW,GAAG,QAAQ,CAAC;AAE5E;AAGO,SAAS,oBAAoB,OAAe;IACjD,OAAO,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,OAAO,KAAK;AACxD;AAGO,SAAS,eAAe,SAAiB;IAC9C,OAAO,SAAS,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;AACjD;AAGO,SAAS,cAAc,OAAe,EAAE,IAAqC;IAClF,OAAO;QACL,IAAI,CAAC,IAAI,EAAE,KAAK,GAAG,IAAI;QACvB;QACA;QACA,WAAW,KAAK,GAAG;IACrB;AACF", "debugId": null}}, {"offset": {"line": 598, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/page.tsx"], "sourcesContent": ["import Link from \"next/link\";\nimport Image from \"next/image\";\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\nimport {\n  faArrowRight,\n  faGraduationCap,\n  faVideo,\n  faNewspaper,\n  faBlog,\n  faToolbox\n} from \"@fortawesome/free-solid-svg-icons\";\nimport { getAllAgents, getFeaturedAgents, getNewAgents } from \"@/lib/agents\";\nimport { Agent } from \"@/types/agent\";\nimport { getCombinedRecentContent, getCombinedPopularContent } from \"@/data/combined-resources\";\n\nexport default function Home() {\n  // Get agents from our data utility\n  const featuredAgents = getFeaturedAgents();\n  const allAgents = getAllAgents();\n\n  // For recently used, we'll simulate by taking the first 2 agents\n  // In a real app, this would come from user history\n  const recentAgents = allAgents.slice(0, 2);\n\n  // Get new agents\n  const newAgents = getNewAgents();\n\n  return (\n    <div className=\"space-y-10\">\n      {/* Colorful elements to demonstrate frosted glass effect */}\n      <div className=\"absolute top-0 left-0 right-0 -z-10 overflow-hidden\">\n        <div className=\"h-64 w-64 rounded-full bg-blue-500 opacity-20 blur-3xl absolute -top-20 -left-20\"></div>\n        <div className=\"h-64 w-64 rounded-full bg-purple-500 opacity-20 blur-3xl absolute top-10 left-40\"></div>\n        <div className=\"h-64 w-64 rounded-full bg-pink-500 opacity-20 blur-3xl absolute -top-10 right-20\"></div>\n      </div>\n      {/* Welcome section */}\n      <section className=\"rounded-lg bg-gradient-to-r from-blue-600 to-indigo-700 p-8 text-white\">\n        <div className=\"mx-auto max-w-4xl\">\n          <h1 className=\"mb-4 text-3xl font-bold md:text-4xl\">Welcome to our AI Hub</h1>\n          <p className=\"mb-6 text-lg md:text-xl\">\n            Discover and interact with our collection of AI agents designed to help you work more efficiently & Learn about AI tools and services in our learning hub.\n          </p>\n          <div className=\"flex flex-wrap gap-4\">\n            <Link\n              href=\"/browse\"\n              className=\"rounded-md bg-white px-4 py-2 font-medium text-blue-700 hover:bg-blue-50\"\n            >\n              Browse All Agents\n            </Link>\n            <Link\n              href=\"/favorites\"\n              className=\"rounded-md bg-blue-800 px-4 py-2 font-medium text-white hover:bg-blue-900\"\n            >\n              View Favorites\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* Featured agents section */}\n      <section>\n        <div className=\"mb-6 flex items-center justify-between\">\n          <h2 className=\"text-2xl font-bold\">Featured Agents</h2>\n          <Link href=\"/browse\" className=\"text-blue-600 hover:underline dark:text-blue-400\">\n            View all\n          </Link>\n        </div>\n        <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4\">\n          {featuredAgents.map((agent) => (\n            <div\n              key={agent.id}\n              className=\"rounded-lg border border-gray-200 bg-white p-6 shadow-sm transition-shadow hover:shadow-md dark:border-gray-800 dark:bg-gray-900\"\n            >\n              <div className=\"mb-4 flex items-center justify-between\">\n                <span className=\"rounded-full bg-gray-100 px-3 py-1 text-xs font-medium text-gray-800 dark:bg-gray-800 dark:text-gray-200\">\n                  {agent.category}\n                </span>\n                {agent.isNew && (\n                  <span className=\"rounded-full bg-green-100 px-3 py-1 text-xs font-medium text-green-800 dark:bg-green-900/30 dark:text-green-400\">\n                    New\n                  </span>\n                )}\n              </div>\n              <h3 className=\"mb-2 text-xl font-bold\">{agent.name}</h3>\n              <p className=\"mb-4 text-sm text-gray-600 dark:text-gray-400\">{agent.description}</p>\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-xs text-gray-500 dark:text-gray-500\">\n                  {agent.usageCount.toLocaleString()} uses\n                </span>\n                <Link\n                  href={`/agent/${agent.id}`}\n                  className=\"rounded-md bg-blue-600 px-3 py-1.5 text-sm font-medium text-white hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800\"\n                >\n                  View Agent\n                </Link>\n              </div>\n            </div>\n          ))}\n        </div>\n      </section>\n\n      {/* Recent and New sections in a grid */}\n      <div className=\"grid grid-cols-1 gap-10 lg:grid-cols-2\">\n        {/* Recently used section */}\n        <section>\n          <div className=\"mb-6 flex items-center justify-between\">\n            <h2 className=\"text-2xl font-bold\">Recently Used</h2>\n            <Link href=\"/recent\" className=\"text-blue-600 hover:underline dark:text-blue-400\">\n              View all\n            </Link>\n          </div>\n          <div className=\"space-y-4\">\n            {recentAgents.map((agent) => (\n              <div\n                key={agent.id}\n                className=\"rounded-lg border border-gray-200 bg-white p-4 shadow-sm transition-shadow hover:shadow-md dark:border-gray-800 dark:bg-gray-900\"\n              >\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <h3 className=\"font-bold\">{agent.name}</h3>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-400\">{agent.description}</p>\n                  </div>\n                  <Link\n                    href={`/agent/${agent.id}`}\n                    className=\"rounded-md bg-blue-600 px-3 py-1.5 text-sm font-medium text-white hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800\"\n                  >\n                    View Agent\n                  </Link>\n                </div>\n              </div>\n            ))}\n          </div>\n        </section>\n\n        {/* New agents section */}\n        <section>\n          <div className=\"mb-6 flex items-center justify-between\">\n            <h2 className=\"text-2xl font-bold\">New Agents</h2>\n            <Link href=\"/new-releases\" className=\"text-blue-600 hover:underline dark:text-blue-400\">\n              View all\n            </Link>\n          </div>\n          <div className=\"space-y-4\">\n            {newAgents.map((agent) => (\n              <div\n                key={agent.id}\n                className=\"rounded-lg border border-gray-200 bg-white p-4 shadow-sm transition-shadow hover:shadow-md dark:border-gray-800 dark:bg-gray-900\"\n              >\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <div className=\"mb-1 flex items-center gap-2\">\n                      <h3 className=\"font-bold\">{agent.name}</h3>\n                      <span className=\"rounded-full bg-green-100 px-2 py-0.5 text-xs font-medium text-green-800 dark:bg-green-900/30 dark:text-green-400\">\n                        New\n                      </span>\n                    </div>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-400\">{agent.description}</p>\n                  </div>\n                  <Link\n                    href={`/agent/${agent.id}`}\n                    className=\"rounded-md bg-blue-600 px-3 py-1.5 text-sm font-medium text-white hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800\"\n                  >\n                    View Agent\n                  </Link>\n                </div>\n              </div>\n            ))}\n          </div>\n        </section>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AAWA;;;;AAIe,SAAS;IACtB,mCAAmC;IACnC,MAAM,iBAAiB,CAAA,GAAA,oHAAA,CAAA,oBAAiB,AAAD;IACvC,MAAM,YAAY,CAAA,GAAA,oHAAA,CAAA,eAAY,AAAD;IAE7B,iEAAiE;IACjE,mDAAmD;IACnD,MAAM,eAAe,UAAU,KAAK,CAAC,GAAG;IAExC,iBAAiB;IACjB,MAAM,YAAY,CAAA,GAAA,oHAAA,CAAA,eAAY,AAAD;IAE7B,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;sCACpD,8OAAC;4BAAE,WAAU;sCAA0B;;;;;;sCAGvC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;0BAQP,8OAAC;;kCACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAqB;;;;;;0CACnC,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAU,WAAU;0CAAmD;;;;;;;;;;;;kCAIpF,8OAAC;wBAAI,WAAU;kCACZ,eAAe,GAAG,CAAC,CAAC,sBACnB,8OAAC;gCAEC,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DACb,MAAM,QAAQ;;;;;;4CAEhB,MAAM,KAAK,kBACV,8OAAC;gDAAK,WAAU;0DAAkH;;;;;;;;;;;;kDAKtI,8OAAC;wCAAG,WAAU;kDAA0B,MAAM,IAAI;;;;;;kDAClD,8OAAC;wCAAE,WAAU;kDAAiD,MAAM,WAAW;;;;;;kDAC/E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;;oDACb,MAAM,UAAU,CAAC,cAAc;oDAAG;;;;;;;0DAErC,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gDAC1B,WAAU;0DACX;;;;;;;;;;;;;+BAtBE,MAAM,EAAE;;;;;;;;;;;;;;;;0BAgCrB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;;0CACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAqB;;;;;;kDACnC,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAU,WAAU;kDAAmD;;;;;;;;;;;;0CAIpF,8OAAC;gCAAI,WAAU;0CACZ,aAAa,GAAG,CAAC,CAAC,sBACjB,8OAAC;wCAEC,WAAU;kDAEV,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAa,MAAM,IAAI;;;;;;sEACrC,8OAAC;4DAAE,WAAU;sEAA4C,MAAM,WAAW;;;;;;;;;;;;8DAE5E,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;oDAC1B,WAAU;8DACX;;;;;;;;;;;;uCAXE,MAAM,EAAE;;;;;;;;;;;;;;;;kCAqBrB,8OAAC;;0CACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAqB;;;;;;kDACnC,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAgB,WAAU;kDAAmD;;;;;;;;;;;;0CAI1F,8OAAC;gCAAI,WAAU;0CACZ,UAAU,GAAG,CAAC,CAAC,sBACd,8OAAC;wCAEC,WAAU;kDAEV,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAAa,MAAM,IAAI;;;;;;8EACrC,8OAAC;oEAAK,WAAU;8EAAoH;;;;;;;;;;;;sEAItI,8OAAC;4DAAE,WAAU;sEAA4C,MAAM,WAAW;;;;;;;;;;;;8DAE5E,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;oDAC1B,WAAU;8DACX;;;;;;;;;;;;uCAhBE,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2B7B", "debugId": null}}, {"offset": {"line": 1075, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/node_modules/next/dist/client/app-dir/link.js/proxy.cjs"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server.edge\");\n\n__turbopack_context__.n(createClientModuleProxy(\"[project]/node_modules/next/dist/client/app-dir/link.js <module evaluation>\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,sBAAsB,CAAC,CAAC,wBAAwB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1082, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/node_modules/next/dist/client/app-dir/link.js/proxy.cjs"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server.edge\");\n\n__turbopack_context__.n(createClientModuleProxy(\"[project]/node_modules/next/dist/client/app-dir/link.js\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,sBAAsB,CAAC,CAAC,wBAAwB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1090, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/node_modules/next/src/client/app-dir/link.tsx"], "sourcesContent": ["'use client'\n\nimport type { NextRouter } from '../../shared/lib/router/router'\n\nimport React from 'react'\nimport type { UrlObject } from 'url'\nimport { formatUrl } from '../../shared/lib/router/utils/format-url'\nimport { AppRouterContext } from '../../shared/lib/app-router-context.shared-runtime'\nimport type { AppRouterInstance } from '../../shared/lib/app-router-context.shared-runtime'\nimport { PrefetchKind } from '../components/router-reducer/router-reducer-types'\nimport { useMergedRef } from '../use-merged-ref'\nimport { isAbsoluteUrl } from '../../shared/lib/utils'\nimport { addBasePath } from '../add-base-path'\nimport { warnOnce } from '../../shared/lib/utils/warn-once'\nimport {\n  mountLinkInstance,\n  onNavigationIntent,\n  unmountLinkInstance,\n} from '../components/links'\n\ntype Url = string | UrlObject\ntype RequiredKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? never : K\n}[keyof T]\ntype OptionalKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? K : never\n}[keyof T]\n\ntype InternalLinkProps = {\n  /**\n   * **Required**. The path or URL to navigate to. It can also be an object (similar to `URL`).\n   *\n   * @example\n   * ```tsx\n   * // Navigate to /dashboard:\n   * <Link href=\"/dashboard\">Dashboard</Link>\n   *\n   * // Navigate to /about?name=test:\n   * <Link href={{ pathname: '/about', query: { name: 'test' } }}>\n   *   About\n   * </Link>\n   * ```\n   *\n   * @remarks\n   * - For external URLs, use a fully qualified URL such as `https://...`.\n   * - In the App Router, dynamic routes must not include bracketed segments in `href`.\n   */\n  href: Url\n\n  /**\n   * @deprecated v10.0.0: `href` props pointing to a dynamic route are\n   * automatically resolved and no longer require the `as` prop.\n   */\n  as?: Url\n\n  /**\n   * Replace the current `history` state instead of adding a new URL into the stack.\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/about\" replace>\n   *   About (replaces the history state)\n   * </Link>\n   * ```\n   */\n  replace?: boolean\n\n  /**\n   * Whether to override the default scroll behavior. If `true`, Next.js attempts to maintain\n   * the scroll position if the newly navigated page is still visible. If not, it scrolls to the top.\n   *\n   * If `false`, Next.js will not modify the scroll behavior at all.\n   *\n   * @defaultValue `true`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" scroll={false}>\n   *   No auto scroll\n   * </Link>\n   * ```\n   */\n  scroll?: boolean\n\n  /**\n   * Update the path of the current page without rerunning data fetching methods\n   * like `getStaticProps`, `getServerSideProps`, or `getInitialProps`.\n   *\n   * @remarks\n   * `shallow` only applies to the Pages Router. For the App Router, see the\n   * [following documentation](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#using-the-native-history-api).\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/blog\" shallow>\n   *   Shallow navigation\n   * </Link>\n   * ```\n   */\n  shallow?: boolean\n\n  /**\n   * Forces `Link` to pass its `href` to the child component. Useful if the child is a custom\n   * component that wraps an `<a>` tag, or if you're using certain styling libraries.\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" passHref>\n   *   <MyStyledAnchor>Dashboard</MyStyledAnchor>\n   * </Link>\n   * ```\n   */\n  passHref?: boolean\n\n  /**\n   * Prefetch the page in the background.\n   * Any `<Link />` that is in the viewport (initially or through scroll) will be prefetched.\n   * Prefetch can be disabled by passing `prefetch={false}`.\n   *\n   * @remarks\n   * Prefetching is only enabled in production.\n   *\n   * - In the **App Router**:\n   *   - `null` (default): Prefetch behavior depends on static vs dynamic routes:\n   *     - Static routes: fully prefetched\n   *     - Dynamic routes: partial prefetch to the nearest segment with a `loading.js`\n   *   - `true`: Always prefetch the full route and data.\n   *   - `false`: Disable prefetching on both viewport and hover.\n   * - In the **Pages Router**:\n   *   - `true` (default): Prefetches the route and data in the background on viewport or hover.\n   *   - `false`: Prefetch only on hover, not on viewport.\n   *\n   * @defaultValue `true` (Pages Router) or `null` (App Router)\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" prefetch={false}>\n   *   Dashboard\n   * </Link>\n   * ```\n   */\n  prefetch?: boolean | null\n\n  /**\n   * The active locale is automatically prepended in the Pages Router. `locale` allows for providing\n   * a different locale, or can be set to `false` to opt out of automatic locale behavior.\n   *\n   * @remarks\n   * Note: locale only applies in the Pages Router and is ignored in the App Router.\n   *\n   * @example\n   * ```tsx\n   * // Use the 'fr' locale:\n   * <Link href=\"/about\" locale=\"fr\">\n   *   About (French)\n   * </Link>\n   *\n   * // Disable locale prefix:\n   * <Link href=\"/about\" locale={false}>\n   *   About (no locale prefix)\n   * </Link>\n   * ```\n   */\n  locale?: string | false\n\n  /**\n   * Enable legacy link behavior, requiring an `<a>` tag to wrap the child content\n   * if the child is a string or number.\n   *\n   * @defaultValue `false`\n   * @see https://github.com/vercel/next.js/commit/489e65ed98544e69b0afd7e0cfc3f9f6c2b803b7\n   */\n  legacyBehavior?: boolean\n\n  /**\n   * Optional event handler for when the mouse pointer is moved onto the `<Link>`.\n   */\n  onMouseEnter?: React.MouseEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is touched.\n   */\n  onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is clicked.\n   */\n  onClick?: React.MouseEventHandler<HTMLAnchorElement>\n}\n\n// TODO-APP: Include the full set of Anchor props\n// adding this to the publicly exported type currently breaks existing apps\n\n// `RouteInferType` is a stub here to avoid breaking `typedRoutes` when the type\n// isn't generated yet. It will be replaced when the webpack plugin runs.\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport type LinkProps<RouteInferType = any> = InternalLinkProps\ntype LinkPropsRequired = RequiredKeys<LinkProps>\ntype LinkPropsOptional = OptionalKeys<Omit<InternalLinkProps, 'locale'>>\n\nfunction isModifiedEvent(event: React.MouseEvent): boolean {\n  const eventTarget = event.currentTarget as HTMLAnchorElement | SVGAElement\n  const target = eventTarget.getAttribute('target')\n  return (\n    (target && target !== '_self') ||\n    event.metaKey ||\n    event.ctrlKey ||\n    event.shiftKey ||\n    event.altKey || // triggers resource download\n    (event.nativeEvent && event.nativeEvent.which === 2)\n  )\n}\n\nfunction linkClicked(\n  e: React.MouseEvent,\n  router: NextRouter | AppRouterInstance,\n  href: string,\n  as: string,\n  replace?: boolean,\n  shallow?: boolean,\n  scroll?: boolean\n): void {\n  const { nodeName } = e.currentTarget\n\n  // anchors inside an svg have a lowercase nodeName\n  const isAnchorNodeName = nodeName.toUpperCase() === 'A'\n\n  if (isAnchorNodeName && isModifiedEvent(e)) {\n    // ignore click for browser’s default behavior\n    return\n  }\n\n  e.preventDefault()\n\n  const navigate = () => {\n    // If the router is an NextRouter instance it will have `beforePopState`\n    const routerScroll = scroll ?? true\n    if ('beforePopState' in router) {\n      router[replace ? 'replace' : 'push'](href, as, {\n        shallow,\n        scroll: routerScroll,\n      })\n    } else {\n      router[replace ? 'replace' : 'push'](as || href, {\n        scroll: routerScroll,\n      })\n    }\n  }\n\n  React.startTransition(navigate)\n}\n\ntype LinkPropsReal = React.PropsWithChildren<\n  Omit<React.AnchorHTMLAttributes<HTMLAnchorElement>, keyof LinkProps> &\n    LinkProps\n>\n\nfunction formatStringOrUrl(urlObjOrString: UrlObject | string): string {\n  if (typeof urlObjOrString === 'string') {\n    return urlObjOrString\n  }\n\n  return formatUrl(urlObjOrString)\n}\n\n/**\n * A React component that extends the HTML `<a>` element to provide\n * [prefetching](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#2-prefetching)\n * and client-side navigation. This is the primary way to navigate between routes in Next.js.\n *\n * @remarks\n * - Prefetching is only enabled in production.\n *\n * @see https://nextjs.org/docs/app/api-reference/components/link\n */\nconst Link = React.forwardRef<HTMLAnchorElement, LinkPropsReal>(\n  function LinkComponent(props, forwardedRef) {\n    let children: React.ReactNode\n\n    const {\n      href: hrefProp,\n      as: asProp,\n      children: childrenProp,\n      prefetch: prefetchProp = null,\n      passHref,\n      replace,\n      shallow,\n      scroll,\n      onClick,\n      onMouseEnter: onMouseEnterProp,\n      onTouchStart: onTouchStartProp,\n      legacyBehavior = false,\n      ...restProps\n    } = props\n\n    children = childrenProp\n\n    if (\n      legacyBehavior &&\n      (typeof children === 'string' || typeof children === 'number')\n    ) {\n      children = <a>{children}</a>\n    }\n\n    const router = React.useContext(AppRouterContext)\n\n    const prefetchEnabled = prefetchProp !== false\n    /**\n     * The possible states for prefetch are:\n     * - null: this is the default \"auto\" mode, where we will prefetch partially if the link is in the viewport\n     * - true: we will prefetch if the link is visible and prefetch the full page, not just partially\n     * - false: we will not prefetch if in the viewport at all\n     */\n    const appPrefetchKind =\n      prefetchProp === null ? PrefetchKind.AUTO : PrefetchKind.FULL\n\n    if (process.env.NODE_ENV !== 'production') {\n      function createPropError(args: {\n        key: string\n        expected: string\n        actual: string\n      }) {\n        return new Error(\n          `Failed prop type: The prop \\`${args.key}\\` expects a ${args.expected} in \\`<Link>\\`, but got \\`${args.actual}\\` instead.` +\n            (typeof window !== 'undefined'\n              ? \"\\nOpen your browser's console to view the Component stack trace.\"\n              : '')\n        )\n      }\n\n      // TypeScript trick for type-guarding:\n      const requiredPropsGuard: Record<LinkPropsRequired, true> = {\n        href: true,\n      } as const\n      const requiredProps: LinkPropsRequired[] = Object.keys(\n        requiredPropsGuard\n      ) as LinkPropsRequired[]\n      requiredProps.forEach((key: LinkPropsRequired) => {\n        if (key === 'href') {\n          if (\n            props[key] == null ||\n            (typeof props[key] !== 'string' && typeof props[key] !== 'object')\n          ) {\n            throw createPropError({\n              key,\n              expected: '`string` or `object`',\n              actual: props[key] === null ? 'null' : typeof props[key],\n            })\n          }\n        } else {\n          // TypeScript trick for type-guarding:\n          // eslint-disable-next-line @typescript-eslint/no-unused-vars\n          const _: never = key\n        }\n      })\n\n      // TypeScript trick for type-guarding:\n      const optionalPropsGuard: Record<LinkPropsOptional, true> = {\n        as: true,\n        replace: true,\n        scroll: true,\n        shallow: true,\n        passHref: true,\n        prefetch: true,\n        onClick: true,\n        onMouseEnter: true,\n        onTouchStart: true,\n        legacyBehavior: true,\n      } as const\n      const optionalProps: LinkPropsOptional[] = Object.keys(\n        optionalPropsGuard\n      ) as LinkPropsOptional[]\n      optionalProps.forEach((key: LinkPropsOptional) => {\n        const valType = typeof props[key]\n\n        if (key === 'as') {\n          if (props[key] && valType !== 'string' && valType !== 'object') {\n            throw createPropError({\n              key,\n              expected: '`string` or `object`',\n              actual: valType,\n            })\n          }\n        } else if (\n          key === 'onClick' ||\n          key === 'onMouseEnter' ||\n          key === 'onTouchStart'\n        ) {\n          if (props[key] && valType !== 'function') {\n            throw createPropError({\n              key,\n              expected: '`function`',\n              actual: valType,\n            })\n          }\n        } else if (\n          key === 'replace' ||\n          key === 'scroll' ||\n          key === 'shallow' ||\n          key === 'passHref' ||\n          key === 'prefetch' ||\n          key === 'legacyBehavior'\n        ) {\n          if (props[key] != null && valType !== 'boolean') {\n            throw createPropError({\n              key,\n              expected: '`boolean`',\n              actual: valType,\n            })\n          }\n        } else {\n          // TypeScript trick for type-guarding:\n          // eslint-disable-next-line @typescript-eslint/no-unused-vars\n          const _: never = key\n        }\n      })\n    }\n\n    if (process.env.NODE_ENV !== 'production') {\n      if (props.locale) {\n        warnOnce(\n          'The `locale` prop is not supported in `next/link` while using the `app` router. Read more about app router internalization: https://nextjs.org/docs/app/building-your-application/routing/internationalization'\n        )\n      }\n      if (!asProp) {\n        let href: string | undefined\n        if (typeof hrefProp === 'string') {\n          href = hrefProp\n        } else if (\n          typeof hrefProp === 'object' &&\n          typeof hrefProp.pathname === 'string'\n        ) {\n          href = hrefProp.pathname\n        }\n\n        if (href) {\n          const hasDynamicSegment = href\n            .split('/')\n            .some((segment) => segment.startsWith('[') && segment.endsWith(']'))\n\n          if (hasDynamicSegment) {\n            throw new Error(\n              `Dynamic href \\`${href}\\` found in <Link> while using the \\`/app\\` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href`\n            )\n          }\n        }\n      }\n    }\n\n    const { href, as } = React.useMemo(() => {\n      const resolvedHref = formatStringOrUrl(hrefProp)\n      return {\n        href: resolvedHref,\n        as: asProp ? formatStringOrUrl(asProp) : resolvedHref,\n      }\n    }, [hrefProp, asProp])\n\n    // This will return the first child, if multiple are provided it will throw an error\n    let child: any\n    if (legacyBehavior) {\n      if (process.env.NODE_ENV === 'development') {\n        if (onClick) {\n          console.warn(\n            `\"onClick\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link`\n          )\n        }\n        if (onMouseEnterProp) {\n          console.warn(\n            `\"onMouseEnter\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link`\n          )\n        }\n        try {\n          child = React.Children.only(children)\n        } catch (err) {\n          if (!children) {\n            throw new Error(\n              `No children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but one child is required https://nextjs.org/docs/messages/link-no-children`\n            )\n          }\n          throw new Error(\n            `Multiple children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children` +\n              (typeof window !== 'undefined'\n                ? \" \\nOpen your browser's console to view the Component stack trace.\"\n                : '')\n          )\n        }\n      } else {\n        child = React.Children.only(children)\n      }\n    } else {\n      if (process.env.NODE_ENV === 'development') {\n        if ((children as any)?.type === 'a') {\n          throw new Error(\n            'Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor'\n          )\n        }\n      }\n    }\n\n    const childRef: any = legacyBehavior\n      ? child && typeof child === 'object' && child.ref\n      : forwardedRef\n\n    // Use a callback ref to attach an IntersectionObserver to the anchor tag on\n    // mount. In the future we will also use this to keep track of all the\n    // currently mounted <Link> instances, e.g. so we can re-prefetch them after\n    // a revalidation or refresh.\n    const observeLinkVisibilityOnMount = React.useCallback(\n      (element: HTMLAnchorElement | SVGAElement) => {\n        if (prefetchEnabled && router !== null) {\n          mountLinkInstance(element, href, router, appPrefetchKind)\n        }\n        return () => {\n          unmountLinkInstance(element)\n        }\n      },\n      [prefetchEnabled, href, router, appPrefetchKind]\n    )\n\n    const mergedRef = useMergedRef(observeLinkVisibilityOnMount, childRef)\n\n    const childProps: {\n      onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n      onMouseEnter: React.MouseEventHandler<HTMLAnchorElement>\n      onClick: React.MouseEventHandler<HTMLAnchorElement>\n      href?: string\n      ref?: any\n    } = {\n      ref: mergedRef,\n      onClick(e) {\n        if (process.env.NODE_ENV !== 'production') {\n          if (!e) {\n            throw new Error(\n              `Component rendered inside next/link has to pass click event to \"onClick\" prop.`\n            )\n          }\n        }\n\n        if (!legacyBehavior && typeof onClick === 'function') {\n          onClick(e)\n        }\n\n        if (\n          legacyBehavior &&\n          child.props &&\n          typeof child.props.onClick === 'function'\n        ) {\n          child.props.onClick(e)\n        }\n\n        if (!router) {\n          return\n        }\n\n        if (e.defaultPrevented) {\n          return\n        }\n\n        linkClicked(e, router, href, as, replace, shallow, scroll)\n      },\n      onMouseEnter(e) {\n        if (!legacyBehavior && typeof onMouseEnterProp === 'function') {\n          onMouseEnterProp(e)\n        }\n\n        if (\n          legacyBehavior &&\n          child.props &&\n          typeof child.props.onMouseEnter === 'function'\n        ) {\n          child.props.onMouseEnter(e)\n        }\n\n        if (!router) {\n          return\n        }\n\n        if (!prefetchEnabled || process.env.NODE_ENV === 'development') {\n          return\n        }\n\n        onNavigationIntent(e.currentTarget as HTMLAnchorElement | SVGAElement)\n      },\n      onTouchStart: process.env.__NEXT_LINK_NO_TOUCH_START\n        ? undefined\n        : function onTouchStart(e) {\n            if (!legacyBehavior && typeof onTouchStartProp === 'function') {\n              onTouchStartProp(e)\n            }\n\n            if (\n              legacyBehavior &&\n              child.props &&\n              typeof child.props.onTouchStart === 'function'\n            ) {\n              child.props.onTouchStart(e)\n            }\n\n            if (!router) {\n              return\n            }\n\n            if (!prefetchEnabled) {\n              return\n            }\n\n            onNavigationIntent(\n              e.currentTarget as HTMLAnchorElement | SVGAElement\n            )\n          },\n    }\n\n    // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n    // defined, we specify the current 'href', so that repetition is not needed by the user.\n    // If the url is absolute, we can bypass the logic to prepend the basePath.\n    if (isAbsoluteUrl(as)) {\n      childProps.href = as\n    } else if (\n      !legacyBehavior ||\n      passHref ||\n      (child.type === 'a' && !('href' in child.props))\n    ) {\n      childProps.href = addBasePath(as)\n    }\n\n    return legacyBehavior ? (\n      React.cloneElement(child, childProps)\n    ) : (\n      <a {...restProps} {...childProps}>\n        {children}\n      </a>\n    )\n  }\n)\n\nexport default Link\n"], "names": ["isModifiedEvent", "event", "eventTarget", "currentTarget", "target", "getAttribute", "metaKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "nativeEvent", "which", "linkClicked", "e", "router", "href", "as", "replace", "shallow", "scroll", "nodeName", "isAnchorNodeName", "toUpperCase", "preventDefault", "navigate", "routerScroll", "React", "startTransition", "formatStringOrUrl", "urlObjOrString", "formatUrl", "Link", "forwardRef", "LinkComponent", "props", "forwardedRef", "children", "hrefProp", "asProp", "childrenProp", "prefetch", "prefetchProp", "passHref", "onClick", "onMouseEnter", "onMouseEnterProp", "onTouchStart", "onTouchStartProp", "legacyBeh<PERSON>or", "restProps", "a", "useContext", "AppRouterContext", "prefetchEnabled", "appPrefetchKind", "PrefetchKind", "AUTO", "FULL", "process", "env", "NODE_ENV", "createPropError", "args", "Error", "key", "expected", "actual", "window", "requiredPropsGuard", "requiredProps", "Object", "keys", "for<PERSON>ach", "_", "optionalPropsGuard", "optionalProps", "valType", "locale", "warnOnce", "pathname", "hasDynamicSegment", "split", "some", "segment", "startsWith", "endsWith", "useMemo", "resolvedHref", "child", "console", "warn", "Children", "only", "err", "type", "childRef", "ref", "observeLinkVisibilityOnMount", "useCallback", "element", "mountLinkInstance", "unmountLinkInstance", "mergedRef", "useMergedRef", "childProps", "defaultPrevented", "onNavigationIntent", "__NEXT_LINK_NO_TOUCH_START", "undefined", "isAbsoluteUrl", "addBasePath", "cloneElement"], "mappings": "", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1099, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n    } else if (process.env.TURBOPACK) {\n      module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n    } else {\n      module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n    } else if (process.env.TURBOPACK) {\n      module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n    } else {\n      module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAQzC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1CJ,OAAOC,OAAO,GAAGC,QAAQ;QAC3B,OAAO,IAAIL,QAAQC,GAAG,CAACO,SAAS,EAAE;;QAIlC;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1133, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA;YAAA,CAA8C,EAAtB,AAAuB;YAAA;gBAEzG,UAAA,CAAA;gBAAA,QAAA;oBAAA,IAAA,0BAA4D;oBAAA;iBAAA;YAC5D;SAAA,KAAO,MAAMC,cAAc,IAAIX,mBAAmB;;KAChDY,YAAY;cACVC,IAAAA,EAAMZ;YAAAA,MAAAA,CAAUa,QAAQ;iBACxBC,MAAM,QAAA;wBAAA;4BACNC,KAAAA,CAAAA,GAAAA,4MAAAA,CAAAA,KAAU,iBAAA,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACV,OAAA,GAAA,6SAAA,CAAA,UAAA,CAAA,KAAA,CAA2C,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BAC3CC,MAAAA,CAAAA,KAAY,OAAA,CAAA;;qBACZC,UAAU;gBACVC,UAAU,EAAE;UACd;QAAA,UAAA;YAAA,IAAA;YAAA;SAAA;UACAC,UAAU,CAAA;YAAA,IAAA;YAAA;SAAA;cACRC,OAAAA;YAAAA,IAAYnB;YAAAA;SAAAA;UACd,cAAA;YAAA,IAAA;YAAA;SAAA;IACF;CAAA,CAAE", "ignoreList": [0], "debugId": null}}]}