{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/data/resources-original.ts"], "sourcesContent": ["import { Resource, ResourceCategoryInfo } from '@/types/resources';\n\n// Resource categories information\nexport const resourceCategories: ResourceCategoryInfo[] = [\n  {\n    id: 'productivity',\n    name: 'Productivity Tools',\n    description: 'Tools to enhance personal and team productivity, including task management, note-taking, and time tracking.',\n    icon: 'bolt'\n  },\n  {\n    id: 'project-management',\n    name: 'Project Management',\n    description: 'Software and platforms for planning, executing, and monitoring projects of all sizes.',\n    icon: 'tasks'\n  },\n  {\n    id: 'design',\n    name: 'Design Tools',\n    description: 'Resources for UI/UX design, graphic design, prototyping, and visual collaboration.',\n    icon: 'palette'\n  },\n  {\n    id: 'development',\n    name: 'Development Tools',\n    description: 'Tools and services for software development, coding, testing, and deployment.',\n    icon: 'code'\n  },\n  {\n    id: 'research',\n    name: 'Research Tools',\n    description: 'Resources for market research, user research, academic research, and data collection.',\n    icon: 'magnifying-glass-chart'\n  },\n  {\n    id: 'analytics',\n    name: 'Analytics & Data',\n    description: 'Tools for data analysis, visualization, business intelligence, and reporting.',\n    icon: 'chart-line'\n  },\n  {\n    id: 'communication',\n    name: 'Communication',\n    description: 'Platforms for team communication, client meetings, presentations, and email management.',\n    icon: 'comments'\n  },\n  {\n    id: 'collaboration',\n    name: 'Collaboration',\n    description: 'Tools that facilitate teamwork, knowledge sharing, and cross-functional collaboration.',\n    icon: 'users-gear'\n  }\n];\n\n// Sample resources data\nexport const resources: Resource[] = [\n  {\n    id: 'notion',\n    name: 'Notion',\n    description: 'All-in-one workspace for notes, tasks, wikis, and databases.',\n    url: 'https://www.notion.so',\n    category: 'productivity',\n    tags: ['note-taking', 'project-management', 'wiki'],\n    pricing: 'freemium',\n    logoUrl: 'https://www.notion.so/images/favicon.ico'\n  },\n  {\n    id: 'figma',\n    name: 'Figma',\n    description: 'Collaborative interface design tool for teams.',\n    url: 'https://www.figma.com',\n    category: 'design',\n    tags: ['ui-design', 'prototyping', 'collaboration'],\n    pricing: 'freemium',\n    logoUrl: 'https://static.figma.com/app/icon/1/favicon.png'\n  },\n  {\n    id: 'vscode',\n    name: 'Visual Studio Code',\n    description: 'Free, open-source code editor with powerful development features.',\n    url: 'https://code.visualstudio.com',\n    category: 'development',\n    tags: ['code-editor', 'debugging', 'extensions'],\n    pricing: 'free',\n    logoUrl: 'https://code.visualstudio.com/favicon.ico'\n  },\n  {\n    id: 'slack',\n    name: 'Slack',\n    description: 'Channel-based messaging platform for teams and workplaces.',\n    url: 'https://slack.com',\n    category: 'communication',\n    tags: ['messaging', 'team-communication', 'integrations'],\n    pricing: 'freemium',\n    logoUrl: 'https://a.slack-edge.com/80588/marketing/img/meta/favicon-32.png'\n  },\n  {\n    id: 'trello',\n    name: 'Trello',\n    description: 'Visual tool for organizing work with boards, lists, and cards.',\n    url: 'https://trello.com',\n    category: 'project-management',\n    tags: ['kanban', 'task-management', 'collaboration'],\n    pricing: 'freemium',\n    logoUrl: 'https://a.trellocdn.com/prgb/dist/images/ios/apple-touch-icon-152x152-precomposed.0307bc39ec6c9ff499c8.png'\n  },\n  {\n    id: 'google-analytics',\n    name: 'Google Analytics',\n    description: 'Web analytics service that tracks and reports website traffic.',\n    url: 'https://analytics.google.com',\n    category: 'analytics',\n    tags: ['web-analytics', 'reporting', 'user-behavior'],\n    pricing: 'freemium',\n    logoUrl: 'https://www.google.com/analytics/images/ga_icon_256.png'\n  },\n  {\n    id: 'miro',\n    name: 'Miro',\n    description: 'Online collaborative whiteboard platform for teams.',\n    url: 'https://miro.com',\n    category: 'collaboration',\n    tags: ['whiteboard', 'brainstorming', 'visual-collaboration'],\n    pricing: 'freemium',\n    logoUrl: 'https://miro.com/static/images/favicon/apple-touch-icon.png'\n  },\n  {\n    id: 'google-scholar',\n    name: 'Google Scholar',\n    description: 'Search engine for academic literature and research papers.',\n    url: 'https://scholar.google.com',\n    category: 'research',\n    tags: ['academic-research', 'citations', 'literature-search'],\n    pricing: 'free',\n    logoUrl: 'https://scholar.google.com/favicon.ico'\n  }\n];\n"], "names": [], "mappings": ";;;;AAGO,MAAM,qBAA6C;IACxD;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;IACR;CACD;AAGM,MAAM,YAAwB;IACnC;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAe;YAAsB;SAAO;QACnD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAa;YAAe;SAAgB;QACnD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAe;YAAa;SAAa;QAChD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAa;YAAsB;SAAe;QACzD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAU;YAAmB;SAAgB;QACpD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAiB;YAAa;SAAgB;QACrD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAc;YAAiB;SAAuB;QAC7D,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAqB;YAAa;SAAoB;QAC7D,SAAS;QACT,SAAS;IACX;CACD", "debugId": null}}, {"offset": {"line": 181, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/data/resources.ts"], "sourcesContent": ["import { Resource, ResourceCategoryInfo } from '@/types/resources';\nimport { resources as existingResources, resourceCategories } from './resources-original';\n\n// New resources from the AI Presentation Series Summary PDF\nconst newResources: Resource[] = [\n  // Productivity Tools\n  {\n    id: 'perplexity-ai',\n    name: 'Perplexity AI',\n    description: 'An AI-powered search engine with a chatbot interface that understands and responds to user queries using GPT-3.5.',\n    url: 'https://www.perplexity.ai/',\n    category: 'productivity',\n    tags: ['search-engine', 'ai-assistant', 'research'],\n    pricing: 'freemium',\n    logoUrl: 'https://www.perplexity.ai/favicon.ico'\n  },\n  {\n    id: 'claude-ai',\n    name: 'Claude <PERSON>',\n    description: 'Analyzes and suggests improvements for very long content, similar to ChatGPT but with enhanced capabilities for handling lengthy documents.',\n    url: 'https://claude.ai/',\n    category: 'productivity',\n    tags: ['ai-assistant', 'content-generation', 'document-analysis'],\n    pricing: 'freemium',\n    logoUrl: 'https://claude.ai/favicon.ico'\n  },\n  {\n    id: 'fathom-ai',\n    name: 'Fathom AI',\n    description: 'Zoom app that records, transcribes, and highlights key moments from calls, making meeting follow-up more efficient.',\n    url: 'https://fathom.video/',\n    category: 'productivity',\n    tags: ['meeting-assistant', 'transcription', 'video-conferencing'],\n    pricing: 'freemium',\n    logoUrl: 'https://fathom.video/favicon.ico'\n  },\n  {\n    id: 'plaud-ai',\n    name: 'Plaud.ai',\n    description: 'AI tool for note-taking and transcription that helps capture and organize information from meetings and conversations.',\n    url: 'https://www.plaud.ai/',\n    category: 'productivity',\n    tags: ['note-taking', 'transcription', 'meeting-assistant'],\n    pricing: 'freemium',\n    logoUrl: 'https://www.plaud.ai/favicon.ico'\n  },\n  {\n    id: 'whisper',\n    name: 'Whisper',\n    description: 'Converts audio to text and vice versa using AI, providing accurate transcription for various languages and accents.',\n    url: 'https://openai.com/index/whisper/',\n    category: 'productivity',\n    tags: ['transcription', 'audio-processing', 'speech-to-text'],\n    pricing: 'freemium',\n    logoUrl: 'https://openai.com/favicon.ico'\n  },\n  {\n    id: 'notebooklm',\n    name: 'NotebookLM',\n    description: 'Tool for data management and note-taking that uses AI to help organize and retrieve information efficiently.',\n    url: 'https://notebooklm.google.com',\n    category: 'productivity',\n    tags: ['note-taking', 'knowledge-management', 'ai-organization'],\n    pricing: 'free',\n    logoUrl: 'https://notebooklm.google.com/favicon.ico'\n  },\n  {\n    id: 'deepl',\n    name: 'DeepL',\n    description: 'Translation tool for efficient and accurate translations between multiple languages, powered by advanced AI.',\n    url: 'https://www.deepl.com',\n    category: 'productivity',\n    tags: ['translation', 'language-processing', 'communication'],\n    pricing: 'freemium',\n    logoUrl: 'https://www.deepl.com/favicon.ico'\n  },\n\n  // Design Tools\n  {\n    id: 'midjourney',\n    name: 'Midjourney',\n    description: 'Generates images from descriptive language, similar to DALL-E and Stable Diffusion, with a focus on artistic quality.',\n    url: 'https://www.midjourney.com/',\n    category: 'design',\n    tags: ['image-generation', 'ai-art', 'creative-tools'],\n    pricing: 'paid',\n    logoUrl: 'https://www.midjourney.com/favicon.ico'\n  },\n  {\n    id: 'bing-images',\n    name: 'Bing Image Creator',\n    description: 'Provides tools for creating images from text descriptions, potentially offering more features than Midjourney.',\n    url: 'https://www.bing.com/images/create',\n    category: 'design',\n    tags: ['image-generation', 'ai-art', 'creative-tools'],\n    pricing: 'free',\n    logoUrl: 'https://www.bing.com/favicon.ico'\n  },\n  {\n    id: 'meta-imagine',\n    name: 'Meta Imagine',\n    description: 'Uses models to generate images from textual prompts provided by the user, created by Meta (Facebook).',\n    url: 'https://imagine.meta.com',\n    category: 'design',\n    tags: ['image-generation', 'ai-art', 'creative-tools'],\n    pricing: 'free',\n    logoUrl: 'https://imagine.meta.com/favicon.ico'\n  },\n  {\n    id: 'designer-microsoft',\n    name: 'Microsoft Designer',\n    description: 'Microsoft\\'s answer to Canva, focusing on design solutions with AI-powered features for creating professional graphics.',\n    url: 'https://designer.microsoft.com/',\n    category: 'design',\n    tags: ['graphic-design', 'presentation', 'marketing-materials'],\n    pricing: 'freemium',\n    logoUrl: 'https://designer.microsoft.com/favicon.ico'\n  },\n  {\n    id: 'runway',\n    name: 'Runway',\n    description: 'A creative platform for video production and editing with AI-powered tools for visual effects and content creation.',\n    url: 'https://app.runwayml.com/',\n    category: 'design',\n    tags: ['video-editing', 'visual-effects', 'content-creation'],\n    pricing: 'freemium',\n    logoUrl: 'https://app.runwayml.com/favicon.ico'\n  },\n  {\n    id: 'clipdrop',\n    name: 'Clipdrop',\n    description: 'Developed by Stability AI, for various image and video editing tasks with AI-powered features.',\n    url: 'https://clipdrop.co/',\n    category: 'design',\n    tags: ['image-editing', 'video-editing', 'content-creation'],\n    pricing: 'freemium',\n    logoUrl: 'https://clipdrop.co/favicon.ico'\n  },\n  {\n    id: 'interior-ai',\n    name: 'Interior AI',\n    description: 'Interior design tool for generating and visualizing room layouts using AI to create realistic interior designs.',\n    url: 'https://interiorai.com/',\n    category: 'design',\n    tags: ['interior-design', 'visualization', 'architecture'],\n    pricing: 'freemium',\n    logoUrl: 'https://interiorai.com/favicon.ico'\n  },\n  {\n    id: 'meshy-ai',\n    name: 'Meshy.ai',\n    description: 'For 3D modeling, used in architecture and design to create and manipulate 3D objects with AI assistance.',\n    url: 'https://www.meshy.ai',\n    category: 'design',\n    tags: ['3d-modeling', 'architecture', 'design'],\n    pricing: 'freemium',\n    logoUrl: 'https://www.meshy.ai/favicon.ico'\n  },\n  {\n    id: 'mnml-ai',\n    name: 'MNML AI',\n    description: 'Architecture design assistant that helps create minimalist architectural designs with AI guidance.',\n    url: 'https://mnml.ai',\n    category: 'design',\n    tags: ['architecture', 'design', 'minimalism'],\n    pricing: 'freemium',\n    logoUrl: 'https://mnml.ai/favicon.ico'\n  },\n  {\n    id: 'ulama-tech',\n    name: 'Ulama.tech',\n    description: 'For architectural design, specifically for structure and planning with AI-powered tools.',\n    url: 'http://ulama.tech',\n    category: 'design',\n    tags: ['architecture', 'structural-design', 'planning'],\n    pricing: 'freemium',\n    logoUrl: 'http://ulama.tech/favicon.ico'\n  },\n  {\n    id: 'weshop-ai',\n    name: 'WeShop',\n    description: 'Produce high-quality product images inexpensively and quickly using AI-generated visuals.',\n    url: 'https://www.weshop.ai/',\n    category: 'design',\n    tags: ['product-photography', 'e-commerce', 'marketing'],\n    pricing: 'paid',\n    logoUrl: 'https://www.weshop.ai/favicon.ico'\n  },\n  {\n    id: 'botika',\n    name: 'Botika',\n    description: 'Helps fashion retailers save on photo costs and boost sales using AI-generated models for product visualization.',\n    url: 'https://botika.io/',\n    category: 'design',\n    tags: ['fashion', 'e-commerce', 'product-visualization'],\n    pricing: 'paid',\n    logoUrl: 'https://botika.io/favicon.ico'\n  },\n  {\n    id: 'flux-ai',\n    name: 'Flux AI',\n    description: 'Enables creative image generation and animation with AI-powered tools for designers and artists.',\n    url: 'https://flux-ai.io/',\n    category: 'design',\n    tags: ['image-generation', 'animation', 'creative-tools'],\n    pricing: 'freemium',\n    logoUrl: 'https://flux-ai.io/favicon.ico'\n  },\n\n  // Development Tools\n  {\n    id: '10web',\n    name: '10Web',\n    description: 'An AI-Powered WordPress Platform for website development and management with automated features.',\n    url: 'https://10web.io/',\n    category: 'development',\n    tags: ['wordpress', 'website-builder', 'automation'],\n    pricing: 'paid',\n    logoUrl: 'https://10web.io/favicon.ico'\n  },\n  {\n    id: 'framer',\n    name: 'Framer',\n    description: 'A tool for building interactive websites and web applications with a focus on design and user experience.',\n    url: 'https://framer.com/',\n    category: 'development',\n    tags: ['website-builder', 'prototyping', 'design'],\n    pricing: 'freemium',\n    logoUrl: 'https://framer.com/favicon.ico'\n  },\n  {\n    id: 'github-copilot',\n    name: 'GitHub Copilot',\n    description: 'AI pair programmer that assists in code completion and suggestions within code editors, powered by OpenAI Codex.',\n    url: 'https://github.com/features/copilot',\n    category: 'development',\n    tags: ['coding-assistant', 'pair-programming', 'code-completion'],\n    pricing: 'paid',\n    logoUrl: 'https://github.com/favicon.ico'\n  },\n  {\n    id: 'github-spark',\n    name: 'GitHub Spark',\n    description: 'AI tool for building web applications using natural language, aiming to lower the barrier to software development.',\n    url: 'https://github.com/features',\n    category: 'development',\n    tags: ['web-development', 'no-code', 'ai-coding'],\n    pricing: 'paid',\n    logoUrl: 'https://github.com/favicon.ico'\n  },\n  {\n    id: 'langchain',\n    name: 'LangChain',\n    description: 'Builds AI-powered applications by connecting large language models with external data sources and tools.',\n    url: 'https://www.langchain.com/',\n    category: 'development',\n    tags: ['llm-framework', 'ai-development', 'integration'],\n    pricing: 'free',\n    logoUrl: 'https://www.langchain.com/favicon.ico'\n  },\n\n  // Communication\n  {\n    id: 'heygen',\n    name: 'HeyGen',\n    description: 'Produces studio quality videos with AI-generated avatars and voices for professional communication.',\n    url: 'https://www.heygen.com',\n    category: 'communication',\n    tags: ['video-creation', 'avatars', 'presentation'],\n    pricing: 'paid',\n    logoUrl: 'https://www.heygen.com/favicon.ico'\n  },\n  {\n    id: 'beautiful-ai',\n    name: 'Beautiful.ai',\n    description: 'Presentation tool that simplifies the creation of professional presentations using AI to handle design elements.',\n    url: 'http://beautiful.ai',\n    category: 'communication',\n    tags: ['presentation', 'slides', 'design'],\n    pricing: 'freemium',\n    logoUrl: 'http://beautiful.ai/favicon.ico'\n  },\n  {\n    id: 'gamma-app',\n    name: 'Gamma.app',\n    description: 'Creates engaging presentations by transforming ideas into visually appealing slides with AI assistance.',\n    url: 'https://gamma.app/',\n    category: 'communication',\n    tags: ['presentation', 'slides', 'design'],\n    pricing: 'freemium',\n    logoUrl: 'https://gamma.app/favicon.ico'\n  },\n  {\n    id: 'decktopus',\n    name: 'Decktopus',\n    description: 'An AI-powered tool that assists in creating presentation starting points with professional templates and designs.',\n    url: 'https://decktopus.com',\n    category: 'communication',\n    tags: ['presentation', 'slides', 'design'],\n    pricing: 'freemium',\n    logoUrl: 'https://decktopus.com/favicon.ico'\n  },\n  {\n    id: 'opus',\n    name: 'Opus',\n    description: 'Transforms long videos into short clips with a single click using generative AI for more effective communication.',\n    url: 'https://www.opus.pro/',\n    category: 'communication',\n    tags: ['video-editing', 'content-creation', 'summarization'],\n    pricing: 'paid',\n    logoUrl: 'https://www.opus.pro/favicon.ico'\n  },\n  {\n    id: 'vapi',\n    name: 'VAPI',\n    description: 'Builds and optimizes voice agents for customer service and communication applications.',\n    url: 'https://vapi.ai/',\n    category: 'communication',\n    tags: ['voice-agents', 'customer-service', 'automation'],\n    pricing: 'paid',\n    logoUrl: 'https://vapi.ai/favicon.ico'\n  },\n  {\n    id: 'sora',\n    name: 'Sora',\n    description: 'Turn text instructions into detailed video scenes for communication and presentation purposes.',\n    url: 'https://openai.com/sora',\n    category: 'communication',\n    tags: ['video-generation', 'content-creation', 'presentation'],\n    pricing: 'paid',\n    logoUrl: 'https://openai.com/favicon.ico'\n  },\n\n  // Collaboration\n  {\n    id: 'rancelab',\n    name: 'RanceLab',\n    description: 'Integrates WhatsApp with other platforms for better team collaboration and customer communication.',\n    url: 'https://www.rancelab.com/',\n    category: 'collaboration',\n    tags: ['whatsapp-integration', 'communication', 'customer-service'],\n    pricing: 'paid',\n    logoUrl: 'https://www.rancelab.com/favicon.ico'\n  },\n  {\n    id: 'lawgeex',\n    name: 'LawGeex',\n    description: 'Legal automation platform that uses AI to review contracts and facilitate legal collaboration.',\n    url: 'https://www.lawgeex.com/',\n    category: 'collaboration',\n    tags: ['legal', 'contract-review', 'automation'],\n    pricing: 'paid',\n    logoUrl: 'https://www.lawgeex.com/favicon.ico'\n  },\n\n  // Analytics & Data\n  {\n    id: 'browse-ai',\n    name: 'BrowseAI',\n    description: 'Facilitates data extraction and monitoring from websites for easy data acquisition and analysis.',\n    url: 'https://www.browse.ai/',\n    category: 'analytics',\n    tags: ['data-extraction', 'web-scraping', 'monitoring'],\n    pricing: 'freemium',\n    logoUrl: 'https://www.browse.ai/favicon.ico'\n  },\n  {\n    id: 'relevance-ai',\n    name: 'Relevance AI',\n    description: 'Platform providing AI-driven insights and analytics to enhance business decision-making.',\n    url: 'https://relevanceai.com/',\n    category: 'analytics',\n    tags: ['data-analysis', 'insights', 'business-intelligence'],\n    pricing: 'freemium',\n    logoUrl: 'https://relevanceai.com/favicon.ico'\n  },\n\n  // Project Management\n  {\n    id: 'make-com',\n    name: 'Make.com',\n    description: 'Automation platform for streamlining workflows and processes using AI to connect apps and automate tasks.',\n    url: 'https://make.com',\n    category: 'project-management',\n    tags: ['automation', 'workflow', 'integration'],\n    pricing: 'freemium',\n    logoUrl: 'https://make.com/favicon.ico'\n  },\n  {\n    id: 'zapier-central',\n    name: 'Zapier Central',\n    description: 'Automating tasks and workflows using AI-powered integrations between different applications and services.',\n    url: 'https://zapier.com/central',\n    category: 'project-management',\n    tags: ['automation', 'workflow', 'integration'],\n    pricing: 'freemium',\n    logoUrl: 'https://zapier.com/favicon.ico'\n  },\n  {\n    id: 'agents-ai',\n    name: 'Agents.ai',\n    description: 'Professional network of AI agents for business automation and task management.',\n    url: 'https://agents.ai/',\n    category: 'project-management',\n    tags: ['automation', 'ai-agents', 'task-management'],\n    pricing: 'paid',\n    logoUrl: 'https://agents.ai/favicon.ico'\n  },\n  {\n    id: 'napkin-ai',\n    name: 'Napkin.ai',\n    description: 'Useful for generating content, proofreading, and ideation feedback for project planning and documentation.',\n    url: 'http://napkin.ai',\n    category: 'project-management',\n    tags: ['content-generation', 'ideation', 'documentation'],\n    pricing: 'freemium',\n    logoUrl: 'http://napkin.ai/favicon.ico'\n  }\n];\n\n// Combine existing resources with new resources, avoiding duplicates\nconst combinedResources: Resource[] = [\n  ...existingResources,\n  ...newResources.filter(newResource =>\n    !existingResources.some(existingResource =>\n      existingResource.id === newResource.id ||\n      existingResource.name.toLowerCase() === newResource.name.toLowerCase()\n    )\n  )\n];\n\nexport { resourceCategories, combinedResources as resources };\n\n// Helper functions\nexport function getResourcesByCategory(categoryId: string): Resource[] {\n  return combinedResources.filter(resource => resource.category === categoryId);\n}\n\nexport function getCategoryById(categoryId: string): ResourceCategoryInfo | undefined {\n  return resourceCategories.find(category => category.id === categoryId);\n}\n"], "names": [], "mappings": ";;;;;AACA;;AAEA,4DAA4D;AAC5D,MAAM,eAA2B;IAC/B,qBAAqB;IACrB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAiB;YAAgB;SAAW;QACnD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAgB;YAAsB;SAAoB;QACjE,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAqB;YAAiB;SAAqB;QAClE,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAe;YAAiB;SAAoB;QAC3D,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAiB;YAAoB;SAAiB;QAC7D,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAe;YAAwB;SAAkB;QAChE,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAe;YAAuB;SAAgB;QAC7D,SAAS;QACT,SAAS;IACX;IAEA,eAAe;IACf;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAoB;YAAU;SAAiB;QACtD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAoB;YAAU;SAAiB;QACtD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAoB;YAAU;SAAiB;QACtD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAkB;YAAgB;SAAsB;QAC/D,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAiB;YAAkB;SAAmB;QAC7D,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAiB;YAAiB;SAAmB;QAC5D,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAmB;YAAiB;SAAe;QAC1D,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAe;YAAgB;SAAS;QAC/C,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAgB;YAAU;SAAa;QAC9C,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAgB;YAAqB;SAAW;QACvD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAuB;YAAc;SAAY;QACxD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAW;YAAc;SAAwB;QACxD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAoB;YAAa;SAAiB;QACzD,SAAS;QACT,SAAS;IACX;IAEA,oBAAoB;IACpB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAa;YAAmB;SAAa;QACpD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAmB;YAAe;SAAS;QAClD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAoB;YAAoB;SAAkB;QACjE,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAmB;YAAW;SAAY;QACjD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAiB;YAAkB;SAAc;QACxD,SAAS;QACT,SAAS;IACX;IAEA,gBAAgB;IAChB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAkB;YAAW;SAAe;QACnD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAgB;YAAU;SAAS;QAC1C,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAgB;YAAU;SAAS;QAC1C,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAgB;YAAU;SAAS;QAC1C,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAiB;YAAoB;SAAgB;QAC5D,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAgB;YAAoB;SAAa;QACxD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAoB;YAAoB;SAAe;QAC9D,SAAS;QACT,SAAS;IACX;IAEA,gBAAgB;IAChB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAwB;YAAiB;SAAmB;QACnE,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAS;YAAmB;SAAa;QAChD,SAAS;QACT,SAAS;IACX;IAEA,mBAAmB;IACnB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAmB;YAAgB;SAAa;QACvD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAiB;YAAY;SAAwB;QAC5D,SAAS;QACT,SAAS;IACX;IAEA,qBAAqB;IACrB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAc;YAAY;SAAc;QAC/C,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAc;YAAY;SAAc;QAC/C,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAc;YAAa;SAAkB;QACpD,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;YAAC;YAAsB;YAAY;SAAgB;QACzD,SAAS;QACT,SAAS;IACX;CACD;AAED,qEAAqE;AACrE,MAAM,oBAAgC;OACjC,oIAAA,CAAA,YAAiB;OACjB,aAAa,MAAM,CAAC,CAAA,cACrB,CAAC,oIAAA,CAAA,YAAiB,CAAC,IAAI,CAAC,CAAA,mBACtB,iBAAiB,EAAE,KAAK,YAAY,EAAE,IACtC,iBAAiB,IAAI,CAAC,WAAW,OAAO,YAAY,IAAI,CAAC,WAAW;CAGzE;;AAKM,SAAS,uBAAuB,UAAkB;IACvD,OAAO,kBAAkB,MAAM,CAAC,CAAA,WAAY,SAAS,QAAQ,KAAK;AACpE;AAEO,SAAS,gBAAgB,UAAkB;IAChD,OAAO,oIAAA,CAAA,qBAAkB,CAAC,IAAI,CAAC,CAAA,WAAY,SAAS,EAAE,KAAK;AAC7D", "debugId": null}}, {"offset": {"line": 785, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/learning/resources/%5Bcategory%5D/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport { useParams } from 'next/navigation';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { \n  faArrowLeft, \n  faSearch, \n  faExternalLinkAlt, \n  faBolt, \n  faTasks, \n  faPalette, \n  faCode, \n  faMagnifyingGlassChart, \n  faChartLine, \n  faComments, \n  faUsersGear,\n  faFilter,\n  faTag\n} from '@fortawesome/free-solid-svg-icons';\nimport { getResourcesByCategory, getCategoryById } from '@/data/resources';\n\n// Map category IDs to FontAwesome icons\nconst categoryIcons: Record<string, any> = {\n  'productivity': faBolt,\n  'project-management': faTasks,\n  'design': faPalette,\n  'development': faCode,\n  'research': faMagnifyingGlassChart,\n  'analytics': faChartLine,\n  'communication': faComments,\n  'collaboration': faUsersGear\n};\n\nexport default function CategoryPage() {\n  const params = useParams();\n  const categoryId = params.category as string;\n  const category = getCategoryById(categoryId);\n  const resources = getResourcesByCategory(categoryId);\n\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedPricing, setSelectedPricing] = useState<string[]>([]);\n\n  // Get all unique tags from resources in this category\n  const allTags = Array.from(\n    new Set(resources.flatMap(resource => resource.tags || []))\n  ).sort();\n\n  // Get all unique pricing options from resources in this category\n  const allPricingOptions = Array.from(\n    new Set(resources.map(resource => resource.pricing))\n  ).filter(Boolean) as string[];\n\n  // Filter resources based on search term and pricing filter\n  const filteredResources = resources.filter(resource => {\n    const matchesSearch = \n      resource.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      resource.description.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      (resource.tags && resource.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase())));\n    \n    const matchesPricing = \n      selectedPricing.length === 0 || \n      (resource.pricing && selectedPricing.includes(resource.pricing));\n    \n    return matchesSearch && matchesPricing;\n  });\n\n  // Toggle pricing filter\n  const togglePricingFilter = (pricing: string) => {\n    if (selectedPricing.includes(pricing)) {\n      setSelectedPricing(selectedPricing.filter(p => p !== pricing));\n    } else {\n      setSelectedPricing([...selectedPricing, pricing]);\n    }\n  };\n\n  if (!category) {\n    return (\n      <div className=\"flex flex-col items-center justify-center py-12 text-center\">\n        <h2 className=\"mb-4 text-2xl font-bold\">Category Not Found</h2>\n        <p className=\"mb-6 text-gray-600 dark:text-gray-400\">The resource category you're looking for doesn't exist.</p>\n        <Link\n          href=\"/learning/resources\"\n          className=\"flex items-center rounded-md bg-teal-600 px-4 py-2 text-white hover:bg-teal-700\"\n        >\n          <FontAwesomeIcon icon={faArrowLeft} className=\"mr-2 h-4 w-4\" />\n          Back to Resources\n        </Link>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-8\">\n      <div className=\"rounded-lg bg-gradient-to-r from-teal-600 to-emerald-700 p-8 text-white shadow-lg\">\n        <Link\n          href=\"/learning/resources\"\n          className=\"mb-4 inline-flex items-center rounded-md bg-white bg-opacity-20 px-3 py-1 text-sm font-medium text-white backdrop-blur-sm transition hover:bg-opacity-30\"\n        >\n          <FontAwesomeIcon icon={faArrowLeft} className=\"mr-2 h-3 w-3\" />\n          Back to Resources\n        </Link>\n        \n        <div className=\"flex items-center\">\n          <div className=\"mr-4 flex h-16 w-16 items-center justify-center rounded-full bg-white bg-opacity-20 backdrop-blur-sm\">\n            <FontAwesomeIcon icon={categoryIcons[category.id]} className=\"h-8 w-8\" />\n          </div>\n          <div>\n            <h1 className=\"mb-2 text-3xl font-bold\">{category.name}</h1>\n            <p className=\"text-lg\">{category.description}</p>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"flex flex-col space-y-4 md:flex-row md:space-y-0 md:space-x-4\">\n        {/* Search and filters */}\n        <div className=\"w-full md:w-1/4\">\n          <div className=\"rounded-lg border border-gray-200 bg-white p-4 shadow-sm dark:border-gray-800 dark:bg-gray-900\">\n            <h3 className=\"mb-4 font-semibold\">Search & Filter</h3>\n            \n            {/* Search input */}\n            <div className=\"mb-4\">\n              <label htmlFor=\"search\" className=\"mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                Search\n              </label>\n              <div className=\"relative\">\n                <div className=\"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3\">\n                  <FontAwesomeIcon icon={faSearch} className=\"h-4 w-4 text-gray-400\" />\n                </div>\n                <input\n                  type=\"text\"\n                  id=\"search\"\n                  className=\"block w-full rounded-md border-gray-300 pl-10 focus:border-teal-500 focus:ring-teal-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white sm:text-sm\"\n                  placeholder=\"Search resources...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                />\n              </div>\n            </div>\n            \n            {/* Pricing filter */}\n            <div className=\"mb-4\">\n              <h4 className=\"mb-2 flex items-center text-sm font-medium text-gray-700 dark:text-gray-300\">\n                <FontAwesomeIcon icon={faFilter} className=\"mr-2 h-3 w-3\" />\n                Pricing\n              </h4>\n              <div className=\"space-y-2\">\n                {allPricingOptions.map((pricing) => (\n                  <div key={pricing} className=\"flex items-center\">\n                    <input\n                      id={`pricing-${pricing}`}\n                      type=\"checkbox\"\n                      className=\"h-4 w-4 rounded border-gray-300 text-teal-600 focus:ring-teal-500 dark:border-gray-700 dark:bg-gray-800\"\n                      checked={selectedPricing.includes(pricing)}\n                      onChange={() => togglePricingFilter(pricing)}\n                    />\n                    <label\n                      htmlFor={`pricing-${pricing}`}\n                      className=\"ml-2 text-sm text-gray-700 dark:text-gray-300\"\n                    >\n                      {pricing.charAt(0).toUpperCase() + pricing.slice(1)}\n                    </label>\n                  </div>\n                ))}\n              </div>\n            </div>\n            \n            {/* Popular tags */}\n            <div>\n              <h4 className=\"mb-2 flex items-center text-sm font-medium text-gray-700 dark:text-gray-300\">\n                <FontAwesomeIcon icon={faTag} className=\"mr-2 h-3 w-3\" />\n                Popular Tags\n              </h4>\n              <div className=\"flex flex-wrap gap-2\">\n                {allTags.slice(0, 10).map((tag) => (\n                  <button\n                    key={tag}\n                    onClick={() => setSearchTerm(tag)}\n                    className=\"inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700\"\n                  >\n                    {tag}\n                  </button>\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n        \n        {/* Resources table */}\n        <div className=\"w-full md:w-3/4\">\n          <div className=\"rounded-lg border border-gray-200 bg-white shadow-sm dark:border-gray-800 dark:bg-gray-900\">\n            <div className=\"overflow-x-auto\">\n              <table className=\"min-w-full divide-y divide-gray-200 dark:divide-gray-800\">\n                <thead className=\"bg-gray-50 dark:bg-gray-800\">\n                  <tr>\n                    <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400\">\n                      Name\n                    </th>\n                    <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400\">\n                      Description\n                    </th>\n                    <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400\">\n                      Pricing\n                    </th>\n                    <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400\">\n                      Link\n                    </th>\n                  </tr>\n                </thead>\n                <tbody className=\"divide-y divide-gray-200 bg-white dark:divide-gray-800 dark:bg-gray-900\">\n                  {filteredResources.length > 0 ? (\n                    filteredResources.map((resource) => (\n                      <tr key={resource.id} className=\"hover:bg-gray-50 dark:hover:bg-gray-800\">\n                        <td className=\"whitespace-nowrap px-6 py-4\">\n                          <div className=\"flex items-center\">\n                            {resource.logoUrl && (\n                              <img\n                                src={resource.logoUrl}\n                                alt={`${resource.name} logo`}\n                                className=\"mr-3 h-8 w-8 rounded-full object-contain\"\n                              />\n                            )}\n                            <div className=\"font-medium text-gray-900 dark:text-white\">{resource.name}</div>\n                          </div>\n                        </td>\n                        <td className=\"px-6 py-4\">\n                          <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n                            {resource.description}\n                            {resource.tags && resource.tags.length > 0 && (\n                              <div className=\"mt-2 flex flex-wrap gap-1\">\n                                {resource.tags.map((tag) => (\n                                  <span\n                                    key={tag}\n                                    className=\"inline-flex items-center rounded-full bg-teal-100 px-2 py-0.5 text-xs font-medium text-teal-800 dark:bg-teal-900/30 dark:text-teal-300\"\n                                  >\n                                    {tag}\n                                  </span>\n                                ))}\n                              </div>\n                            )}\n                          </div>\n                        </td>\n                        <td className=\"whitespace-nowrap px-6 py-4\">\n                          <span className=\"inline-flex rounded-full px-2 text-xs font-semibold leading-5\">\n                            {resource.pricing === 'free' && (\n                              <span className=\"rounded-full bg-green-100 px-2 py-0.5 text-xs font-medium text-green-800 dark:bg-green-900/30 dark:text-green-300\">\n                                Free\n                              </span>\n                            )}\n                            {resource.pricing === 'freemium' && (\n                              <span className=\"rounded-full bg-blue-100 px-2 py-0.5 text-xs font-medium text-blue-800 dark:bg-blue-900/30 dark:text-blue-300\">\n                                Freemium\n                              </span>\n                            )}\n                            {resource.pricing === 'paid' && (\n                              <span className=\"rounded-full bg-purple-100 px-2 py-0.5 text-xs font-medium text-purple-800 dark:bg-purple-900/30 dark:text-purple-300\">\n                                Paid\n                              </span>\n                            )}\n                            {resource.pricing === 'enterprise' && (\n                              <span className=\"rounded-full bg-gray-100 px-2 py-0.5 text-xs font-medium text-gray-800 dark:bg-gray-700 dark:text-gray-300\">\n                                Enterprise\n                              </span>\n                            )}\n                          </span>\n                        </td>\n                        <td className=\"whitespace-nowrap px-6 py-4 text-sm font-medium\">\n                          <a\n                            href={resource.url}\n                            target=\"_blank\"\n                            rel=\"noopener noreferrer\"\n                            className=\"text-teal-600 hover:text-teal-900 dark:text-teal-400 dark:hover:text-teal-300\"\n                          >\n                            <FontAwesomeIcon icon={faExternalLinkAlt} className=\"mr-1 h-3 w-3\" />\n                            Visit\n                          </a>\n                        </td>\n                      </tr>\n                    ))\n                  ) : (\n                    <tr>\n                      <td colSpan={4} className=\"px-6 py-10 text-center\">\n                        <p className=\"text-gray-500 dark:text-gray-400\">No resources found matching your criteria.</p>\n                        <button\n                          onClick={() => {\n                            setSearchTerm('');\n                            setSelectedPricing([]);\n                          }}\n                          className=\"mt-2 text-sm font-medium text-teal-600 hover:text-teal-500 dark:text-teal-400\"\n                        >\n                          Clear filters\n                        </button>\n                      </td>\n                    </tr>\n                  )}\n                </tbody>\n              </table>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAeA;AAAA;AArBA;;;;;;;;AAuBA,wCAAwC;AACxC,MAAM,gBAAqC;IACzC,gBAAgB,wKAAA,CAAA,SAAM;IACtB,sBAAsB,wKAAA,CAAA,UAAO;IAC7B,UAAU,wKAAA,CAAA,YAAS;IACnB,eAAe,wKAAA,CAAA,SAAM;IACrB,YAAY,wKAAA,CAAA,yBAAsB;IAClC,aAAa,wKAAA,CAAA,cAAW;IACxB,iBAAiB,wKAAA,CAAA,aAAU;IAC3B,iBAAiB,wKAAA,CAAA,cAAW;AAC9B;AAEe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,aAAa,OAAO,QAAQ;IAClC,MAAM,WAAW,CAAA,GAAA,wIAAA,CAAA,kBAAe,AAAD,EAAE;IACjC,MAAM,YAAY,CAAA,GAAA,wIAAA,CAAA,yBAAsB,AAAD,EAAE;IAEzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAEnE,sDAAsD;IACtD,MAAM,UAAU,MAAM,IAAI,CACxB,IAAI,IAAI,UAAU,OAAO,CAAC,CAAA,WAAY,SAAS,IAAI,IAAI,EAAE,IACzD,IAAI;IAEN,iEAAiE;IACjE,MAAM,oBAAoB,MAAM,IAAI,CAClC,IAAI,IAAI,UAAU,GAAG,CAAC,CAAA,WAAY,SAAS,OAAO,IAClD,MAAM,CAAC;IAET,2DAA2D;IAC3D,MAAM,oBAAoB,UAAU,MAAM,CAAC,CAAA;QACzC,MAAM,gBACJ,SAAS,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC3D,SAAS,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACjE,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAE/F,MAAM,iBACJ,gBAAgB,MAAM,KAAK,KAC1B,SAAS,OAAO,IAAI,gBAAgB,QAAQ,CAAC,SAAS,OAAO;QAEhE,OAAO,iBAAiB;IAC1B;IAEA,wBAAwB;IACxB,MAAM,sBAAsB,CAAC;QAC3B,IAAI,gBAAgB,QAAQ,CAAC,UAAU;YACrC,mBAAmB,gBAAgB,MAAM,CAAC,CAAA,IAAK,MAAM;QACvD,OAAO;YACL,mBAAmB;mBAAI;gBAAiB;aAAQ;QAClD;IACF;IAEA,IAAI,CAAC,UAAU;QACb,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAA0B;;;;;;8BACxC,8OAAC;oBAAE,WAAU;8BAAwC;;;;;;8BACrD,8OAAC,4JAAA,CAAA,UAAI;oBACH,MAAK;oBACL,WAAU;;sCAEV,8OAAC,oKAAA,CAAA,kBAAe;4BAAC,MAAM,wKAAA,CAAA,cAAW;4BAAE,WAAU;;;;;;wBAAiB;;;;;;;;;;;;;IAKvE;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;;0CAEV,8OAAC,oKAAA,CAAA,kBAAe;gCAAC,MAAM,wKAAA,CAAA,cAAW;gCAAE,WAAU;;;;;;4BAAiB;;;;;;;kCAIjE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,oKAAA,CAAA,kBAAe;oCAAC,MAAM,aAAa,CAAC,SAAS,EAAE,CAAC;oCAAE,WAAU;;;;;;;;;;;0CAE/D,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA2B,SAAS,IAAI;;;;;;kDACtD,8OAAC;wCAAE,WAAU;kDAAW,SAAS,WAAW;;;;;;;;;;;;;;;;;;;;;;;;0BAKlD,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAqB;;;;;;8CAGnC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,SAAQ;4CAAS,WAAU;sDAAkE;;;;;;sDAGpG,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,oKAAA,CAAA,kBAAe;wDAAC,MAAM,wKAAA,CAAA,WAAQ;wDAAE,WAAU;;;;;;;;;;;8DAE7C,8OAAC;oDACC,MAAK;oDACL,IAAG;oDACH,WAAU;oDACV,aAAY;oDACZ,OAAO;oDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;8CAMnD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC,oKAAA,CAAA,kBAAe;oDAAC,MAAM,wKAAA,CAAA,WAAQ;oDAAE,WAAU;;;;;;gDAAiB;;;;;;;sDAG9D,8OAAC;4CAAI,WAAU;sDACZ,kBAAkB,GAAG,CAAC,CAAC,wBACtB,8OAAC;oDAAkB,WAAU;;sEAC3B,8OAAC;4DACC,IAAI,CAAC,QAAQ,EAAE,SAAS;4DACxB,MAAK;4DACL,WAAU;4DACV,SAAS,gBAAgB,QAAQ,CAAC;4DAClC,UAAU,IAAM,oBAAoB;;;;;;sEAEtC,8OAAC;4DACC,SAAS,CAAC,QAAQ,EAAE,SAAS;4DAC7B,WAAU;sEAET,QAAQ,MAAM,CAAC,GAAG,WAAW,KAAK,QAAQ,KAAK,CAAC;;;;;;;mDAZ3C;;;;;;;;;;;;;;;;8CAoBhB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC,oKAAA,CAAA,kBAAe;oDAAC,MAAM,wKAAA,CAAA,QAAK;oDAAE,WAAU;;;;;;gDAAiB;;;;;;;sDAG3D,8OAAC;4CAAI,WAAU;sDACZ,QAAQ,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,oBACzB,8OAAC;oDAEC,SAAS,IAAM,cAAc;oDAC7B,WAAU;8DAET;mDAJI;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAajB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;4CAAM,WAAU;sDACf,cAAA,8OAAC;;kEACC,8OAAC;wDAAG,OAAM;wDAAM,WAAU;kEAAoG;;;;;;kEAG9H,8OAAC;wDAAG,OAAM;wDAAM,WAAU;kEAAoG;;;;;;kEAG9H,8OAAC;wDAAG,OAAM;wDAAM,WAAU;kEAAoG;;;;;;kEAG9H,8OAAC;wDAAG,OAAM;wDAAM,WAAU;kEAAoG;;;;;;;;;;;;;;;;;sDAKlI,8OAAC;4CAAM,WAAU;sDACd,kBAAkB,MAAM,GAAG,IAC1B,kBAAkB,GAAG,CAAC,CAAC,yBACrB,8OAAC;oDAAqB,WAAU;;sEAC9B,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAI,WAAU;;oEACZ,SAAS,OAAO,kBACf,8OAAC;wEACC,KAAK,SAAS,OAAO;wEACrB,KAAK,GAAG,SAAS,IAAI,CAAC,KAAK,CAAC;wEAC5B,WAAU;;;;;;kFAGd,8OAAC;wEAAI,WAAU;kFAA6C,SAAS,IAAI;;;;;;;;;;;;;;;;;sEAG7E,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAI,WAAU;;oEACZ,SAAS,WAAW;oEACpB,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,MAAM,GAAG,mBACvC,8OAAC;wEAAI,WAAU;kFACZ,SAAS,IAAI,CAAC,GAAG,CAAC,CAAC,oBAClB,8OAAC;gFAEC,WAAU;0FAET;+EAHI;;;;;;;;;;;;;;;;;;;;;sEAUjB,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAK,WAAU;;oEACb,SAAS,OAAO,KAAK,wBACpB,8OAAC;wEAAK,WAAU;kFAAoH;;;;;;oEAIrI,SAAS,OAAO,KAAK,4BACpB,8OAAC;wEAAK,WAAU;kFAAgH;;;;;;oEAIjI,SAAS,OAAO,KAAK,wBACpB,8OAAC;wEAAK,WAAU;kFAAwH;;;;;;oEAIzI,SAAS,OAAO,KAAK,8BACpB,8OAAC;wEAAK,WAAU;kFAA6G;;;;;;;;;;;;;;;;;sEAMnI,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEACC,MAAM,SAAS,GAAG;gEAClB,QAAO;gEACP,KAAI;gEACJ,WAAU;;kFAEV,8OAAC,oKAAA,CAAA,kBAAe;wEAAC,MAAM,wKAAA,CAAA,oBAAiB;wEAAE,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;mDA7DlE,SAAS,EAAE;;;;0EAoEtB,8OAAC;0DACC,cAAA,8OAAC;oDAAG,SAAS;oDAAG,WAAU;;sEACxB,8OAAC;4DAAE,WAAU;sEAAmC;;;;;;sEAChD,8OAAC;4DACC,SAAS;gEACP,cAAc;gEACd,mBAAmB,EAAE;4DACvB;4DACA,WAAU;sEACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAczB", "debugId": null}}]}