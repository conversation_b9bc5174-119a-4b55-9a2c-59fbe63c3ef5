{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/profile/components/ProfileHeader.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Image from 'next/image';\nimport { User } from '@/types/auth';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { faUser, faCamera } from '@fortawesome/free-solid-svg-icons';\n\ninterface ProfileHeaderProps {\n  user: User;\n}\n\nexport default function ProfileHeader({ user }: ProfileHeaderProps) {\n  return (\n    <div className=\"mb-8 rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-800 dark:bg-gray-900\">\n      <div className=\"flex flex-col items-center space-y-4 sm:flex-row sm:space-x-6 sm:space-y-0\">\n        <div className=\"relative\">\n          <div className=\"relative h-24 w-24 overflow-hidden rounded-full bg-gray-200 dark:bg-gray-700\">\n            {user.image ? (\n              <Image\n                src={user.image}\n                alt={user.name}\n                width={96}\n                height={96}\n                className=\"h-full w-full object-cover\"\n              />\n            ) : (\n              <FontAwesomeIcon\n                icon={faUser}\n                className=\"h-full w-full p-4 text-gray-400\"\n              />\n            )}\n          </div>\n          <button\n            type=\"button\"\n            className=\"absolute bottom-0 right-0 rounded-full bg-blue-600 p-2 text-white hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800\"\n            aria-label=\"Change profile picture\"\n          >\n            <FontAwesomeIcon icon={faCamera} className=\"h-4 w-4\" />\n          </button>\n        </div>\n        \n        <div className=\"text-center sm:text-left\">\n          <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n            {user.name}\n          </h1>\n          <p className=\"text-gray-600 dark:text-gray-400\">{user.email}</p>\n          <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-500\">\n            Member since {new Date(user.createdAt).toLocaleDateString()}\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AACA;AANA;;;;;AAYe,SAAS,cAAc,EAAE,IAAI,EAAsB;IAChE,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACZ,KAAK,KAAK,iBACT,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAK,KAAK,KAAK;gCACf,KAAK,KAAK,IAAI;gCACd,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;qDAGZ,8OAAC,oKAAA,CAAA,kBAAe;gCACd,MAAM,wKAAA,CAAA,SAAM;gCACZ,WAAU;;;;;;;;;;;sCAIhB,8OAAC;4BACC,MAAK;4BACL,WAAU;4BACV,cAAW;sCAEX,cAAA,8OAAC,oKAAA,CAAA,kBAAe;gCAAC,MAAM,wKAAA,CAAA,WAAQ;gCAAE,WAAU;;;;;;;;;;;;;;;;;8BAI/C,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCACX,KAAK,IAAI;;;;;;sCAEZ,8OAAC;4BAAE,WAAU;sCAAoC,KAAK,KAAK;;;;;;sCAC3D,8OAAC;4BAAE,WAAU;;gCAAgD;gCAC7C,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;AAMrE", "debugId": null}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/profile/components/ProfileForm.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { User } from '@/types/auth';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { faUser, faEnvelope, faSpinner, faC<PERSON>ck, faExclamationTriangle } from '@fortawesome/free-solid-svg-icons';\n\ninterface ProfileFormProps {\n  user: User;\n  onUpdate: (updatedUser: User) => void;\n}\n\nexport default function ProfileForm({ user, onUpdate }: ProfileFormProps) {\n  const [name, setName] = useState(user.name);\n  const [email, setEmail] = useState(user.email);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n  \n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    // Reset states\n    setError(null);\n    setSuccess(null);\n    \n    // Validate form\n    if (!name.trim() || !email.trim()) {\n      setError('Name and email are required');\n      return;\n    }\n    \n    // Email validation\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    if (!emailRegex.test(email)) {\n      setError('Please enter a valid email address');\n      return;\n    }\n    \n    setIsSubmitting(true);\n    \n    try {\n      const response = await fetch('/api/user/profile', {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ name, email }),\n      });\n      \n      const data = await response.json();\n      \n      if (!response.ok) {\n        throw new Error(data.message || 'Failed to update profile');\n      }\n      \n      // Update the user in the parent component\n      onUpdate(data);\n      \n      setSuccess('Profile updated successfully');\n    } catch (error) {\n      setError(error instanceof Error ? error.message : 'An error occurred');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  \n  return (\n    <div className=\"mb-8 rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-800 dark:bg-gray-900\">\n      <h2 className=\"mb-4 text-xl font-bold text-gray-900 dark:text-white\">\n        Profile Information\n      </h2>\n      \n      {error && (\n        <div className=\"mb-4 rounded-md bg-red-50 p-4 dark:bg-red-900/30\">\n          <div className=\"flex\">\n            <FontAwesomeIcon\n              icon={faExclamationTriangle}\n              className=\"h-5 w-5 text-red-400\"\n            />\n            <div className=\"ml-3\">\n              <p className=\"text-sm text-red-700 dark:text-red-300\">{error}</p>\n            </div>\n          </div>\n        </div>\n      )}\n      \n      {success && (\n        <div className=\"mb-4 rounded-md bg-green-50 p-4 dark:bg-green-900/30\">\n          <div className=\"flex\">\n            <FontAwesomeIcon\n              icon={faCheck}\n              className=\"h-5 w-5 text-green-400\"\n            />\n            <div className=\"ml-3\">\n              <p className=\"text-sm text-green-700 dark:text-green-300\">{success}</p>\n            </div>\n          </div>\n        </div>\n      )}\n      \n      <form onSubmit={handleSubmit}>\n        <div className=\"mb-4\">\n          <label\n            htmlFor=\"name\"\n            className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\"\n          >\n            Full Name\n          </label>\n          <div className=\"relative mt-1 rounded-md shadow-sm\">\n            <div className=\"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3\">\n              <FontAwesomeIcon icon={faUser} className=\"h-5 w-5 text-gray-400\" />\n            </div>\n            <input\n              type=\"text\"\n              id=\"name\"\n              value={name}\n              onChange={(e) => setName(e.target.value)}\n              className=\"block w-full rounded-md border-gray-300 pl-10 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400\"\n              placeholder=\"John Doe\"\n            />\n          </div>\n        </div>\n        \n        <div className=\"mb-6\">\n          <label\n            htmlFor=\"email\"\n            className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\"\n          >\n            Email Address\n          </label>\n          <div className=\"relative mt-1 rounded-md shadow-sm\">\n            <div className=\"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3\">\n              <FontAwesomeIcon icon={faEnvelope} className=\"h-5 w-5 text-gray-400\" />\n            </div>\n            <input\n              type=\"email\"\n              id=\"email\"\n              value={email}\n              onChange={(e) => setEmail(e.target.value)}\n              className=\"block w-full rounded-md border-gray-300 pl-10 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400\"\n              placeholder=\"<EMAIL>\"\n            />\n          </div>\n        </div>\n        \n        <div className=\"flex justify-end\">\n          <button\n            type=\"submit\"\n            disabled={isSubmitting}\n            className=\"inline-flex items-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-75 dark:bg-blue-700 dark:hover:bg-blue-800\"\n          >\n            {isSubmitting ? (\n              <>\n                <FontAwesomeIcon icon={faSpinner} className=\"mr-2 h-4 w-4 animate-spin\" />\n                Saving...\n              </>\n            ) : (\n              'Save Changes'\n            )}\n          </button>\n        </div>\n      </form>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AALA;;;;;AAYe,SAAS,YAAY,EAAE,IAAI,EAAE,QAAQ,EAAoB;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,IAAI;IAC1C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,KAAK;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEtD,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,eAAe;QACf,SAAS;QACT,WAAW;QAEX,gBAAgB;QAChB,IAAI,CAAC,KAAK,IAAI,MAAM,CAAC,MAAM,IAAI,IAAI;YACjC,SAAS;YACT;QACF;QAEA,mBAAmB;QACnB,MAAM,aAAa;QACnB,IAAI,CAAC,WAAW,IAAI,CAAC,QAAQ;YAC3B,SAAS;YACT;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,qBAAqB;gBAChD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAM;gBAAM;YACrC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,OAAO,IAAI;YAClC;YAEA,0CAA0C;YAC1C,SAAS;YAET,WAAW;QACb,EAAE,OAAO,OAAO;YACd,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAAuD;;;;;;YAIpE,uBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oKAAA,CAAA,kBAAe;4BACd,MAAM,wKAAA,CAAA,wBAAqB;4BAC3B,WAAU;;;;;;sCAEZ,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;;;;;;;;;;;YAM9D,yBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oKAAA,CAAA,kBAAe;4BACd,MAAM,wKAAA,CAAA,UAAO;4BACb,WAAU;;;;;;sCAEZ,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAA8C;;;;;;;;;;;;;;;;;;;;;;0BAMnE,8OAAC;gBAAK,UAAU;;kCACd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAQ;gCACR,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oKAAA,CAAA,kBAAe;4CAAC,MAAM,wKAAA,CAAA,SAAM;4CAAE,WAAU;;;;;;;;;;;kDAE3C,8OAAC;wCACC,MAAK;wCACL,IAAG;wCACH,OAAO;wCACP,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;wCACvC,WAAU;wCACV,aAAY;;;;;;;;;;;;;;;;;;kCAKlB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAQ;gCACR,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oKAAA,CAAA,kBAAe;4CAAC,MAAM,wKAAA,CAAA,aAAU;4CAAE,WAAU;;;;;;;;;;;kDAE/C,8OAAC;wCACC,MAAK;wCACL,IAAG;wCACH,OAAO;wCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wCACxC,WAAU;wCACV,aAAY;;;;;;;;;;;;;;;;;;kCAKlB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,MAAK;4BACL,UAAU;4BACV,WAAU;sCAET,6BACC;;kDACE,8OAAC,oKAAA,CAAA,kBAAe;wCAAC,MAAM,wKAAA,CAAA,YAAS;wCAAE,WAAU;;;;;;oCAA8B;;+CAI5E;;;;;;;;;;;;;;;;;;;;;;;AAOd", "debugId": null}}, {"offset": {"line": 437, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/profile/components/PasswordForm.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { faLock, faSpinner, faCheck, faExclamationTriangle } from '@fortawesome/free-solid-svg-icons';\n\nexport default function PasswordForm() {\n  const [currentPassword, setCurrentPassword] = useState('');\n  const [newPassword, setNewPassword] = useState('');\n  const [confirmPassword, setConfirmPassword] = useState('');\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n  \n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    // Reset states\n    setError(null);\n    setSuccess(null);\n    \n    // Validate form\n    if (!currentPassword || !newPassword || !confirmPassword) {\n      setError('All fields are required');\n      return;\n    }\n    \n    if (newPassword !== confirmPassword) {\n      setError('New passwords do not match');\n      return;\n    }\n    \n    if (newPassword.length < 8) {\n      setError('New password must be at least 8 characters long');\n      return;\n    }\n    \n    setIsSubmitting(true);\n    \n    try {\n      const response = await fetch('/api/user/password', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ currentPassword, newPassword }),\n      });\n      \n      const data = await response.json();\n      \n      if (!response.ok) {\n        throw new Error(data.message || 'Failed to change password');\n      }\n      \n      setSuccess('Password changed successfully');\n      \n      // Clear form\n      setCurrentPassword('');\n      setNewPassword('');\n      setConfirmPassword('');\n    } catch (error) {\n      setError(error instanceof Error ? error.message : 'An error occurred');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  \n  return (\n    <div className=\"mb-8 rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-800 dark:bg-gray-900\">\n      <h2 className=\"mb-4 text-xl font-bold text-gray-900 dark:text-white\">\n        Change Password\n      </h2>\n      \n      {error && (\n        <div className=\"mb-4 rounded-md bg-red-50 p-4 dark:bg-red-900/30\">\n          <div className=\"flex\">\n            <FontAwesomeIcon\n              icon={faExclamationTriangle}\n              className=\"h-5 w-5 text-red-400\"\n            />\n            <div className=\"ml-3\">\n              <p className=\"text-sm text-red-700 dark:text-red-300\">{error}</p>\n            </div>\n          </div>\n        </div>\n      )}\n      \n      {success && (\n        <div className=\"mb-4 rounded-md bg-green-50 p-4 dark:bg-green-900/30\">\n          <div className=\"flex\">\n            <FontAwesomeIcon\n              icon={faCheck}\n              className=\"h-5 w-5 text-green-400\"\n            />\n            <div className=\"ml-3\">\n              <p className=\"text-sm text-green-700 dark:text-green-300\">{success}</p>\n            </div>\n          </div>\n        </div>\n      )}\n      \n      <form onSubmit={handleSubmit}>\n        <div className=\"mb-4\">\n          <label\n            htmlFor=\"currentPassword\"\n            className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\"\n          >\n            Current Password\n          </label>\n          <div className=\"relative mt-1 rounded-md shadow-sm\">\n            <div className=\"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3\">\n              <FontAwesomeIcon icon={faLock} className=\"h-5 w-5 text-gray-400\" />\n            </div>\n            <input\n              type=\"password\"\n              id=\"currentPassword\"\n              value={currentPassword}\n              onChange={(e) => setCurrentPassword(e.target.value)}\n              className=\"block w-full rounded-md border-gray-300 pl-10 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400\"\n              placeholder=\"••••••••\"\n            />\n          </div>\n        </div>\n        \n        <div className=\"mb-4\">\n          <label\n            htmlFor=\"newPassword\"\n            className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\"\n          >\n            New Password\n          </label>\n          <div className=\"relative mt-1 rounded-md shadow-sm\">\n            <div className=\"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3\">\n              <FontAwesomeIcon icon={faLock} className=\"h-5 w-5 text-gray-400\" />\n            </div>\n            <input\n              type=\"password\"\n              id=\"newPassword\"\n              value={newPassword}\n              onChange={(e) => setNewPassword(e.target.value)}\n              className=\"block w-full rounded-md border-gray-300 pl-10 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400\"\n              placeholder=\"••••••••\"\n            />\n          </div>\n          <p className=\"mt-1 text-xs text-gray-500 dark:text-gray-400\">\n            Password must be at least 8 characters long\n          </p>\n        </div>\n        \n        <div className=\"mb-6\">\n          <label\n            htmlFor=\"confirmPassword\"\n            className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\"\n          >\n            Confirm New Password\n          </label>\n          <div className=\"relative mt-1 rounded-md shadow-sm\">\n            <div className=\"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3\">\n              <FontAwesomeIcon icon={faLock} className=\"h-5 w-5 text-gray-400\" />\n            </div>\n            <input\n              type=\"password\"\n              id=\"confirmPassword\"\n              value={confirmPassword}\n              onChange={(e) => setConfirmPassword(e.target.value)}\n              className=\"block w-full rounded-md border-gray-300 pl-10 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400\"\n              placeholder=\"••••••••\"\n            />\n          </div>\n        </div>\n        \n        <div className=\"flex justify-end\">\n          <button\n            type=\"submit\"\n            disabled={isSubmitting}\n            className=\"inline-flex items-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-75 dark:bg-blue-700 dark:hover:bg-blue-800\"\n          >\n            {isSubmitting ? (\n              <>\n                <FontAwesomeIcon icon={faSpinner} className=\"mr-2 h-4 w-4 animate-spin\" />\n                Changing...\n              </>\n            ) : (\n              'Change Password'\n            )}\n          </button>\n        </div>\n      </form>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEtD,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,eAAe;QACf,SAAS;QACT,WAAW;QAEX,gBAAgB;QAChB,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,iBAAiB;YACxD,SAAS;YACT;QACF;QAEA,IAAI,gBAAgB,iBAAiB;YACnC,SAAS;YACT;QACF;QAEA,IAAI,YAAY,MAAM,GAAG,GAAG;YAC1B,SAAS;YACT;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAiB;gBAAY;YACtD;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,OAAO,IAAI;YAClC;YAEA,WAAW;YAEX,aAAa;YACb,mBAAmB;YACnB,eAAe;YACf,mBAAmB;QACrB,EAAE,OAAO,OAAO;YACd,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAAuD;;;;;;YAIpE,uBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oKAAA,CAAA,kBAAe;4BACd,MAAM,wKAAA,CAAA,wBAAqB;4BAC3B,WAAU;;;;;;sCAEZ,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;;;;;;;;;;;YAM9D,yBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oKAAA,CAAA,kBAAe;4BACd,MAAM,wKAAA,CAAA,UAAO;4BACb,WAAU;;;;;;sCAEZ,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAA8C;;;;;;;;;;;;;;;;;;;;;;0BAMnE,8OAAC;gBAAK,UAAU;;kCACd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAQ;gCACR,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oKAAA,CAAA,kBAAe;4CAAC,MAAM,wKAAA,CAAA,SAAM;4CAAE,WAAU;;;;;;;;;;;kDAE3C,8OAAC;wCACC,MAAK;wCACL,IAAG;wCACH,OAAO;wCACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;wCAClD,WAAU;wCACV,aAAY;;;;;;;;;;;;;;;;;;kCAKlB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAQ;gCACR,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oKAAA,CAAA,kBAAe;4CAAC,MAAM,wKAAA,CAAA,SAAM;4CAAE,WAAU;;;;;;;;;;;kDAE3C,8OAAC;wCACC,MAAK;wCACL,IAAG;wCACH,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;wCACV,aAAY;;;;;;;;;;;;0CAGhB,8OAAC;gCAAE,WAAU;0CAAgD;;;;;;;;;;;;kCAK/D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAQ;gCACR,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oKAAA,CAAA,kBAAe;4CAAC,MAAM,wKAAA,CAAA,SAAM;4CAAE,WAAU;;;;;;;;;;;kDAE3C,8OAAC;wCACC,MAAK;wCACL,IAAG;wCACH,OAAO;wCACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;wCAClD,WAAU;wCACV,aAAY;;;;;;;;;;;;;;;;;;kCAKlB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,MAAK;4BACL,UAAU;4BACV,WAAU;sCAET,6BACC;;kDACE,8OAAC,oKAAA,CAAA,kBAAe;wCAAC,MAAM,wKAAA,CAAA,YAAS;wCAAE,WAAU;;;;;;oCAA8B;;+CAI5E;;;;;;;;;;;;;;;;;;;;;;;AAOd", "debugId": null}}, {"offset": {"line": 811, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/lib/events.ts"], "sourcesContent": ["'use client';\n\n// Custom event types\nexport const EVENTS = {\n  FAVORITES_UPDATED: 'favorites-updated',\n  RECENTLY_VIEWED_UPDATED: 'recently-viewed-updated',\n};\n\n// Function to dispatch a custom event\nexport function dispatchCustomEvent(eventName: string, detail?: any) {\n  if (typeof window === 'undefined') return;\n  \n  const event = new CustomEvent(eventName, { detail });\n  window.dispatchEvent(event);\n}\n\n// Function to add an event listener\nexport function addCustomEventListener(eventName: string, callback: (event: CustomEvent) => void) {\n  if (typeof window === 'undefined') return () => {};\n  \n  const eventListener = (e: Event) => callback(e as CustomEvent);\n  window.addEventListener(eventName, eventListener);\n  \n  // Return a function to remove the event listener\n  return () => {\n    window.removeEventListener(eventName, eventListener);\n  };\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAGO,MAAM,SAAS;IACpB,mBAAmB;IACnB,yBAAyB;AAC3B;AAGO,SAAS,oBAAoB,SAAiB,EAAE,MAAY;IACjE,wCAAmC;;IAEnC,MAAM;AAER;AAGO,SAAS,uBAAuB,SAAiB,EAAE,QAAsC;IAC9F,wCAAmC,OAAO,KAAO;;IAEjD,MAAM;AAOR", "debugId": null}}, {"offset": {"line": 837, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/lib/userPreferences.ts"], "sourcesContent": ["'use client';\n\nimport { UserPreferences } from '@/types/auth';\nimport { dispatchCustomEvent, EVENTS } from './events';\n\n// Default user preferences\nconst defaultPreferences: UserPreferences = {\n  favoriteAgentIds: [],\n  recentlyViewedAgentIds: [],\n  darkMode: false,\n  defaultView: 'grid',\n  pageSize: 12,\n};\n\n// Get user preferences from localStorage\nexport function getUserPreferences(userId?: string): UserPreferences {\n  if (typeof window === 'undefined') {\n    return defaultPreferences;\n  }\n\n  // If userId is provided, use it to get user-specific preferences\n  const storageKey = userId ? `userPreferences_${userId}` : 'userPreferences';\n  const storedPreferences = localStorage.getItem(storageKey);\n  if (!storedPreferences) {\n    return defaultPreferences;\n  }\n\n  try {\n    return JSON.parse(storedPreferences) as UserPreferences;\n  } catch (error) {\n    console.error('Error parsing user preferences:', error);\n    return defaultPreferences;\n  }\n}\n\n// Save user preferences to localStorage\nexport function saveUserPreferences(preferences: UserPreferences, userId?: string): void {\n  if (typeof window === 'undefined') {\n    return;\n  }\n\n  // If userId is provided, use it to save user-specific preferences\n  const storageKey = userId ? `userPreferences_${userId}` : 'userPreferences';\n  localStorage.setItem(storageKey, JSON.stringify(preferences));\n}\n\n// Add an agent to favorites\nexport function toggleFavoriteAgent(agentId: string, userId?: string): boolean {\n  const preferences = getUserPreferences(userId);\n  const isFavorite = preferences.favoriteAgentIds.includes(agentId);\n\n  if (isFavorite) {\n    preferences.favoriteAgentIds = preferences.favoriteAgentIds.filter(id => id !== agentId);\n  } else {\n    preferences.favoriteAgentIds.push(agentId);\n  }\n\n  saveUserPreferences(preferences, userId);\n\n  // Dispatch event to notify other components\n  dispatchCustomEvent(EVENTS.FAVORITES_UPDATED, {\n    agentId,\n    isFavorite: !isFavorite,\n    userId\n  });\n\n  return !isFavorite; // Return the new favorite status\n}\n\n// Check if an agent is favorited\nexport function isAgentFavorited(agentId: string, userId?: string): boolean {\n  const preferences = getUserPreferences(userId);\n  return preferences.favoriteAgentIds.includes(agentId);\n}\n\n// Add an agent to recently viewed\nexport function addToRecentlyViewed(agentId: string, userId?: string): void {\n  const preferences = getUserPreferences(userId);\n\n  // Remove if already exists to avoid duplicates\n  preferences.recentlyViewedAgentIds = preferences.recentlyViewedAgentIds.filter(id => id !== agentId);\n\n  // Add to the beginning of the array\n  preferences.recentlyViewedAgentIds.unshift(agentId);\n\n  // Keep only the 10 most recent\n  if (preferences.recentlyViewedAgentIds.length > 10) {\n    preferences.recentlyViewedAgentIds = preferences.recentlyViewedAgentIds.slice(0, 10);\n  }\n\n  saveUserPreferences(preferences, userId);\n\n  // Dispatch event to notify other components\n  dispatchCustomEvent(EVENTS.RECENTLY_VIEWED_UPDATED, {\n    agentId,\n    recentlyViewedIds: preferences.recentlyViewedAgentIds,\n    userId\n  });\n}\n\n// Get recently viewed agents\nexport function getRecentlyViewedAgentIds(userId?: string): string[] {\n  const preferences = getUserPreferences(userId);\n  return preferences.recentlyViewedAgentIds;\n}\n\n// Update page size preference\nexport function updatePageSize(pageSize: number, userId?: string): void {\n  const preferences = getUserPreferences(userId);\n  preferences.pageSize = pageSize;\n  saveUserPreferences(preferences, userId);\n}\n\n// Get page size preference\nexport function getPageSize(userId?: string): number {\n  const preferences = getUserPreferences(userId);\n  return preferences.pageSize;\n}\n\n// Update default view preference\nexport function updateDefaultView(view: 'grid' | 'list', userId?: string): void {\n  const preferences = getUserPreferences(userId);\n  preferences.defaultView = view;\n  saveUserPreferences(preferences, userId);\n}\n\n// Get default view preference\nexport function getDefaultView(userId?: string): 'grid' | 'list' {\n  const preferences = getUserPreferences(userId);\n  return preferences.defaultView;\n}\n\n// Toggle dark mode\nexport function toggleDarkMode(userId?: string): boolean {\n  const preferences = getUserPreferences(userId);\n  preferences.darkMode = !preferences.darkMode;\n  saveUserPreferences(preferences, userId);\n  return preferences.darkMode;\n}\n\n// Get dark mode preference\nexport function getDarkMode(userId?: string): boolean {\n  const preferences = getUserPreferences(userId);\n  return preferences.darkMode;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAGA;AAHA;;AAKA,2BAA2B;AAC3B,MAAM,qBAAsC;IAC1C,kBAAkB,EAAE;IACpB,wBAAwB,EAAE;IAC1B,UAAU;IACV,aAAa;IACb,UAAU;AACZ;AAGO,SAAS,mBAAmB,MAAe;IAChD,wCAAmC;QACjC,OAAO;IACT;;IAEA,iEAAiE;IACjE,MAAM;IACN,MAAM;AAWR;AAGO,SAAS,oBAAoB,WAA4B,EAAE,MAAe;IAC/E,wCAAmC;QACjC;IACF;;IAEA,kEAAkE;IAClE,MAAM;AAER;AAGO,SAAS,oBAAoB,OAAe,EAAE,MAAe;IAClE,MAAM,cAAc,mBAAmB;IACvC,MAAM,aAAa,YAAY,gBAAgB,CAAC,QAAQ,CAAC;IAEzD,IAAI,YAAY;QACd,YAAY,gBAAgB,GAAG,YAAY,gBAAgB,CAAC,MAAM,CAAC,CAAA,KAAM,OAAO;IAClF,OAAO;QACL,YAAY,gBAAgB,CAAC,IAAI,CAAC;IACpC;IAEA,oBAAoB,aAAa;IAEjC,4CAA4C;IAC5C,CAAA,GAAA,oHAAA,CAAA,sBAAmB,AAAD,EAAE,oHAAA,CAAA,SAAM,CAAC,iBAAiB,EAAE;QAC5C;QACA,YAAY,CAAC;QACb;IACF;IAEA,OAAO,CAAC,YAAY,iCAAiC;AACvD;AAGO,SAAS,iBAAiB,OAAe,EAAE,MAAe;IAC/D,MAAM,cAAc,mBAAmB;IACvC,OAAO,YAAY,gBAAgB,CAAC,QAAQ,CAAC;AAC/C;AAGO,SAAS,oBAAoB,OAAe,EAAE,MAAe;IAClE,MAAM,cAAc,mBAAmB;IAEvC,+CAA+C;IAC/C,YAAY,sBAAsB,GAAG,YAAY,sBAAsB,CAAC,MAAM,CAAC,CAAA,KAAM,OAAO;IAE5F,oCAAoC;IACpC,YAAY,sBAAsB,CAAC,OAAO,CAAC;IAE3C,+BAA+B;IAC/B,IAAI,YAAY,sBAAsB,CAAC,MAAM,GAAG,IAAI;QAClD,YAAY,sBAAsB,GAAG,YAAY,sBAAsB,CAAC,KAAK,CAAC,GAAG;IACnF;IAEA,oBAAoB,aAAa;IAEjC,4CAA4C;IAC5C,CAAA,GAAA,oHAAA,CAAA,sBAAmB,AAAD,EAAE,oHAAA,CAAA,SAAM,CAAC,uBAAuB,EAAE;QAClD;QACA,mBAAmB,YAAY,sBAAsB;QACrD;IACF;AACF;AAGO,SAAS,0BAA0B,MAAe;IACvD,MAAM,cAAc,mBAAmB;IACvC,OAAO,YAAY,sBAAsB;AAC3C;AAGO,SAAS,eAAe,QAAgB,EAAE,MAAe;IAC9D,MAAM,cAAc,mBAAmB;IACvC,YAAY,QAAQ,GAAG;IACvB,oBAAoB,aAAa;AACnC;AAGO,SAAS,YAAY,MAAe;IACzC,MAAM,cAAc,mBAAmB;IACvC,OAAO,YAAY,QAAQ;AAC7B;AAGO,SAAS,kBAAkB,IAAqB,EAAE,MAAe;IACtE,MAAM,cAAc,mBAAmB;IACvC,YAAY,WAAW,GAAG;IAC1B,oBAAoB,aAAa;AACnC;AAGO,SAAS,eAAe,MAAe;IAC5C,MAAM,cAAc,mBAAmB;IACvC,OAAO,YAAY,WAAW;AAChC;AAGO,SAAS,eAAe,MAAe;IAC5C,MAAM,cAAc,mBAAmB;IACvC,YAAY,QAAQ,GAAG,CAAC,YAAY,QAAQ;IAC5C,oBAAoB,aAAa;IACjC,OAAO,YAAY,QAAQ;AAC7B;AAGO,SAAS,YAAY,MAAe;IACzC,MAAM,cAAc,mBAAmB;IACvC,OAAO,YAAY,QAAQ;AAC7B", "debugId": null}}, {"offset": {"line": 956, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/hooks/useUserPreferences.ts"], "sourcesContent": ["'use client';\n\nimport { useAuth } from '@/contexts/AuthContext';\nimport { UserPreferences } from '@/types/auth';\nimport * as userPreferencesLib from '@/lib/userPreferences';\n\nexport function useUserPreferences() {\n  const { user } = useAuth();\n  const userId = user?.id;\n\n  const getUserPreferences = (): UserPreferences => {\n    return userPreferencesLib.getUserPreferences(userId);\n  };\n\n  const saveUserPreferences = (preferences: UserPreferences): void => {\n    userPreferencesLib.saveUserPreferences(preferences, userId);\n  };\n\n  const toggleFavoriteAgent = (agentId: string): boolean => {\n    return userPreferencesLib.toggleFavoriteAgent(agentId, userId);\n  };\n\n  const isAgentFavorited = (agentId: string): boolean => {\n    return userPreferencesLib.isAgentFavorited(agentId, userId);\n  };\n\n  const addToRecentlyViewed = (agentId: string): void => {\n    userPreferencesLib.addToRecentlyViewed(agentId, userId);\n  };\n\n  const getRecentlyViewedAgentIds = (): string[] => {\n    return userPreferencesLib.getRecentlyViewedAgentIds(userId);\n  };\n\n  const updatePageSize = (pageSize: number): void => {\n    userPreferencesLib.updatePageSize(pageSize, userId);\n  };\n\n  const getPageSize = (): number => {\n    return userPreferencesLib.getPageSize(userId);\n  };\n\n  const updateDefaultView = (view: 'grid' | 'list'): void => {\n    userPreferencesLib.updateDefaultView(view, userId);\n  };\n\n  const getDefaultView = (): 'grid' | 'list' => {\n    return userPreferencesLib.getDefaultView(userId);\n  };\n\n  const toggleDarkMode = (): boolean => {\n    return userPreferencesLib.toggleDarkMode(userId);\n  };\n\n  const getDarkMode = (): boolean => {\n    return userPreferencesLib.getDarkMode(userId);\n  };\n\n  return {\n    getUserPreferences,\n    saveUserPreferences,\n    toggleFavoriteAgent,\n    isAgentFavorited,\n    addToRecentlyViewed,\n    getRecentlyViewedAgentIds,\n    updatePageSize,\n    getPageSize,\n    updateDefaultView,\n    getDefaultView,\n    toggleDarkMode,\n    getDarkMode,\n  };\n}\n"], "names": [], "mappings": ";;;AAEA;AAEA;AAJA;;;AAMO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,SAAS,MAAM;IAErB,MAAM,qBAAqB;QACzB,OAAO,CAAA,GAAA,6HAAA,CAAA,qBAAqC,AAAD,EAAE;IAC/C;IAEA,MAAM,sBAAsB,CAAC;QAC3B,CAAA,GAAA,6HAAA,CAAA,sBAAsC,AAAD,EAAE,aAAa;IACtD;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAO,CAAA,GAAA,6HAAA,CAAA,sBAAsC,AAAD,EAAE,SAAS;IACzD;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAO,CAAA,GAAA,6HAAA,CAAA,mBAAmC,AAAD,EAAE,SAAS;IACtD;IAEA,MAAM,sBAAsB,CAAC;QAC3B,CAAA,GAAA,6HAAA,CAAA,sBAAsC,AAAD,EAAE,SAAS;IAClD;IAEA,MAAM,4BAA4B;QAChC,OAAO,CAAA,GAAA,6HAAA,CAAA,4BAA4C,AAAD,EAAE;IACtD;IAEA,MAAM,iBAAiB,CAAC;QACtB,CAAA,GAAA,6HAAA,CAAA,iBAAiC,AAAD,EAAE,UAAU;IAC9C;IAEA,MAAM,cAAc;QAClB,OAAO,CAAA,GAAA,6HAAA,CAAA,cAA8B,AAAD,EAAE;IACxC;IAEA,MAAM,oBAAoB,CAAC;QACzB,CAAA,GAAA,6HAAA,CAAA,oBAAoC,AAAD,EAAE,MAAM;IAC7C;IAEA,MAAM,iBAAiB;QACrB,OAAO,CAAA,GAAA,6HAAA,CAAA,iBAAiC,AAAD,EAAE;IAC3C;IAEA,MAAM,iBAAiB;QACrB,OAAO,CAAA,GAAA,6HAAA,CAAA,iBAAiC,AAAD,EAAE;IAC3C;IAEA,MAAM,cAAc;QAClB,OAAO,CAAA,GAAA,6HAAA,CAAA,cAA8B,AAAD,EAAE;IACxC;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 1024, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/profile/components/PreferencesForm.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useUserPreferences } from '@/hooks/useUserPreferences';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { faCheck, faSpinner } from '@fortawesome/free-solid-svg-icons';\n\nexport default function PreferencesForm() {\n  const {\n    getUserPreferences,\n    updateDefaultView,\n    updatePageSize,\n    toggleDarkMode,\n  } = useUserPreferences();\n  \n  const [defaultView, setDefaultView] = useState<'grid' | 'list'>('grid');\n  const [pageSize, setPageSize] = useState(12);\n  const [darkMode, setDarkMode] = useState(false);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [success, setSuccess] = useState<string | null>(null);\n  \n  // Load user preferences\n  useEffect(() => {\n    const preferences = getUserPreferences();\n    setDefaultView(preferences.defaultView);\n    setPageSize(preferences.pageSize);\n    setDarkMode(preferences.theme === 'dark');\n  }, [getUserPreferences]);\n  \n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    setIsSubmitting(true);\n    setSuccess(null);\n    \n    try {\n      // Update preferences\n      updateDefaultView(defaultView);\n      updatePageSize(pageSize);\n      \n      // Show success message\n      setSuccess('Preferences updated successfully');\n      \n      // Hide success message after 3 seconds\n      setTimeout(() => {\n        setSuccess(null);\n      }, 3000);\n    } catch (error) {\n      console.error('Error updating preferences:', error);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  \n  return (\n    <div className=\"mb-8 rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-800 dark:bg-gray-900\">\n      <h2 className=\"mb-4 text-xl font-bold text-gray-900 dark:text-white\">\n        Preferences\n      </h2>\n      \n      {success && (\n        <div className=\"mb-4 rounded-md bg-green-50 p-4 dark:bg-green-900/30\">\n          <div className=\"flex\">\n            <FontAwesomeIcon\n              icon={faCheck}\n              className=\"h-5 w-5 text-green-400\"\n            />\n            <div className=\"ml-3\">\n              <p className=\"text-sm text-green-700 dark:text-green-300\">{success}</p>\n            </div>\n          </div>\n        </div>\n      )}\n      \n      <form onSubmit={handleSubmit}>\n        <div className=\"mb-4\">\n          <label\n            htmlFor=\"defaultView\"\n            className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\"\n          >\n            Default View\n          </label>\n          <select\n            id=\"defaultView\"\n            value={defaultView}\n            onChange={(e) => setDefaultView(e.target.value as 'grid' | 'list')}\n            className=\"mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-blue-500 focus:outline-none focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white sm:text-sm\"\n          >\n            <option value=\"grid\">Grid</option>\n            <option value=\"list\">List</option>\n          </select>\n          <p className=\"mt-1 text-xs text-gray-500 dark:text-gray-400\">\n            Choose how agents are displayed on the browse page\n          </p>\n        </div>\n        \n        <div className=\"mb-4\">\n          <label\n            htmlFor=\"pageSize\"\n            className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\"\n          >\n            Items Per Page\n          </label>\n          <select\n            id=\"pageSize\"\n            value={pageSize}\n            onChange={(e) => setPageSize(Number(e.target.value))}\n            className=\"mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-blue-500 focus:outline-none focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white sm:text-sm\"\n          >\n            <option value=\"6\">6</option>\n            <option value=\"12\">12</option>\n            <option value=\"24\">24</option>\n            <option value=\"48\">48</option>\n          </select>\n          <p className=\"mt-1 text-xs text-gray-500 dark:text-gray-400\">\n            Number of agents to display per page\n          </p>\n        </div>\n        \n        <div className=\"mb-6\">\n          <div className=\"flex items-center justify-between\">\n            <label\n              htmlFor=\"darkMode\"\n              className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\"\n            >\n              Dark Mode\n            </label>\n            <div className=\"relative inline-block h-6 w-11 flex-shrink-0\">\n              <input\n                type=\"checkbox\"\n                id=\"darkMode\"\n                checked={darkMode}\n                onChange={() => setDarkMode(!darkMode)}\n                className=\"peer sr-only\"\n              />\n              <span className=\"absolute inset-0 cursor-pointer rounded-full bg-gray-300 transition peer-checked:bg-blue-600 dark:bg-gray-600 dark:peer-checked:bg-blue-700\"></span>\n              <span className=\"absolute inset-y-0 left-0 ml-1 flex h-5 w-5 translate-y-0.5 items-center justify-center rounded-full bg-white transition peer-checked:translate-x-5 dark:bg-gray-200\"></span>\n            </div>\n          </div>\n          <p className=\"mt-1 text-xs text-gray-500 dark:text-gray-400\">\n            Enable dark mode for the entire application\n          </p>\n        </div>\n        \n        <div className=\"flex justify-end\">\n          <button\n            type=\"submit\"\n            disabled={isSubmitting}\n            className=\"inline-flex items-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-75 dark:bg-blue-700 dark:hover:bg-blue-800\"\n          >\n            {isSubmitting ? (\n              <>\n                <FontAwesomeIcon icon={faSpinner} className=\"mr-2 h-4 w-4 animate-spin\" />\n                Saving...\n              </>\n            ) : (\n              'Save Preferences'\n            )}\n          </button>\n        </div>\n      </form>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,EACJ,kBAAkB,EAClB,iBAAiB,EACjB,cAAc,EACd,cAAc,EACf,GAAG,CAAA,GAAA,kIAAA,CAAA,qBAAkB,AAAD;IAErB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAChE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEtD,wBAAwB;IACxB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc;QACpB,eAAe,YAAY,WAAW;QACtC,YAAY,YAAY,QAAQ;QAChC,YAAY,YAAY,KAAK,KAAK;IACpC,GAAG;QAAC;KAAmB;IAEvB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,gBAAgB;QAChB,WAAW;QAEX,IAAI;YACF,qBAAqB;YACrB,kBAAkB;YAClB,eAAe;YAEf,uBAAuB;YACvB,WAAW;YAEX,uCAAuC;YACvC,WAAW;gBACT,WAAW;YACb,GAAG;QACL,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAAuD;;;;;;YAIpE,yBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oKAAA,CAAA,kBAAe;4BACd,MAAM,wKAAA,CAAA,UAAO;4BACb,WAAU;;;;;;sCAEZ,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAA8C;;;;;;;;;;;;;;;;;;;;;;0BAMnE,8OAAC;gBAAK,UAAU;;kCACd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAQ;gCACR,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,IAAG;gCACH,OAAO;gCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gCAC9C,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAO;;;;;;kDACrB,8OAAC;wCAAO,OAAM;kDAAO;;;;;;;;;;;;0CAEvB,8OAAC;gCAAE,WAAU;0CAAgD;;;;;;;;;;;;kCAK/D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAQ;gCACR,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,IAAG;gCACH,OAAO;gCACP,UAAU,CAAC,IAAM,YAAY,OAAO,EAAE,MAAM,CAAC,KAAK;gCAClD,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAI;;;;;;kDAClB,8OAAC;wCAAO,OAAM;kDAAK;;;;;;kDACnB,8OAAC;wCAAO,OAAM;kDAAK;;;;;;kDACnB,8OAAC;wCAAO,OAAM;kDAAK;;;;;;;;;;;;0CAErB,8OAAC;gCAAE,WAAU;0CAAgD;;;;;;;;;;;;kCAK/D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAQ;wCACR,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,MAAK;gDACL,IAAG;gDACH,SAAS;gDACT,UAAU,IAAM,YAAY,CAAC;gDAC7B,WAAU;;;;;;0DAEZ,8OAAC;gDAAK,WAAU;;;;;;0DAChB,8OAAC;gDAAK,WAAU;;;;;;;;;;;;;;;;;;0CAGpB,8OAAC;gCAAE,WAAU;0CAAgD;;;;;;;;;;;;kCAK/D,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,MAAK;4BACL,UAAU;4BACV,WAAU;sCAET,6BACC;;kDACE,8OAAC,oKAAA,CAAA,kBAAe;wCAAC,MAAM,wKAAA,CAAA,YAAS;wCAAE,WAAU;;;;;;oCAA8B;;+CAI5E;;;;;;;;;;;;;;;;;;;;;;;AAOd", "debugId": null}}, {"offset": {"line": 1368, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/profile/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { User } from '@/types/auth';\nimport ProfileHeader from './components/ProfileHeader';\nimport ProfileForm from './components/ProfileForm';\nimport PasswordForm from './components/PasswordForm';\nimport PreferencesForm from './components/PreferencesForm';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { faSpinner } from '@fortawesome/free-solid-svg-icons';\n\nexport default function ProfilePage() {\n  const { user, isLoading } = useAuth();\n  const router = useRouter();\n\n  const [profileUser, setProfileUser] = useState<User | null>(null);\n  const [profileError, setProfileError] = useState<string | null>(null);\n\n  useEffect(() => {\n    // Redirect to login if not authenticated\n    if (!isLoading && !user) {\n      router.push('/auth/login?callbackUrl=/profile');\n      return;\n    }\n\n    // Set user from auth context\n    if (user) {\n      setProfileUser(user);\n    }\n  }, [user, isLoading, router]);\n\n  // For debugging\n  useEffect(() => {\n    console.log('Profile page user:', user);\n    console.log('Profile page profileUser:', profileUser);\n  }, [user, profileUser]);\n\n  const handleProfileUpdate = (updatedUser: User) => {\n    setProfileUser(updatedUser);\n  };\n\n  // Show loading state\n  if (isLoading) {\n    return (\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"flex h-64 items-center justify-center\">\n          <FontAwesomeIcon icon={faSpinner} className=\"h-8 w-8 animate-spin text-blue-600\" />\n        </div>\n      </div>\n    );\n  }\n\n  // Show error state\n  if (profileError) {\n    return (\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"rounded-lg border border-red-200 bg-red-50 p-6 text-center dark:border-red-800 dark:bg-red-900/30\">\n          <h2 className=\"mb-2 text-lg font-semibold text-red-800 dark:text-red-200\">\n            Error\n          </h2>\n          <p className=\"text-red-700 dark:text-red-300\">{profileError}</p>\n        </div>\n      </div>\n    );\n  }\n\n  // Show profile if user is loaded\n  if (profileUser) {\n    return (\n      <div className=\"container mx-auto px-4 py-8\">\n        <h1 className=\"mb-6 text-2xl font-bold text-gray-900 dark:text-white md:text-3xl\">\n          My Profile\n        </h1>\n\n        <ProfileHeader user={profileUser} />\n\n        <div className=\"grid grid-cols-1 gap-6 lg:grid-cols-2\">\n          <div>\n            <ProfileForm user={profileUser} onUpdate={handleProfileUpdate} />\n            <PasswordForm />\n          </div>\n          <div>\n            <PreferencesForm />\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // Fallback\n  return null;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAXA;;;;;;;;;;;AAae,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAClC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC5D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEhE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,yCAAyC;QACzC,IAAI,CAAC,aAAa,CAAC,MAAM;YACvB,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,6BAA6B;QAC7B,IAAI,MAAM;YACR,eAAe;QACjB;IACF,GAAG;QAAC;QAAM;QAAW;KAAO;IAE5B,gBAAgB;IAChB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ,GAAG,CAAC,sBAAsB;QAClC,QAAQ,GAAG,CAAC,6BAA6B;IAC3C,GAAG;QAAC;QAAM;KAAY;IAEtB,MAAM,sBAAsB,CAAC;QAC3B,eAAe;IACjB;IAEA,qBAAqB;IACrB,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,oKAAA,CAAA,kBAAe;oBAAC,MAAM,wKAAA,CAAA,YAAS;oBAAE,WAAU;;;;;;;;;;;;;;;;IAIpD;IAEA,mBAAmB;IACnB,IAAI,cAAc;QAChB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA4D;;;;;;kCAG1E,8OAAC;wBAAE,WAAU;kCAAkC;;;;;;;;;;;;;;;;;IAIvD;IAEA,iCAAiC;IACjC,IAAI,aAAa;QACf,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAoE;;;;;;8BAIlF,8OAAC,qJAAA,CAAA,UAAa;oBAAC,MAAM;;;;;;8BAErB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC,mJAAA,CAAA,UAAW;oCAAC,MAAM;oCAAa,UAAU;;;;;;8CAC1C,8OAAC,oJAAA,CAAA,UAAY;;;;;;;;;;;sCAEf,8OAAC;sCACC,cAAA,8OAAC,uJAAA,CAAA,UAAe;;;;;;;;;;;;;;;;;;;;;;IAK1B;IAEA,WAAW;IACX,OAAO;AACT", "debugId": null}}]}