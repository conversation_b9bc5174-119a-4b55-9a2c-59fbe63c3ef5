{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/data/learning-resources.ts"], "sourcesContent": ["import { LearningResource } from '@/types/learning';\n\nexport const learningResources: LearningResource[] = [\n  // Videos\n  {\n    id: 'video-1',\n    title: 'Introduction to AI Agents',\n    description: 'Learn the basics of AI agents and how they can help you in your daily tasks.',\n    type: 'video',\n    thumbnail: 'https://placehold.co/640x360.png?text=Introduction+to+AI+Agents',\n    content: 'This video provides a comprehensive introduction to AI agents, covering their capabilities, limitations, and practical applications.',\n    url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',\n    tags: ['beginner', 'introduction', 'ai-basics'],\n    author: 'Dr. <PERSON>',\n    publishedAt: new Date('2023-10-15'),\n  },\n  {\n    id: 'video-2',\n    title: 'Advanced AI Agent Techniques',\n    description: 'Dive deeper into AI agent capabilities with advanced techniques and strategies.',\n    type: 'video',\n    thumbnail: 'https://placehold.co/640x360.png?text=Advanced+AI+Agent+Techniques',\n    content: 'This advanced tutorial explores sophisticated AI agent techniques, including prompt engineering, context management, and multi-agent systems.',\n    url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',\n    tags: ['advanced', 'techniques', 'prompt-engineering'],\n    author: 'Prof. <PERSON>',\n    publishedAt: new Date('2023-11-05'),\n  },\n  {\n    id: 'video-3',\n    title: 'AI Agents for Data Analysis',\n    description: 'Learn how to use AI agents to analyze and visualize complex datasets.',\n    type: 'video',\n    thumbnail: 'https://placehold.co/640x360.png?text=AI+Agents+for+Data+Analysis',\n    content: 'This tutorial demonstrates how to leverage AI agents for data analysis tasks, from data cleaning to visualization and interpretation.',\n    url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',\n    tags: ['data-analysis', 'visualization', 'practical'],\n    author: 'Emma Rodriguez',\n    publishedAt: new Date('2023-12-10'),\n  },\n\n  // Articles\n  {\n    id: 'article-1',\n    title: 'The Future of AI Agents in Enterprise',\n    description: 'An in-depth look at how AI agents are transforming enterprise operations and decision-making.',\n    type: 'article',\n    thumbnail: 'https://placehold.co/800x400.png?text=Future+of+AI+Agents',\n    content: `\n# The Future of AI Agents in Enterprise\n\nArtificial Intelligence (AI) agents are rapidly transforming how enterprises operate, making processes more efficient and enabling new capabilities that were previously impossible. This article explores the current state and future potential of AI agents in enterprise settings.\n\n## Current Applications\n\nToday, enterprises are using AI agents for:\n\n- **Customer Service**: Handling routine inquiries and providing 24/7 support\n- **Data Analysis**: Processing large datasets to extract actionable insights\n- **Process Automation**: Streamlining workflows and reducing manual tasks\n- **Decision Support**: Providing recommendations based on complex data analysis\n\n## Future Directions\n\nLooking ahead, we can expect AI agents to evolve in several key ways:\n\n### 1. Increased Autonomy\n\nFuture AI agents will operate with greater independence, making decisions and taking actions with minimal human oversight. This will free up human workers to focus on more creative and strategic tasks.\n\n### 2. Multi-Agent Systems\n\nRather than single agents working in isolation, we'll see sophisticated networks of specialized agents collaborating to solve complex problems. These multi-agent systems will be able to tackle challenges that would be impossible for a single agent.\n\n### 3. Enhanced Personalization\n\nAI agents will become increasingly adept at understanding individual user preferences and adapting their behavior accordingly. This will enable highly personalized experiences for employees and customers alike.\n\n### 4. Seamless Integration\n\nThe distinction between AI agents and traditional software will blur, with AI capabilities being embedded throughout enterprise systems rather than existing as standalone tools.\n\n## Challenges and Considerations\n\nDespite their promise, enterprises must navigate several challenges when implementing AI agents:\n\n- **Data Privacy**: Ensuring that AI agents handle sensitive information appropriately\n- **Transparency**: Making AI decision-making processes understandable to humans\n- **Skills Gap**: Training employees to work effectively alongside AI agents\n- **Ethical Considerations**: Addressing questions about automation and job displacement\n\n## Conclusion\n\nAI agents represent a transformative technology for enterprises, offering unprecedented opportunities for efficiency, innovation, and growth. Organizations that thoughtfully integrate these tools into their operations will gain significant competitive advantages in the years ahead.\n    `,\n    tags: ['enterprise', 'future-trends', 'business'],\n    author: 'Dr. James Wilson',\n    publishedAt: new Date('2023-09-20'),\n  },\n  {\n    id: 'article-2',\n    title: 'Ethical Considerations for AI Agent Deployment',\n    description: 'Exploring the ethical dimensions of deploying AI agents in various contexts.',\n    type: 'article',\n    thumbnail: 'https://placehold.co/800x400.png?text=AI+Ethics',\n    content: `\n# Ethical Considerations for AI Agent Deployment\n\nAs AI agents become increasingly integrated into our daily lives and business operations, it's crucial to consider the ethical implications of their deployment. This article examines key ethical considerations that should guide the development and implementation of AI agent systems.\n\n## Transparency and Explainability\n\nOne of the fundamental ethical requirements for AI agents is transparency. Users should understand:\n\n- When they are interacting with an AI rather than a human\n- The general capabilities and limitations of the AI system\n- How the AI makes decisions or recommendations\n\nExplainability is equally important—the ability to provide understandable explanations for why an AI agent took a particular action or made a specific recommendation.\n\n## Privacy and Data Protection\n\nAI agents often require access to significant amounts of data to function effectively. Organizations must:\n\n- Be transparent about what data is collected and how it's used\n- Implement robust security measures to protect sensitive information\n- Comply with relevant regulations like GDPR or CCPA\n- Minimize data collection to what's necessary for the agent to function\n\n## Fairness and Bias Mitigation\n\nAI agents can inadvertently perpetuate or amplify existing biases if not carefully designed and monitored:\n\n- Training data should be diverse and representative\n- Systems should be regularly audited for biased outcomes\n- Developers should implement bias detection and mitigation techniques\n- Organizations should establish clear standards for fairness\n\n## Accountability and Oversight\n\nClear lines of accountability are essential when deploying AI agents:\n\n- Organizations should designate specific individuals responsible for AI systems\n- Regular auditing and monitoring should be implemented\n- Feedback mechanisms should allow users to report concerns\n- Processes should exist to address harmful or unintended consequences\n\n## Human Autonomy and Agency\n\nAI agents should enhance human capabilities rather than diminish human agency:\n\n- Users should maintain meaningful control over important decisions\n- Systems should be designed to complement human judgment, not replace it\n- People should be able to override AI recommendations when appropriate\n- The division of labor between humans and AI should be thoughtfully considered\n\n## Conclusion\n\nEthical deployment of AI agents requires ongoing attention and commitment. By prioritizing transparency, privacy, fairness, accountability, and human agency, organizations can harness the benefits of AI while minimizing potential harms. As these technologies continue to evolve, our ethical frameworks must evolve alongside them, ensuring that AI agents serve human values and well-being.\n    `,\n    tags: ['ethics', 'responsible-ai', 'governance'],\n    author: 'Prof. Elena Martinez',\n    publishedAt: new Date('2023-10-05'),\n  },\n\n  // Blog Posts\n  {\n    id: 'blog-1',\n    title: 'How We Built Our First AI Agent',\n    description: 'A behind-the-scenes look at our journey developing our first AI agent.',\n    type: 'blog',\n    thumbnail: 'https://placehold.co/800x400.png?text=Building+AI+Agents',\n    content: `\n# How We Built Our First AI Agent: Lessons Learned\n\nWhen we set out to build our first AI agent six months ago, we had no idea how challenging—and rewarding—the journey would be. In this post, I'll share our experience, including the obstacles we faced and the insights we gained along the way.\n\n## The Initial Vision\n\nOur goal was straightforward: create an AI agent that could help our customer support team handle routine inquiries, freeing them to focus on more complex issues. We envisioned a system that could:\n\n- Answer frequently asked questions\n- Help users troubleshoot common problems\n- Collect necessary information before escalating to a human agent\n- Learn and improve over time\n\nSimple enough, right? Not quite.\n\n## Challenges We Encountered\n\n### 1. Defining the Scope\n\nOur first challenge was scope creep. As we brainstormed capabilities, our simple support agent began transforming into an all-purpose assistant that would do everything from technical support to sales to product recommendations.\n\n**Lesson learned**: Start narrow and expand later. We eventually refocused on technical support for a specific product line, which gave us a manageable scope.\n\n### 2. Data Quality Issues\n\nWe initially trained our agent on our support documentation and past ticket logs. However, we quickly discovered inconsistencies in how our team had resolved similar issues in the past, leading to confused responses from our agent.\n\n**Lesson learned**: Clean and standardize your training data before implementation. We ended up creating a curated dataset of best-practice responses.\n\n### 3. Integration Complexities\n\nConnecting our agent to existing systems—our knowledge base, CRM, and ticketing system—proved more difficult than anticipated.\n\n**Lesson learned**: Plan your integration strategy early and thoroughly. Consider building a middleware layer if your systems don't have robust APIs.\n\n### 4. User Acceptance\n\nSome team members were hesitant to adopt the agent, fearing it might eventually replace them.\n\n**Lesson learned**: Involve end-users from the beginning and emphasize how the agent will enhance their work rather than replace it.\n\n## What Worked Well\n\nDespite the challenges, several approaches proved successful:\n\n### 1. Iterative Development\n\nRather than aiming for perfection from the start, we released early versions internally and gathered feedback. This allowed us to identify and address issues quickly.\n\n### 2. Human-in-the-Loop Design\n\nWe designed our agent to collaborate with human agents rather than operate independently. This improved performance and helped with team acceptance.\n\n### 3. Clear Success Metrics\n\nWe established specific metrics to evaluate our agent's performance, including resolution rate, customer satisfaction, and time savings for human agents.\n\n### 4. Continuous Learning\n\nWe implemented a feedback loop where human agents could flag problematic responses, helping our system improve over time.\n\n## Results and Next Steps\n\nSix months in, our AI agent now successfully handles about 40% of initial customer inquiries, reducing wait times and allowing our support team to focus on more complex issues.\n\nOur next steps include:\n\n- Expanding to additional product lines\n- Implementing more sophisticated natural language understanding\n- Adding proactive support capabilities\n- Exploring voice interface options\n\n## Conclusion\n\nBuilding an effective AI agent is more challenging than it might initially appear, but the benefits can be substantial. By starting with a focused scope, prioritizing data quality, planning integrations carefully, and involving end-users throughout the process, you can create an agent that genuinely enhances your team's capabilities.\n\nHave you built an AI agent for your organization? I'd love to hear about your experience in the comments!\n    `,\n    tags: ['case-study', 'development', 'lessons-learned'],\n    author: 'Alex Thompson',\n    publishedAt: new Date('2023-11-15'),\n  },\n  {\n    id: 'blog-2',\n    title: 'Monthly AI Agent Updates - January 2024',\n    description: 'The latest updates and improvements to our AI agent platform.',\n    type: 'blog',\n    thumbnail: 'https://placehold.co/800x400.png?text=January+Updates',\n    content: `\n# Monthly AI Agent Updates - January 2024\n\nWelcome to our first monthly update of 2024! We've been hard at work improving our AI agent platform, and we're excited to share the latest enhancements, bug fixes, and upcoming features.\n\n## New Features\n\n### 1. Enhanced Context Management\n\nOur agents can now maintain context more effectively across longer conversations. This means they can refer back to information mentioned earlier in the discussion without requiring users to repeat themselves.\n\n### 2. Multi-Modal Capabilities\n\nAgents can now process and respond to both text and images. This is particularly useful for troubleshooting scenarios where users can share screenshots of errors or problems they're experiencing.\n\n### 3. Custom Agent Creation UI\n\nWe've launched a new user interface for creating and customizing agents without coding. This drag-and-drop interface allows you to:\n\n- Define conversation flows\n- Create custom knowledge bases\n- Set up integration with your existing tools\n- Customize the agent's tone and personality\n\n## Improvements\n\n### 1. Performance Optimization\n\nWe've significantly improved response times across the platform:\n\n- 40% faster initial response time\n- 25% reduction in token usage\n- Improved handling of concurrent requests\n\n### 2. Better Error Handling\n\nAgents now recover more gracefully from misunderstandings and provide clearer guidance when they need additional information.\n\n### 3. Enhanced Analytics\n\nThe analytics dashboard now provides deeper insights into:\n\n- Common user queries and pain points\n- Success rates for different types of requests\n- Patterns in escalations to human agents\n- User satisfaction metrics\n\n## Bug Fixes\n\n- Fixed an issue where agents occasionally lost context after API errors\n- Resolved a problem with webhook integrations timing out\n- Fixed formatting issues in exported conversation logs\n- Addressed authentication issues with certain SSO providers\n\n## Coming Soon\n\nWe're excited about several features currently in development:\n\n### 1. Agent Collaboration\n\nSoon, multiple specialized agents will be able to collaborate on complex requests, each handling the aspects they're best suited for.\n\n### 2. Scheduled Actions\n\nAgents will be able to perform actions at scheduled times, such as sending follow-ups or checking on the status of issues.\n\n### 3. Voice Interface\n\nWe're working on a voice interface that will allow users to interact with agents through spoken conversation.\n\n## Community Spotlight\n\nWe want to highlight some impressive implementations from our community:\n\n- **HealthTech Solutions** created an agent that helps patients understand their medication schedules and potential side effects\n- **GlobalLogistics** built an agent that provides real-time shipping updates and resolves delivery issues\n- **EduLearn** developed a tutoring agent that adapts to different learning styles\n\n## Feedback Request\n\nWe're considering several directions for our Q2 roadmap and would love your input. Please take our quick survey to help prioritize upcoming features: [Survey Link]\n\n## Conclusion\n\nThank you for being part of our community! As always, we welcome your feedback and suggestions. You can reach our <NAME_EMAIL> or through the feedback form in your dashboard.\n\nHappy agent building!\n\nThe AI Agent Platform Team\n    `,\n    tags: ['updates', 'new-features', 'roadmap'],\n    author: 'The AI Hub Team',\n    publishedAt: new Date('2024-01-10'),\n  },\n];\n\n// Helper functions to filter resources by type\nexport const getVideos = () => learningResources.filter(resource => resource.type === 'video');\nexport const getArticles = () => learningResources.filter(resource => resource.type === 'article');\nexport const getBlogPosts = () => learningResources.filter(resource => resource.type === 'blog');\n\n// Helper function to get a resource by ID\nexport const getResourceById = (id: string) => learningResources.find(resource => resource.id === id);\n\n// Helper function to get featured or recent resources\nexport const getFeaturedResources = (count: number = 3) => {\n  // In a real app, you might have a 'featured' flag or use other criteria\n  // Here we'll just return the most recent resources\n  return [...learningResources].sort((a, b) =>\n    b.publishedAt.getTime() - a.publishedAt.getTime()\n  ).slice(0, count);\n};\n\n// Helper function to get the most recent resources across all types\nexport const getRecentResources = (count: number = 4) => {\n  return [...learningResources].sort((a, b) =>\n    b.publishedAt.getTime() - a.publishedAt.getTime()\n  ).slice(0, count);\n};\n\n// Helper function to get popular resources (simulated with a random selection)\nexport const getPopularResources = (count: number = 4) => {\n  // In a real app, this would be based on view counts or user engagement\n  // For now, we'll shuffle the array and take the first few items\n  const shuffled = [...learningResources].sort(() => 0.5 - Math.random());\n  return shuffled.slice(0, count);\n};\n\n// Helper function to combine resources with their type for display\nexport const getCombinedLearningResources = (resources: LearningResource[]) => {\n  return resources.map(resource => ({\n    ...resource,\n    // Add a URL based on the resource type\n    viewUrl: resource.type === 'video'\n      ? `/learning/videos/${resource.id}`\n      : resource.type === 'article'\n        ? `/learning/articles/${resource.id}`\n        : `/learning/blog/${resource.id}`\n  }));\n};\n"], "names": [], "mappings": ";;;;;;;;;;;AAEO,MAAM,oBAAwC;IACnD,SAAS;IACT;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,WAAW;QACX,SAAS;QACT,KAAK;QACL,MAAM;YAAC;YAAY;YAAgB;SAAY;QAC/C,QAAQ;QACR,aAAa,IAAI,KAAK;IACxB;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,WAAW;QACX,SAAS;QACT,KAAK;QACL,MAAM;YAAC;YAAY;YAAc;SAAqB;QACtD,QAAQ;QACR,aAAa,IAAI,KAAK;IACxB;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,WAAW;QACX,SAAS;QACT,KAAK;QACL,MAAM;YAAC;YAAiB;YAAiB;SAAY;QACrD,QAAQ;QACR,aAAa,IAAI,KAAK;IACxB;IAEA,WAAW;IACX;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,WAAW;QACX,SAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA8CV,CAAC;QACD,MAAM;YAAC;YAAc;YAAiB;SAAW;QACjD,QAAQ;QACR,aAAa,IAAI,KAAK;IACxB;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,WAAW;QACX,SAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAsDV,CAAC;QACD,MAAM;YAAC;YAAU;YAAkB;SAAa;QAChD,QAAQ;QACR,aAAa,IAAI,KAAK;IACxB;IAEA,aAAa;IACb;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,WAAW;QACX,SAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA8EV,CAAC;QACD,MAAM;YAAC;YAAc;YAAe;SAAkB;QACtD,QAAQ;QACR,aAAa,IAAI,KAAK;IACxB;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,WAAW;QACX,SAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAyFV,CAAC;QACD,MAAM;YAAC;YAAW;YAAgB;SAAU;QAC5C,QAAQ;QACR,aAAa,IAAI,KAAK;IACxB;CACD;AAGM,MAAM,YAAY,IAAM,kBAAkB,MAAM,CAAC,CAAA,WAAY,SAAS,IAAI,KAAK;AAC/E,MAAM,cAAc,IAAM,kBAAkB,MAAM,CAAC,CAAA,WAAY,SAAS,IAAI,KAAK;AACjF,MAAM,eAAe,IAAM,kBAAkB,MAAM,CAAC,CAAA,WAAY,SAAS,IAAI,KAAK;AAGlF,MAAM,kBAAkB,CAAC,KAAe,kBAAkB,IAAI,CAAC,CAAA,WAAY,SAAS,EAAE,KAAK;AAG3F,MAAM,uBAAuB,CAAC,QAAgB,CAAC;IACpD,wEAAwE;IACxE,mDAAmD;IACnD,OAAO;WAAI;KAAkB,CAAC,IAAI,CAAC,CAAC,GAAG,IACrC,EAAE,WAAW,CAAC,OAAO,KAAK,EAAE,WAAW,CAAC,OAAO,IAC/C,KAAK,CAAC,GAAG;AACb;AAGO,MAAM,qBAAqB,CAAC,QAAgB,CAAC;IAClD,OAAO;WAAI;KAAkB,CAAC,IAAI,CAAC,CAAC,GAAG,IACrC,EAAE,WAAW,CAAC,OAAO,KAAK,EAAE,WAAW,CAAC,OAAO,IAC/C,KAAK,CAAC,GAAG;AACb;AAGO,MAAM,sBAAsB,CAAC,QAAgB,CAAC;IACnD,uEAAuE;IACvE,gEAAgE;IAChE,MAAM,WAAW;WAAI;KAAkB,CAAC,IAAI,CAAC,IAAM,MAAM,KAAK,MAAM;IACpE,OAAO,SAAS,KAAK,CAAC,GAAG;AAC3B;AAGO,MAAM,+BAA+B,CAAC;IAC3C,OAAO,UAAU,GAAG,CAAC,CAAA,WAAY,CAAC;YAChC,GAAG,QAAQ;YACX,uCAAuC;YACvC,SAAS,SAAS,IAAI,KAAK,UACvB,CAAC,iBAAiB,EAAE,SAAS,EAAE,EAAE,GACjC,SAAS,IAAI,KAAK,YAChB,CAAC,mBAAmB,EAAE,SAAS,EAAE,EAAE,GACnC,CAAC,eAAe,EAAE,SAAS,EAAE,EAAE;QACvC,CAAC;AACH", "debugId": null}}, {"offset": {"line": 435, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/INTERNAL/---%20AI%20---/EXPERIMENTS/AI-HUB/ai-hub/src/app/learning/page.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { faVideo, faNewspaper, faBlog, faArrowRight } from '@fortawesome/free-solid-svg-icons';\nimport { getFeaturedResources } from '@/data/learning-resources';\n\nexport default function LearningHub() {\n  const featuredResources = getFeaturedResources(3);\n\n  return (\n    <div className=\"space-y-8\">\n      <div className=\"rounded-lg bg-gradient-to-r from-blue-600 to-indigo-700 p-8 text-white shadow-lg\">\n        <h1 className=\"mb-4 text-3xl font-bold\">Learning Hub</h1>\n        <p className=\"mb-6 text-lg\">\n          Explore our collection of educational resources to help you master AI agents and get the most out of our platform.\n        </p>\n        <div className=\"flex flex-wrap gap-4\">\n          <Link\n            href=\"/learning/videos\"\n            className=\"flex items-center rounded-md bg-indigo-900 bg-opacity-50 px-4 py-2 font-medium text-white backdrop-blur-sm transition hover:bg-opacity-70\"\n          >\n            <FontAwesomeIcon icon={faVideo} className=\"mr-2 h-4 w-4\" />\n            Browse Videos\n          </Link>\n          <Link\n            href=\"/learning/articles\"\n            className=\"flex items-center rounded-md bg-indigo-900 bg-opacity-50 px-4 py-2 font-medium text-white backdrop-blur-sm transition hover:bg-opacity-70\"\n          >\n            <FontAwesomeIcon icon={faNewspaper} className=\"mr-2 h-4 w-4\" />\n            Read Articles\n          </Link>\n          <Link\n            href=\"/learning/blog\"\n            className=\"flex items-center rounded-md bg-indigo-900 bg-opacity-50 px-4 py-2 font-medium text-white backdrop-blur-sm transition hover:bg-opacity-70\"\n          >\n            <FontAwesomeIcon icon={faBlog} className=\"mr-2 h-4 w-4\" />\n            Visit Blog\n          </Link>\n        </div>\n      </div>\n\n      <section>\n        <div className=\"mb-6 flex items-center justify-between\">\n          <h2 className=\"text-2xl font-bold\">Featured Resources</h2>\n          <Link\n            href=\"/learning/all\"\n            className=\"flex items-center text-sm font-medium text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300\"\n          >\n            View all resources\n            <FontAwesomeIcon icon={faArrowRight} className=\"ml-1 h-3 w-3\" />\n          </Link>\n        </div>\n\n        <div className=\"grid gap-6 md:grid-cols-2 lg:grid-cols-3\">\n          {featuredResources.map((resource) => (\n            <Link\n              key={resource.id}\n              href={`/learning/${resource.type}s/${resource.id}`}\n              className=\"group overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm transition hover:shadow-md dark:border-gray-800 dark:bg-gray-900\"\n            >\n              <div className=\"relative h-48 overflow-hidden\">\n                <img\n                  src={resource.thumbnail || 'https://via.placeholder.com/800x400.png?text=Resource'}\n                  alt={resource.title}\n                  className=\"h-full w-full object-cover transition duration-300 group-hover:scale-105\"\n                />\n                <div className=\"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-4 text-white\">\n                  <div className=\"mb-1 inline-block rounded bg-blue-600 px-2 py-1 text-xs font-medium uppercase\">\n                    {resource.type}\n                  </div>\n                  <h3 className=\"text-lg font-semibold\">{resource.title}</h3>\n                </div>\n              </div>\n              <div className=\"p-4\">\n                <p className=\"mb-3 text-sm text-gray-600 dark:text-gray-400\">{resource.description}</p>\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-xs text-gray-500 dark:text-gray-500\">\n                    {resource.publishedAt.toLocaleDateString()}\n                  </span>\n                  <span className=\"text-sm font-medium text-blue-600 dark:text-blue-400\">Read more</span>\n                </div>\n              </div>\n            </Link>\n          ))}\n        </div>\n      </section>\n\n      <section className=\"grid gap-6 md:grid-cols-3\">\n        <div className=\"rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-800 dark:bg-gray-900\">\n          <div className=\"mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400\">\n            <FontAwesomeIcon icon={faVideo} className=\"h-6 w-6\" />\n          </div>\n          <h3 className=\"mb-2 text-xl font-bold\">Video Tutorials</h3>\n          <p className=\"mb-4 text-gray-600 dark:text-gray-400\">\n            Watch step-by-step tutorials and demonstrations to help you get started with AI agents.\n          </p>\n          <Link\n            href=\"/learning/videos\"\n            className=\"inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300\"\n          >\n            Browse videos\n            <FontAwesomeIcon icon={faArrowRight} className=\"ml-1 h-3 w-3\" />\n          </Link>\n        </div>\n\n        <div className=\"rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-800 dark:bg-gray-900\">\n          <div className=\"mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-indigo-100 text-indigo-600 dark:bg-indigo-900/30 dark:text-indigo-400\">\n            <FontAwesomeIcon icon={faNewspaper} className=\"h-6 w-6\" />\n          </div>\n          <h3 className=\"mb-2 text-xl font-bold\">In-Depth Articles</h3>\n          <p className=\"mb-4 text-gray-600 dark:text-gray-400\">\n            Explore comprehensive guides and articles about AI agent capabilities, best practices, and use cases.\n          </p>\n          <Link\n            href=\"/learning/articles\"\n            className=\"inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300\"\n          >\n            Read articles\n            <FontAwesomeIcon icon={faArrowRight} className=\"ml-1 h-3 w-3\" />\n          </Link>\n        </div>\n\n        <div className=\"rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-800 dark:bg-gray-900\">\n          <div className=\"mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-purple-100 text-purple-600 dark:bg-purple-900/30 dark:text-purple-400\">\n            <FontAwesomeIcon icon={faBlog} className=\"h-6 w-6\" />\n          </div>\n          <h3 className=\"mb-2 text-xl font-bold\">Blog Updates</h3>\n          <p className=\"mb-4 text-gray-600 dark:text-gray-400\">\n            Stay up-to-date with the latest platform updates, case studies, and insights from our team.\n          </p>\n          <Link\n            href=\"/learning/blog\"\n            className=\"inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300\"\n          >\n            Visit blog\n            <FontAwesomeIcon icon={faArrowRight} className=\"ml-1 h-3 w-3\" />\n          </Link>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AANA;;;;;;AAQe,SAAS;IACtB,MAAM,oBAAoB,CAAA,GAAA,oIAAA,CAAA,uBAAoB,AAAD,EAAE;IAE/C,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,8OAAC;wBAAE,WAAU;kCAAe;;;;;;kCAG5B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC,oKAAA,CAAA,kBAAe;wCAAC,MAAM,wKAAA,CAAA,UAAO;wCAAE,WAAU;;;;;;oCAAiB;;;;;;;0CAG7D,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC,oKAAA,CAAA,kBAAe;wCAAC,MAAM,wKAAA,CAAA,cAAW;wCAAE,WAAU;;;;;;oCAAiB;;;;;;;0CAGjE,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC,oKAAA,CAAA,kBAAe;wCAAC,MAAM,wKAAA,CAAA,SAAM;wCAAE,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAMhE,8OAAC;;kCACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAqB;;;;;;0CACnC,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;oCACX;kDAEC,8OAAC,oKAAA,CAAA,kBAAe;wCAAC,MAAM,wKAAA,CAAA,eAAY;wCAAE,WAAU;;;;;;;;;;;;;;;;;;kCAInD,8OAAC;wBAAI,WAAU;kCACZ,kBAAkB,GAAG,CAAC,CAAC,yBACtB,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,CAAC,UAAU,EAAE,SAAS,IAAI,CAAC,EAAE,EAAE,SAAS,EAAE,EAAE;gCAClD,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,KAAK,SAAS,SAAS,IAAI;gDAC3B,KAAK,SAAS,KAAK;gDACnB,WAAU;;;;;;0DAEZ,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACZ,SAAS,IAAI;;;;;;kEAEhB,8OAAC;wDAAG,WAAU;kEAAyB,SAAS,KAAK;;;;;;;;;;;;;;;;;;kDAGzD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAiD,SAAS,WAAW;;;;;;0DAClF,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEACb,SAAS,WAAW,CAAC,kBAAkB;;;;;;kEAE1C,8OAAC;wDAAK,WAAU;kEAAuD;;;;;;;;;;;;;;;;;;;+BAvBtE,SAAS,EAAE;;;;;;;;;;;;;;;;0BA+BxB,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,oKAAA,CAAA,kBAAe;oCAAC,MAAM,wKAAA,CAAA,UAAO;oCAAE,WAAU;;;;;;;;;;;0CAE5C,8OAAC;gCAAG,WAAU;0CAAyB;;;;;;0CACvC,8OAAC;gCAAE,WAAU;0CAAwC;;;;;;0CAGrD,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;oCACX;kDAEC,8OAAC,oKAAA,CAAA,kBAAe;wCAAC,MAAM,wKAAA,CAAA,eAAY;wCAAE,WAAU;;;;;;;;;;;;;;;;;;kCAInD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,oKAAA,CAAA,kBAAe;oCAAC,MAAM,wKAAA,CAAA,cAAW;oCAAE,WAAU;;;;;;;;;;;0CAEhD,8OAAC;gCAAG,WAAU;0CAAyB;;;;;;0CACvC,8OAAC;gCAAE,WAAU;0CAAwC;;;;;;0CAGrD,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;oCACX;kDAEC,8OAAC,oKAAA,CAAA,kBAAe;wCAAC,MAAM,wKAAA,CAAA,eAAY;wCAAE,WAAU;;;;;;;;;;;;;;;;;;kCAInD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,oKAAA,CAAA,kBAAe;oCAAC,MAAM,wKAAA,CAAA,SAAM;oCAAE,WAAU;;;;;;;;;;;0CAE3C,8OAAC;gCAAG,WAAU;0CAAyB;;;;;;0CACvC,8OAAC;gCAAE,WAAU;0CAAwC;;;;;;0CAGrD,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;oCACX;kDAEC,8OAAC,oKAAA,CAAA,kBAAe;wCAAC,MAAM,wKAAA,CAAA,eAAY;wCAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3D", "debugId": null}}]}