[{"/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/agent/[id]/components/AgentHeader.tsx": "1", "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/agent/[id]/components/AgentInfo.tsx": "2", "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/agent/[id]/components/ChatInterface.tsx": "3", "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/agent/[id]/components/RelatedAgents.tsx": "4", "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/agent/[id]/components/SessionControls.tsx": "5", "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/agent/[id]/components/SessionHistory.tsx": "6", "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/agent/[id]/page.tsx": "7", "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/browse/components/AdvancedFilters.tsx": "8", "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/browse/components/AgentGrid.tsx": "9", "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/browse/components/AgentList.tsx": "10", "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/browse/components/FilterBar.tsx": "11", "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/browse/components/Pagination.tsx": "12", "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/browse/components/RecentlyViewed.tsx": "13", "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/browse/components/SearchBar.tsx": "14", "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/browse/page.tsx": "15", "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/favorites/page.tsx": "16", "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/layout.tsx": "17", "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/page.tsx": "18", "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/popular/components/CategoryDistributionChart.tsx": "19", "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/popular/components/MetricCard.tsx": "20", "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/popular/components/PopularAgentCard.tsx": "21", "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/popular/components/RatingDistribution.tsx": "22", "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/popular/components/TopQueries.tsx": "23", "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/popular/components/UsageByDayChart.tsx": "24", "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/popular/components/UsageByTimeChart.tsx": "25", "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/popular/components/UsageChart.tsx": "26", "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/popular/page.tsx": "27", "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/components/layout/Header.tsx": "28", "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/components/layout/Layout.tsx": "29", "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/components/layout/Sidebar.tsx": "30", "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/lib/agents.ts": "31", "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/lib/events.ts": "32", "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/lib/metrics.ts": "33", "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/lib/userPreferences.ts": "34", "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/types/agent.ts": "35", "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/types/metrics.ts": "36"}, {"size": 4220, "mtime": 1743751205929, "results": "37", "hashOfConfig": "38"}, {"size": 2677, "mtime": 1743751219945, "results": "39", "hashOfConfig": "38"}, {"size": 4770, "mtime": 1743751241534, "results": "40", "hashOfConfig": "38"}, {"size": 3082, "mtime": 1743752499568, "results": "41", "hashOfConfig": "38"}, {"size": 4830, "mtime": 1743751286162, "results": "42", "hashOfConfig": "38"}, {"size": 4861, "mtime": 1743751263362, "results": "43", "hashOfConfig": "38"}, {"size": 5125, "mtime": 1743752538226, "results": "44", "hashOfConfig": "38"}, {"size": 8351, "mtime": 1743753157676, "results": "45", "hashOfConfig": "38"}, {"size": 6432, "mtime": 1743756872330, "results": "46", "hashOfConfig": "38"}, {"size": 8102, "mtime": 1743756924863, "results": "47", "hashOfConfig": "38"}, {"size": 15288, "mtime": 1743753136054, "results": "48", "hashOfConfig": "38"}, {"size": 6524, "mtime": 1743753083059, "results": "49", "hashOfConfig": "38"}, {"size": 3282, "mtime": 1743753174235, "results": "50", "hashOfConfig": "38"}, {"size": 1613, "mtime": 1743753146647, "results": "51", "hashOfConfig": "38"}, {"size": 7924, "mtime": 1743753124172, "results": "52", "hashOfConfig": "38"}, {"size": 9735, "mtime": 1743756978224, "results": "53", "hashOfConfig": "38"}, {"size": 767, "mtime": 1743750882336, "results": "54", "hashOfConfig": "38"}, {"size": 6668, "mtime": 1743751452417, "results": "55", "hashOfConfig": "38"}, {"size": 3755, "mtime": 1743758265611, "results": "56", "hashOfConfig": "38"}, {"size": 2215, "mtime": 1743758205771, "results": "57", "hashOfConfig": "38"}, {"size": 5061, "mtime": 1743758245050, "results": "58", "hashOfConfig": "38"}, {"size": 2281, "mtime": 1743758113217, "results": "59", "hashOfConfig": "38"}, {"size": 1516, "mtime": 1743758152759, "results": "60", "hashOfConfig": "38"}, {"size": 2976, "mtime": 1743758192653, "results": "61", "hashOfConfig": "38"}, {"size": 3436, "mtime": 1743758175913, "results": "62", "hashOfConfig": "38"}, {"size": 3344, "mtime": 1743758088124, "results": "63", "hashOfConfig": "38"}, {"size": 8366, "mtime": 1743758316603, "results": "64", "hashOfConfig": "38"}, {"size": 6621, "mtime": 1743753034892, "results": "65", "hashOfConfig": "38"}, {"size": 1367, "mtime": 1743753049247, "results": "66", "hashOfConfig": "38"}, {"size": 12083, "mtime": 1743758446215, "results": "67", "hashOfConfig": "38"}, {"size": 17773, "mtime": 1743752169806, "results": "68", "hashOfConfig": "38"}, {"size": 859, "mtime": 1743756767780, "results": "69", "hashOfConfig": "38"}, {"size": 9790, "mtime": 1743758057343, "results": "70", "hashOfConfig": "38"}, {"size": 3954, "mtime": 1743756819549, "results": "71", "hashOfConfig": "38"}, {"size": 828, "mtime": 1743752045958, "results": "72", "hashOfConfig": "38"}, {"size": 1064, "mtime": 1743758009293, "results": "73", "hashOfConfig": "38"}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "yknhv5", {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/agent/[id]/components/AgentHeader.tsx", [], [], "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/agent/[id]/components/AgentInfo.tsx", [], [], "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/agent/[id]/components/ChatInterface.tsx", [], [], "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/agent/[id]/components/RelatedAgents.tsx", [], [], "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/agent/[id]/components/SessionControls.tsx", [], [], "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/agent/[id]/components/SessionHistory.tsx", [], [], "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/agent/[id]/page.tsx", ["182", "183", "184", "185", "186"], [], "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/browse/components/AdvancedFilters.tsx", [], [], "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/browse/components/AgentGrid.tsx", ["187"], [], "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/browse/components/AgentList.tsx", ["188"], [], "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/browse/components/FilterBar.tsx", [], [], "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/browse/components/Pagination.tsx", [], ["189"], "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/browse/components/RecentlyViewed.tsx", [], [], "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/browse/components/SearchBar.tsx", [], [], "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/browse/page.tsx", ["190"], [], "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/favorites/page.tsx", ["191", "192", "193"], [], "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/layout.tsx", [], [], "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/page.tsx", ["194", "195"], [], "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/popular/components/CategoryDistributionChart.tsx", [], [], "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/popular/components/MetricCard.tsx", [], [], "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/popular/components/PopularAgentCard.tsx", [], [], "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/popular/components/RatingDistribution.tsx", [], [], "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/popular/components/TopQueries.tsx", [], [], "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/popular/components/UsageByDayChart.tsx", [], [], "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/popular/components/UsageByTimeChart.tsx", [], [], "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/popular/components/UsageChart.tsx", [], [], "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/app/popular/page.tsx", ["196", "197", "198"], [], "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/components/layout/Header.tsx", ["199"], [], "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/components/layout/Layout.tsx", [], [], "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/components/layout/Sidebar.tsx", ["200"], [], "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/lib/agents.ts", [], [], "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/lib/events.ts", ["201"], [], "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/lib/metrics.ts", ["202"], [], "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/lib/userPreferences.ts", [], [], "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/types/agent.ts", [], [], "/Users/<USER>/PROJECTS/INTERNAL/AI/EXPERIMENTS/AI-HUB/ai-hub/src/types/metrics.ts", [], [], {"ruleId": "203", "severity": 2, "message": "204", "line": 13, "column": 25, "nodeType": null, "messageId": "205", "endLine": 13, "endColumn": 41}, {"ruleId": "203", "severity": 2, "message": "206", "line": 20, "column": 17, "nodeType": null, "messageId": "205", "endLine": 20, "endColumn": 25}, {"ruleId": "207", "severity": 2, "message": "208", "line": 115, "column": 26, "nodeType": "209", "messageId": "210", "suggestions": "211"}, {"ruleId": "207", "severity": 2, "message": "208", "line": 115, "column": 47, "nodeType": "209", "messageId": "210", "suggestions": "212"}, {"ruleId": "213", "severity": 2, "message": "214", "line": 117, "column": 11, "nodeType": "215", "endLine": 120, "endColumn": 12}, {"ruleId": "207", "severity": 2, "message": "208", "line": 82, "column": 72, "nodeType": "209", "messageId": "210", "suggestions": "216"}, {"ruleId": "207", "severity": 2, "message": "208", "line": 82, "column": 72, "nodeType": "209", "messageId": "210", "suggestions": "217"}, {"ruleId": "218", "severity": 1, "message": "219", "line": 33, "column": 6, "nodeType": "220", "endLine": 33, "endColumn": 8, "suggestions": "221", "suppressions": "222"}, {"ruleId": "207", "severity": 2, "message": "208", "line": 215, "column": 78, "nodeType": "209", "messageId": "210", "suggestions": "223"}, {"ruleId": "203", "severity": 2, "message": "224", "line": 6, "column": 30, "nodeType": null, "messageId": "205", "endLine": 6, "endColumn": 46}, {"ruleId": "225", "severity": 2, "message": "226", "line": 67, "column": 9, "nodeType": "227", "messageId": "228", "endLine": 67, "endColumn": 15, "fix": "229"}, {"ruleId": "218", "severity": 1, "message": "230", "line": 88, "column": 6, "nodeType": "220", "endLine": 88, "endColumn": 14, "suggestions": "231"}, {"ruleId": "203", "severity": 2, "message": "232", "line": 2, "column": 8, "nodeType": null, "messageId": "205", "endLine": 2, "endColumn": 13}, {"ruleId": "203", "severity": 2, "message": "233", "line": 4, "column": 10, "nodeType": null, "messageId": "205", "endLine": 4, "endColumn": 15}, {"ruleId": "203", "severity": 2, "message": "234", "line": 8, "column": 3, "nodeType": null, "messageId": "205", "endLine": 8, "endColumn": 23}, {"ruleId": "203", "severity": 2, "message": "235", "line": 26, "column": 25, "nodeType": null, "messageId": "205", "endLine": 26, "endColumn": 41}, {"ruleId": "203", "severity": 2, "message": "236", "line": 28, "column": 32, "nodeType": null, "messageId": "205", "endLine": 28, "endColumn": 55}, {"ruleId": "203", "severity": 2, "message": "232", "line": 4, "column": 8, "nodeType": null, "messageId": "205", "endLine": 4, "endColumn": 13}, {"ruleId": "203", "severity": 2, "message": "237", "line": 3, "column": 27, "nodeType": null, "messageId": "205", "endLine": 3, "endColumn": 36}, {"ruleId": "238", "severity": 2, "message": "239", "line": 10, "column": 65, "nodeType": "240", "messageId": "241", "endLine": 10, "endColumn": 68, "suggestions": "242"}, {"ruleId": "203", "severity": 2, "message": "243", "line": 14, "column": 3, "nodeType": null, "messageId": "205", "endLine": 14, "endColumn": 12}, "@typescript-eslint/no-unused-vars", "'getRelatedAgents' is defined but never used.", "unusedVar", "'setAgent' is assigned a value but never used.", "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["244", "245", "246", "247"], ["248", "249", "250", "251"], "@next/next/no-html-link-for-pages", "Do not use an `<a>` element to navigate to `/`. Use `<Link />` from `next/link` instead. See: https://nextjs.org/docs/messages/no-html-link-for-pages", "JSXOpeningElement", ["252", "253", "254", "255"], ["256", "257", "258", "259"], "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'onPageSizeChange' and 'pageSize'. Either include them or remove the dependency array. If 'onPageSizeChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.", "ArrayExpression", ["260"], ["261"], ["262", "263", "264", "265"], "'isAgentFavorited' is defined but never used.", "prefer-const", "'sorted' is never reassigned. Use 'const' instead.", "Identifier", "useConst", {"range": "266", "text": "267"}, "React Hook useEffect has a missing dependency: 'favoriteAgents'. Either include it or remove the dependency array.", ["268"], "'Image' is defined but never used.", "'Agent' is defined but never used.", "'getTopAgentsByMetric' is defined but never used.", "'setPopularAgents' is assigned a value but never used.", "'setCategoryDistribution' is assigned a value but never used.", "'useEffect' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["269", "270"], "'TimeRange' is defined but never used.", {"messageId": "271", "data": "272", "fix": "273", "desc": "274"}, {"messageId": "271", "data": "275", "fix": "276", "desc": "277"}, {"messageId": "271", "data": "278", "fix": "279", "desc": "280"}, {"messageId": "271", "data": "281", "fix": "282", "desc": "283"}, {"messageId": "271", "data": "284", "fix": "285", "desc": "274"}, {"messageId": "271", "data": "286", "fix": "287", "desc": "277"}, {"messageId": "271", "data": "288", "fix": "289", "desc": "280"}, {"messageId": "271", "data": "290", "fix": "291", "desc": "283"}, {"messageId": "271", "data": "292", "fix": "293", "desc": "274"}, {"messageId": "271", "data": "294", "fix": "295", "desc": "277"}, {"messageId": "271", "data": "296", "fix": "297", "desc": "280"}, {"messageId": "271", "data": "298", "fix": "299", "desc": "283"}, {"messageId": "271", "data": "300", "fix": "301", "desc": "274"}, {"messageId": "271", "data": "302", "fix": "303", "desc": "277"}, {"messageId": "271", "data": "304", "fix": "305", "desc": "280"}, {"messageId": "271", "data": "306", "fix": "307", "desc": "283"}, {"desc": "308", "fix": "309"}, {"kind": "310", "justification": "311"}, {"messageId": "271", "data": "312", "fix": "313", "desc": "274"}, {"messageId": "271", "data": "314", "fix": "315", "desc": "277"}, {"messageId": "271", "data": "316", "fix": "317", "desc": "280"}, {"messageId": "271", "data": "318", "fix": "319", "desc": "283"}, [2162, 2195], "const sorted = [...favoriteAgents];", {"desc": "320", "fix": "321"}, {"messageId": "322", "fix": "323", "desc": "324"}, {"messageId": "325", "fix": "326", "desc": "327"}, "replaceWithAlt", {"alt": "328"}, {"range": "329", "text": "330"}, "Replace with `&apos;`.", {"alt": "331"}, {"range": "332", "text": "333"}, "Replace with `&lsquo;`.", {"alt": "334"}, {"range": "335", "text": "336"}, "Replace with `&#39;`.", {"alt": "337"}, {"range": "338", "text": "339"}, "Replace with `&rsquo;`.", {"alt": "328"}, {"range": "340", "text": "341"}, {"alt": "331"}, {"range": "342", "text": "343"}, {"alt": "334"}, {"range": "344", "text": "345"}, {"alt": "337"}, {"range": "346", "text": "347"}, {"alt": "328"}, {"range": "348", "text": "349"}, {"alt": "331"}, {"range": "350", "text": "351"}, {"alt": "334"}, {"range": "352", "text": "353"}, {"alt": "337"}, {"range": "354", "text": "355"}, {"alt": "328"}, {"range": "356", "text": "349"}, {"alt": "331"}, {"range": "357", "text": "351"}, {"alt": "334"}, {"range": "358", "text": "353"}, {"alt": "337"}, {"range": "359", "text": "355"}, "Update the dependencies array to be: [onPageSizeChange, pageSize]", {"range": "360", "text": "361"}, "directive", "", {"alt": "328"}, {"range": "362", "text": "363"}, {"alt": "331"}, {"range": "364", "text": "365"}, {"alt": "334"}, {"range": "366", "text": "367"}, {"alt": "337"}, {"range": "368", "text": "369"}, "Update the dependencies array to be: [favoriteAgents, sortBy]", {"range": "370", "text": "371"}, "suggestUnknown", {"range": "372", "text": "373"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "374", "text": "375"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "&apos;", [3962, 4049], "\n            The agent you&apos;re looking for doesn't exist or has been removed.\n          ", "&lsquo;", [3962, 4049], "\n            The agent you&lsquo;re looking for doesn't exist or has been removed.\n          ", "&#39;", [3962, 4049], "\n            The agent you&#39;re looking for doesn't exist or has been removed.\n          ", "&rsquo;", [3962, 4049], "\n            The agent you&rsquo;re looking for doesn't exist or has been removed.\n          ", [3962, 4049], "\n            The agent you're looking for doesn&apos;t exist or has been removed.\n          ", [3962, 4049], "\n            The agent you're looking for doesn&lsquo;t exist or has been removed.\n          ", [3962, 4049], "\n            The agent you're looking for doesn&#39;t exist or has been removed.\n          ", [3962, 4049], "\n            The agent you're looking for doesn&rsquo;t exist or has been removed.\n          ", [2540, 2637], "\n          Try adjusting your search or filter criteria to find what you&apos;re looking for.\n        ", [2540, 2637], "\n          Try adjusting your search or filter criteria to find what you&lsquo;re looking for.\n        ", [2540, 2637], "\n          Try adjusting your search or filter criteria to find what you&#39;re looking for.\n        ", [2540, 2637], "\n          Try adjusting your search or filter criteria to find what you&rsquo;re looking for.\n        ", [2540, 2637], [2540, 2637], [2540, 2637], [2540, 2637], [952, 954], "[onPageSizeChange, pageSize]", [7363, 7472], "\n                Try adjusting your search or filter criteria to find what you&apos;re looking for.\n              ", [7363, 7472], "\n                Try adjusting your search or filter criteria to find what you&lsquo;re looking for.\n              ", [7363, 7472], "\n                Try adjusting your search or filter criteria to find what you&#39;re looking for.\n              ", [7363, 7472], "\n                Try adjusting your search or filter criteria to find what you&rsquo;re looking for.\n              ", [2753, 2761], "[favoriteAgents, sortBy]", [264, 267], "unknown", [264, 267], "never"]